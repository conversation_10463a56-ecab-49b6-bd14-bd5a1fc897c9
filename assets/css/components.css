@layer components {
    .swiper-pagination-bullet.swiper-pagination-bullet-active-next {
        background-color: white !important;
        border: 1px solid gray !important;
        width: 10px !important;
        height: 10px !important;
    }
    .swiper-pagination-bullet.swiper-pagination-bullet-active-next-next {
        background-color: white !important;
        border: 1px solid gray !important;
        width: 10px !important;
        height: 10px !important;
    }
    .swiper-pagination-bullet.swiper-pagination-bullet-active-main {
        border: none !important;
    }
    .swiper-pagination-bullet.swiper-pagination-bullet-active-prev {
        background-color: white !important;
        border: 1px solid gray !important;
        width: 10px !important;
        height: 10px !important;
    }
    .swiper-pagination-bullet.swiper-pagination-bullet-active-prev-prev {
        background-color: white !important;
        border: 1px solid gray !important;
        width: 10px !important;
        height: 10px !important;
    }

    .custom-form-input .form-control .form-input-group .form-field:focus {
        box-shadow: none;
        transition: none !important;
        border-color: #cacaca;
        cursor: text;
    }
    .custom-form-input .form-control .form-input-group .form-field {
        padding-top: 0px;
    }
    .custom-form-input
        .form-control
        .form-input-group
        .form-field::placeholder {
        font-size: 13px;
        color: #757575;
        display: flex;
        justify-content: flex-start;
        text-align: start;
        padding-bottom: -15px !important;
    }
    .custom-form-input .form-control .form-input-group .form-label {
        display: none !important;
    }
    .custom-form-input-2 .form-control .form-input-group .form-field:focus {
        box-shadow: none;
        transition: none !important;
        border-color: #cacaca;
        cursor: text;
    }
    .custom-form-input-2 .form-control .form-input-group .form-field {
        height: 56px;
        padding-top: 0px;
    }
    .custom-form-input-2
        .form-control
        .form-input-group
        .form-field::placeholder {
        font-size: 13px;
        position: relative;
        color: #757575;
        display: flex;
        justify-content: flex-start;
        text-align: start;
        padding-bottom: -15px !important;
    }

    .custom-form-input-2 select {
        font-size: 13px;
        position: relative;

        display: flex;
        justify-content: flex-start;
        text-align: start;
        padding-bottom: -15px !important;
    }

    .custom-form-input-2 .form-control .form-input-group .form-label {
        display: none !important;
    }
    .custom-form-input-2 .form-error-message {
        position: absolute;
        bottom: -10px;
        left: 2px;
        color: #9f2842;
        font-size: 0.6rem;
    }
    .custom-form-input-2 .form-control {
        padding-bottom: 5px;
    }

    .thumb-swiper .swiper-slide-thumb-active {
        @apply border !border-transparent;
    }

    .thumb-swiper .swiper-slide {
        @apply border border-transparent;
    }

    .disable-slide-navigation {
        @apply !hidden;
    }

    .home-main-slider {
        --swiper-navigation-size: 24px;
    }
    .home-main-slider.container {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    @media screen and (max-width: 768px) {
        .home-main-slider {
            --swiper-navigation-size: 18px;
        }
    }

    .home-main-slider .swiper-button-next,
    .home-main-slider .swiper-button-prev {
        @apply aspect-h-10 -mt-7 h-16 w-6 origin-bottom scale-0 !rounded-none  bg-blackAlpha-300 text-white opacity-0 md:-mt-8 md:h-24 md:w-14;
        backdrop-filter: blur(10px);
    }

    .home-main-slider .swiper-button-next {
        @apply right-0 origin-left;
    }

    .home-main-slider .swiper-button-prev {
        @apply left-0 origin-right;
    }

    .home-main-slider .swiper-button-next,
    .home-main-slider .swiper-button-prev {
        @apply scale-100 opacity-100 shadow-lg;
        background-color: rgba(0, 0, 0, 0.12);
    }

    .home-main-slider:hover .swiper-button-next,
    .home-main-slider:hover .swiper-button-prev {
        @apply scale-100 opacity-100 shadow-lg;
        background-color: rgba(0, 0, 0, 0.12);
    }

    .thumb-swiper-wrapper {
        --swiper-navigation-size: 24px;
    }

    @media screen and (max-width: 768px) {
        .thumb-swiper-wrapper {
            --swiper-navigation-size: 18px;
        }
    }

    .thumb-swiper-wrapper .swiper-button-next,
    .thumb-swiper-wrapper .swiper-button-prev {
        @apply aspect-h-10 -mt-7 h-16 w-3 origin-bottom scale-0 !rounded-none  text-white opacity-0 md:-mt-8 md:h-[100px] md:w-[50px];
        background-color: rgba(0, 0, 0, 0.12);
    }

    .thumb-swiper-wrapper .swiper-button-next {
        @apply right-0 origin-left;
    }

    .thumb-swiper-wrapper .swiper-button-prev {
        @apply left-0 origin-right;
    }

    .thumb-swiper-wrapper .swiper-button-next,
    .thumb-swiper-wrapper .swiper-button-prev {
        @apply scale-100 opacity-100 shadow-lg;
    }

    .thumb-swiper-wrapper:hover .swiper-button-next,
    .thumb-swiper-wrapper:hover .swiper-button-prev {
        @apply scale-100 opacity-100 shadow-lg;
    }

    .cookie-container-custom {
        @apply fixed bottom-0 left-0 right-0 z-[1000] flex flex-col items-center justify-between gap-4 bg-brand-cookie bg-opacity-90 px-5 py-3 text-sm text-white sm:flex-row md:px-14;
    }

    .cookie-btn-accept {
        @apply ml-3 w-80  rounded-md border-transparent bg-primary-600 py-2 text-xs font-semibold uppercase text-white transition sm:w-72 xl:w-96;
    }

    .cookie-btn-accept:hover {
        @apply bg-primary-600/80;
    }

    .cookie-btn-decline {
        @apply ml-3 w-80 rounded-md border-white bg-transparent py-2 text-xs font-semibold uppercase text-white transition sm:w-72 xl:w-96;
    }

    .cookie-btn-decline:hover {
        @apply bg-white bg-opacity-10;
    }
    .cookie-btn-wrapper {
        @apply flex-col items-center justify-center gap-2 md:flex-row md:gap-0;
    }

    .form-checkbox.form-checkbox-md {
        width: 1rem;
        height: 1rem;
    }

    .form-checkbox.form-checkbox-md {
        border-radius: 0;
        border: 1px solid #ccc;
    }
    .form-checkbox.form-checkbox-md:checked:hover {
        border-color: #ccc;
    }

    .form-checkbox.form-checkbox-md:focus {
        border-color: #ccc;
        outline: none;
        box-shadow: none;
    }
    .form-checkbox.color-checkbox {
        border-radius: 100%;
    }

    .shadow-custom {
        @apply shadow-[0_0_20px_#eee6db];
    }

    .hprose h2 {
        @apply mb-2 flex max-w-lg items-start justify-start text-[33px] font-bold 2xl:max-w-2xl;
    }

    .hprose h3 {
        @apply mb-3 max-w-lg font-semibold leading-5 tracking-normal text-base xl:text-2xl xl:leading-8 2xl:max-w-2xl;
    }

    .hprose h4 {
        @apply mb-4 max-w-lg font-sans text-sm font-medium leading-6 tracking-wide xl:leading-7 xl:text-base 2xl:max-w-2xl;
    }
    .hprose p {
        @apply mb-4 leading-relaxed text-base text-gray-700;
    }
    .hprose .hmain-text {
        @apply my-6 mt-10 max-w-lg 2xl:max-w-2xl;
    }
    .hprose .mid {
        @apply relative mb-4 mt-6 w-full max-w-4xl text-left  lg:mt-12;
    }
    .hprose .mid .mid-text {
        @apply top-12 max-w-lg text-sm font-light leading-4 tracking-normal lg:absolute lg:text-[17px] lg:leading-7;
    }

    .hprose .mid img {
        @apply h-full w-full;
    }

    .hprose .bottom-text {
        @apply mb-4 grid place-items-center;
    }
    .hprose .bottom-text h2 {
        @apply mt-16 text-center text-lg xl:text-3xl;
    }
    .hprose .bottom-text h4 {
        @apply mt-4 max-w-md text-center text-sm !font-light leading-5 !tracking-widest xl:text-lg xl:leading-7;
    }

    .production-swiper-pagination .swiper-pagination-bullet {
        @apply h-[10px] w-[10px]  bg-secondary-100;
    }

    .ckb-swiper-pagination .swiper-pagination-bullet {
        @apply h-[10px] w-[10px]  bg-secondary-100;
    }
    .ckb-swiper-pagination .swiper-wrapper {
        @apply pb-12;
    }

    .cprose h1 {
        @apply mb-2 flex max-w-lg items-start justify-start font-dm-serif text-4xl font-bold 2xl:max-w-full;
    }

    .cprose h2 {
        @apply mb-2 flex max-w-lg items-start justify-start font-dm-serif text-[33px] font-bold 2xl:max-w-2xl;
    }

    .cprose h3 {
        @apply mb-3 max-w-lg font-semibold leading-5 tracking-normal text-base xl:text-2xl xl:leading-8 2xl:max-w-2xl;
    }

    .cprose h4 {
        @apply mb-4 max-w-lg font-sans text-sm font-medium leading-6 tracking-wide xl:leading-7 xl:text-base 2xl:max-w-2xl;
    }
    .cprose p {
        @apply mb-4  text-sm leading-relaxed text-[#3b3b3b];
    }
    .cprose .hmain-text {
        @apply my-6 mt-10 max-w-lg 2xl:max-w-2xl;
    }
    .cprose .mid {
        @apply relative mb-4 mt-6 w-full max-w-4xl text-left  lg:mt-12;
    }
    .cprose .mid .mid-text {
        @apply top-12 max-w-lg text-sm font-light leading-4 tracking-normal lg:absolute lg:text-[17px] lg:leading-7;
    }

    .cprose .mid img {
        @apply h-full w-full;
    }

    .cprose .bottom-text {
        @apply mb-4 grid place-items-center;
    }
    .cprose .bottom-text h2 {
        @apply mt-16 text-center text-lg xl:text-3xl;
    }
    .cprose .bottom-text h4 {
        @apply mt-4 max-w-md text-center text-sm !font-light leading-5 !tracking-widest xl:text-lg xl:leading-7;
    }
}

.btn-whatsapp-pulse {
    background: #25d366;
    color: white;
    position: fixed;
    bottom: 0px;
    right: 30px;
    font-size: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 0;
    height: 0;
    padding: 35px;
    text-decoration: none;
    border-radius: 50%;
    animation-name: pulse;
    animation-duration: 1.5s;
    animation-timing-function: ease-out;
    animation-iteration-count: infinite;
}

@media screen and (max-width: 768px) {
    .btn-whatsapp-pulse {
        padding: 25px;
        left: 30px;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.5);
    }
    80% {
        box-shadow: 0 0 0 14px rgba(37, 211, 102, 0);
    }
}

.btn-whatsapp-pulse-border {
    bottom: 90px;
    right: 35px;
    animation-play-state: paused;
}

.btn-whatsapp-pulse-border::before {
    content: '';
    position: absolute;
    border-radius: 50%;
    padding: 25px;
    border: 5px solid #25d366;
    opacity: 0.75;
    animation-name: pulse-border;
    animation-duration: 1.5s;
    animation-timing-function: ease-out;
    animation-iteration-count: infinite;
}

@keyframes pulse-border {
    0% {
        padding: 25px;
        opacity: 0.75;
    }
    75% {
        padding: 50px;
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}

.form-input-addon {
    box-shadow: none !important;
}

.un-discounted-price {
    @apply !text-sm  xl:!text-base xl:!text-gray-400;
}

.sticky-details > span > span:first-of-type {
    font-size: 18px !important;
    color: #cacaca !important;
}

.ql-video{
    width: 100% !important;
    height: 350px !important;
}
