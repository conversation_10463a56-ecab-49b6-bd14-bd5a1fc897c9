const defaultConfig = require('./core/config/tailwind');
const themeConfig = require('./core/config/theme');

/** @type {import('tailwindcss').Config} */
module.exports = {
    ...defaultConfig,
    theme: {
        ...defaultConfig.theme,
        extend: {
            ...defaultConfig.theme.extend,
            container: {
                center: true,
                padding: '1rem',
                screens: {xl: '1400px'}
            },
            colors: {
                ...defaultConfig.theme.extend.colors,
                primary: {
                    ...defaultConfig.theme.extend.colors.primary,
                    50: '#FDF3F4',
                    100: '#FBE8E9',
                    200: '#f7d4d7',
                    300: '#f1b0b6',
                    400: '#e88490',
                    500: '#8b263f',
                    600: '#9F2842',
                    700: '#9f2842',
                    800: '#8b263f',
                    900: '#78233b'
                },
                secondary: {
                    50: '#F9ECEE',
                    100: '#9F2842',
                    200: '#F7F7F7',
                    300: '#D1C5AC',
                    400: '#686868',
                    500: '#BABABA',
                    600: '#9F2842',
                    700: '#C2A364',
                    800: '#232323',
                    900: '#797877'
                },
                brand: {
                    budget: '#FCF5F6',
                    pink: '#FEFCFC',
                    code: '#D4D4D4',
                    buttongray: '#ECF0F3',
                    search: '#F4F4F4',
                    black: '#333333',
                    clr: '#3B3B3B',
                    cookie: '#2d2d2d'
                },
                boxShadow: {
                    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                    '3xl': '0 35px 60px -15px rgba(0, 0, 0, 0.3)'
                }
            },
            fontSize: {
                '2xs': '0.6rem',
                '3xs': '0.7rem'
            },
            fontFamily: {
                sans: [
                    'var(--mulish)',
                    ...themeConfig.fontFamily.split(',').map(p => p.trim())
                ],
                'dm-serif': ['var(--dm-serif)'],
                hurme: ['var(--hurme)'],
                spectral: ['var(--spectral)']
            },
            backgroundImage: {
                'custom-gradient':
                    'linear-gradient(282.05deg, #F3F4F4 8.08%, #D4DADE 46.46%, #D3D9DD 90.01%)'
            }
        }
    }
};
