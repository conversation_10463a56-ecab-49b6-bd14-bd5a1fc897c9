import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    useEffect,
    useMemo,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev} from '@core/helpers';
import {useStore, useViewportSize} from '@core/hooks';
import MobileHeader from './MobileHeader';
import MobileSearch from './MobileSearch';
import {MobileContextType} from './types';
import {Filter} from '@core/types';

export const MobileContext = createContext<MobileContextType>(null as any);

if (isDev) {
    MobileContext.displayName = 'MobileContext';
}

export const MobileProvider: FC<PropsWithChildren<unknown>> = memo(
    ({children}) => {
        const router = useRouter();
        const {width: viewportWidth} = useViewportSize();
        const [mobileTitle, setMobileTitle] = useState('');
        const [isMobileSearchShown, setIsMobileSearchShown] = useState(false);
        const {navigation, pageType} = useStore();
        const [isMobile, setIsMobile] = useState(false);
        const [isDateChecked, setIsDateChecked] = useState(false);
        const [selectedFilter, setSelectedFilter] = useState<Filter>();
        const isMobileViewport = useMemo(
            () => viewportWidth > 0 && viewportWidth < 1400,
            [viewportWidth]
        );

        useEffect(() => {
            if (isMobileViewport) {
                setIsMobile(true);
            }
        }, [isMobileViewport]);

        const cleanPath = useMemo(() => {
            try {
                const path = router.asPath.startsWith('/')
                    ? router.asPath
                    : `/${router.asPath}`;
                const [pathname, search] = path.split('?');
                if (search) {
                    const searchParams = new URLSearchParams(search);

                    const trackingParams = [
                        'srsltid',
                        'utm_source',
                        'utm_medium',
                        'utm_campaign',
                        'gclid',
                        'gad_source',
                        'gbraid'
                    ];

                    trackingParams.forEach(param => searchParams.delete(param));

                    const cleanSearch = searchParams.toString();
                    return cleanSearch
                        ? `${pathname}?${cleanSearch}`
                        : pathname;
                }

                return pathname;
            } catch (error) {
                console.error('Error cleaning path:', error);
                return router.asPath;
            }
        }, [router.asPath]);

        const activeTab = useMemo(() => {
            if (cleanPath.includes('/mobile/menu')) {
                return 'menu';
            } else if (cleanPath.includes('/mobile/my-cart')) {
                return 'my-cart';
            } else if (cleanPath.includes('/my-favorites')) {
                return 'my-favorites';
            } else if (
                cleanPath.includes('/mobile/my-account') ||
                (cleanPath !== '/account/my-favorites' &&
                    (cleanPath.includes('/auth') ||
                        cleanPath.includes('/account/')))
            ) {
                return 'my-account';
            }

            return 'store';
        }, [cleanPath]);

        const value: any = useMemo(
            () => ({
                mobileTitle,
                isMobileSearchShown,
                activeTab,
                isMobile,
                setMobileTitle,
                setIsMobileSearchShown,
                selectedFilter,
                setSelectedFilter
            }),
            [
                mobileTitle,
                isMobileSearchShown,
                activeTab,
                isMobile,
                setIsMobileSearchShown,
                selectedFilter,
                setSelectedFilter
            ]
        );

        const mainNavItems = useMemo(
            () =>
                (navigation || []).filter(item => {
                    return (
                        item.depth === 0 &&
                        item.type === 'product-catalog' &&
                        item.showInMainMenu
                    );
                }),
            // eslint-disable-next-line react-hooks/exhaustive-deps
            []
        );

        const isHome = useMemo(() => cleanPath === '/', [cleanPath]);

        const shouldShowScrollNav =
            mainNavItems.find(item => `/${item.slug}` === cleanPath) !==
                undefined ||
            isHome ||
            pageType === 'product' ||
            pageType === 'catalog';

        const topBarOpportunity = useMemo(() => {
            return navigation
                ?.filter(
                    navigationItem =>
                        navigationItem.type === 'story' &&
                        navigationItem.section === 'page-top-opportunity' &&
                        navigationItem.depth === 0
                )
                .map(navigationItem => ({
                    title: navigationItem.name,
                    link: navigationItem.href,
                    images: navigationItem.images,
                    description: navigationItem.description
                }))?.[0];
        }, [navigation]);

        const dateControl = useMemo(() => {
            let targetDate;
            const description = topBarOpportunity?.description || '';

            if (description) {
                const parts = description.split(' ');
                if (parts.length === 2) {
                    const dateParts = parts[0].split('-');
                    const timeParts = parts[1].split(':');

                    if (dateParts.length === 3 && timeParts.length >= 1) {
                        const day = parseInt(dateParts[0], 10);
                        const month = parseInt(dateParts[1], 10) - 1;
                        const year = parseInt(dateParts[2], 10);
                        const hour = parseInt(timeParts[0], 10);
                        const minute =
                            timeParts.length > 1
                                ? parseInt(timeParts[1], 10)
                                : 0;

                        targetDate = new Date(
                            year,
                            month,
                            day,
                            hour,
                            minute,
                            0
                        );
                    }
                }
            }

            if (!targetDate || isNaN(targetDate.getTime())) {
                targetDate = new Date();
            }

            const now = new Date();
            const isExpired = targetDate.getTime() <= now.getTime();

            setIsDateChecked(true);

            return {
                targetDate,
                isExpired
            };
        }, [topBarOpportunity?.description]);

        const showTopBarOpportunity =
            isDateChecked && topBarOpportunity && !dateControl.isExpired;

        return (
            <MobileContext.Provider value={value}>
                <div
                    className={cls(
                        'relative h-full w-full overflow-hidden xl:pt-0',
                        {'pt-mobile-header': !shouldShowScrollNav},
                        {'pt-[148px]': shouldShowScrollNav},
                        {
                            'xs:!pt-[275px] !pt-[275px] sm:!pt-[230px] md:!pt-[355px] lg:!pt-[395px] xl:!pt-0':
                                shouldShowScrollNav &&
                                topBarOpportunity &&
                                showTopBarOpportunity
                        },
                        {
                            'xs:!pt-[210px] !pt-[210px] sm:!pt-[230px] md:!pt-[300px] lg:!pt-[380px] xl:!pt-0':
                                shouldShowScrollNav &&
                                topBarOpportunity &&
                                !showTopBarOpportunity
                        }
                    )}
                >
                    <MobileHeader
                        shouldShowScrollNav={shouldShowScrollNav}
                        mainNavItems={mainNavItems}
                    />

                    <div className="content-wrapper h-full w-full overflow-y-auto">
                        {children}
                    </div>
                </div>

                <MobileSearch />
            </MobileContext.Provider>
        );
    }
);

if (isDev) {
    MobileProvider.displayName = 'MobileProvider';
}
