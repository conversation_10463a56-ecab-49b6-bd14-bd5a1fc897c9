import {Filter} from '@core/types';
import {Dispatch, SetStateAction} from 'react';

export type MobileContextType = {
    mobileTitle: string;
    isMobile: boolean;
    isMobileSearchShown: boolean;
    activeTab: 'store' | 'menu' | 'my-cart' | 'my-favorites' | 'my-account';
    setMobileTitle: Dispatch<SetStateAction<string>>;
    setIsMobileSearchShown: Dispatch<SetStateAction<boolean>>;
    selectedFilter: Filter;
    setSelectedFilter: Dispatch<SetStateAction<Filter | undefined>>;
};
