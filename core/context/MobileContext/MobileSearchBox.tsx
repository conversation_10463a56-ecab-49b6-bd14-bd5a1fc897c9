import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useMobile, useTrans} from '@core/hooks';
import {UiInput} from '@core/components/ui';
import {SearchIcon} from '@core/icons/outline';

const MobileSearchBox: FC = memo(() => {
    const t = useTrans();
    const {setIsMobileSearchShown} = useMobile();

    return (
        <div className="container block py-2 xl:hidden">
            <UiInput.Group
                size="lg"
                className="flex-1 items-center justify-normal"
            >
                <UiInput.LeftElement className="flex items-center justify-center">
                    <SearchIcon className="h-4 w-4 text-default" />
                </UiInput.LeftElement>

                <UiInput
                    placeholder={t('36 Piece Cutlery Set')}
                    className="h-10 flex-1 rounded-none border-gray-500 placeholder:text-3xs hover:border-gray-500 focus:!border-gray-500 focus:!ring-0"
                    readOnly
                    onFocus={() => setIsMobileSearchShown(true)}
                />

                <UiInput.RightElement className="flex items-center justify-start">
                    <div>
                        <p className="text-xs text-gray-500">ARA</p>
                    </div>
                </UiInput.RightElement>
            </UiInput.Group>
        </div>
    );
});

if (isDev) {
    MobileSearchBox.displayName = 'MobileSearchBox';
}

export default MobileSearchBox;
