import {
    FC,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import siteLogo from '@assets/images/common/site-logo.svg';
import storeConfig from '~/store.config';
import {NavigationItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useCart, useMobile, useStore, useTrans} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import MobileSearchBox from './MobileSearchBox';
import {
    ChevronLeft,
    Close,
    Favourite,
    HiOutlineShoppingBag,
    Menu,
    User
} from '@components/icons';
import {Autoplay} from 'swiper/modules';
import {SliderInterface} from '@core/components/ui/Slider';
import CountdownTimer from '@components/common/CountdownTimer';
import NehirUstMobilBanner from '@assets/images/common/nehir_ust_bant_mobil.jpg';
type MobileHeaderProps = {
    title?: string;
    shouldShowScrollNav?: boolean;
    mainNavItems?: NavigationItem[];
};

const MobileHeader: FC<MobileHeaderProps> = memo(({shouldShowScrollNav}) => {
    const router = useRouter();
    const onGoBack = useCallback(() => router.back(), [router]);
    const t = useTrans();
    const {itemCount: cartItemsCount} = useCart();
    const {activeTab} = useMobile();

    const swiperRef = useRef<SliderInterface>();

    const {navigation, pageType} = useStore();
    const [isScrolled, setIsScrolled] = useState(false);
    const [isDateChecked, setIsDateChecked] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 650) {
                setIsScrolled(true);
            } else {
                setIsScrolled(false);
            }
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    const topBarItems = useMemo(() => {
        return navigation
            .filter(
                navigationItem =>
                    navigationItem.type === 'story' &&
                    navigationItem.section === 'top-bar' &&
                    navigationItem.depth === 0
            )
            .map(navigationItem => ({
                title: navigationItem.name,
                link: navigationItem.href
            }));
    }, [navigation]);

    const topBarOpportunity = useMemo(() => {
        return navigation
            ?.filter(
                navigationItem =>
                    navigationItem.type === 'story' &&
                    navigationItem.section === 'page-top-opportunity' &&
                    navigationItem.depth === 0
            )
            .map(navigationItem => ({
                title: navigationItem.name,
                link: navigationItem.href,
                images: navigationItem.images,
                description: navigationItem.description
            }))?.[0];
    }, [navigation]);

    const dateControl = useMemo(() => {
        let targetDate;
        const description = topBarOpportunity?.description || '';

        if (description) {
            const parts = description.split(' ');
            if (parts.length === 2) {
                const dateParts = parts[0].split('-');
                const timeParts = parts[1].split(':');

                if (dateParts.length === 3 && timeParts.length >= 1) {
                    const day = parseInt(dateParts[0], 10);
                    const month = parseInt(dateParts[1], 10) - 1;
                    const year = parseInt(dateParts[2], 10);
                    const hour = parseInt(timeParts[0], 10);
                    const minute =
                        timeParts.length > 1 ? parseInt(timeParts[1], 10) : 0;

                    targetDate = new Date(year, month, day, hour, minute, 0);
                }
            }
        }

        if (!targetDate || isNaN(targetDate.getTime())) {
            targetDate = new Date();
        }

        const now = new Date();

        const isExpired = targetDate.getTime() <= now.getTime();

        setIsDateChecked(true);

        return {
            targetDate,
            isExpired
        };
    }, [topBarOpportunity?.description]);

    const showTopBarOpportunity =
        isDateChecked && topBarOpportunity && !dateControl.isExpired;

    return (
        <div
            className={cls(
                'fixed left-0 right-0 top-0 z-[51] w-full border-b border-gray-300 bg-white xl:hidden',
                {'flex h-mobile-header': !shouldShowScrollNav}
            )}
        >
            {shouldShowScrollNav ? (
                <>
                    {pageType !== 'product' && (
                        <div
                            className={cls(
                                'transition-all duration-300 ease-in-out',
                                isScrolled
                                    ? 'pointer-events-none  max-h-0 opacity-0'
                                    : 'visible relative max-h-[300px] opacity-100'
                            )}
                        >
                            {topBarOpportunity && (
                                <div className="relative">
                                    <UiImage
                                        src={
                                            topBarOpportunity &&
                                            topBarOpportunity.images
                                                ? topBarOpportunity.images?.[1]
                                                : '/no-images.png'
                                        }
                                        alt={topBarOpportunity.title}
                                        className="w-full"
                                        width={1900}
                                        height={1000}
                                    />
                                    {showTopBarOpportunity && (
                                        <div className="transition-all duration-100 ease-in-out">
                                            <CountdownTimer
                                                title={
                                                    topBarOpportunity?.title ||
                                                    ''
                                                }
                                                targetDate={
                                                    topBarOpportunity?.description ||
                                                    ''
                                                }
                                                className="sm:px-2 sm:py-0"
                                            />
                                        </div>
                                    )}
                                </div>
                            )}

                            <div
                                className={cls(
                                    'flex items-center justify-center transition-all duration-100 ease-in-out',
                                    isScrolled
                                        ? '-translate-y-6 transform opacity-0'
                                        : 'translate-y-0 transform opacity-100'
                                )}
                            >
                                <div
                                    className={cls(
                                        'relative  w-full flex-nowrap bg-secondary-50 text-center transition-all duration-100 ease-in-out',
                                        isScrolled ? 'h-0' : 'h-7'
                                    )}
                                >
                                    <div className="relative flex items-center justify-between pt-1.5">
                                        <div className="relative overflow-hidden">
                                            <UiSlider
                                                modules={[Autoplay]}
                                                autoplay={{delay: 3000}}
                                                spaceBetween={12}
                                                loop
                                                onSwiper={swiper => {
                                                    swiperRef.current = swiper;
                                                }}
                                            >
                                                {topBarItems.map(
                                                    (link, index) => (
                                                        <UiSlider.Slide
                                                            key={index}
                                                        >
                                                            <div className="flex h-full cursor-pointer items-center justify-center font-extrabold">
                                                                <p className="cursor-pointer font-dm-serif text-xs font-medium">
                                                                    {t(
                                                                        link.title
                                                                    )}
                                                                </p>
                                                            </div>
                                                        </UiSlider.Slide>
                                                    )
                                                )}
                                            </UiSlider>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="container z-20 flex items-center justify-between border-b border-gray-100 py-2">
                        <UiLink href="/mobile/menu">
                            <Menu className="h-8 w-8 pt-1.5 text-secondary-100" />
                        </UiLink>
                        <UiLink href="/" aria-label="Logo" className="py-1.5">
                            <UiImage
                                src={siteLogo}
                                alt={storeConfig.title}
                                priority
                            />
                        </UiLink>
                        <div className="flex">
                            <UiLink
                                className="flex flex-col items-center p-2"
                                href="/mobile/my-cart"
                            >
                                <div className="relative flex flex-1 flex-row items-center justify-between">
                                    {activeTab === 'my-cart' ? (
                                        <HiOutlineShoppingBag className="h-7 w-7 text-secondary-600" />
                                    ) : (
                                        <HiOutlineShoppingBag className="h-7 w-7" />
                                    )}

                                    {cartItemsCount >= 0 && (
                                        <span className="absolute left-[14px] top-[55%] flex -translate-x-1/2 -translate-y-1/2 transform items-center justify-center text-sm font-medium text-brand-black">
                                            {cartItemsCount}
                                        </span>
                                    )}

                                    <div className="ml-2 hidden text-[9px] font-bold uppercase text-brand-black sm:flex">
                                        {t('My Cart')}
                                    </div>
                                </div>
                            </UiLink>
                            <UiLink
                                className="flex flex-row items-center justify-between p-2"
                                href="/account/my-favorites"
                            >
                                {activeTab === 'my-favorites' ? (
                                    <Favourite className="h-8 w-8 text-secondary-600" />
                                ) : (
                                    <Favourite className="h-7 w-7" />
                                )}

                                <div className="ml-2 hidden text-[9px] font-bold uppercase text-brand-black sm:flex">
                                    {t('My Favorites')}
                                </div>
                            </UiLink>
                            <UiLink
                                className="flex flex-row items-center justify-between p-2"
                                href="/mobile/my-account"
                            >
                                {activeTab === 'my-account' ? (
                                    <User className="h-7 w-7 text-secondary-600" />
                                ) : (
                                    <User className="h-7 w-7" />
                                )}

                                <div className="ml-2 hidden text-[9px] font-bold uppercase text-brand-black sm:flex">
                                    {t('Sign In')}
                                </div>
                            </UiLink>
                            {/*
                            <UiLink
                                className="flex items-center p-2 pr-0 text-secondary-600 md:hidden"
                                href="https://www.nehirkitchen.com/"
                            >
                                EN
                            </UiLink> */}
                        </div>
                    </div>

                    <MobileSearchBox />
                </>
            ) : (
                <div className="container flex items-center justify-between px-6  ">
                    {router.asPath === '/mobile/menu' ? (
                        <div
                            className="flex cursor-pointer items-center gap-2 "
                            onClick={onGoBack}
                        >
                            <Close className="h-[18px] w-[18px]" />
                            <span className="text-xs font-bold text-secondary-100">
                                KAPAT
                            </span>
                        </div>
                    ) : (
                        <div
                            className="flex cursor-pointer items-center gap-2 "
                            onClick={onGoBack}
                        >
                            <ChevronLeft className="h-5 w-5 fill-secondary-100 stroke-secondary-100 stroke-[10px]" />
                            <span className="text-xs font-bold text-secondary-100">
                                GERİ
                            </span>
                        </div>
                    )}

                    <UiLink href="/" className="flex justify-center">
                        <UiImage
                            src="/favicon.ico"
                            alt=""
                            width={20}
                            height={20}
                        />
                    </UiLink>
                    <div></div>

                    {/* <UiLink className="text-secondary-600" href="/">
                        EN
                    </UiLink> */}
                </div>
            )}
        </div>
    );
});

if (isDev) {
    MobileHeader.displayName = 'MobileHeader';
}

export default MobileHeader;
