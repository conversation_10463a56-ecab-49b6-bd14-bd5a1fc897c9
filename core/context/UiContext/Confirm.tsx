import {FC, memo, useCallback, useState} from 'react';
import {isDev} from '@core/helpers';
import {useUI} from '@core/hooks';
import {UiButton} from '@core/components/ui';

type ConfirmProps = {
    title: string;
    description: string;
    actionLabel: string;
    onConfirm: () => any;
};

const Confirm: FC<ConfirmProps> = memo(
    ({description, actionLabel, onConfirm}) => {
        const {closeSideBar} = useUI();
        const [isLoading, setIsLoading] = useState(false);

        const onClick = useCallback(async () => {
            setIsLoading(true);

            try {
                await onConfirm();
                closeSideBar();
            } catch (error) {}

            setIsLoading(false);
        }, [closeSideBar, onConfirm]);

        return (
            <div className="mt-4 flex flex-col px-4 pb-4 xl:mt-2 xl:px-6 xl:pb-6">
                <div className="">{description}</div>

                <UiButton
                    className="mt-4 w-full xl:mt-6"
                    variant="solid"
                    color="danger"
                    size="xl"
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={onClick}
                >
                    {actionLabel}
                </UiButton>
            </div>
        );
    }
);

if (isDev) {
    Confirm.displayName = 'Confirm';
}

export default Confirm;
