import {FC, isValidElement, memo, useContext, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {UiModal, UiSideBar} from '@core/components/ui';
import {UiContext} from '.';

const Dialogs: FC = memo(() => {
    const {
        modalView,
        isModalShown,
        modalTitle,
        modalParams,
        closeModal,

        sideBarView,
        isSideBarShown,
        sideBarTitle,
        sideBarSize,
        closeSideBar
    } = useContext(UiContext);

    const ModalView = useMemo(
        () =>
            // @ts-ignore
            !!modalView &&
            !isValidElement(modalView) &&
            // @ts-ignore
            (!!modalView.render || !!modalView.type)
                ? modalView
                : () => modalView,
        [modalView]
    );
    const SideBarView = useMemo(
        () =>
            // @ts-ignore
            !!sideBarView &&
            !isValidElement(sideBarView) &&
            // @ts-ignore
            (!!sideBarView.render || !!sideBarView.type)
                ? sideBarView
                : () => sideBarView,
        [sideBarView]
    );

    return (
        <>
            <UiModal
                isShown={isModalShown}
                title={modalTitle}
                isLarge={!!modalParams.isLarge}
                isClosable={modalParams.isClosable}
                onClose={closeModal}
            >
                {Object.keys(modalParams).length > 0 ? (
                    // @ts-ignore
                    <ModalView {...modalParams} />
                ) : (
                    // @ts-ignore
                    <ModalView />
                )}
            </UiModal>

            <UiSideBar
                isShown={isSideBarShown}
                title={sideBarTitle}
                size={sideBarSize}
                onClose={closeSideBar}
            >
                {/* @ts-ignore*/}
                <SideBarView />
            </UiSideBar>
        </>
    );
});

if (isDev) {
    Dialogs.displayName = 'Dialogs';
}

export default Dialogs;
