import {Dispatch, FC, ReactNode, SetStateAction} from 'react';

export type UiContextType = {
    isModalShown: boolean;
    modalTitle: string | null;
    modalView: ReactNode | null;
    modalParams: Record<string, any>;
    openModal: (
        title: string | null,
        view: ReactNode | FC<any>,
        params?: Record<string, any>
    ) => void;
    closeModal: () => void;

    isSideBarShown: boolean;
    sideBarTitle: string | null;
    setSideBarTitle: Dispatch<SetStateAction<string | null>>;
    sideBarView: ReactNode | null;
    sideBarSize: 'normal' | 'large' | number;
    openSideBar: (
        title: string | null,
        view: ReactNode | FC<any>,
        size?: 'normal' | 'large' | number,
        onClose?: () => void
    ) => void;
    closeSideBar: () => void;

    confirm: (
        title: string,
        description: string,
        actionLabel: string,
        onConfirm: () => void
    ) => void;
};
