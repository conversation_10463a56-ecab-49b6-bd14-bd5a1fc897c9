import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    ReactNode,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {toast} from 'sonner';
import {useRouter} from 'next/router';
import {defaultTheme, VechaiProvider, VechaiTheme} from '@vechaiui/react';
import {isDev} from '@core/helpers';
import Confirm from './Confirm';
import {UiContextType} from './types';

export const UiContext = createContext<UiContextType>({} as any);

if (isDev) {
    UiContext.displayName = 'UiContext';
}

export const UiProvider: FC<PropsWithChildren<unknown>> = memo(props => {
    const {children} = props;
    const router = useRouter();
    const [isModalShown, setIsModalShown] = useState(false);
    const [modalTitle, setModalTitle] = useState<string | null>(null);
    const [modalView, setModalView] = useState<ReactNode | null>(null);
    const [modalParams, setModalParams] = useState<Record<string, any>>({});
    const [isSideBarShown, setIsSideBarShown] = useState(false);
    const [sideBarTitle, setSideBarTitle] = useState<string | null>(null);
    const [sideBarView, setSideBarView] = useState<ReactNode | null>(null);
    const [sideBarSize, setSideBarSize] = useState<'normal' | 'large' | number>(
        'normal'
    );
    const [onSideBarClosed, setOnSideBarClosed] = useState(() => () => {});

    // Scroll to top.
    useEffect(() => {
        const handleRouteChange = (
            url: string,
            {shallow}: {shallow: boolean}
        ) => {
            if (shallow) {
                return;
            }

            const container = document.querySelector('.content-wrapper');
            if (container !== null) {
                container.scrollTo({top: 0});
            }
        };

        router.events.on('routeChangeComplete', handleRouteChange);

        return () => {
            router.events.off('routeChangeComplete', handleRouteChange);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const closModalTimerRef = useRef<any>();
    const openModal = useCallback(
        (
            title: string | null,
            view: ReactNode,
            params: Record<string, any> = {}
        ) => {
            try {
                clearTimeout(closModalTimerRef.current);
            } catch (error: any) {}

            closeSideBar();
            setIsModalShown(true);
            setModalTitle(title);
            toast.dismiss();

            // @ts-ignore
            if (!view?.type) {
                // @ts-ignore
                view = memo(view);
            }

            setModalView(view);

            if (typeof params === 'object' && params !== null) {
                setModalParams(params);
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );
    const closeModal = useCallback(() => {
        setIsModalShown(false);

        closModalTimerRef.current = setTimeout(() => {
            setModalTitle(null);
            setModalView(null);
            setModalParams({});
        }, 500);
    }, []);

    const closSideBarTimerRef = useRef<any>();
    const openSideBar = useCallback(
        (
            title: string | null,
            view: ReactNode,
            size: 'normal' | 'large' | number = 'normal',
            onClose?: () => void
        ) => {
            try {
                clearTimeout(closSideBarTimerRef.current);
            } catch (error: any) {}

            closeModal();
            setIsSideBarShown(true);
            setSideBarTitle(title);
            toast.dismiss();

            // @ts-ignore
            if (!view?.type) {
                // @ts-ignore
                view = memo(view);
            }

            setSideBarView(view);
            setSideBarSize(size);
            if (typeof onClose !== 'undefined') {
                setOnSideBarClosed(() => onClose);
            } else {
                setOnSideBarClosed(() => () => {});
            }
        },
        [closeModal]
    );
    const closeSideBar = useCallback(() => {
        setIsSideBarShown(false);
        onSideBarClosed();

        // return new Promise(res => {
        //     closSideBarTimerRef.current = setTimeout(() => {
        //         setSideBarTitle(null);
        //         setSideBarView(null);
        //         setSideBarSize('normal');
        //         onSideBarClosed();
        //
        //         res(true);
        //     }, 750);
        // });

        return Promise.resolve(true);
    }, [onSideBarClosed]);

    const confirm = useCallback(
        (
            title: string,
            description: string,
            actionLabel: string,
            onConfirm: () => void
        ) => {
            openSideBar(
                title,
                <Confirm
                    title={title}
                    description={description}
                    actionLabel={actionLabel}
                    onConfirm={onConfirm}
                />
            );
        },
        [openSideBar]
    );

    const theme: VechaiTheme = useMemo(
        () => ({
            ...defaultTheme,
            cursor: 'pointer'
        }),
        []
    );

    const value: any = useMemo(
        () => ({
            isModalShown,
            modalTitle,
            modalView,
            modalParams,
            openModal,
            closeModal,

            isSideBarShown,
            sideBarTitle,
            setSideBarTitle,
            sideBarView,
            sideBarSize,
            openSideBar,
            closeSideBar,

            confirm
        }),
        [
            isModalShown,
            modalTitle,
            modalView,
            modalParams,
            openModal,
            closeModal,

            isSideBarShown,
            sideBarTitle,
            setSideBarTitle,
            sideBarView,
            sideBarSize,
            openSideBar,
            closeSideBar,

            confirm
        ]
    );

    return (
        <VechaiProvider theme={theme}>
            <UiContext.Provider value={value}>{children}</UiContext.Provider>
        </VechaiProvider>
    );
});

if (isDev) {
    UiProvider.displayName = 'UiProvider';
}
