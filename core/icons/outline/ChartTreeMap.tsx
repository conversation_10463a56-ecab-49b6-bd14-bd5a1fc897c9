const Icon = ({...props}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
            fill="currentColor"
            {...props}
        >
            <path d="M464 32h-160C277.5 32 256 53.49 256 80v32C256 138.5 277.5 160 304 160h160C490.5 160 512 138.5 512 112v-32C512 53.49 490.5 32 464 32zM480 112C480 120.8 472.8 128 464 128h-160C295.2 128 288 120.8 288 112v-32C288 71.18 295.2 64 304 64h160C472.8 64 480 71.18 480 80V112zM176 272h-128C21.49 272 0 293.5 0 320v112C0 458.5 21.49 480 48 480h128C202.5 480 224 458.5 224 432V320C224 293.5 202.5 272 176 272zM192 432C192 440.8 184.8 448 176 448h-128C39.18 448 32 440.8 32 432V320c0-8.822 7.178-16 16-16h128C184.8 304 192 311.2 192 320V432zM176 32h-128C21.49 32 0 53.49 0 80V192c0 26.51 21.49 48 48 48h128C202.5 240 224 218.5 224 192V80C224 53.49 202.5 32 176 32zM192 192c0 8.822-7.178 16-16 16h-128C39.18 208 32 200.8 32 192V80C32 71.18 39.18 64 48 64h128C184.8 64 192 71.18 192 80V192zM464 352h-160c-26.51 0-48 21.49-48 48v32c0 26.51 21.49 48 48 48h160c26.51 0 48-21.49 48-48v-32C512 373.5 490.5 352 464 352zM480 432c0 8.822-7.178 16-16 16h-160c-8.822 0-16-7.178-16-16v-32c0-8.822 7.178-16 16-16h160c8.822 0 16 7.178 16 16V432zM464 192h-160C277.5 192 256 213.5 256 240v32c0 26.51 21.49 48 48 48h160c26.51 0 48-21.49 48-48v-32C512 213.5 490.5 192 464 192zM480 272C480 280.8 472.8 288 464 288h-160C295.2 288 288 280.8 288 272v-32C288 231.2 295.2 224 304 224h160C472.8 224 480 231.2 480 240V272z" />
        </svg>
    );
};

export default Icon;
