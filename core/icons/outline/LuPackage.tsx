const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="15"
            height="15"
            viewBox="0 0 15 15"
            {...props}
        >
            <g fill="none" fillRule="evenodd">
                <g fill="#363636" fillRule="nonzero">
                    <g>
                        <path
                            d="M7.111 0L.001 3.762v7.4l7.11 3.763 7.11-3.762V3.762L7.11 0zM1.003 10.64V4.286l6.108-3.231 6.107 3.231v6.353L7.11 13.87 1.003 10.64z"
                            transform="translate(-1457 -9) translate(1457 9)"
                        />
                        <path
                            d="M0.249 4.418L7.111 8.048 13.972 4.418 13.467 3.63 7.111 6.994 0.755 3.63z"
                            transform="translate(-1457 -9) translate(1457 9)"
                        />
                        <path
                            d="M10.255 6.065L3.646 2.568 4.151 1.781 10.76 5.278zM6.61 14.397L7.612 14.397 7.612 7.521 6.61 7.521z"
                            transform="translate(-1457 -9) translate(1457 9)"
                        />
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default Icon;
