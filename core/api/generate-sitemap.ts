import {createWriteStream} from 'fs';
import {resolve} from 'path';
import {SitemapAndIndexStream, SitemapStream} from 'sitemap';
import storeConfig from '~/store.config';
import {trim} from '@core/helpers';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const token = process.env.API_SECRET;
    const {token: suppliedToken} = req.body as {
        token: string;
    };

    // Check for secret to confirm this is a valid request
    if (token !== suppliedToken) {
        return res.status(401).json({message: 'Invalid token'});
    }

    try {
        const siteUrl = `${trim(
            process.env.NEXT_PUBLIC_SITE_URL as string,
            '/'
        )}/`;
        const locale = storeConfig.defaultLocale;
        const sitemapDir = resolve(process.cwd(), 'public/sitemap');

        const sms = new SitemapAndIndexStream({
            limit: 5000,
            getSitemapStream: i => {
                const sitemapStream = new SitemapStream({
                    hostname: siteUrl
                });
                const path = `./sitemap-${i}.xml`;

                const ws = createWriteStream(resolve(sitemapDir, path));
                sitemapStream.pipe(ws);

                return [
                    new URL(path, `${trim(siteUrl, '/')}/sitemap/`).toString(),
                    sitemapStream,
                    ws
                ];
            }
        });

        sms.pipe(createWriteStream(resolve(sitemapDir, './sitemap-index.xml')));

        let skip = 0;
        while (true) {
            const records = await erpClient.post('common/sitemap-records', {
                locale,
                skip,
                limit: 1000
            });

            if (!records || !Array.isArray(records) || records.length < 1) {
                break;
            }

            records.forEach(item => sms.write(item));

            skip += 1000;
        }

        sms.end();

        return res.json({generated: true});
    } catch (err) {
        return res.status(500).send('Error while generating sitemap.');
    }
});
