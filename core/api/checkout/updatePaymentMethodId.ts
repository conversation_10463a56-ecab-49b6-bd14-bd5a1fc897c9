import Cookies from 'cookies';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);
    const cartId = cookies.get('cart-id');
    const {paymentMethodId} = req.body;

    return res.json(
        await erpClient.post('checkout/update-payment-method-id', {
            cartId,
            paymentMethodId
        })
    );
});
