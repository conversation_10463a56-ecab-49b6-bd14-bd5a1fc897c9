import {endpoint, erpClient} from '@core/helpers/server';
import {Installment} from '@core/types';
import {randomId} from '@core/helpers';

export default endpoint.post(async (ctx, req, res) => {
    const {amount} = req.body;

    if (typeof amount !== 'number') throw new Error('Amount must be a number');

    const result = await erpClient.post('checkout/installments', {amount});

    const installmentTerms = Array.from({length: 12}, (_, i) => i + 1);

    for (const item of result) {
        item.installments = installmentTerms.map(count => {
            const installment = item.installments.find(
                (installment: Installment['installments'][0]) =>
                    installment.installmentCount === count
            );
            return installment
                ? {id: randomId(), ...installment}
                : {
                      id: randomId(),
                      installmentAmount: 0,
                      installmentCount: count,
                      plusInstallmentCount: 0,
                      installmentRate: 0,
                      total: 0
                  };
        });
    }

    return res.json(result);
});
