import Cookies from 'cookies';
import storeConfig from '~/store.config';
import {template} from '@core/helpers';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {locale = storeConfig.defaultLocale} = req.body as {locale: string};
    const cookies = new Cookies(req, res);
    const cartId = cookies.get('cart-id');

    // Get sales contact text.
    let salesContractText = (
        await erpClient.post('common/text', {
            type: 'sales-contract-text',
            locale
        })
    ).content;

    // Get preliminary information form.
    let preliminaryInformationForm = (
        await erpClient.post('common/text', {
            type: 'preliminary-information-form',
            locale
        })
    ).content;

    // Get fields for texts.
    const fields = await erpClient.post('checkout/get-fields-for-texts', {
        cartId,
        locale
    });
    const items = fields.items;
    fields.items = '|itemsTable|';

    salesContractText = template(salesContractText ?? '', {
        ...(fields ?? {})
    }).replaceAll('|itemsTable|', items);
    preliminaryInformationForm = template(preliminaryInformationForm ?? '', {
        ...(fields ?? {})
    }).replaceAll('|itemsTable|', items);

    return res.json({
        salesContractText,
        preliminaryInformationForm
    });
});
