import storeConfig from '~/store.config';
import {template} from '@core/helpers';
import {endpoint} from '@core/helpers/server';

export default endpoint.get(async (ctx, req, res) => {
    const {erpClient} = ctx;
    const {code} = req.query;

    // res.removeHeader('X-DNS-Prefetch-Control');
    // res.removeHeader('Strict-Transport-Security');
    // res.removeHeader('X-XSS-Protection');
    // res.removeHeader('X-Frame-Options');
    // res.removeHeader('X-Content-Type-Options');
    // res.removeHeader('Referrer-Policy');

    if (typeof code !== 'string' || code.length < 2) {
        return res.status(404).send('Not found!');
    }

    const paymentIntent = await erpClient.post('checkout/get-payment-intent', {
        code
    });

    let html = '';
    try {
        html = template(
            `
<!DOCTYPE html>
<html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <meta http-equiv='X-UA-Compatible' content='ie=edge'>
        <meta name='robots' content='noindex, nofollow'>
        <title>Payment Form</title>
        <style>
            html, body {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
                margin: 0;
            }

            .loader {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                display: block;
                margin: 15px auto;
                position: relative;
                color: {{color}};
                box-sizing: border-box;
                animation: animloader 1s linear infinite;
            }

            @keyframes animloader {
                0% {
                    box-shadow: 14px 0 0 -2px, 38px 0 0 -2px, -14px 0 0 -2px, -38px 0 0 -2px;
                }
                25% {
                    box-shadow: 14px 0 0 -2px, 38px 0 0 -2px, -14px 0 0 -2px, -38px 0 0 2px;
                }
                50% {
                    box-shadow: 14px 0 0 -2px, 38px 0 0 -2px, -14px 0 0 2px, -38px 0 0 -2px;
                }
                75% {
                    box-shadow: 14px 0 0 2px, 38px 0 0 -2px, -14px 0 0 -2px, -38px 0 0 -2px;
                }
                100% {
                    box-shadow: 14px 0 0 -2px, 38px 0 0 2px, -14px 0 0 -2px, -38px 0 0 -2px;
                }
            }
        </style>
    </head>
    <body>
        <span class='loader'></span>

        <form id='paymentForm' action='https://www.paytr.com/odeme' method='post'>
            <input type='hidden' name='merchant_id' value='{{ payload.merchant_id }}'>
            <input type='hidden' name='user_ip' value='{{ payload.user_ip }}'>
            <input type='hidden' name='merchant_oid' value='{{ payload.merchant_oid }}'>
            <input type='hidden' name='email' value='{{ payload.email }}'>
            <input type='hidden' name='payment_type' value='{{ payload.payment_type }}'>
            <input type='hidden' name='payment_amount' value='{{ payload.payment_amount }}'>
            <input type='hidden' name='currency' value='{{ payload.currency }}'>
            <input type='hidden' name='test_mode' value='{{ payload.test_mode }}'>
            <input type='hidden' name='non_3d' value='{{ payload.non_3d }}'>
            <input type='hidden' name='merchant_ok_url' value='{{ payload.merchant_ok_url }}'>
            <input type='hidden' name='merchant_fail_url' value='{{ payload.merchant_fail_url }}'>
            <input type='hidden' name='user_name' value='{{ payload.user_name }}'>
            <input type='hidden' name='user_address' value='{{ payload.user_address }}'>
            <input type='hidden' name='user_phone' value='{{ payload.user_phone }}'>
            <input type='hidden' name='user_basket' value='{{ payload.user_basket }}'>
            <input type='hidden' name='debug_on' value='{{ payload.debug_on }}'>
            <input type='hidden' name='client_lang' value='{{ payload.client_lang }}'>
            <input type='hidden' name='paytr_token' value='{{ payload.paytr_token }}'>
            <input type='hidden' name='non3d_test_failed' value='{{ payload.non3d_test_failed }}'>
            <input type='hidden' name='installment_count' value='{{ payload.installment_count }}'>
            <input type='hidden' name='card_type' value='{{ payload.card_type }}'>
            <input type='hidden' name='cc_owner' value='{{ payload.cc_owner }}'>
            <input type='hidden' name='card_number' value='{{ payload.card_number }}'>
            <input type='hidden' name='expiry_month' value='{{ payload.expiry_month }}'>
            <input type='hidden' name='expiry_year' value='{{ payload.expiry_year }}'>
            <input type='hidden' name='cvv' value='{{ payload.cvv }}'>
        </form>

        <script>
            window.onload = () => {
                const paymentForm = document.getElementById('paymentForm');
                paymentForm.submit();
            };
        </script>
    </body>
</html>
            `.trim(),
            {
                payload: paymentIntent.integrationPayload,
                color: storeConfig.theme.colors.primary['600']
            }
        );
    } catch (error: any) {
        return res.status(400).send(error.message);
    }

    return res.send(html);
});
