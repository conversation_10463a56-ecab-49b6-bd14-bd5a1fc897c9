import Cookies from 'cookies';
import {endpoint, erpClient, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);
    const cartId = cookies.get('cart-id');
    const {payload} = req.body;

    await validator
        .object()
        .shape({
            firstName: validator.string().min(2).required(),
            lastName: validator.string().min(2).required(),
            email: validator.string().email().min(4).required(),
            phoneCountryCode: validator.string().notRequired(),
            phoneCode: validator.string().notRequired(),
            phoneNumber: validator.string().notRequired(),
            isSubscribedToNewsletter: validator.boolean().required(),
            useDeliveryAddressAsBillingAddress: validator.boolean().required(),
            invoiceType: validator.string().oneOf(['individual', 'corporate']),
            companyName:
                payload.invoiceType === 'corporate'
                    ? validator.string().min(2).required()
                    : validator.string().notRequired(),
            taxIdentificationNumber:
                payload.invoiceType === 'corporate'
                    ? validator.string().min(2).required()
                    : validator.string().notRequired(),
            taxOffice:
                payload.invoiceType === 'corporate'
                    ? validator.string().min(2).required()
                    : validator.string().notRequired(),
            deliveryAddress: validator
                .object()
                .shape({
                    street: validator.string().min(2).required(),
                    street2: validator.string().notRequired(),
                    city: validator.string().min(2).required(),
                    district: validator.string().min(2).required(),
                    subDistrict: validator.string().min(2).required(),
                    state: validator.string().min(2).notRequired(),
                    postalCode: validator.string().min(2).required(),
                    countryId: validator.string().min(2).required(),
                    countryName: validator.string().min(2).required()
                })
                .required(),
            billingAddress: validator
                .object()
                .shape({
                    street: validator.string().min(2).required(),
                    street2: validator.string().notRequired(),
                    city: validator.string().min(2).required(),
                    district: validator.string().min(2).required(),
                    subDistrict: validator.string().min(2).required(),
                    state: validator.string().min(2).notRequired(),
                    postalCode: validator.string().min(2).required(),
                    countryId: validator.string().min(2).required(),
                    countryName: validator.string().min(2).required()
                })
                .required()
        })
        .isValid(payload);

    payload.step = 'delivery';

    return res.json(
        await erpClient.post('checkout/save-information', {
            cartId,
            payload
        })
    );
});
