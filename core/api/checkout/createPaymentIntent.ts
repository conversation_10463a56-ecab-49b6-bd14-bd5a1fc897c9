import Cookies from 'cookies';
import requestIp from 'request-ip';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);
    const cartId = cookies.get('cart-id');
    const payload = req.body;

    const result = await erpClient.post('checkout/create-payment-intent', {
        cartId,
        payload: {
            ...payload,
            ip: requestIp.getClientIp(req)
        }
    });

    return res.json(result);
});
