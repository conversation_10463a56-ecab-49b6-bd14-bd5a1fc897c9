import Cookies from 'cookies';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);
    const cartId = cookies.get('cart-id');
    const {payload} = req.body;

    const result = await erpClient.post('checkout/approve-payment', {
        cartId,
        payload
    });
    const date = new Date();

    cookies.set('cart-id', null, {
        httpOnly: false,
        expires: new Date(date.setMonth(date.getMonth() + 1))
    });

    return res.json(result);
});
