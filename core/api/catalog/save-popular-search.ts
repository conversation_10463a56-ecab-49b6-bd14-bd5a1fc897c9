import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;
    const {searchQuery} = req.body as {
        searchQuery: string;
    };

    if (!searchQuery || searchQuery.trim().length < 1) {
        return res.json({status: 'ok'});
    }

    const result = await erpClient.post('catalog/save-popular-search', {
        searchQuery
    });

    return res.json(result);
});
