import {endpoint} from '@core/helpers/server';

type Payload = {
    categoryPaths: string[];
    groupIds: string[];
    tagIds: string[];
    brandIds: string[];
    set?: string;
    advancedFilters?: Record<string, any>;
    extraQuery: Record<string, any>;
};

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;
    const {
        categoryPaths,
        groupIds,
        brandIds,
        tagIds,
        advancedFilters,
        set,
        extraQuery
    }: Payload = req.body;

    const result = await erpClient.post('catalog/filters', {
        categoryPaths,
        groupIds,
        tagIds,
        brandIds,
        set,
        advancedFilters,
        extraQuery
    });

    return res.json(result);
});
