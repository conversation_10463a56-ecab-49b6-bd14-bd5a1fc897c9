import storeConfig from '~/store.config';
import {endpoint} from '@core/helpers/server';

type Sort = {
    field: string;
    direction: 'asc' | 'desc';
};

type Payload = {
    productId: string;
    extraQuery: Record<string, any>;
    skip?: number;
    limit?: number;
    sort?: Sort | Sort[];
    paginated?: boolean;
};

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {
        productId,
        extraQuery = {},
        skip = 0,
        limit,
        sort = {field: 'createdAt', direction: 'desc'},
        paginated = false
    }: Payload = req.body;

    // Prepare sort.
    let sortQuery: Record<string, number> = {};
    if (Array.isArray(sort)) {
        for (const s of sort) {
            sortQuery[s.field] = s.direction === 'asc' ? 1 : -1;
        }
    } else if (typeof sort === 'object' && sort !== null) {
        sortQuery[sort.field] = sort.direction === 'asc' ? 1 : -1;
    } else {
        sortQuery = {createdAt: -1};
    }

    const result = await erpClient.post('catalog/products', {
        set: 'related-products',
        productId,
        extraQuery,
        fields: storeConfig.catalog.productListItemFields,
        skip: skip,
        limit:
            typeof limit === 'number'
                ? limit
                : storeConfig.catalog.productsPerPage || 48,
        sort: sortQuery,
        ...(typeof customer !== 'undefined' ? {customerId: customer.id} : {}),
        paginated
    });

    if (paginated) {
        return res.send({
            products: result.products,
            hasNextPage: result.hasNextPage,
            totalProductCountText: result.totalProductCountText
        });
    }

    return res.json(result);
});
