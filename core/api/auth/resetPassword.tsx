import {hash} from 'bcryptjs';
import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;
    const {password, token, locale} = req.body;

    await validator
        .object()
        .shape({
            token: validator.string().required(),
            password: validator.string().required()
        })
        .isValid({
            token,
            password
        });

    let result = null;
    try {
        result = await erpClient.post('customers/reset-password', {
            token,
            password: await hash(password, 12),
            locale
        });
    } catch (error: any) {
        return res.status(422).json({
            code: error.code,
            message: error.message,
            field: error.field
        });
    }

    return res.status(200).json(result);
});
