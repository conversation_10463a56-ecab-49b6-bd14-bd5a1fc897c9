import {hash} from 'bcryptjs';
import Cookies from 'cookies';
import {trim} from '@core/helpers';
import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;
    const {
        firstName,
        lastName,
        email,
        password,
        isSubscribedToNewsletter,
        acceptedClarificationText
    } = req.body;

    if (!acceptedClarificationText) {
        return res.status(422).json({
            code: 'clarification_text_not_accepted',
            message:
                'The clarification text for the processing of personal data must be accepted.',
            field: 'acceptedClarificationText'
        });
    }

    await validator
        .object()
        .shape({
            firstName: validator.string().required(),
            lastName: validator.string().required(),
            email: validator.string().email().required(),
            password: validator.string().min(3).required()
        })
        .isValid({
            firstName,
            lastName,
            email,
            password
        });

    let customer = null;
    try {
        customer = await erpClient.post('customers/create-customer', {
            firstName,
            lastName,
            email: email.toLowerCase(),
            password: await hash(password, 12),
            isSubscribedToNewsletter: !!isSubscribedToNewsletter
        });

        // Set existing cart customer.
        const cookies = new Cookies(req, res);
        let cartId: string | undefined = trim(cookies.get('cart-id') || '');
        if (cartId === '') {
            cartId = undefined;
        }
        if (!!cartId) {
            try {
                await erpClient.post('cart/set-customer', {
                    cartId,
                    customerId: customer.id
                });
            } catch (error: any) {}
        }
    } catch (error: any) {
        return res.status(422).json({
            code: error.code,
            message: error.message,
            field: error.field
        });
    }

    return res.status(201).json(customer);
});
