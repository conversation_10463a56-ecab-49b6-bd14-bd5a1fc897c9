import {compare} from 'bcryptjs';
import Cookies from 'cookies';
import NextAuth, {DefaultSession, NextAuthOptions} from 'next-auth';
import credentialsProvider from 'next-auth/providers/credentials';
import {Customer} from '@core/types';
import {trim} from '@core/helpers';
import {erpClient, redis} from '@core/helpers/server';

declare module 'next-auth' {
    interface Session extends DefaultSession {
        user?: Customer;
    }
}

export const authOptions: NextAuthOptions = {
    secret: 'oRfzaDHsy8lCN0OW2iqzWrMYgVeP0JUT',
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        jwt({token, user}) {
            if (typeof user === 'object') {
                token.id = user.id;
                token.email = user.email;
            }

            return token;
        },
        async session({session, token}): Promise<any> {
            if (typeof token === 'object' && token !== null) {
                const customerKey = `customer-${token.id}`;
                const cachedCustomer = await redis.get(customerKey);
                let customer: Record<string, any> = {};

                if (
                    cachedCustomer !== null &&
                    typeof cachedCustomer !== 'undefined'
                ) {
                    customer = JSON.parse(cachedCustomer);
                } else {
                    customer = await erpClient.post('customers/customer', {
                        email: token.email
                    });

                    await redis.set(
                        customerKey,
                        JSON.stringify(customer),
                        'EX',
                        60 * 60 * 24
                    );
                }

                session.user = {
                    id: customer.id,
                    name: customer.name,
                    firstName: customer.firstName,
                    lastName: customer.lastName,
                    email: customer.email,
                    phoneNumber: customer.phoneNumber,
                    phoneCode: customer.phoneCode,
                    phoneCountryCode: customer.phoneCountryCode
                };
            }

            return session;
        }
    },
    events: {
        signOut: async ({token}) => {
            const customerKey = `customer-${token.id}`;

            try {
                await redis.del(customerKey);
            } catch (error) {}
        }
    },
    providers: [
        credentialsProvider({
            credentials: {
                email: {label: 'Email address', type: 'text', placeholder: ''},
                password: {label: 'Password', type: 'password'}
            },
            async authorize(credentials, req) {
                let result = null;
                try {
                    result = await erpClient.post('customers/customer', {
                        email: credentials?.email.toLowerCase()
                    });

                    const customerKey = `customer-${result.id}`;
                    await redis.set(
                        customerKey,
                        JSON.stringify(result),
                        'EX',
                        60 * 60 * 24
                    );

                    // Set existing cart customer.
                    const cookies = new Cookies(req as any, {} as any);
                    let cartId: string | undefined = trim(
                        cookies.get('cart-id') || ''
                    );
                    if (cartId === '') {
                        cartId = undefined;
                    }
                    if (!!cartId) {
                        try {
                            await erpClient.post('cart/set-customer', {
                                cartId,
                                customerId: result.id
                            });
                        } catch (error: any) {}
                    }
                } catch (error: any) {
                    if (error.code === 'email_is_invalid') {
                        throw new Error('Email is invalid');
                    }

                    throw new Error(
                        'Sign in information is incorrect. Please check your e-mail address and password and try again.'
                    );
                }

                if (
                    !(await compare(
                        credentials?.password as string,
                        result.password ?? ''
                    ))
                ) {
                    throw new Error(
                        'Sign in information is incorrect. Please check your e-mail address and password and try again.'
                    );
                }

                return {
                    id: result.id,
                    email: result.email
                };
            }
        })
    ]
};

export default NextAuth(authOptions);
