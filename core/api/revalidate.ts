import {trim} from '@core/helpers';
import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const token = process.env.API_SECRET;
    const {token: suppliedToken, paths} = req.body as {
        token: string;
        paths: string[];
    };

    // Check for secret to confirm this is a valid request
    if (token !== suppliedToken) {
        return res.status(401).json({message: 'Invalid token'});
    }

    try {
        for (const path of paths) {
            await res.revalidate(`/${trim(trim(path), '/')}`);
        }

        return res.json({revalidated: true});
    } catch (err) {
        return res.status(500).send('Error revalidating');
    }
});
