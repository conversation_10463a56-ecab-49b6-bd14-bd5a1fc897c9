import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient} = ctx;
    const {countryId, city, district} = req.body as {
        countryId: string;
        city: string;
        district: string;
    };

    const result = await erpClient.post('common/sub-districts', {
        countryId,
        city,
        district
    });

    return res.json(result);
});
