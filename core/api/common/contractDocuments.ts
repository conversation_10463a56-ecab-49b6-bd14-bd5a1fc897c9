import storeConfig from '~/store.config';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {locale = storeConfig.defaultLocale} = req.body as {locale: string};

    // Get terms of membership text.
    const termsOfMembershipText = (
        await erpClient.post('common/text', {
            type: 'terms-of-membership-text',
            locale
        })
    ).content;

    // Get clarification text.
    const clarificationText = (
        await erpClient.post('common/text', {
            type: 'clarification-text',
            locale
        })
    ).content;

    // Get clarification text.
    const commentPostingCriteriaText = (
        await erpClient.post('common/text', {
            type: 'comment-posting-criteria-text',
            locale
        })
    ).content;

    // Get sales contact text.
    const salesContractText = (
        await erpClient.post('common/text', {
            type: 'sales-contract-text',
            locale
        })
    ).content;

    // Get preliminary information form.
    const preliminaryInformationForm = (
        await erpClient.post('common/text', {
            type: 'preliminary-information-form',
            locale
        })
    ).content;

    return res.json({
        termsOfMembershipText,
        clarificationText,
        commentPostingCriteriaText,
        salesContractText,
        preliminaryInformationForm
    });
});
