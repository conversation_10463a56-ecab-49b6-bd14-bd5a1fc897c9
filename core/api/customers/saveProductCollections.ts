import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {collectionId, productId, collectionIds} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    return res.json(
        await erpClient.post('customers/save-product-collections', {
            customerId,
            collectionId,
            productId,
            collectionIds: Array.isArray(collectionIds) ? collectionIds : []
        })
    );
}, true);
