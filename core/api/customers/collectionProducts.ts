import {endpoint} from '@core/helpers/server';

type Payload = {
    collectionId: string;
    skip?: number;
    limit?: number;
    sort?: string;
};

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {collectionId, skip, limit, sort}: Payload = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    return res.json(
        await erpClient.post('customers/collection-products', {
            customerId: customer.id,
            collectionId,
            skip,
            limit,
            sort
        })
    );
}, true);
