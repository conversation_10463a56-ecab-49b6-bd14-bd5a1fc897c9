import {endpoint} from '@core/helpers/server';

type Payload = {
    skip?: number;
    limit?: number;
    sort?: string;
};

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {skip, limit, sort}: Payload = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    return res.json(
        await erpClient.post('customers/favorite-products', {
            customerId: customer.id,
            skip,
            limit,
            sort
        })
    );
}, true);
