import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {
        locale,
        shipmentTackingCode,
        returnItems,
        reasonId,
        description,
        orderId,
        isIntegrationOrder
    } = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    await validator
        .object()
        .shape({
            locale: validator.string().required(),
            shipmentTackingCode: validator.string().required(),
            returnItems: validator
                .array()
                .of(
                    validator.object({
                        productId: validator.string().required(),
                        quantity: validator.number().required()
                    })
                )
                .required(),
            reasonId: validator.string().required(),
            description: validator.string().notRequired().max(1000),
            orderId: validator.string().required(),
            isIntegrationOrder: validator.boolean().notRequired()
        })
        .isValid({
            locale,
            shipmentTackingCode,
            returnItems,
            reasonId,
            description,
            orderId,
            isIntegrationOrder
        });

    return res.json(
        await erpClient.post('customers/create-return-order', {
            locale,
            shipmentTackingCode,
            returnItems,
            reasonId,
            description,
            orderId,
            isIntegrationOrder
        })
    );
}, true);
