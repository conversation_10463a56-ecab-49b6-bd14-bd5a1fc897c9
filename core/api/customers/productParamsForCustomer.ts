import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {productId} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    return res.json(
        await erpClient.post('customers/product-params-for-customer', {
            customerId,
            productId
        })
    );
}, true);
