import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {locale, orderId, productIds} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    await validator
        .object()
        .shape({
            locale: validator.string().required(),
            orderId: validator.string().required(),
            productIds: validator.array().required()
        })
        .isValid({
            locale,
            orderId,
            productIds
        });

    return res.json(
        await erpClient.post('customers/cancel-return-order', {
            locale,
            orderId,
            productIds
        })
    );
}, true);
