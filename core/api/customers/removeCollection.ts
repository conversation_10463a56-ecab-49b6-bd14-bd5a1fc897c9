import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {collectionId} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    await validator
        .object()
        .shape({
            customerId: validator.string().required(),
            collectionId: validator.string().required()
        })
        .isValid({
            customerId,
            collectionId
        });

    return res.json(
        await erpClient.post('customers/remove-collection', {
            customerId,
            collectionId
        })
    );
}, true);
