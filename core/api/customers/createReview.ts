import {cleanHtml, trim} from '@core/helpers';
import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {
        locale,
        productId,
        rating,
        content,
        isAuthorNameShown,
        acceptedMembershipAgreement
    } = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    if (!acceptedMembershipAgreement) {
        return res.status(422).json({
            code: 'membership_agreement_not_accepted',
            message:
                'The membership agreement for the creating comment must be accepted.',
            field: 'acceptedMembershipAgreement'
        });
    }

    await validator
        .object()
        .shape({
            locale: validator.string().required(),
            rating: validator.number().required().min(1).max(5),
            content: validator.string().notRequired().max(2000),
            isAuthorNameShown: validator.boolean().required()
        })
        .isValid({
            locale,
            rating,
            content,
            isAuthorNameShown
        });

    const normalizedContent = trim(cleanHtml(content ?? ''))
        .split('\n')
        .map(s => `<p>${trim(s)}</p>`)
        .join('\n');

    return res.json(
        await erpClient.post('customers/create-review', {
            locale,
            customerId,
            productId,
            rating,
            content: normalizedContent,
            isAuthorNameShown
        })
    );
}, true);
