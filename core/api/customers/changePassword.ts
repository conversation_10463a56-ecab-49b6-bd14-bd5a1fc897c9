import {hash} from 'bcryptjs';
import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {password} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    await validator
        .object()
        .shape({
            password: validator.string().required()
        })
        .isValid({
            password
        });

    let result = null;
    try {
        result = await erpClient.post('customers/change-password', {
            customerId: customer.id,
            password: await hash(password, 12)
        });
    } catch (error: any) {
        return res.status(422).json({
            code: error.code,
            message: error.message,
            field: error.field
        });
    }

    return res.status(200).json(result);
}, true);
