import {endpoint, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {cartId} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    await validator
        .object()
        .shape({
            customerId: validator.string().required(),
            cartId: validator.string().required()
        })
        .isValid({
            customerId,
            cartId
        });

    return res.json(
        await erpClient.post('customers/cancel-order-cancellation-request', {
            customerId,
            cartId
        })
    );
}, true);
