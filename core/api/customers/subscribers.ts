import {endpoint} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {email, source, name} = req.body;

    const customerId = customer?.id;

    return res.json(
        await erpClient.post('customers/subscribers', {
            customerId,
            email,
            source,
            name
        })
    );
});
