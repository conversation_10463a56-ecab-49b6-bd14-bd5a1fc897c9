import {endpoint, redis, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const {erpClient, customer} = ctx;
    const {payload} = req.body;

    if (!customer) {
        throw new Error('Unauthorized');
    }

    const customerId = customer.id;

    await validator
        .object()
        .shape({
            customerId: validator.string().required(),
            payload: validator
                .object()
                .shape({
                    firstName: validator.string().required(),
                    lastName: validator.string().required(),
                    email: validator.string().required(),
                    phoneNumber: validator.string().notRequired(),
                    phoneCode: validator.string().notRequired(),
                    phoneCountryCode: validator.string().notRequired()
                })
                .required()
        })
        .isValid({
            customerId,
            payload
        });

    await erpClient.post('customers/update-customer', {
        customerId,
        payload
    });
    const result = await erpClient.post('customers/customer', {
        email: payload.email
    });
    await redis.set(
        `customer-${customerId}`,
        JSON.stringify(result),
        'EX',
        60 * 60 * 24
    );

    return res.json(result);
}, true);
