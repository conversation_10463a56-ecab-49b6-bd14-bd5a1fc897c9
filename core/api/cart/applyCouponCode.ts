import Cookies from 'cookies';
import {trim} from '@core/helpers';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);

    let cartId: string | undefined = trim(cookies.get('cart-id') || '');
    if (cartId === '') {
        cartId = undefined;
    }

    const {couponCode} = req.body;
    if (typeof couponCode !== 'string' || couponCode.length < 3) {
        throw new Error('Please enter a valid coupon code!');
    }

    const cart = await erpClient.post('cart/apply-coupon-code', {
        cartId,
        customerId: ctx.customer?.id,
        couponCode
    });

    res.json(cart);
});
