import Cookies from 'cookies';
import {trim} from '@core/helpers';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);

    let cartId: string | undefined = trim(cookies.get('cart-id') || '');
    if (cartId === '') {
        cartId = undefined;
    }

    const campaignProducts = await erpClient.post('cart/campaign-products', {
        cartId,
        customerId: ctx.customer?.id
    });

    res.json(campaignProducts);
});
