import Cookies from 'cookies';
import {trim} from '@core/helpers';
import {endpoint, erpClient} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);
    const cartId = cookies.get('cart-id');

    if (typeof cartId === 'string' && trim(cartId) !== '') {
        let cart = null;

        try {
            cart = await erpClient.post('cart', {cartId});
        } catch (error: any) {}

        if (!!cart) {
            return res.json(cart);
        }
    }

    if (typeof ctx.customer === 'object' && ctx.customer !== null) {
        let cart = null;

        try {
            cart = await erpClient.post('cart', {
                customerId: ctx.customer.id
            });

            const date = new Date();

            cookies.set('cart-id', cart.id, {
                httpOnly: false,
                expires: new Date(date.setMonth(date.getMonth() + 1))
            });
        } catch (error) {}

        if (!!cart) {
            return res.json(cart);
        }
    }

    res.json({
        status: 'draft',
        step: 'information',
        subTotal: 0,
        discountTotal: 0,
        taxTotal: 0,
        deliveryTotal: 0,
        cashOnDeliveryServiceFee: 0,
        grandTotal: 0,
        itemCount: 0,
        productCount: 0,
        discounts: [],
        items: [],
        deliveryType: 'standard'
    });
});
