import Cookies from 'cookies';
import {trim} from '@core/helpers';
import {endpoint, erpClient, validator} from '@core/helpers/server';

export default endpoint.post(async (ctx, req, res) => {
    const cookies = new Cookies(req, res);

    let cartId: string | undefined = trim(cookies.get('cart-id') || '');
    if (cartId === '') {
        cartId = undefined;
    }

    const {item} = req.body;

    await validator
        .object()
        .shape({
            productId: validator.string().required(),
            productSlug: validator.string().required(),
            productImage: validator.string().required(),
            productName: validator.string().required(),
            productCategory: validator.string().notRequired(),
            brandName: validator.string().notRequired(),
            productAttributes: validator
                .array()
                .of(
                    validator.object().shape({
                        code: validator.string().required(),
                        label: validator.string().required(),
                        value: validator.string().required(),
                        color: validator.string().notRequired()
                    })
                )
                .notRequired(),
            productStockQuantity: validator.number().required(),
            productRating: validator.number().required(),
            productReviewCount: validator.number().required(),
            productLink: validator.string().required(),
            price: validator.number().required(),
            unitId: validator.string().required(),
            unitName: validator.string().required(),
            quantity: validator.number().required(),
            weight: validator.number().required(),
            width: validator.number().required(),
            height: validator.number().required(),
            depth: validator.number().required(),
            volumetricWeight: validator.number().required(),
            deliveryOptionIds: validator
                .array()
                .of(validator.string().required()),
            deliveryOptionId: validator.string().notRequired(),
            deliveryPrice: validator.number().required(),
            estimatedDeliveryDuration: validator.number().notRequired(),
            selected: validator.boolean().required(),
            removed: validator.boolean().required()
        })
        .isValid(item);

    const cart = await erpClient.post('cart/add-item', {
        cartId,
        customerId: ctx.customer?.id,
        item
    });

    const date = new Date();

    cookies.set('cart-id', cart.id, {
        httpOnly: false,
        expires: new Date(date.setMonth(date.getMonth() + 1))
    });

    return res.json(cart);
});
