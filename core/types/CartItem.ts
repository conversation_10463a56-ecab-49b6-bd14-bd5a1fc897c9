type ShipmentStatusType =
    | 'order-created'
    | 'shipment-prepared'
    | 'in-transfer-phase'
    | 'arrived-at-the-delivery-unit'
    | 'forwarded-to-recipient-address'
    | 'delivered';

type ShipmentStatusTypeDescription =
    | 'Shipment is being prepared'
    | 'Shipment prepared'
    | 'In transfer phase'
    | 'Arrived at the delivery unit'
    | 'Forwarded to recipient address'
    | 'Delivered';

type ShipmentStatus = {
    value: ShipmentStatusType;
    label: ShipmentStatusTypeDescription;
    isActive: boolean;
};

type ShipmentRecord = {
    date: Date;
    status: string;
    description: string;
    location?: string;
    locationPhone?: string;
    locationAddress?: string;
};

type CartItem = {
    productId: string;
    productSlug: string;
    productImage: string;
    productName: string;

    hasFreeShipping?: boolean;
    brandName?: string;
    productCategory?: string;
    productAttributes?: {
        code: string;
        label: string;
        value: string;
        color?: string;
    }[];
    productStockQuantity: number;
    productRating: number;
    productReviewCount: number;
    productLink: string;
    price: number;
    discountedPrice?: number;
    unitId: string;
    unitName: string;
    quantity: number;
    weight: number;
    width: number;
    height: number;
    depth: number;
    volumetricWeight: number;
    deliveryOptionIds: string[];
    deliveryOptionId?: string;
    deliveryType: 'standard' | 'special' | 'store-delivery';
    deliveryPrice: number;
    storagePrice?: number;
    estimatedDeliveryDuration?: number;
    deliveryAtSpecifiedDate?: boolean;
    deliveryAtSpecifiedTime?: boolean;
    deliveryDate?: Date;
    deliveryTime?: Date;
    selected: boolean;
    removed: boolean;
    productCode?: string;
    salesPrice?: number;
    discount?: number;
    hasDiscount?: boolean;
    total?: number;
    grossTotal?: number;

    status?: string;
    isCanceled?: boolean;
    canBeReturned?: boolean;
    carrierName?: string;
    carrierLogo?: string;
    shipmentStatus?: ShipmentStatusType;
    shippingStatusDescription?: ShipmentStatusTypeDescription;
    shipmentStatuses?: ShipmentStatus[];
    shipmentTackingCode?: string;
    shipmentRecords?: ShipmentRecord[];
    shipmentDeliveryDate?: Date;
    storeDeliveryStatuses?: {
        value: string;
        label: string;
        isActive: boolean;
    }[];

    isPCMProduct?: boolean;
    pcmPayload?: Record<string, any>;

    isKitProduct?: boolean;
    subItems?: {
        productId: string;
        productCode: string;
        productDefinition: string;
        quantity: number;
        unitId: string;
    }[];

    returnShipmentTackingCode?: string;
    isReturnRequested?: boolean;
    shipmentTackingUrl?: string;
    unDiscountedSalesPrice?: number;
    campaigns?: {
        title: string;
        description: string;
        endDate: string;
        id: string;
        startDate: string;
        type: string;
        rewardValue: number;
        rewardValueType: string;
        campaignType: string;
    }[];
};

export default CartItem;
