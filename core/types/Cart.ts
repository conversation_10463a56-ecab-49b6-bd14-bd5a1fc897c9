import type Address from './Address';
import type Campaign from './Campaign';
import type CartDiscount from './CartDiscount';
import type CartItem from './CartItem';

type Cart = {
    id?: string;
    status?: 'draft' | 'completed';
    step: string;
    customerId?: string;

    subTotal: number;
    discountTotal: number;
    discountTotalIncludingProductDiscounts?: number;
    taxTotal: number;
    deliveryTotal: number;
    cashOnDeliveryServiceFee: number;
    grandTotal: number;
    itemCount: number;
    productCount: number;
    discounts: CartDiscount[];
    items: CartItem[];
    campaigns?: Campaign[];

    firstName?: string;
    lastName?: string;
    email?: string;
    phoneCountryCode?: string;
    phoneCode?: string;
    phoneNumber?: string;
    isSubscribedToNewsletter?: string;
    useDeliveryAddressAsBillingAddress?: string;
    invoiceType?: 'individual' | 'corporate';
    companyName?: string;
    taxIdentificationNumber?: string;
    taxOffice?: string;
    deliveryAddressId?: string;
    deliveryAddress?: Address;
    billingAddressId?: string;
    billingAddress?: Address;

    deliveryType: 'standard' | 'special' | 'store-delivery';
    deliveryOptionId: string;
    storeDeliveryWarehouseId?: string;

    paymentMethodId?: string;
    subPaymentMethodId?: string;
    cardBrand: string;
    installmentCount: number;
    installmentOptions?: {
        installmentCount: number;
        installmentAmount: number;
        amount: number;
    }[];
    dueDifference: number;
    couponCode?: string;
    minimumPurchaseAmountForFreeShipping: number;
    showCampaignProducts?: boolean;
    hasAutomaticRewardAddition?: boolean;
};

export default Cart;
