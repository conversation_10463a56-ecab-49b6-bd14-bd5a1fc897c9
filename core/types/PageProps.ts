import Brand from './Brand';
import Breadcrumb from './Breadcrumb';
import ContentPage from './ContentPage';
import {Filter} from './Filter';
import NavigationItem from './NavigationItem';
import Product from './Product';
import ProductListItem from './ProductListItem';
import StoreInfo from './StoreInfo';

type PageProps = {
    storeInfo: StoreInfo;
    navigationItem?: NavigationItem;
    pageType:
        | 'home'
        | 'catalog'
        | 'product'
        | 'page'
        | 'custom'
        | 'blog'
        | 'content';
    slug?: string;
    pageNumber?: number;
    breadcrumbs?: Breadcrumb[];
    page?: ContentPage;
    products?: ProductListItem[];
    hasNextPage?: boolean;
    totalProductCountText?: string;
    filters?: Filter[];
    brand?: Brand;
    product?: Product;
    selectedAttributes?: Record<string, any>;
    isSpecialPage?: boolean;
    [key: string]: any;
};

export default PageProps;
