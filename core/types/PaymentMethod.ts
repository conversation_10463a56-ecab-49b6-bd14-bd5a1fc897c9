type PaymentMethod = {
    id: string;
    type: 'credit-card' | 'money-order' | 'cash-on-delivery';
    name: string;
    description: string;
    integrationType?: 'stripe' | 'pay-tr' | 'virtual-pos';
    integrationParams?: Record<string, any>;
    bankAccounts?: {
        paymentMethodId: string;
        bankName: string;
        bankLogo: string;
        bankBranchName: string;
        accountNumber: string;
        iban: string;
    }[];
    params?: Record<string, any>;
};

export default PaymentMethod;
