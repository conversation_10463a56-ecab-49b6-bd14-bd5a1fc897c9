type Contact = {
    id: string;
    type: 'delivery-address' | 'billing-address';
    name: string;
    relevantContact?: {
        name: string;
        email?: string;
        phoneNumber?: string;
        phoneCode?: string;
        phoneCountryCode?: string;
    };
    invoiceType?: 'individual' | 'corporate';
    companyName?: string;
    taxIdentificationNumber?: string;
    taxOffice?: string;
    identityNumber?: string;
    address: {
        street: string;
        street2?: string;
        state?: string;
        city: string;
        district: string;
        subDistrict: string;
        postalCode: string;
        countryId: string;
        countryCode: string;
        countryName: string;
    };
};

export default Contact;
