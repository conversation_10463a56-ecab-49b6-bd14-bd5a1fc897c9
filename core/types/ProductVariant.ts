type ProductVariant = {
    productId: string;
    code: string;
    name: string;
    definition: string;
    slug: string;
    barcode?: string;
    shortDescription?: string;
    description?: string;
    rating: number;
    reviewCount: number;
    salesCount: number;
    favoritesCount: number;
    images?: string[];
    isSuggestedProduct?: boolean;
    isBestSellingProduct?: boolean;
    isNewProduct?: boolean;
    isDiscountedProduct?: boolean;
    unitId: string;
    unitName: string;
    deliveryOptionIds: string[];
    estimatedDeliveryDuration?: number;
    deliveryAtSpecifiedDate?: boolean;
    deliveryAtSpecifiedTime?: boolean;
    weight: number;
    height: number;
    width: number;
    depth: number;
    salesPrice: number;
    unDiscountedSalesPrice: number;
    discount: number;
    hasDiscount: boolean;
    quantity: number;
    attributes: Record<string, string>;
    colorAttributeCode?: string;
    colorAttributeValue?: string;
    attachments?: {
        attachmentId: string;
        fileName: string;
        previewUrl: string;
        extension: string;
    }[];
    tags?: {
        tagId: string;
        name: string;
        description: string;
        images: string[];
    }[];
    additionalInformations?: {
        code: string;
        label: string;
        value: string;
    }[];
};

export default ProductVariant;
