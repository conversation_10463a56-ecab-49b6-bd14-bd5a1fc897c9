import type Product from './Product';

type SelectedProduct = Omit<Product, 'quantity' | 'variants'> & {
    availableQuantity: number; // Must be checked on each request.
    quantity: number; // Must be 1 by default.
    inStock?: boolean; // Must be checked on each request.
    attributes?: Record<string, string>;
    colorAttributeCode?: string;
    colorAttributeValue?: string;
};

export default SelectedProduct;
