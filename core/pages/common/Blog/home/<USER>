import {FC, memo} from 'react';
import {BlogPost, Category} from '@core/types';
import {isDev} from '@core/helpers';
import BlogPosts from '../common/BlogPosts';
import FeaturedBlogPosts from '../common/FeaturedBlogPosts';
import HomeSeo from './HomeSeo';

type BlogProps = {
    featuredBlogPosts: BlogPost[];
    categories: Category[];
};

const Blog: FC<BlogProps> = memo(({categories, featuredBlogPosts}) => {
    return (
        <>
            <section className="container my-4 grid gap-6 lg:grid-cols-12 xl:my-10">
                <BlogPosts />
                <FeaturedBlogPosts featuredBlogPosts={featuredBlogPosts} />
            </section>

            <HomeSeo categories={categories} />
        </>
    );
});

if (isDev) {
    Blog.displayName = 'Blog';
}

export default Blog;
