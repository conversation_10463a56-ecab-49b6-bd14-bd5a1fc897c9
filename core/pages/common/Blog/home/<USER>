import {FC, useMemo} from 'react';
import {useRouter} from 'next/router';
import {Category} from '@core/types';
import {useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';

type HomeSeoProps = {
    categories: Category[];
};

const HomeSeo: FC<HomeSeoProps> = ({categories}) => {
    const t = useTrans();

    const router = useRouter();

    const activeCategory = useMemo(() => {
        return categories.find(
            category => category.slug === router.asPath.split('/')[3]
        );
    }, [router, categories]);

    return (
        <Seo
            title={activeCategory?.name}
            description={activeCategory?.description}
            additionalMetaTags={[
                {
                    property: 'keywords',
                    content: categories
                        ?.map(category => t(category.name))
                        ?.join(', ')
                }
            ]}
        />
    );
};

export default HomeSeo;
