import {FC} from 'react';
import {ArticleJsonLd} from 'next-seo';
import {BlogPost} from '@core/types';
import {cleanHtml} from '@core/helpers';
import {useTrans} from '@core/hooks';
import Seo from '@components/common/Seo';

type ContentSeoProps = {
    blogPost: BlogPost;
};

const ContentSeo: FC<ContentSeoProps> = ({blogPost}) => {
    const t = useTrans();

    return (
        <>
            <Seo
                title={t(blogPost.title)}
                description={t(cleanHtml(blogPost.shortContent))}
                openGraph={{
                    title: t(blogPost.title),
                    description: t(cleanHtml(blogPost.shortContent)),
                    type: 'article',
                    article: {
                        publishedTime: blogPost.postedOn,
                        modifiedTime: blogPost.updatedAt,
                        authors: [blogPost.author.name],
                        section:
                            Array.isArray(blogPost.subTitles) &&
                            blogPost.subTitles.length > 0
                                ? blogPost.subTitles[0].title
                                : '',
                        tags: blogPost.tags?.map(tag => t(tag.name))
                    },
                    images: [
                        {
                            url:
                                Array.isArray(blogPost.images) &&
                                blogPost.images.length > 0
                                    ? blogPost.images[0]
                                    : '',
                            alt: t(blogPost.title)
                        }
                    ]
                }}
                additionalMetaTags={[
                    {
                        property: 'keywords',
                        content: blogPost.tags
                            ?.map(tag => t(tag.name))
                            ?.join(', ')
                    }
                ]}
            />

            <ArticleJsonLd
                type="BlogPosting"
                url="https://www.baxana.com/blog"
                title={t(blogPost.title)}
                images={blogPost.images}
                datePublished={blogPost.postedOn}
                dateModified={blogPost.updatedAt}
                authorName={blogPost.author.name}
                description={t(cleanHtml(blogPost.shortContent))}
                isAccessibleForFree
            />
        </>
    );
};

export default ContentSeo;
