import {FC, memo} from 'react';
import {BlogPost} from '@core/types';
import {isDev} from '@core/helpers';
import {UiImage} from '@core/components/ui';
import ContentSeo from './ContentSeo';

type BlogContentProps = {
    blogPost: BlogPost;
};

const BlogContent: FC<BlogContentProps> = memo(({blogPost}) => {
    return (
        <>
            <div className="container grid gap-4 py-6 xl:py-10">
                <h1 className="text-4xl font-semibold">{blogPost.title}</h1>
                <UiImage
                    src={
                        Array.isArray(blogPost.images) &&
                        blogPost.images.length > 0
                            ? blogPost.images[0]
                            : '/no-image.png'
                    }
                    alt={blogPost.title}
                    className="rounded-lg"
                    aspectW={2}
                    aspectH={1}
                    priority
                />
                <div
                    className="prose max-w-none"
                    dangerouslySetInnerHTML={{
                        __html: blogPost.content
                    }}
                />
            </div>

            <ContentSeo blogPost={blogPost} />
        </>
    );
});

if (isDev) {
    BlogContent.displayName = 'BlogContent';
}

export default BlogContent;
