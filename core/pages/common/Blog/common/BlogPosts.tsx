import {FC} from 'react';
import {useRouter} from 'next/router';
import {cls} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import useBlog from '@core/layouts/Blog/context';

const MAX_ALLOWED_POST = 5;

const BlogPosts: FC = () => {
    const t = useTrans();

    const {blogPosts} = useBlog();

    const router = useRouter();

    const dateFormatter = (date: string) => {
        return new Date(date).toLocaleDateString(router.locale, {
            dateStyle: 'long'
        });
    };

    return (
        <div className="grid gap-6 md:grid-cols-2 lg:col-span-9 [&>*:nth-child(1)]:first:col-span-full">
            {Array.isArray(blogPosts) && blogPosts.length > 0 ? (
                blogPosts.map((blogPost, index) => {
                    if (index >= MAX_ALLOWED_POST) return;

                    return (
                        <UiLink
                            key={blogPost._id}
                            href={blogPost.slug}
                            className="group flex flex-col gap-2 rounded-lg border py-2"
                        >
                            <p className="px-4 text-2xl font-bold">
                                {blogPost.title}
                            </p>
                            <p className="px-4 text-sm font-medium italic text-gray-400">
                                {dateFormatter(blogPost.postedOn)}
                            </p>
                            <div className="aspect-h-1 aspect-w-2 overflow-hidden">
                                <UiImage
                                    src={
                                        Array.isArray(blogPost.images) &&
                                        blogPost.images.length > 0
                                            ? blogPost.images[0]
                                            : '/no-image.png'
                                    }
                                    alt={blogPost.title}
                                    className="transition group-hover:scale-105"
                                    fit="cover"
                                    position="center"
                                    fill
                                />
                            </div>
                            <div
                                className={cls(
                                    'prose max-w-none max-xl:px-4',
                                    index === 0 ? 'xl:px-16' : 'px-4'
                                )}
                                dangerouslySetInnerHTML={{
                                    __html: blogPost.shortContent
                                }}
                            />
                        </UiLink>
                    );
                })
            ) : (
                <div className="flex flex-col items-center justify-center">
                    <h2 className="text-center text-2xl font-semibold">
                        {t('Aradığınız kriterlere uygun yazı bulunamadı!')}
                    </h2>
                    <p className="mt-2 text-center text-muted">
                        {t(
                            'Lütfen arama kriterlerinizi değiştirin ve tekrar deneyin.'
                        )}
                    </p>
                </div>
            )}
        </div>
    );
};

export default BlogPosts;
