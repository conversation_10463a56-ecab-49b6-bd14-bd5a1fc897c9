import {FC, memo} from 'react';
import {useRouter} from 'next/router';
import {BlogPost} from '@core/types';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';

const MAX_ALLOWED_POST = 3;

type FeaturedBlogPostsProps = {
    featuredBlogPosts: BlogPost[];
};

const FeaturedBlogPosts: FC<FeaturedBlogPostsProps> = memo(
    ({featuredBlogPosts}) => {
        const router = useRouter();

        const t = useTrans();

        const dateFormatter = (date: string) => {
            return new Date(date).toLocaleDateString(router.locale, {
                dateStyle: 'long'
            });
        };

        return Array.isArray(featuredBlogPosts) &&
            featuredBlogPosts.length > 0 ? (
            <div className="flex h-fit flex-col border-l border-l-[#7bc377] lg:col-span-3">
                <div className="flex h-8 items-center justify-center bg-[#7bc377]">
                    <p className="text-xl font-medium text-white">
                        {t('ÇOK OKUNANLAR')}
                    </p>
                </div>
                <div className="grid p-3">
                    {featuredBlogPosts?.map((blogPost, index) => {
                        if (index >= MAX_ALLOWED_POST) return;

                        return (
                            <UiLink
                                key={blogPost._id}
                                href={blogPost.slug}
                                className="group py-2"
                            >
                                <p className="font-semibold">
                                    {blogPost.title}
                                </p>
                                <p className="text-sm font-medium italic text-gray-400">
                                    {dateFormatter(blogPost.postedOn)}
                                </p>
                                <div className="aspect-h-1 aspect-w-2 mt-2 overflow-hidden rounded-lg">
                                    <UiImage
                                        src={
                                            Array.isArray(blogPost.images) &&
                                            blogPost.images.length > 0
                                                ? blogPost.images[0]
                                                : '/no-image.png'
                                        }
                                        alt={blogPost.title}
                                        fill
                                        fit="cover"
                                        position="center"
                                        className="rounded-lg transition group-hover:scale-105"
                                    />
                                </div>
                            </UiLink>
                        );
                    })}
                </div>
            </div>
        ) : null;
    }
);

if (isDev) {
    FeaturedBlogPosts.displayName = 'FeaturedBlogPosts';
}

export default FeaturedBlogPosts;
