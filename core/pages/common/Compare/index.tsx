import {FC, memo, useCallback, useMemo} from 'react';
import {useRouter} from 'next/router';
import {Cookies} from 'react-cookie-consent';
import {cls, isDev, trim} from '@core/helpers';
import {ProductFeature, ProductListItem} from '@core/types';
import {useTrans} from '@core/hooks';
import {ArrowRightArrowLeftIcon, XIcon} from '@core/icons/outline';
import {UiImage, UiLink, UiRating} from '@core/components/ui';
import CoreBreadcrumbs from '@components/common/Breadcrumbs';
import Price from '@components/common/Price';
import Seo from '@components/common/Seo';

interface ExtendedProductListItem extends ProductListItem {
    features?: ProductFeature[];
}

type CompareProps = {
    products: ExtendedProductListItem[];
};

const Compare: FC<CompareProps> = memo(({products}) => {
    const t = useTrans();
    const router = useRouter();

    const {aggregatedFeatures, productFeatureLookup} = useMemo(() => {
        const featureSet: Record<string, ProductFeature> = {};
        const lookup: Record<string, Record<string, string>> = {};

        for (const product of products) {
            lookup[product.productId] = {};

            if (
                Array.isArray(product.features) &&
                product.features.length > 0
            ) {
                for (const feature of product.features) {
                    if (!feature.code) continue;
                    lookup[product.productId][feature.code] = feature.value;
                    if (featureSet[feature.code]) continue;
                    featureSet[feature.code] = feature;
                }
            }
        }

        return {
            aggregatedFeatures: Object.values(featureSet),
            productFeatureLookup: lookup
        };
    }, [products]);

    const removeProductFromComparisonList = useCallback(
        (productId: string) => {
            const productIds = decodeURIComponent(
                trim((router.query.q ?? '').toString())
            ).split(',');

            const filteredProductIds = productIds.filter(
                id => id !== productId
            );

            let comparisonList = localStorage.getItem('comparison-list');
            if (comparisonList) {
                try {
                    comparisonList = JSON.parse(comparisonList);
                } catch (error) {}

                if (
                    Array.isArray(comparisonList) &&
                    comparisonList.length > 0
                ) {
                    const updatedComparisonList = comparisonList.filter(
                        listItem => listItem?.productId !== productId
                    );

                    localStorage.setItem(
                        'comparison-list',
                        JSON.stringify(updatedComparisonList)
                    );
                }
            }

            if (filteredProductIds.length > 0) {
                router.push({
                    pathname: '/compare',
                    query: {
                        q: encodeURIComponent(filteredProductIds.join(','))
                    }
                });
            } else {
                router.push('/compare');
            }
        },
        [router]
    );

    return (
        <section className="container pb-8">
            <Seo title={t('Product Comparison')} />

            {products.length > 0 ? (
                <>
                    <CoreBreadcrumbs
                        breadcrumbs={[
                            {slug: '/', name: 'Home', href: '/'},
                            {
                                slug: '/compare',
                                name: t('Product Comparison')
                            }
                        ]}
                    />

                    <div className="mt-8 overflow-hidden rounded-sm border">
                        <div className="scrollbar-fade h-full max-h-[75vh] w-full overflow-auto">
                            <table className="w-full table-fixed border-separate border-spacing-0 text-sm">
                                <thead className="[&>tr>th:first-child]:border-l-0 max-xl:[&>tr>th:nth-child(2)]:border-l-0 [&>tr>th]:border-l [&>tr>th]:px-4 [&>tr>th]:py-2.5 [&_tr]:border-b">
                                    <tr>
                                        <th className="sticky left-0 top-0 z-[4] w-64 border-b bg-white text-left text-gray-700 max-xl:hidden">
                                            <h1 className="text-lg font-bold">
                                                {t('Product Comparison')}
                                            </h1>
                                            <p className="font-normal text-muted">
                                                {t(
                                                    'There are {item} items on your list',
                                                    {item: products.length}
                                                )}
                                            </p>
                                        </th>
                                        {products.map(product => (
                                            <th
                                                key={product.productId}
                                                className="sticky top-0 z-[2] w-64 border-b bg-white text-left text-sm font-medium text-gray-700"
                                            >
                                                <button
                                                    onClick={() =>
                                                        removeProductFromComparisonList(
                                                            product.productId
                                                        )
                                                    }
                                                    className="absolute right-1.5 top-1.5 flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition hover:bg-gray-200 hover:text-gray-500"
                                                >
                                                    <XIcon
                                                        className="h-4 w-4"
                                                        aria-hidden="true"
                                                    />
                                                </button>

                                                <UiLink
                                                    href={product.slug}
                                                    className="group flex items-center gap-2"
                                                >
                                                    <div className="relative h-20 w-20">
                                                        <UiImage
                                                            src={
                                                                Array.isArray(
                                                                    product.images
                                                                ) &&
                                                                product.images
                                                                    .length > 0
                                                                    ? `${product.images[0]}?w=240&q=75`
                                                                    : '/no-image.png'
                                                            }
                                                            alt={product.name}
                                                            fit="contain"
                                                            fill
                                                            priority
                                                        />
                                                    </div>
                                                    <span className="line-clamp-2 transition group-hover:text-gray-500">
                                                        {product.name}
                                                    </span>
                                                </UiLink>
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className="text-muted [&>tr>td:first-child]:border-l-0 max-xl:[&>tr>td:nth-child(2)]:border-l-0 [&>tr>td]:border-l [&_td]:px-4 [&_td]:py-2.5">
                                    <tr>
                                        <td className="sticky left-0 z-[1] bg-white max-xl:hidden">
                                            {t('Product Rating')}
                                        </td>
                                        {products.map(product => (
                                            <td
                                                key={product.productId}
                                                className="text-center"
                                            >
                                                <UiRating
                                                    size="xs"
                                                    initialRating={
                                                        product.rating
                                                    }
                                                    readonly
                                                />{' '}
                                                <span className="ml-1.5">
                                                    ({product.reviewCount})
                                                </span>
                                            </td>
                                        ))}
                                    </tr>
                                    <tr>
                                        <td className="sticky left-0 bg-white max-xl:hidden">
                                            {t('Price')}
                                        </td>
                                        {products.map(product => (
                                            <td
                                                key={product.productId}
                                                className="text-center"
                                            >
                                                <Price
                                                    className="text-xl font-medium text-default"
                                                    price={product.salesPrice}
                                                />
                                            </td>
                                        ))}
                                    </tr>
                                    <tr className="font-bold max-xl:hidden">
                                        <td className="sticky left-0 bg-white">
                                            {t('Product Features')}
                                        </td>
                                        {products.map(product => (
                                            <td
                                                key={product.productId}
                                                className="text-center"
                                            ></td>
                                        ))}
                                    </tr>
                                    {aggregatedFeatures.map(
                                        (feature, index) => (
                                            <tr key={feature.code}>
                                                <td className="sticky left-0 z-[3] bg-white max-xl:hidden">
                                                    {feature.label}
                                                </td>
                                                {products.map(product => {
                                                    const featureValue =
                                                        productFeatureLookup[
                                                            product.productId
                                                        ]?.[feature.code];

                                                    return (
                                                        <td
                                                            key={
                                                                product.productId
                                                            }
                                                            className={cls(
                                                                'text-center',
                                                                {
                                                                    'bg-gray-100':
                                                                        index %
                                                                            2 ===
                                                                        0
                                                                }
                                                            )}
                                                        >
                                                            {index % 2 ===
                                                                0 && (
                                                                <p className="mb-2 text-xs italic text-gray-400 xl:hidden">
                                                                    {
                                                                        feature.label
                                                                    }
                                                                </p>
                                                            )}
                                                            <p>
                                                                {featureValue
                                                                    ? t(
                                                                          featureValue
                                                                      )
                                                                    : '-'}
                                                            </p>
                                                        </td>
                                                    );
                                                })}
                                            </tr>
                                        )
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </>
            ) : (
                <div className="flex flex-1 flex-col items-center justify-center px-12 py-24">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                        <ArrowRightArrowLeftIcon className="h-7 w-7" />
                    </div>

                    <h1 className="pt-8 text-center text-2xl font-semibold">
                        {t('There are no items in your comparison list.')}
                    </h1>

                    <p className="px-10 pt-2 text-center text-muted xl:w-8/12">
                        {t(
                            'You can add the item you want to your list using the compare button on the detail page.'
                        )}
                    </p>
                </div>
            )}
        </section>
    );
});

if (isDev) {
    Compare.displayName = 'Compare';
}

export default Compare;
