import {memo, useMemo} from 'react';
import {OrderedGroupedItems, Page, SpecialPageProducts} from '@core/types';
import {isDev, trim} from '@core/helpers';
import {useStore} from '@core/hooks';
import Collections from '@components/common/Collections';
import FeaturedProductSlider from '@components/common/FeaturedProductSlider';
import MainSlider from '@components/common/MainSlider';
import Seo from '@components/common/Seo';
import StorySlider from '@components/common/StorySlider';

type HomePageProps = {
    productCatalogMap: SpecialPageProducts;
};

const HomePage: Page<HomePageProps> = memo(({productCatalogMap}) => {
    const {navigation} = useStore();

    const orderedGroupedItems = useMemo(() => {
        const subItems = navigation.filter(
            item => item.depth === 0 && !item.showInMainMenu
        );

        const items: OrderedGroupedItems[] = [];

        let index = 0;

        for (const subItem of subItems) {
            if (subItem.type !== 'product-catalog') {
                const lastItem =
                    items.length > 0 ? items[items.length - 1] : undefined;

                if (typeof lastItem !== 'undefined') {
                    if (lastItem.type === subItem.type) {
                        lastItem.items.push(subItem);
                    } else {
                        items.push({
                            id: `${subItem.type}-${index}`,
                            type: subItem.type,
                            items: [subItem]
                        });
                    }
                } else {
                    items.push({
                        id: `${subItem.type}-${index}`,
                        type: subItem.type,
                        items: [subItem]
                    });
                }
            } else {
                items.push({
                    id: `${subItem.type}-${index}`,
                    type: subItem.type,
                    items: [subItem],
                    products: productCatalogMap[subItem.id]?.products,
                    detailPageLink:
                        productCatalogMap[subItem.id]?.detailPageLink,
                    catalogName: productCatalogMap[subItem.id]?.catalogName
                });
            }

            index++;
        }

        return items;
    }, [navigation, productCatalogMap]);

    return (
        <>
            <Seo
                canonical={`${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/`}
            />

            {orderedGroupedItems.map(item => {
                if (item.type === 'slide') {
                    return (
                        <MainSlider
                            key={item.id}
                            forSpecialPage
                            items={item.items}
                        />
                    );
                } else if (item.type === 'story') {
                    return (
                        <StorySlider
                            key={item.id}
                            forSpecialPage
                            items={item.items}
                        />
                    );
                } else if (item.type === 'collection') {
                    return (
                        <Collections
                            key={item.id}
                            forSpecialPage
                            items={item.items}
                        />
                    );
                } else if (item.type === 'product-catalog') {
                    return (
                        <FeaturedProductSlider
                            key={item.id}
                            forSpecialPage
                            products={item.products ?? []}
                            detailPageLink={item.detailPageLink ?? ''}
                            productSliderName={item.catalogName ?? ''}
                        />
                    );
                }
            })}
        </>
    );
});

if (isDev) {
    HomePage.displayName = 'HomePage';
}

HomePage.initPageProps = async props => {
    return props;
};

export default HomePage;
