import {memo, useMemo} from 'react';
import {
    Breadcrumb,
    ContentPage as ContentPageType,
    NavigationItem,
    Page
} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiDivider, UiLink, UiStickyBox} from '@core/components/ui';
import Breadcrumbs from '@components/common/Breadcrumbs';
import Seo from '@components/common/Seo';

type ContentPageProps = {
    breadcrumbs: Breadcrumb[];
    page: ContentPageType;
    sideNavigation: (NavigationItem & {isActive: boolean})[];
};

const ContentPage: Page<ContentPageProps> = memo(props => {
    const {breadcrumbs, page, sideNavigation} = props;
    const {navigation, navigationItem} = useStore();

    const parentItem = useMemo(() => {
        if (typeof navigationItem === 'undefined') {
            return null;
        }

        const slugParts = navigationItem.slug.split('/');
        if (slugParts.length < 2) {
            return null;
        }

        const parentSlug = slugParts.slice(0, slugParts.length - 1).join('/');

        return navigation.find(item => item.slug === parentSlug);
    }, [navigation, navigationItem]);

    return (
        <>
            <Seo
                title={navigationItem?.seoTitle ?? navigationItem?.name}
                description={navigationItem?.seoDescription ?? ''}
            />

            <div className="container">
                <Breadcrumbs breadcrumbs={breadcrumbs} />

                {sideNavigation.length > 1 ? (
                    <div className="grid grid-cols-12 gap-x-12 py-12">
                        <div className="col-span-3 select-none">
                            <UiStickyBox>
                                <>
                                    {parentItem && (
                                        <>
                                            <h3 className="text-lg font-semibold leading-5 text-default">
                                                {parentItem.name}
                                            </h3>

                                            <UiDivider
                                                orientation="horizontal"
                                                className="mb-4 mt-3 border-gray-200"
                                            />
                                        </>
                                    )}

                                    <div className="-mx-3 space-y-1">
                                        {sideNavigation.map(item => (
                                            <UiLink
                                                key={item.slug}
                                                href={item.href}
                                                className={cls(
                                                    'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                                    {
                                                        'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                                            item.isActive
                                                    }
                                                )}
                                            >
                                                {item.name}
                                            </UiLink>
                                        ))}
                                    </div>
                                </>
                            </UiStickyBox>
                        </div>

                        <div className="col-span-9">
                            <h1 className="text-2xl font-semibold leading-6 text-default">
                                {navigationItem?.name}
                            </h1>

                            <UiDivider
                                orientation="horizontal"
                                className="mb-6 mt-4 border-gray-200"
                            />

                            <div
                                className="prose max-w-none"
                                dangerouslySetInnerHTML={{
                                    __html: page.content ?? ''
                                }}
                            />
                        </div>
                    </div>
                ) : (
                    <div
                        className="prose max-w-none py-12"
                        dangerouslySetInnerHTML={{
                            __html: page.content ?? ''
                        }}
                    />
                )}
            </div>
        </>
    );
});

ContentPage.initPageProps = async props => {
    return props;
};

if (isDev) {
    ContentPage.displayName = 'ContentPage';
}

export default ContentPage;
