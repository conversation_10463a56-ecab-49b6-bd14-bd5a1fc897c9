import {memo} from 'react';
import {Page} from '@core/types';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import MiniCart from '@components/common/MiniCart';
import Seo from '@components/common/Seo';

const MobileMyCartPage: Page = memo(() => {
    const t = useTrans();

    return (
        <>
            <Seo title={t('My Cart')} />
            <MiniCart />
        </>
    );
});

if (isDev) {
    MobileMyCartPage.displayName = 'MobileMyCartPage';
}

export default MobileMyCartPage;
