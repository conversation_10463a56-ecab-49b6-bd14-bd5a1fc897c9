import {memo} from 'react';
import {useRouter} from 'next/router';
import {signOut} from 'next-auth/react';
import {Cookies} from 'react-cookie-consent';
import storeConfig from '~/store.config';
import {Page} from '@core/types';
import {isDev} from '@core/helpers';
import {useCustomer, useIOSDevice, useTrans} from '@core/hooks';
import {UiAvatar, UiButton, UiLink} from '@core/components/ui';
import {
    BookmarkIcon,
    ChevronRightIcon,
    HeartIcon,
    LocationIcon,
    PowerOffIcon,
    ReceiptIcon,
    ReviewIcon,
    UserIcon
} from '@core/icons/outline';
import {ArrowLeftIcon as ArrowLeftSolidIcon} from '@core/icons/solid';
import Seo from '@components/common/Seo';

const MobileMyAccountPage: Page = memo(() => {
    const t = useTrans();
    const router = useRouter();
    const customer = useCustomer();
    const {isIOSDevice} = useIOSDevice();

    return (
        <>
            <Seo title={t('My Account')} />

            <div
                className="fixed left-0 top-0 z-[51] w-full overflow-hidden bg-white"
                style={{
                    height: `calc(100% - 0px)`
                }}
            >
                <div className="fixed left-0 top-0 z-[52] flex h-mobile-header w-full select-none bg-gradient-to-l from-primary-600 to-primary-500 text-white xl:hidden">
                    <div className="container">
                        <div className="flex h-full w-full items-center space-x-2">
                            <button
                                className="flex h-8 w-8 items-center justify-center rounded-full transition active:opacity-30"
                                onClick={() => router.back()}
                            >
                                <ArrowLeftSolidIcon className="h-4 w-4" />
                            </button>

                            <div className="min-w-0 flex-1">
                                <div className="w-full truncate font-semibold">
                                    {t('My Account')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="h-full w-full overflow-y-auto">
                    <div className="flex h-2/5 items-center justify-center bg-gradient-to-l from-primary-600 to-primary-500 pt-mobile-header text-white">
                        <div className="container">
                            <div className="flex flex-col items-center">
                                <UiAvatar
                                    className="flex h-16 w-16 items-center justify-center bg-white !text-xl text-primary-600"
                                    name={customer?.name}
                                />

                                <div className="mt-6 truncate text-xl font-semibold">
                                    {customer?.name}
                                </div>
                                <div className="mt-1 truncate text-xs">
                                    {customer?.email}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="divide-y divide-gray-200">
                        <UiLink
                            className="flex h-16 w-full items-center justify-between px-4"
                            href="/account/my-orders"
                        >
                            <div className="flex items-center">
                                <ReceiptIcon className="mr-3 h-4 w-4" />
                                {t('My Orders')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        <UiLink
                            className="flex h-16 w-full items-center justify-between px-4"
                            href="/account/my-favorites"
                        >
                            <div className="flex items-center">
                                <HeartIcon className="mr-3 h-4 w-4" />
                                {t('My Favorites')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        <UiLink
                            className="flex h-16 w-full items-center justify-between px-4"
                            href="/account/my-collections"
                        >
                            <div className="flex items-center">
                                <BookmarkIcon className="mr-3 h-4 w-4" />
                                {t('My Collections')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        <UiLink
                            className="flex h-16 w-full items-center justify-between px-4"
                            href="/account/my-reviews"
                        >
                            <div className="flex items-center">
                                <ReviewIcon className="mr-3 h-4 w-4" />
                                {t('My Reviews')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        <UiLink
                            className="flex h-16 w-full items-center justify-between px-4"
                            href="/account/my-addresses"
                        >
                            <div className="flex items-center">
                                <LocationIcon className="mr-3 h-4 w-4" />
                                {t('My Addresses')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>
                        <UiLink
                            className="flex h-16 w-full items-center justify-between px-4"
                            href="/account/my-account"
                        >
                            <div className="flex items-center">
                                <UserIcon className="mr-3 h-4 w-4" />
                                {t('My Account')}
                            </div>
                            <ChevronRightIcon className="h-4 w-4 text-muted" />
                        </UiLink>

                        <div className="w-full border-t border-gray-200 p-4">
                            <UiButton
                                className="w-full"
                                variant="outline"
                                leftIcon={
                                    <PowerOffIcon className="mr-3 h-4 w-4" />
                                }
                                onClick={() => {
                                    Cookies.remove('cart-id');
                                    signOut();
                                }}
                            >
                                {t('Sign Out')}
                            </UiButton>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
});

if (isDev) {
    MobileMyAccountPage.displayName = 'MobileMyAccountPage';
}

export default MobileMyAccountPage;
