import {memo, useCallback, useMemo} from 'react';
import {useRouter} from 'next/router';
import {NavigationItem, Page} from '@core/types';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {Ui<PERSON>utton, UiDivider, UiLink} from '@core/components/ui';
import {ChevronRightIcon} from '@core/icons/outline';
import Seo from '@components/common/Seo';
import {FacebookIcon, InstagramIcon, TwitterIcon} from '@core/icons/brand';
import {PackageIcon} from '@components/icons';
import {NextSeo} from 'next-seo';

type MobileMenuPageProps = {
    currentId?: string;
};

const MobileMenuPage: Page<MobileMenuPageProps> = memo(props => {
    const {currentId} = props;
    const router = useRouter();
    const t = useTrans();
    const {navigation} = useStore();
    const currentItem = useMemo(
        () =>
            navigation.find(navigationItem => navigationItem.id === currentId),
        [currentId, navigation]
    );
    const items = useMemo(() => {
        if (typeof currentItem === 'undefined') {
            return navigation.filter(
                navigationItem =>
                    navigationItem.showInMainMenu &&
                    navigationItem.slug.split('/').length == 1
            );
        }

        return navigation
            .filter(navigationItem => navigationItem.showInMainMenu)
            .filter(
                navigationItem =>
                    navigationItem.slug.includes(currentItem.slug) &&
                    navigationItem.slug.startsWith(currentItem.slug) &&
                    navigationItem.id !== currentItem.id &&
                    navigationItem.slug.split('/').length ==
                        currentItem.slug.split('/').length + 1
            );
    }, [navigation, currentItem]);

    const hasSubItems = useCallback(
        (item: NavigationItem) =>
            navigation.some(
                navigationItem =>
                    navigationItem.slug.includes(item.slug) &&
                    navigationItem.slug !== item.slug
            ),
        [navigation]
    );

    return (
        <>
            {currentItem ? (
                <NextSeo
                    title={currentItem.seoTitle ?? currentItem.name}
                    description={
                        currentItem?.seoDescription ?? currentItem.description
                    }
                />
            ) : (
                <NextSeo title={t('Menu')} />
            )}

            <div className="flex h-full w-full flex-col justify-between overflow-hidden">
                <div className="flex-1 overflow-y-auto">
                    <div className="divide-y divide-gray-300">
                        {items.map(item => {
                            if (
                                item.type === 'collection' ||
                                item.type === 'story' ||
                                item.type === 'slide'
                            )
                                return;

                            return (
                                <UiLink
                                    className="flex h-16 w-full items-center justify-between py-[34px] pl-12 pr-8"
                                    key={item.id}
                                    href={
                                        hasSubItems(item) &&
                                        item.hasChild === true
                                            ? `/mobile/menu/${item.id}`
                                            : item.href
                                    }
                                >
                                    <div className="font-hurme text-sm font-semibold tracking-normal">
                                        {item.name}
                                    </div>
                                    <ChevronRightIcon className="h-2.5 w-2.5 stroke-current stroke-[30px] text-secondary-100" />
                                </UiLink>
                            );
                        })}
                    </div>
                    <UiDivider className="-mt-0.5" />
                    <div className="flex items-center justify-center divide-y divide-gray-300">
                        <div className="mt-4 grid place-items-center ">
                            <p className="text-[9px] text-secondary-100">
                                {t('Follow us!')}
                            </p>

                            <div className="flex items-center gap-6 pt-3">
                                <a
                                    href="https://www.facebook.com/NehirMutfak"
                                    rel="noopener noreferrer"
                                    target="_blank"
                                    title="Facebook"
                                    className="flex h-9 w-9 items-center justify-center rounded-full border border-secondary-100 transition duration-150 ease-in-out hover:border-gray-600   "
                                >
                                    <FacebookIcon className="h-4 w-4 text-secondary-100 hover:text-gray-600" />
                                </a>
                                <a
                                    href="https://twitter.com/nehirmutfakta"
                                    rel="noopener noreferrer"
                                    target="_blank"
                                    title="LinkedIn"
                                    className="flex h-9 w-9 items-center justify-center rounded-full border border-secondary-100 transition duration-150 ease-in-out hover:border-gray-600   "
                                >
                                    <TwitterIcon className="h-4 w-4 text-secondary-100 hover:text-gray-600" />
                                </a>
                                <a
                                    href="https://www.instagram.com/nehirmutfak/"
                                    rel="noopener noreferrer"
                                    target="_blank"
                                    title="Instagram"
                                    className="flex h-9 w-9 items-center justify-center rounded-full border border-secondary-100 transition duration-150 ease-in-out hover:border-gray-600   "
                                >
                                    <InstagramIcon className="h-4 w-4 text-secondary-100 hover:text-gray-600" />
                                </a>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4 flex flex-col items-center   gap-8 p-4 ">
                        <div className="flex items-center gap-10 font-semibold">
                            <UiLink
                                className="font-hurme text-[10px]"
                                href={'/cozum-merkezi/islem-rehberi'}
                            >
                                MÜŞTERİ HİZMETLERİ
                            </UiLink>
                            <UiLink
                                className="font-hurme text-[10px]"
                                href={'/kurumsal/hakkimizda'}
                            >
                                HAKKIMIZDA
                            </UiLink>
                            <UiLink
                                className="font-hurme text-[10px]"
                                href={'/account/my-orders'}
                            >
                                <span> SİPARİŞ TAKİP</span>
                            </UiLink>
                        </div>
                    </div>
                </div>

                {currentItem &&
                    currentItem.type == 'product-catalog' &&
                    currentItem.section !== 'header' && (
                        <div className="border-t border-gray-200 p-4">
                            <UiButton
                                variant="solid"
                                color="primary"
                                className="w-full"
                                onClick={() => router.push(currentItem.href)}
                            >
                                {t('Show All Products')}
                            </UiButton>
                        </div>
                    )}
            </div>
        </>
    );
});

if (isDev) {
    MobileMenuPage.displayName = 'MobileMenuPage';
}

export default MobileMenuPage;
