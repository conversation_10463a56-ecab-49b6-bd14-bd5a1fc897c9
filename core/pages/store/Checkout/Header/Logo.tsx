import {FC, memo} from 'react';
import Link from 'next/link';
import siteLogo from '@assets/images/common/site-logo.png';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {UiImage} from '@core/components/ui';

const Logo: FC = memo(() => {
    return (
        <div className="flex items-center">
            <Link
                className="h-logo cursor-pointer"
                title={storeConfig.title}
                aria-label="Logo"
                href="/"
            >
                <UiImage
                    src={siteLogo}
                    alt={storeConfig.title}
                    width={parseFloat(
                        storeConfig.theme.accountLogoWidth.replace('px', '')
                    )}
                    height={parseFloat(
                        storeConfig.theme.accountLogoHeight.replace('px', '')
                    )}
                    priority={true}
                />
            </Link>
        </div>
    );
});

if (isDev) {
    Logo.displayName = 'Logo';
}

export default Logo;
