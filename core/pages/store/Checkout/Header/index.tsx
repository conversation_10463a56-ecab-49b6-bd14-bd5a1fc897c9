import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import Logo from './Logo';
import Steps from './Steps';
import UserNav from './UserNav';

const Header: FC = memo(() => {
    return (
        <div className="min-h-account-header relative hidden border-b border-gray-200 bg-white xl:block">
            <div className="container">
                <div className="flex h-account-header items-center">
                    {/* Logo */}
                    <Logo />

                    {/* Center */}
                    <Steps />

                    {/* User Nav */}
                    <UserNav />
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    Header.displayName = 'Header';
}

export default Header;
