import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useCheckout from './useCheckout';

type MobileStepProps = {
    text: string;
    isCurrent?: boolean;
    isCompleted?: boolean;
    isDisabled?: boolean;
    onClick?: () => void;
};

const MobileStep: FC<MobileStepProps> = memo(
    ({text, isCurrent, isCompleted, onClick, isDisabled}) => {
        return (
            <button
                className={cls('flex flex-col items-center justify-center', {
                    'text-primary-600': isCurrent && !isCompleted,
                    'text-default': !isCurrent && !isCompleted,
                    'text-success-600': isCompleted && !isDisabled,
                    'text-gray-500': isCompleted && isDisabled
                })}
                onClick={() =>
                    isCompleted && !isDisabled && !!onClick && onClick()
                }
            >
                <div className="h-3 w-3 rounded-full bg-current"></div>
                <div className="mt-1.5 text-sm">
                    <span className="whitespace-nowrap">{text}</span>
                </div>
            </button>
        );
    }
);

if (isDev) {
    MobileStep.displayName = 'MobileStep';
}

const MobileSteps: FC = memo(() => {
    const t = useTrans();
    const {cart, updateStep} = useCheckout();
    const step = useMemo(() => cart.step, [cart]);

    return (
        <div className="mt-6 flex items-center justify-center xl:hidden">
            <MobileStep
                text={t('Information')}
                isCurrent={step === 'information'}
                isCompleted={step === 'delivery' || step === 'payment'}
                isDisabled={cart.status === 'completed'}
                onClick={() => updateStep('information')}
            />
            <div className="mx-3 w-12 border border-primary-100"></div>
            <MobileStep
                text={t('Delivery')}
                isCurrent={step === 'delivery'}
                isCompleted={step === 'payment'}
                isDisabled={cart.status === 'completed'}
                onClick={() => updateStep('delivery')}
            />
            <div className="mx-3 w-12 border border-primary-100"></div>
            <MobileStep
                text={t('Payment')}
                isCurrent={step === 'payment' && cart.status !== 'completed'}
                isDisabled={cart.status === 'completed'}
                isCompleted={cart.status === 'completed'}
            />
        </div>
    );
});

if (isDev) {
    MobileSteps.displayName = 'MobileSteps';
}

export default MobileSteps;
