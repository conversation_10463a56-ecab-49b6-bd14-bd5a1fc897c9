import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';

const Footer: FC = memo(() => {
    const t = useTrans();

    return (
        <footer
            aria-labelledby="footer-heading"
            className="border-gary-200 relative hidden border-t bg-white xl:block"
        >
            <h2 id="footer-heading" className="sr-only">
                Footer
            </h2>

            <div className="container">
                <div className="flex items-center justify-between py-4">
                    <div className="text-center text-sm text-gray-700">
                        &copy; {new Date().getFullYear()}{' '}
                        {t('All Rights Reserved')}
                    </div>

                    <div className="flex items-center justify-end text-sm text-gray-700">
                        <span className="mr-1 inline-block">Powered by</span>
                        <a
                            className="text-primary-600 hover:underline"
                            href="https://entererp.com"
                            target="_blank"
                            rel="noreferrer"
                        >
                            EnterERP
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    );
});

if (isDev) {
    Footer.displayName = 'Footer';
}

export default Footer;
