import {FC, memo, useCallback, useRef, useState} from 'react';
import {Cart, CartItem} from '@core/types';
import {clone, isDev} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {
    UiAlert,
    UiButton,
    UiImage,
    UiInput,
    UiSelect
} from '@core/components/ui';

type SpecialDeliveryProps = {
    cart: Cart;
    updateDeliveryType: (
        deliveryType: 'standard' | 'special' | 'store-delivery',
        deliveryOptionId: string | null,
        items?: {productId: string; deliveryDate?: Date; deliveryTime?: Date}[]
    ) => Promise<void>;
};

type DeliveryItem = CartItem & {
    selectedDeliveryDate?: string;
    selectedDeliveryTime?: string;
};

const SpecialDelivery: FC<SpecialDeliveryProps> = memo(props => {
    const {cart, updateDeliveryType} = props;
    const {closeModal} = useUI();
    const [items, setItems] = useState<DeliveryItem[]>(() =>
        clone(cart.items as DeliveryItem[])
            .filter(
                item =>
                    !!item.deliveryAtSpecifiedDate ||
                    !!item.deliveryAtSpecifiedTime
            )
            .map(item => {
                const now = new Date();

                if (typeof item.deliveryDate === 'undefined') {
                    item.deliveryDate = new Date(
                        new Date().setDate(
                            now.getDate() +
                                (item.estimatedDeliveryDuration ?? 3)
                        )
                    );
                }

                let y: any = item.deliveryDate.getFullYear();
                let m: any = item.deliveryDate.getMonth() + 1;
                m = m > 9 ? m : `0${m}`;
                let d: any = item.deliveryDate.getDate();
                d = d > 9 ? d : `0${d}`;

                item.selectedDeliveryDate = `${y}-${m}-${d}`;

                if (typeof item.deliveryTime !== 'undefined') {
                    let h: any = item.deliveryTime.getHours();
                    h = h > 9 ? h : `0${h}`;
                    let m: any = item.deliveryTime.getMinutes();
                    m = m > 9 ? m : `0${m}`;

                    item.selectedDeliveryTime = `${h}:${m}`;
                } else {
                    item.selectedDeliveryTime = '13:00';
                }

                return item;
            })
    );
    const t = useTrans();
    const getMinDate = useCallback(
        (item: DeliveryItem) => {
            const originalItem = cart.items.find(
                i => i.productId === item.productId
            );
            const now = new Date();

            const date = new Date(
                new Date().setDate(
                    now.getDate() +
                        (originalItem?.estimatedDeliveryDuration ?? 3)
                )
            );

            let y: any = date.getFullYear();
            let m: any = date.getMonth() + 1;
            m = m > 9 ? m : `0${m}`;
            let d: any = date.getDate();
            d = d > 9 ? d : `0${d}`;

            return `${y}-${m}-${d}`;
        },
        [cart]
    );
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const inProgress = useRef(false);

    const onSave = useCallback(() => {
        if (inProgress.current) return;

        inProgress.current = true;
        setIsLoading(true);

        try {
            if (
                items.some(item => {
                    if (item.deliveryDate) {
                        const minDate = getMinDate(item);

                        return (
                            new Date(minDate).getTime() >
                            item.deliveryDate.getTime()
                        );
                    }

                    return false;
                })
            ) {
                // noinspection ExceptionCaughtLocallyJS
                throw new Error('Invalid delivery date!');
            }

            (async () => {
                await updateDeliveryType(
                    'special',
                    null,
                    items.map(item => ({
                        productId: item.productId,
                        deliveryDate: item.deliveryDate,
                        deliveryTime: item.deliveryTime
                    }))
                );

                closeModal();
            })();
        } catch (error: any) {
            setErrorMessage(error.message);
            setIsLoading(false);
        }

        inProgress.current = false;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [items, getMinDate]);

    return (
        <div className="px-8 pb-6 pt-6">
            {!!errorMessage && (
                <UiAlert className="mb-6" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <table className="w-full text-gray-500">
                <thead className="border-t border-gray-200 text-left text-sm text-gray-500">
                    <tr>
                        <th scope="col" className="py-3 pr-8 font-normal">
                            {t('Product')}
                        </th>
                        <th scope="col" className="w-1/3 py-3 pr-8 font-normal">
                            {t('Delivery Date')}
                        </th>
                    </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 border-b border-t border-gray-200 text-sm">
                    {items.map(item => (
                        <tr key={item.productId}>
                            <td className="py-6 pr-8">
                                <div className="flex items-center">
                                    <div className="relative h-16 w-12 rounded">
                                        <UiImage
                                            className="h-full w-full rounded"
                                            src={`${item.productImage}?w=180&q=50`}
                                            alt={item.productName}
                                            width={48}
                                            height={64}
                                            fit="cover"
                                            position="center"
                                            quality={75}
                                        />

                                        <span
                                            className="
                                        absolute flex items-center justify-center rounded-full border
                                        border-gray-100 bg-gray-900 text-xs font-medium text-white
                                        "
                                            style={{
                                                top: '-10px',
                                                right: '-10px',
                                                paddingLeft: '2.5px',
                                                paddingRight: '2.5px',
                                                minWidth: '1.25rem',
                                                minHeight: '1.25rem'
                                            }}
                                        >
                                            {item.quantity}
                                        </span>
                                    </div>

                                    <div className="ml-4 flex-1">
                                        <h3 className="font-medium text-base text-default">
                                            {item.productName}
                                        </h3>

                                        <div className="mt-1 text-sm">
                                            {Array.isArray(
                                                item.productAttributes
                                            ) &&
                                                item.productAttributes.length >
                                                    0 && (
                                                    <div className="flex items-center space-x-3">
                                                        {item.productAttributes.map(
                                                            attribute => (
                                                                <div
                                                                    className="flex items-center text-muted"
                                                                    key={
                                                                        attribute.value
                                                                    }
                                                                >
                                                                    <div className="mr-0.5">
                                                                        {
                                                                            attribute.label
                                                                        }
                                                                        :
                                                                    </div>
                                                                    <div>
                                                                        {
                                                                            attribute.value
                                                                        }
                                                                    </div>
                                                                </div>
                                                            )
                                                        )}
                                                    </div>
                                                )}
                                        </div>
                                    </div>
                                </div>
                            </td>

                            <td className="py-6 pr-8">
                                <div className="flex items-center space-x-4">
                                    <UiInput
                                        size="lg"
                                        className="flex-1"
                                        type="date"
                                        disabled={!item.deliveryAtSpecifiedDate}
                                        value={item.selectedDeliveryDate}
                                        min={getMinDate(item)}
                                        onChange={e => {
                                            setItems(prevItems => {
                                                prevItems = prevItems.map(
                                                    prevItem => {
                                                        if (
                                                            prevItem.productId ===
                                                            item.productId
                                                        ) {
                                                            item.deliveryDate =
                                                                new Date(
                                                                    e.target.value
                                                                );
                                                            item.selectedDeliveryDate =
                                                                e.target.value;
                                                        }

                                                        return prevItem;
                                                    }
                                                );

                                                return prevItems;
                                            });
                                        }}
                                    />

                                    {!!item.deliveryAtSpecifiedTime && (
                                        <UiSelect
                                            value={item.selectedDeliveryTime}
                                            onChange={e => {
                                                setItems(prevItems => {
                                                    prevItems = prevItems.map(
                                                        prevItem => {
                                                            if (
                                                                prevItem.productId ===
                                                                item.productId
                                                            ) {
                                                                const parts =
                                                                    e.target.value.split(
                                                                        ':'
                                                                    );
                                                                const h =
                                                                    parseInt(
                                                                        parts[0]
                                                                    );
                                                                const m =
                                                                    parseInt(
                                                                        parts[1]
                                                                    );

                                                                prevItem.deliveryTime =
                                                                    new Date(
                                                                        (
                                                                            prevItem.deliveryDate as Date
                                                                        ).getFullYear(),
                                                                        (
                                                                            prevItem.deliveryDate as Date
                                                                        ).getMonth(),
                                                                        (
                                                                            prevItem.deliveryDate as Date
                                                                        ).getDate(),
                                                                        h,
                                                                        m,
                                                                        0
                                                                    );
                                                                item.selectedDeliveryTime =
                                                                    e.target.value;
                                                            }

                                                            return prevItem;
                                                        }
                                                    );

                                                    return prevItems;
                                                });
                                            }}
                                            size="lg"
                                            className="w-28"
                                        >
                                            <option value="09:00">09:00</option>
                                            <option value="09:30">09:30</option>
                                            <option value="10:00">10:00</option>
                                            <option value="10:30">10:30</option>
                                            <option value="11:00">11:00</option>
                                            <option value="11:30">11:30</option>
                                            <option value="12:00">12:00</option>
                                            <option value="12:30">12:30</option>
                                            <option value="13:00">13:00</option>
                                            <option value="13:30">13:30</option>
                                            <option value="14:00">14:00</option>
                                            <option value="14:30">14:30</option>
                                            <option value="15:00">15:00</option>
                                            <option value="15:30">15:30</option>
                                            <option value="16:00">16:00</option>
                                            <option value="16:30">16:30</option>
                                            <option value="17:00">17:00</option>
                                            <option value="17:30">17:30</option>
                                            <option value="18:00">18:00</option>
                                        </UiSelect>
                                    )}
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>

            <div className="flex items-center justify-between pt-6">
                <UiButton
                    variant="outline"
                    color="primary"
                    size="lg"
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={closeModal}
                >
                    {t('Cancel Special Delivery')}
                </UiButton>

                <UiButton
                    variant="solid"
                    color="primary"
                    size="lg"
                    loading={isLoading}
                    onClick={onSave}
                >
                    {t('Save Special Delivery')}
                </UiButton>
            </div>
        </div>
    );
});

if (isDev) {
    SpecialDelivery.displayName = 'SpecialDelivery';
}

export default SpecialDelivery;
