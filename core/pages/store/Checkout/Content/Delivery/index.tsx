import {
    FC,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {DeliveryOption} from '@core/types';
import {cls, isDev, jsonRequest} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {
    notification,
    UiAlert,
    UiButton,
    UiDivider,
    UiLink,
    UiListBox,
    UiRadioGroup
} from '@core/components/ui';
import {ArrowRightIcon, CheckCircleIcon} from '@core/icons/solid';
import useCheckout from '../../useCheckout';
import SpecialDelivery from './SpecialDelivery';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/outline';

type Warehouse = {
    id: string;
    address: Record<string, string>;
    name: string;
};

const Delivery: FC = memo(() => {
    const t = useTrans();
    const router = useRouter();
    const {openModal} = useUI();
    const {
        cart,
        deliveryOptions,
        updateDeliveryType,
        saveDelivery,
        errorMessage,
        setErrorMessage,
        isLoading,
        setIsLoading
    } = useCheckout();
    const inProgress = useRef(false);

    const [selectedDeliveryOptionId, setSelectedDeliveryOptionId] = useState<
        string | null
    >(null);
    const [selectedWarehouseId, setSelectedWarehouseId] = useState<
        string | null
    >(null);
    const [isFetchingWarehouses, setIsFetchingWarehouses] = useState(true);
    const [warehouses, setWarehouses] = useState<Warehouse[]>([]);

    const getDurationText = useCallback(
        (deliveryOption: DeliveryOption) => {
            let text = '';
            if (
                deliveryOption.minDeliveryDays ===
                deliveryOption.maxDeliveryDays
            ) {
                text = deliveryOption.minDeliveryDays.toString();
                text +=
                    deliveryOption.minDeliveryDays > 1
                        ? ` ${t('Days')}`
                        : ` ${t('Day')}`;
            } else {
                text = `${deliveryOption.minDeliveryDays} - ${deliveryOption.maxDeliveryDays}`;
                text +=
                    deliveryOption.minDeliveryDays > 1 ||
                    deliveryOption.maxDeliveryDays > 1
                        ? ` ${t('Days')}`
                        : ` ${t('Day')}`;
            }
            return text;
        },
        [t]
    );

    const fetchWarehouses = useCallback(async (deliveryOptionId: string) => {
        try {
            const warehouses = await jsonRequest<Warehouse[]>({
                url: '/api/checkout/store-delivery-warehouses',
                method: 'POST',
                data: {deliveryOptionId}
            });
            setWarehouses(warehouses);
        } catch (error) {
            notification({
                title: t('Error'),
                description: t(
                    'An error occurred while adding the product to favourites!'
                ),
                status: 'error'
            });
        } finally {
            setIsFetchingWarehouses(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onDeliveryTypeChange = useCallback(
        async (deliveryOptionId: string) => {
            if (
                inProgress.current ||
                deliveryOptionId === selectedDeliveryOptionId
            )
                return;
            inProgress.current = true;
            setIsLoading(true);
            setErrorMessage('');

            const selectedOption = deliveryOptions.find(
                option => option.deliveryOptionId === deliveryOptionId
            );
            if (!selectedOption) {
                console.error('Selected delivery option not found');
                inProgress.current = false;
                setIsLoading(false);
                return;
            }

            setSelectedDeliveryOptionId(deliveryOptionId);

            if (selectedOption.type === 'special') {
                openModal(t('Special Delivery'), SpecialDelivery, {
                    cart,
                    updateDeliveryType,
                    isLarge: true
                });
            } else {
                try {
                    if (selectedOption.type === 'store-delivery') {
                        await fetchWarehouses(deliveryOptionId);
                    }

                    await updateDeliveryType(
                        selectedOption.type,
                        deliveryOptionId
                    );
                } catch (error: any) {
                    setErrorMessage(t(error.message));
                }
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        [
            selectedDeliveryOptionId,
            setIsLoading,
            setErrorMessage,
            deliveryOptions,
            openModal,
            t,
            cart,
            updateDeliveryType,
            fetchWarehouses
        ]
    );

    const defaultDeliveryOptionId =
        cart.deliveryOptionId ||
        deliveryOptions.find(option => option.isDefault)?.deliveryOptionId ||
        deliveryOptions[0]?.deliveryOptionId;

    useEffect(() => {
        if (defaultDeliveryOptionId) {
            setSelectedDeliveryOptionId(defaultDeliveryOptionId);
            const defaultOption = deliveryOptions.find(
                option => option.deliveryOptionId === defaultDeliveryOptionId
            );
            if (defaultOption && defaultOption.type === 'store-delivery') {
                fetchWarehouses(defaultDeliveryOptionId);
            }
        }
    }, [defaultDeliveryOptionId, deliveryOptions, fetchWarehouses]);

    const onSubmit = useCallback(async () => {
        if (inProgress.current || !selectedDeliveryOptionId) return;
        inProgress.current = true;
        setIsLoading(true);

        const selectedOption = deliveryOptions.find(
            option => option.deliveryOptionId === selectedDeliveryOptionId
        );

        if (selectedOption?.type === 'store-delivery' && !selectedWarehouseId) {
            setErrorMessage(t('Please select a store for delivery'));
            setIsLoading(false);
            inProgress.current = false;
            return;
        }

        try {
            await saveDelivery({
                deliveryPayload: {
                    deliveryOptionId: selectedDeliveryOptionId,
                    storeDeliveryWarehouseId: selectedWarehouseId
                }
            });
        } catch (error: any) {
            setErrorMessage(t(error.message));
            const container = document.querySelector('.content-wrapper');
            if (container !== null) {
                container.scrollTo({top: 0, behavior: 'smooth'});
            }
        }

        setIsLoading(false);
        inProgress.current = false;
    }, [
        deliveryOptions,
        selectedWarehouseId,
        selectedDeliveryOptionId,
        saveDelivery,
        setIsLoading,
        setErrorMessage,
        t
    ]);

    const selectedWarehouse = useMemo(() => {
        return warehouses.find(
            warehouse => warehouse.id === selectedWarehouseId
        );
    }, [selectedWarehouseId, warehouses]);

    return (
        <>
            {!!errorMessage && (
                <UiAlert className="mb-4 xl:mb-8" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <UiRadioGroup
                value={selectedDeliveryOptionId || ''}
                onChange={onDeliveryTypeChange}
                disabled={isLoading}
            >
                <UiRadioGroup.Label className="text-lg font-medium">
                    {t('Delivery Options')}
                </UiRadioGroup.Label>

                <div className="mt-4 space-y-2 xl:mt-6 xl:space-y-4">
                    {deliveryOptions.map(deliveryOption => (
                        <UiRadioGroup.Option
                            key={deliveryOption.deliveryOptionId}
                            value={deliveryOption.deliveryOptionId}
                            className={({checked}) =>
                                cls(
                                    checked
                                        ? 'border-transparent'
                                        : 'border-gray-300',
                                    'relative cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none'
                                )
                            }
                        >
                            {({checked}) => (
                                <>
                                    <div className="flex items-center">
                                        {checked ? (
                                            <CheckCircleIcon
                                                className="mr-4 h-7 w-7 text-primary-600"
                                                aria-hidden="true"
                                            />
                                        ) : (
                                            <div className="mr-4 h-7 w-7 rounded-full border border-gray-300"></div>
                                        )}

                                        <div className="flex flex-1 items-center">
                                            <div className="flex flex-1 flex-col gap-1.5">
                                                <UiRadioGroup.Label
                                                    as="span"
                                                    className="block text-sm font-medium"
                                                >
                                                    {t(deliveryOption.name)}
                                                </UiRadioGroup.Label>

                                                <UiRadioGroup.Description
                                                    as="span"
                                                    className="flex items-center text-sm text-gray-500"
                                                >
                                                    {t(
                                                        deliveryOption.description
                                                    )}
                                                </UiRadioGroup.Description>
                                            </div>

                                            {deliveryOption.type !==
                                                'store-delivery' && (
                                                <div className="ml-4 flex flex-col items-end justify-center gap-1.5">
                                                    <UiRadioGroup.Description
                                                        as="span"
                                                        className="text-xs font-medium text-gray-500"
                                                    >
                                                        {t(
                                                            'Estimated delivery date'
                                                        )}
                                                    </UiRadioGroup.Description>

                                                    <UiRadioGroup.Description
                                                        as="span"
                                                        className="flex items-center text-sm text-gray-500"
                                                    >
                                                        {getDurationText(
                                                            deliveryOption
                                                        )}
                                                    </UiRadioGroup.Description>
                                                </div>
                                            )}
                                        </div>

                                        <div
                                            className={cls(
                                                checked
                                                    ? 'border-primary-600'
                                                    : 'border-transparent',
                                                'pointer-events-none absolute -inset-px rounded-lg border-2'
                                            )}
                                            aria-hidden="true"
                                        />
                                    </div>

                                    {deliveryOption.type === 'store-delivery' &&
                                        checked && (
                                            <>
                                                <div className="mt-4 h-9 w-full">
                                                    {isFetchingWarehouses ? (
                                                        <div className="skeleton-card h-9 w-full rounded-lg"></div>
                                                    ) : (
                                                        <UiListBox
                                                            value={
                                                                selectedWarehouseId ||
                                                                ''
                                                            }
                                                            onChange={value => {
                                                                setErrorMessage(
                                                                    ''
                                                                );
                                                                setSelectedWarehouseId(
                                                                    value
                                                                );
                                                            }}
                                                            as="div"
                                                            className="relative w-full min-w-full space-y-1"
                                                        >
                                                            {({open}) => (
                                                                <>
                                                                    <UiListBox.Button
                                                                        className={cls(
                                                                            'relative inline-flex h-9 w-full cursor-pointer appearance-none items-center rounded border border-gray-100 bg-gray-100 px-3 py-0 pr-6 text-sm shadow-sm transition hover:border-gray-300 focus:outline-none',
                                                                            {
                                                                                'border-primary-600 !bg-white ring-1 ring-primary-600':
                                                                                    open
                                                                            }
                                                                        )}
                                                                    >
                                                                        <span className="truncate text-sm">
                                                                            {selectedWarehouse?.name ??
                                                                                t(
                                                                                    'Please select a store'
                                                                                )}
                                                                        </span>
                                                                        <span className="pointer-events-none absolute right-2 ml-3 flex items-center">
                                                                            {open ? (
                                                                                <ChevronUpIcon
                                                                                    className="h-3 w-3 text-primary-600"
                                                                                    aria-hidden="true"
                                                                                />
                                                                            ) : (
                                                                                <ChevronDownIcon
                                                                                    className="h-3 w-3 text-muted"
                                                                                    aria-hidden="true"
                                                                                />
                                                                            )}
                                                                        </span>
                                                                    </UiListBox.Button>
                                                                    <UiListBox.Options className="absolute left-0 top-9 z-40 max-h-64 w-full origin-bottom-left overflow-auto rounded border border-gray-200 bg-white p-1.5 shadow-sm outline-none">
                                                                        {warehouses.map(
                                                                            warehouse => (
                                                                                <UiListBox.Option
                                                                                    className="relative"
                                                                                    key={
                                                                                        warehouse.id
                                                                                    }
                                                                                    value={
                                                                                        warehouse.id
                                                                                    }
                                                                                >
                                                                                    {({
                                                                                        active,
                                                                                        selected,
                                                                                        disabled
                                                                                    }) => (
                                                                                        <button
                                                                                            disabled={
                                                                                                disabled
                                                                                            }
                                                                                            aria-disabled={
                                                                                                disabled
                                                                                            }
                                                                                            className={cls(
                                                                                                'flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal focus:outline-none',
                                                                                                active &&
                                                                                                    'bg-gray-100'
                                                                                            )}
                                                                                        >
                                                                                            <span
                                                                                                className={cls(
                                                                                                    'block flex-1 truncate',
                                                                                                    selected
                                                                                                        ? 'font-medium'
                                                                                                        : 'font-normal'
                                                                                                )}
                                                                                            >
                                                                                                {
                                                                                                    warehouse.name
                                                                                                }
                                                                                            </span>
                                                                                            {selected && (
                                                                                                <span
                                                                                                    className="absolute -left-1 h-6 rounded-full bg-primary-600"
                                                                                                    style={{
                                                                                                        width: 2
                                                                                                    }}
                                                                                                ></span>
                                                                                            )}
                                                                                        </button>
                                                                                    )}
                                                                                </UiListBox.Option>
                                                                            )
                                                                        )}
                                                                    </UiListBox.Options>
                                                                </>
                                                            )}
                                                        </UiListBox>
                                                    )}
                                                </div>

                                                {selectedWarehouse &&
                                                    selectedWarehouse.address &&
                                                    selectedWarehouse.address
                                                        .address && (
                                                        <p className="mt-4 text-xs">
                                                            <span className="font-semibold">
                                                                {t('Address')}:{' '}
                                                            </span>
                                                            {
                                                                selectedWarehouse
                                                                    .address
                                                                    .address
                                                            }
                                                        </p>
                                                    )}
                                            </>
                                        )}
                                </>
                            )}
                        </UiRadioGroup.Option>
                    ))}
                </div>
            </UiRadioGroup>

            <UiDivider
                orientation="horizontal"
                className="mb-4 mt-6 block border-gray-200 xl:hidden"
            />

            <div className="flex flex-col xl:mt-12 xl:flex-row xl:items-center xl:justify-between">
                <UiLink className="hidden xl:block" href="/">
                    <span aria-hidden="true">&larr; </span>
                    {t('Continue Shopping')}
                </UiLink>

                <UiButton
                    type="submit"
                    variant="solid"
                    color="primary"
                    size="xl"
                    leftIcon={<ArrowRightIcon className="mr-2 h-4 w-4" />}
                    loading={isLoading}
                    disabled={!!errorMessage}
                    onClick={onSubmit}
                >
                    {t('Continue to Payment')}
                </UiButton>

                <UiButton
                    className="mt-2 block w-full xl:hidden"
                    variant="light"
                    color="primary"
                    size="md"
                    onClick={() => router.push('/')}
                >
                    <span aria-hidden="true">&larr; </span>
                    {t('Continue Shopping')}
                </UiButton>
            </div>
        </>
    );
});

if (isDev) {
    Delivery.displayName = 'Delivery';
}

export default Delivery;
