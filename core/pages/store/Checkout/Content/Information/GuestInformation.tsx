import {FC, memo, useCallback, useEffect, useRef} from 'react';
import {useRouter} from 'next/router';
import {FormProvider, useForm, useWatch} from 'react-hook-form';
import {isDev, regexp} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {
    UiAlert,
    UiButton,
    UiCheckbox,
    UiDivider,
    UiForm,
    UiLink
} from '@core/components/ui';
import {ArrowRightIcon} from '@core/icons/solid';
import AddressForm from '@components/common/AddressForm';
import useCheckout from '../../useCheckout';

const GuestInformation: FC = memo(() => {
    const methods = useForm();
    const {
        register,
        control,
        formState: {errors},
        setValue,
        getValues,
        setError,
        clearErrors,
        watch
    } = methods;
    const router = useRouter();
    const t = useTrans();
    const {
        cart,
        countries,
        initialStates,
        initialCities,
        saveGuestInformation,
        errorMessage,
        setErrorMessage,
        isLoading,
        setIsLoading
    } = useCheckout();
    const inProgress = useRef(false);
    const useDeliveryAddressAsBillingAddress: string = useWatch({
        name: 'useDeliveryAddressAsBillingAddress',
        defaultValue: true,
        control
    });
    const invoiceType: string = useWatch({
        name: 'invoiceType',
        defaultValue: 'individual',
        control
    });
    const phone = watch('phone', {});

    useEffect(() => {
        if (typeof cart.firstName !== 'undefined')
            setValue('firstName', cart.firstName);
        if (typeof cart.lastName !== 'undefined')
            setValue('lastName', cart.lastName);
        if (typeof cart.email !== 'undefined') setValue('email', cart.email);
        if (!!cart.phoneCountryCode && !!cart.phoneCode && !!cart.phoneNumber) {
            setValue('phone', {
                countryCode: cart.phoneCountryCode,
                code: cart.phoneCode,
                number: cart.phoneNumber
            });
        }
        if (typeof cart.isSubscribedToNewsletter !== 'undefined')
            setValue('isSubscribedToNewsletter', cart.isSubscribedToNewsletter);
        if (typeof cart.useDeliveryAddressAsBillingAddress !== 'undefined')
            setValue(
                'useDeliveryAddressAsBillingAddress',
                cart.useDeliveryAddressAsBillingAddress
            );
        if (typeof cart.invoiceType !== 'undefined')
            setValue('invoiceType', cart.invoiceType);
        if (typeof cart.companyName !== 'undefined')
            setValue('companyName', cart.companyName);
        if (typeof cart.taxIdentificationNumber !== 'undefined')
            setValue('taxIdentificationNumber', cart.taxIdentificationNumber);
        if (typeof cart.taxOffice !== 'undefined')
            setValue('taxOffice', cart.taxOffice);

        if (
            typeof cart.deliveryAddress === 'object' &&
            Object.keys(cart.deliveryAddress).length > 0
        ) {
            if (typeof cart.deliveryAddress.street !== 'undefined')
                setValue('deliveryAddress-street', cart.deliveryAddress.street);
            if (typeof cart.deliveryAddress.street2 !== 'undefined')
                setValue(
                    'deliveryAddress-street2',
                    cart.deliveryAddress.street2
                );
            if (typeof cart.deliveryAddress.city !== 'undefined')
                setValue('deliveryAddress-city', cart.deliveryAddress.city);
            if (typeof cart.deliveryAddress.district !== 'undefined')
                setValue(
                    'deliveryAddress-district',
                    cart.deliveryAddress.district
                );
            if (typeof cart.deliveryAddress.subDistrict !== 'undefined')
                setValue(
                    'deliveryAddress-subDistrict',
                    cart.deliveryAddress.subDistrict
                );
            if (typeof cart.deliveryAddress.state !== 'undefined')
                setValue('deliveryAddress-state', cart.deliveryAddress.state);
            if (typeof cart.deliveryAddress.postalCode !== 'undefined')
                setValue(
                    'deliveryAddress-postalCode',
                    cart.deliveryAddress.postalCode
                );
            if (typeof cart.deliveryAddress.countryId !== 'undefined')
                setValue(
                    'deliveryAddress-countryId',
                    cart.deliveryAddress.countryId
                );
        }
        if (
            typeof cart.billingAddress === 'object' &&
            Object.keys(cart.billingAddress).length > 0
        ) {
            if (typeof cart.billingAddress.street !== 'undefined')
                setValue('billingAddress-street', cart.billingAddress.street);
            if (typeof cart.billingAddress.street2 !== 'undefined')
                setValue('billingAddress-street2', cart.billingAddress.street2);
            if (typeof cart.billingAddress.city !== 'undefined')
                setValue('billingAddress-city', cart.billingAddress.city);
            if (typeof cart.billingAddress.district !== 'undefined')
                setValue(
                    'billingAddress-district',
                    cart.billingAddress.district
                );
            if (typeof cart.billingAddress.subDistrict !== 'undefined')
                setValue(
                    'billingAddress-subDistrict',
                    cart.billingAddress.subDistrict
                );
            if (typeof cart.billingAddress.state !== 'undefined')
                setValue('billingAddress-state', cart.billingAddress.state);
            if (typeof cart.billingAddress.postalCode !== 'undefined')
                setValue(
                    'billingAddress-postalCode',
                    cart.billingAddress.postalCode
                );
            if (typeof cart.billingAddress.countryId !== 'undefined')
                setValue(
                    'billingAddress-countryId',
                    cart.billingAddress.countryId
                );
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        const phoneNumber = phone.number?.replace(/[^\d]/g, '');
        if (
            phoneNumber?.length === 10 &&
            phoneNumber?.[0] === '5' &&
            phone?.countryCode === 'TR'
        ) {
            clearErrors('phone');
        }
    }, [clearErrors, phone]);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            const phoneNumber = getValues('phone.number')?.replace(
                /[^\d]/g,
                ''
            );
            if (phoneNumber?.length !== 10) {
                setError('phone', {type: 'required'});
                return;
            }
            if (phoneNumber?.[0] !== '5' && data?.phone?.countryCode === 'TR') {
                setError('phone', {
                    type: 'required',
                    message: t(
                        'Turkey mobile numbers begin with +90 followed by a 5 in the second group. (e.g. +905XXXXXXXXX)'
                    )
                });
                return;
            }

            clearErrors('phone');

            inProgress.current = true;
            setIsLoading(true);

            try {
                await saveGuestInformation(data);
            } catch (error: any) {
                if (error.code === 'already_exists') {
                    setErrorMessage(
                        t(
                            'The email address you entered is in use by another customer. If this email address belongs to you, please sign in first. Or try again with a different email address.'
                        )
                    );
                } else {
                    setErrorMessage(error.message);
                }

                const container = document.querySelector('.content-wrapper');
                if (container !== null) {
                    container.scrollTo({top: 0, behavior: 'smooth'});
                }
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return (
        <FormProvider {...methods}>
            <UiForm onSubmit={methods.handleSubmit(onSubmit)}>
                {!!errorMessage && (
                    <UiAlert className="mb-4 xl:mb-8" color="danger">
                        {t(errorMessage)}
                    </UiAlert>
                )}

                <h2 className="mb-0 flex flex-col text-lg font-medium xl:mb-6 xl:flex-row xl:items-center xl:justify-between">
                    <div className="flex items-center">
                        <div className="mr-3 flex h-7 w-7 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                            1
                        </div>
                        {t('Personal Information')}
                    </div>

                    <p className="mb-2 mt-4 text-sm xl:mb-0 xl:mt-0">
                        {t('Already have an account?')}
                        <UiLink
                            className="ml-2 text-primary-600 hover:underline"
                            href="/auth?redirect=/checkout"
                        >
                            {t('Sign In')}
                        </UiLink>
                    </p>
                </h2>
                <div className="space-y-2 xl:space-y-4">
                    <div className="flex space-x-4">
                        <UiForm.Field
                            label={t('First name')}
                            error={
                                errors.firstName &&
                                errors.firstName.type === 'required'
                                    ? t('First name is required')
                                    : undefined
                            }
                            {...register('firstName', {required: true})}
                        />

                        <UiForm.Field
                            label={t('Last name')}
                            error={
                                errors.lastName &&
                                errors.lastName.type === 'required'
                                    ? t('Last name is required')
                                    : undefined
                            }
                            {...register('lastName', {required: true})}
                        />
                    </div>

                    <UiForm.Field
                        label={t('Email address')}
                        autoCorrect="off"
                        autoCapitalize="none"
                        error={
                            errors.email && errors.email.type === 'required'
                                ? t('Email address is required')
                                : errors.email &&
                                  errors.email.type === 'pattern'
                                ? t('Email address is invalid')
                                : undefined
                        }
                        {...register('email', {
                            required: true,
                            pattern: regexp.email
                        })}
                    />

                    <UiForm.Field
                        label={t('Phone')}
                        phone
                        countries={countries}
                        error={
                            errors.phone && errors.phone.type === 'required'
                                ? errors.phone.message?.toString() ||
                                  t('Phone is required')
                                : undefined
                        }
                        {...register('phone', {required: true})}
                    />

                    <UiForm.Control>
                        <UiCheckbox
                            className="mt-1 self-start"
                            {...register('isSubscribedToNewsletter', {
                                required: false
                            })}
                        >
                            {t(
                                'I accept the processing of my personal data and the sending of electronic messages so that I can be informed about the campaigns.'
                            )}
                        </UiCheckbox>
                    </UiForm.Control>
                </div>

                <UiDivider
                    orientation="horizontal"
                    className="my-6 block border-gray-200 xl:hidden"
                />

                <h2 className="mb-4 flex items-center text-lg font-medium xl:mb-6 xl:mt-12">
                    <div className="mr-3 flex h-7 w-7 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                        2
                    </div>
                    {t('Delivery Address')}
                </h2>
                <div className="space-y-2 xl:space-y-4">
                    <AddressForm
                        parentField="deliveryAddress"
                        countries={countries}
                        initialStates={initialStates}
                        initialCities={initialCities}
                        initialCountryId={cart.deliveryAddress?.countryId}
                    />
                    <UiForm.Control>
                        <UiCheckbox
                            className="mt-1 self-start"
                            {...register('useDeliveryAddressAsBillingAddress', {
                                required: false,
                                value: true
                            })}
                        >
                            {t('Also use this address as billing address.')}
                            <div className="mt-1.5 text-xs text-muted">
                                {t(
                                    'The address selected for the invoice is for information purposes only. Your products are delivered to the address entered in the "Delivery Address" section.'
                                )}
                            </div>
                        </UiCheckbox>
                    </UiForm.Control>
                </div>

                <UiDivider
                    orientation="horizontal"
                    className="my-6 block border-gray-200 xl:hidden"
                />

                <h2 className="mb-4 flex items-center text-lg font-medium xl:mb-6 xl:mt-12">
                    <div className="mr-3 flex h-7 w-7 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                        3
                    </div>
                    {t('Invoice Information')}
                </h2>
                <div className="space-y-2 xl:space-y-4">
                    <UiForm.Field
                        label={t('Invoice type')}
                        error={
                            errors.invoiceType &&
                            errors.invoiceType.type === 'required'
                                ? t('{label} is required', {
                                      label: t('Invoice type')
                                  })
                                : undefined
                        }
                        {...register('invoiceType', {
                            required: true
                        })}
                        defaultValue="individual"
                        selection
                    >
                        <option value="individual">{t('Individual')}</option>
                        <option value="corporate">{t('Corporate')}</option>
                    </UiForm.Field>
                    {invoiceType === 'corporate' && (
                        <>
                            <UiForm.Field
                                label={t('Company name')}
                                error={
                                    errors.invoiceType &&
                                    errors.invoiceType.type === 'required'
                                        ? t('{label} is required', {
                                              label: t('Company name')
                                          })
                                        : undefined
                                }
                                {...register('companyName', {required: true})}
                            />
                            <UiForm.Field
                                label={t('Tax identification number')}
                                error={
                                    errors.taxIdentificationNumber &&
                                    errors.taxIdentificationNumber.type ===
                                        'required'
                                        ? t('{label} is required', {
                                              label: t(
                                                  'Tax identification number'
                                              )
                                          })
                                        : undefined
                                }
                                {...register('taxIdentificationNumber', {
                                    required: true
                                })}
                            />
                            <UiForm.Field
                                label={t('Tax office')}
                                error={
                                    errors.taxOffice &&
                                    errors.taxOffice.type === 'required'
                                        ? t('{label} is required', {
                                              label: t('Tax office')
                                          })
                                        : undefined
                                }
                                {...register('taxOffice', {required: true})}
                            />
                        </>
                    )}
                    {!useDeliveryAddressAsBillingAddress && (
                        <AddressForm
                            parentField="billingAddress"
                            countries={countries}
                            initialStates={initialStates}
                            initialCities={initialCities}
                        />
                    )}
                </div>

                <UiDivider
                    orientation="horizontal"
                    className="mb-4 mt-6 block border-gray-200 xl:hidden"
                />

                <div className="flex flex-col xl:mt-12 xl:flex-row xl:items-center xl:justify-between">
                    <UiLink className="hidden xl:block" href="/">
                        <span aria-hidden="true">&larr; </span>
                        {t('Continue Shopping')}
                    </UiLink>

                    <UiButton
                        type="submit"
                        variant="solid"
                        color="primary"
                        size="xl"
                        leftIcon={<ArrowRightIcon className="mr-2 h-4 w-4" />}
                        loading={isLoading}
                    >
                        {t('Continue to Delivery')}
                    </UiButton>

                    <UiButton
                        className="mt-2 block w-full xl:hidden"
                        variant="light"
                        color="primary"
                        size="md"
                        onClick={() => router.push('/')}
                    >
                        <span aria-hidden="true">&larr; </span>
                        {t('Continue Shopping')}
                    </UiButton>
                </div>
            </UiForm>
        </FormProvider>
    );
});

if (isDev) {
    GuestInformation.displayName = 'GuestInformation';
}

export default GuestInformation;
