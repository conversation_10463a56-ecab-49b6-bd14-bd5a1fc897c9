import {FC, memo, useEffect, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiImage} from '@core/components/ui';

type CardBoxProps = {
    cardBrandLogo?: string;
    bankLogo?: string;
    cardSchemaLogo?: string;
    cardNumber: string;
    cardHolder: string;
    cardExpiry: string;
    cardCvv: string;
    focussedField?: string;
};

const CardBox: FC<CardBoxProps> = memo(props => {
    const {
        cardBrandLogo,
        bankLogo,
        cardSchemaLogo,
        cardNumber,
        cardHolder,
        cardExpiry,
        cardCvv,
        focussedField
    } = props;

    const [focusCardNumber, setFocusCardNumber] = useState(false);

    useEffect(() => {
        const inputElement = document.querySelector(
            'input[name=cardNumber]'
        ) as HTMLInputElement;

        if (inputElement !== null && focusCardNumber) {
            inputElement.focus();
        }

        setTimeout(() => {
            setFocusCardNumber(false);
        }, 25);
    }, [focusCardNumber]);

    return (
        <div className="mt-0 flex justify-center">
            <div className="h-[170px] min-w-[300px] xl:min-w-[360px]">
                <div
                    className="relative h-full w-full bg-transparent"
                    style={{perspective: '1000px'}}
                >
                    <div
                        onClick={() => setFocusCardNumber(true)}
                        className="shadow-card relative h-full w-full rounded-lg"
                        style={{
                            transition: 'transform 0.8s',
                            transformStyle: 'preserve-3d',
                            ...(focussedField === 'cardCvv'
                                ? {
                                      transform: 'rotateY(180deg)'
                                  }
                                : {})
                        }}
                    >
                        <div
                            className="absolute left-0 top-0 flex h-full w-full flex-col justify-between p-4"
                            style={{
                                backfaceVisibility: 'hidden'
                            }}
                        >
                            <div className="flex items-start justify-between object-contain">
                                {cardBrandLogo ? (
                                    <UiImage
                                        className="h-12 w-auto rounded-lg"
                                        src={cardBrandLogo}
                                        alt=""
                                        // @ts-ignore
                                        raw
                                        width={300}
                                        height={150}
                                    />
                                ) : (
                                    <div className="h-10 w-20 rounded-lg bg-gray-200"></div>
                                )}

                                {bankLogo ? (
                                    <UiImage
                                        className="h-10 w-auto rounded-lg object-contain"
                                        src={bankLogo}
                                        alt=""
                                        // @ts-ignore
                                        raw
                                        width={150}
                                        height={150}
                                    />
                                ) : (
                                    <div className="h-10 w-10 rounded-lg bg-gray-200"></div>
                                )}
                            </div>

                            <div className="flex flex-col items-start">
                                <div
                                    className={cls(
                                        '-ml-1.5 mb-1 rounded-md border border-transparent px-1.5 font-mono font-semibold',
                                        {
                                            'border border-primary-600':
                                                focussedField === 'cardNumber'
                                        }
                                    )}
                                >
                                    {cardNumber.length > 0
                                        ? cardNumber
                                        : '---- ---- ---- ----'}
                                </div>

                                <div className="flex items-end justify-between self-stretch">
                                    <div className="flex flex-col items-start">
                                        <div
                                            className={cls(
                                                '-ml-1.5 mb-1 rounded-md border border-transparent px-1.5 text-sm',
                                                {
                                                    'border border-primary-600':
                                                        focussedField ===
                                                        'cardHolder'
                                                }
                                            )}
                                        >
                                            {cardHolder.length > 0
                                                ? cardHolder
                                                : '------ -----'}
                                        </div>
                                        <div
                                            className={cls(
                                                '-ml-1.5 rounded-md border border-transparent px-1.5 font-mono text-sm',
                                                {
                                                    'border border-primary-600':
                                                        focussedField ===
                                                        'cardExpiry'
                                                }
                                            )}
                                        >
                                            {cardExpiry.length > 0
                                                ? cardExpiry
                                                : '-- / --'}
                                        </div>
                                    </div>

                                    <div>
                                        {cardSchemaLogo ? (
                                            <UiImage
                                                className="h-8 w-auto rounded-lg object-contain"
                                                src={cardSchemaLogo}
                                                alt=""
                                                // @ts-ignore
                                                raw
                                                width={300}
                                                height={150}
                                            />
                                        ) : (
                                            <div className="h-8 w-8 rounded-lg bg-gray-200"></div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div
                            className="absolute left-0 top-0 h-full w-full rounded-lg border-2 bg-white"
                            style={{
                                backfaceVisibility: 'hidden',
                                transform: 'rotateY(180deg)'
                            }}
                        >
                            <div className="mt-9 h-12 w-full bg-gray-200"></div>

                            <div className="mt-2 flex items-center justify-end p-5 text-xl font-semibold">
                                <div className="pr-3 text-muted">XXXXXXXX</div>
                                <div className="-ml-1.5 w-[51px] rounded-md border border-primary-600 px-1.5 text-center font-mono">
                                    {cardCvv.length > 0 ? cardCvv : '---'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    CardBox.displayName = 'CardBox';
}

export default CardBox;
