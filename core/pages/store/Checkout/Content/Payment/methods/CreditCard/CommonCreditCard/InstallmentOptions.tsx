import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {CheckCircleIcon} from '@core/icons/solid';
import useCheckout from '@core/pages/store/Checkout/useCheckout';
import Price from '@components/common/Price';

type InstallmentOptionsProps = {
    installmentOptions: {
        installmentCount: number;
        installmentAmount: number;
        amount: number;
    }[];
    installmentCount: number;
};

function format(
    price: number,
    options: {
        locale: string;
        decimals: number;
        name: string;
        symbol: string;
        template: string;
    }
) {
    const {locale, decimals, name, symbol, template} = options;
    const formatOptions = {
        style: 'decimal',
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    } as const;
    let formatted = Intl.NumberFormat(locale, formatOptions).format(price);

    return template
        .replace('{{price}}', formatted)
        .replace('{{name}}', name)
        .replace('{{symbol}}', symbol);
}

const InstallmentOptions: FC<InstallmentOptionsProps> = memo(
    ({installmentOptions, installmentCount}) => {
        const t = useTrans();
        const {locale, currency} = useStore();
        const {updateInstallmentCount} = useCheckout();

        return (
            <div className="mt-5 border-t border-gray-200 pt-4">
                <div className="-mb-1 -mt-2 ml-4 divide-y divide-gray-200">
                    {installmentOptions.map(option => (
                        <div key={option.installmentCount} className="relative">
                            <div
                                className="flex cursor-pointer select-none items-center py-3"
                                onClick={() =>
                                    updateInstallmentCount(
                                        option.installmentCount
                                    )
                                }
                            >
                                {option.installmentCount ===
                                installmentCount ? (
                                    <CheckCircleIcon
                                        className="mr-4 h-6 w-6 text-primary-600"
                                        aria-hidden="true"
                                    />
                                ) : (
                                    <div className="mr-4 h-6 w-6 rounded-full border border-gray-300"></div>
                                )}

                                <div className="flex flex-1 items-center">
                                    {option.installmentCount === 1 ? (
                                        <div className="flex flex-1 flex-col">
                                            <span className="block text-sm font-medium">
                                                {t('Single installment')}
                                            </span>

                                            <span className="mt-1 flex items-center text-sm text-gray-500">
                                                {t('Charge full amount.')}
                                            </span>
                                        </div>
                                    ) : (
                                        <div className="flex flex-1 flex-col">
                                            <span className="block text-sm font-medium">
                                                {t(
                                                    '{installmentCount} installments',
                                                    {
                                                        installmentCount:
                                                            option.installmentCount
                                                    }
                                                )}
                                            </span>

                                            <span className="mt-1 flex items-center text-sm text-gray-500">
                                                {t(
                                                    '{installmentCount} months, {installmentAmount} per month.',
                                                    {
                                                        installmentCount:
                                                            option.installmentCount,
                                                        installmentAmount:
                                                            format(
                                                                option.installmentAmount,
                                                                {
                                                                    locale,
                                                                    decimals: 2,
                                                                    name: currency.name,
                                                                    symbol: currency.symbol,
                                                                    template:
                                                                        currency.template
                                                                }
                                                            )
                                                    }
                                                )}
                                            </span>
                                        </div>
                                    )}
                                </div>

                                <div className="flex items-center">
                                    <Price
                                        className="ml-4 text-sm font-semibold xl:text-base"
                                        price={option.amount}
                                    />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }
);

if (isDev) {
    InstallmentOptions.displayName = 'InstallmentOptions';
}

export default InstallmentOptions;
