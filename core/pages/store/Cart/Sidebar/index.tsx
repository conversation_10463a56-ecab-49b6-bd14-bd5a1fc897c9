import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {UiStickyBox} from '@core/components/ui';
import Price from '@components/common/Price';
import Actions from './Actions';

const SideBar: FC = memo(() => {
    const t = useTrans();
    const {cart} = useCart();

    return (
        <div className="relative w-80">
            <UiStickyBox offsetTop={16}>
                <div className="rounded border border-gray-200 bg-gray-100 px-6 py-4 shadow-sm">
                    <h2 id="summary-heading" className="text-lg font-medium">
                        {t('Order Summary')}
                    </h2>

                    <div className="mt-6">
                        <div className="flex items-center justify-between pb-3 text-sm">
                            <div>{t('Products total')}</div>
                            <Price
                                className="font-medium"
                                price={cart.subTotal}
                            />
                        </div>
                        <div className="flex items-center justify-between border-t border-gray-100 py-3 text-sm">
                            <div>{t('Tax estimate')}</div>
                            <Price
                                className="font-medium"
                                price={cart.taxTotal}
                            />
                        </div>
                        <div className="flex items-center justify-between border-t border-gray-100 py-3 text-sm">
                            <div>{t('Delivery estimate')}</div>
                            <Price
                                className="font-medium"
                                price={cart.deliveryTotal}
                            />
                        </div>
                        {Array.isArray(cart.discounts) &&
                            cart.discounts.length > 0 &&
                            cart.discounts.map(discount => (
                                <div
                                    key={discount.id}
                                    className="flex items-center justify-between border-t border-gray-100 py-2 text-xs"
                                >
                                    <div>{discount.description}</div>
                                    <Price
                                        className="font-medium text-primary-600"
                                        price={-discount.amount}
                                    />
                                </div>
                            ))}
                        <div className="flex items-center justify-between border-t border-gray-200 pt-4 text-base">
                            {typeof cart.discountTotalIncludingProductDiscounts ===
                                'number' &&
                                cart.discountTotalIncludingProductDiscounts >
                                    0 && (
                                    <div className="flex flex-col items-center justify-center rounded border border-primary-600 px-1.5 py-1 text-xs font-semibold leading-4 text-primary-600">
                                        <p className="text-default">
                                            {t('Total Discount')}
                                        </p>
                                        <Price
                                            price={
                                                cart.discountTotalIncludingProductDiscounts
                                            }
                                        />
                                    </div>
                                )}
                            <Price
                                className="ml-auto font-semibold"
                                price={cart.grandTotal}
                            />
                        </div>
                    </div>
                </div>

                <Actions />
            </UiStickyBox>
        </div>
    );
});

if (isDev) {
    SideBar.displayName = 'SideBar';
}

export default SideBar;
