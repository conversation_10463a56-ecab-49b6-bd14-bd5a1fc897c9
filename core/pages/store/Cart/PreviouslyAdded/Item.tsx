import Price from '@components/common/Price';
import {UiImage, UiLink} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {CheckCircleIcon, TrashIcon, XCircleIcon} from '@core/icons/outline';
import {CartItem, ProductListItem} from '@core/types';
import React, {FC, useEffect} from 'react';
import CartAddToCartAction from '@core/components/common/ProductCard/CartAddToCartAction';

type ItemProps = {
    item: CartItem;
    setPreviouslyAddedItems: (previouslyAddedItems: CartItem[]) => void;
};

const Item: FC<ItemProps> = props => {
    const {item, setPreviouslyAddedItems} = props;

    const addToCartItem: ProductListItem = {
        productId: item.productId,
        availableQuantity: item.productStockQuantity
            ? item.productStockQuantity
            : item.quantity,
        code: item.productCode || '',
        name: item.productName,
        categoryName: item.productCategory || '',
        brandName: item.brandName,
        brandSlug: '',
        slug: item.productSlug,
        shortDescription: '',
        rating: item.productRating,
        reviewCount: item.productReviewCount,
        images: Array.isArray(item.productImage)
            ? item.productImage
            : item.productImage
            ? [item.productImage]
            : [],
        salesPrice: item.price || 0,
        discountedPrice: item.discountedPrice || 0,
        unDiscountedSalesPrice: item.discount || 0,
        discount: item.discount || 0,
        hasDiscount: item.discount ? true : false,
        quantity: item.quantity || 1,
        link: item.productLink,
        deliveryOptionIds: [],
        weight: 0,
        width: 0,
        height: 0,
        depth: 0,
        deliveryAtSpecifiedDate: false,
        deliveryAtSpecifiedTime: false,
        unitName: '',
        unitId: '',
        isKitProduct: false
    };

    const t = useTrans();
    const {cart} = useCart();

    const handleRemoveItem = (items: CartItem) => {
        const previouslyItems = localStorage.getItem('previouslyItems');

        if (previouslyItems) {
            const newItems = JSON.parse(previouslyItems).filter(
                (item: any) => item.productId !== items.productId
            );

            localStorage.setItem('previouslyItems', JSON.stringify(newItems));
            setPreviouslyAddedItems(newItems);
        }
    };

    useEffect(() => {
        const previouslyItems = localStorage.getItem('previouslyItems');

        if (previouslyItems) {
            setPreviouslyAddedItems(JSON.parse(previouslyItems));
        }
    }, [cart, setPreviouslyAddedItems]);

    const cartCheck = cart.items.find(
        cartItem => cartItem.productId === item.productId
    );

    const cartCheckQuantity = cartCheck?.quantity ?? 0;

    return (
        <div className="group relative flex flex-col items-stretch rounded border p-4 shadow-sm transition duration-200  hover:shadow ">
            <div className="grid h-full w-full grid-cols-12 justify-between gap-0">
                <div className={cls('relative col-span-3 flex ')}>
                    <UiImage
                        className="h-full max-h-28 !w-20 rounded  border lg:!w-28"
                        src={
                            item.productImage
                                ? `${item.productImage}?w=360&q=75`
                                : '/no-image.png'
                        }
                        alt={item.productName}
                        fill
                        fit="cover"
                        position="center"
                    />
                </div>

                <div className="col-span-9 grid  gap-4">
                    <div className="ml-2 flex  justify-between gap-2  ">
                        <UiLink
                            className="block  text-sm"
                            href={`/${item.productSlug}`}
                        >
                            <h2 className="text-xs font-semibold lg:text-sm">
                                {item.productName}
                            </h2>
                            <div className=" mt-4 text-xs">
                                {item.productStockQuantity - cartCheckQuantity >
                                5 ? (
                                    <div className="flex items-center space-x-2 text-gray-600">
                                        <CheckCircleIcon className="h-3 w-3 text-green-600" />
                                        <div>{t('In Stock')}</div>
                                    </div>
                                ) : item.productStockQuantity -
                                      cartCheckQuantity >
                                  0 ? (
                                    <div className="flex items-center space-x-2 text-gray-600">
                                        <CheckCircleIcon className="h-3 w-3 text-green-600" />
                                        <div>
                                            {item.productStockQuantity -
                                                cartCheckQuantity >
                                            1
                                                ? t('Last {count} products', {
                                                      count: !cartCheck
                                                          ? item.productStockQuantity
                                                          : item.productStockQuantity -
                                                            cartCheckQuantity
                                                  })
                                                : t('Last {count} product', {
                                                      count: !cartCheck
                                                          ? item.productStockQuantity
                                                          : item.productStockQuantity -
                                                            cartCheckQuantity
                                                  })}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex items-center space-x-2 text-gray-600">
                                        <XCircleIcon className="h-3 w-3 text-red-600" />
                                        <div>{t('Out Of Stock')}</div>
                                    </div>
                                )}
                            </div>
                        </UiLink>
                        <div
                            className="flex h-6 w-6 cursor-pointer select-none items-center text-muted hover:text-danger-600"
                            onClick={() => handleRemoveItem(item)}
                        >
                            <TrashIcon className="h-5 w-5" />
                        </div>
                    </div>
                    <div className="ml-2 flex items-end justify-between  gap-2 ">
                        <Price
                            className="block text-sm font-normal text-brand-black  lg:text-2xl [&>span]:!text-primary-600 md:[&>span]:text-base "
                            price={item.price}
                            dontWrapDiscountedPrice={true}
                            discountedPrice={
                                typeof item.discountedPrice === 'number'
                                    ? item.discountedPrice
                                    : undefined
                            }
                            decimal={0}
                        />
                        <div className=" z-1 ">
                            <CartAddToCartAction
                                disabled={
                                    item.productStockQuantity < 1 ||
                                    item.productStockQuantity <=
                                        cartCheckQuantity
                                }
                                product={addToCartItem}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Item;
