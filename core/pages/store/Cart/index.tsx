import {FC, memo, useEffect, useState} from 'react';
import {useSession} from 'next-auth/react';
import {CartItem, Cart as CartType} from '@core/types';
import {cls, isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useCart, useStore, useTrans} from '@core/hooks';
import {TrashIcon} from '@core/icons/outline';
import Breadcrumbs from '@components/common/Breadcrumbs';
import Seo from '@components/common/Seo';
import Items from './Items';
import SideBar from './Sidebar';
import PreviouslyAdded from './PreviouslyAdded';
import {UiTab} from '@core/components/ui';
import MyFavorites from './MyFavorites';
import LastViewed from './LastViewed';
import CampaignProducts from './CampaignProducts';

type CartProps = {
    cart: CartType;
};

const Cart: FC<CartProps> = memo(props => {
    const {cart: initialCart} = props;
    const {data: session} = useSession();
    const t = useTrans();
    const {productCount, isLoading, setCart, removeItems} = useCart();
    const {currency} = useStore();
    const [previouslyAddedItems, setPreviouslyAddedItems] = useState<
        CartItem[]
    >([]);
    const [lastViewedItems, setLastViewedItems] = useState<CartItem[]>([]);

    const [activeTab, setActiveTab] = useState('previouslyAdded');

    // Set Initial cart.
    useEffect(() => {
        if (typeof initialCart !== 'undefined') {
            setCart(initialCart);
            // ---------- Google Tag Manager ----------
            pushIntoGTMDataLayer({
                event: 'view_cart',
                data: {
                    currency: currency.name === 'TL' ? 'TRY' : currency.name,
                    value: initialCart.items
                        .map((item: CartItem) => item.price * item.quantity)
                        .reduce((a, b) => a + b, 0),
                    items: initialCart.items.map(item => ({
                        item_id: item.productCode,
                        item_name: item.productName,
                        discount: item.discountedPrice
                            ? item.price - item.discountedPrice
                            : 0,
                        price: item.price,
                        item_brand: item.brandName,
                        item_category: item.productCategory,
                        quantity: item.quantity
                    }))
                }
            });
            // ----------------------------------------
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <Seo title={t('My Cart')} />

            <div className="container">
                <Breadcrumbs
                    breadcrumbs={[
                        {name: t('Home'), slug: '/', href: '/'},
                        {name: t('My Cart'), slug: '/cart'}
                    ]}
                />

                <div className="relative">
                    <div className="flex items-stretch space-x-12 py-12">
                        <div className="grid flex-1 gap-8">
                            <div className="flex-1 rounded border border-gray-200 p-8 shadow-sm">
                                {productCount > 0 && (
                                    <div className="mb-8 flex items-center justify-between gap-4">
                                        <h1 className="text-2xl font-medium">
                                            {t('My Cart')} (
                                            {productCount > 1
                                                ? t('{count} Products', {
                                                      count: productCount
                                                  })
                                                : t('{count} Product', {
                                                      count: 1
                                                  })}
                                            )
                                        </h1>
                                        <button
                                            onClick={removeItems}
                                            className="flex items-center gap-2 text-sm font-medium text-muted hover:text-danger-600"
                                        >
                                            <span>{t('Remove Products')}</span>
                                            <TrashIcon className="h-4 w-4" />
                                        </button>
                                    </div>
                                )}

                                <Items />
                            </div>

                            <div className="-mb-px flex justify-start">
                                <UiTab.Group>
                                    <div className="grid w-full gap-12">
                                        <div className=" w-full border-b border-gray-200 bg-white">
                                            <UiTab.List className="-mb-px flex justify-start space-x-8">
                                                {initialCart?.showCampaignProducts && (
                                                    <UiTab
                                                        onClick={() =>
                                                            setActiveTab(
                                                                'campaings'
                                                            )
                                                        }
                                                        className={cls(
                                                            activeTab ===
                                                                'campaings'
                                                                ? 'border-primary-600 text-primary-600 '
                                                                : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                            'cursor-pointer whitespace-nowrap border-b-2 py-5 text-sm font-medium uppercase transition'
                                                        )}
                                                    >
                                                        {t('Campaign Products')}
                                                    </UiTab>
                                                )}
                                                <UiTab
                                                    onClick={() =>
                                                        setActiveTab(
                                                            'previouslyAdded'
                                                        )
                                                    }
                                                    className={cls(
                                                        activeTab ===
                                                            'previouslyAdded'
                                                            ? 'border-primary-600 text-primary-600 '
                                                            : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                        'cursor-pointer whitespace-nowrap border-b-2 py-5 text-sm font-medium uppercase transition'
                                                    )}
                                                >
                                                    {t('Previously Added')}
                                                </UiTab>
                                                <UiTab
                                                    onClick={() =>
                                                        setActiveTab(
                                                            'lastViewed'
                                                        )
                                                    }
                                                    className={cls(
                                                        activeTab ===
                                                            'lastViewed'
                                                            ? 'border-primary-600 text-primary-600 '
                                                            : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                        'cursor-pointer whitespace-nowrap border-b-2 py-5 text-sm font-medium uppercase transition'
                                                    )}
                                                >
                                                    {t(
                                                        'Recently Viewed Products'
                                                    )}
                                                </UiTab>
                                                {session?.user && (
                                                    <UiTab
                                                        onClick={() =>
                                                            setActiveTab(
                                                                'myFavorites'
                                                            )
                                                        }
                                                        className={cls(
                                                            activeTab ===
                                                                'myFavorites'
                                                                ? 'border-primary-600 text-primary-600 '
                                                                : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                            'cursor-pointer whitespace-nowrap border-b-2 py-5 text-sm font-medium uppercase transition'
                                                        )}
                                                    >
                                                        {t('My Favorites')}
                                                    </UiTab>
                                                )}
                                            </UiTab.List>
                                        </div>
                                        <UiTab.Panels>
                                            {initialCart?.showCampaignProducts && (
                                                <UiTab.Panel>
                                                    <CampaignProducts />
                                                </UiTab.Panel>
                                            )}
                                            <UiTab.Panel>
                                                <PreviouslyAdded
                                                    previouslyAddedItems={
                                                        previouslyAddedItems
                                                    }
                                                    setPreviouslyAddedItems={
                                                        setPreviouslyAddedItems
                                                    }
                                                />
                                            </UiTab.Panel>
                                            <UiTab.Panel>
                                                <LastViewed
                                                    lastViewedItems={
                                                        lastViewedItems
                                                    }
                                                    setLastViewedItems={
                                                        setLastViewedItems
                                                    }
                                                />
                                            </UiTab.Panel>
                                            <UiTab.Panel>
                                                <MyFavorites />
                                            </UiTab.Panel>
                                        </UiTab.Panels>
                                    </div>
                                </UiTab.Group>
                            </div>
                        </div>

                        <SideBar />
                    </div>

                    {isLoading && (
                        <div className="absolute inset-0 flex h-full w-full items-center justify-center bg-white bg-opacity-30"></div>
                    )}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    Cart.displayName = 'Cart';
}

export default Cart;
