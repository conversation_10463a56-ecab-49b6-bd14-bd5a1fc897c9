import {FC, memo} from 'react';
import storeConfig from '~/store.config';
import {CartItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {
    BagIcon,
    CheckCircleIcon,
    TrashIcon,
    XCircleIcon
} from '@core/icons/outline';
import Price from '@components/common/Price';
import Quantity from '@components/common/Quantity';

type ItemProps = {
    item: CartItem;
};

const Item: FC<ItemProps> = memo(props => {
    const {item} = props;
    const t = useTrans();
    const {updateItem, removeItem} = useCart();

    const handleRemoveItem = (item: CartItem) => {
        const previouslyItems = localStorage.getItem('previouslyItems');
        let previouslyItemsArray: {
            productId: string;
            productName: string;
            price: number;
            quantity: number;
            productSlug: string;
            productImage: string;
            discountedPrice: number;
            link: string;
            code?: string;
            productStockQuantity?: number;
        }[] = previouslyItems ? JSON.parse(previouslyItems) : [];

        const itemExists = previouslyItemsArray.some(
            (previoulyItem: {productId: string}) =>
                previoulyItem.productId === item.productId
        );

        if (!itemExists) {
            previouslyItemsArray.unshift({
                productId: item.productId,
                productName: item.productName,
                price: item.price,
                discountedPrice: item.discountedPrice || 0,
                quantity: item.quantity,
                productSlug: item.productSlug,
                productImage: item.productImage,
                link: item.productLink,
                code: item.productCode,
                productStockQuantity: item.productStockQuantity
            });

            if (previouslyItemsArray.length > 10) {
                previouslyItemsArray.pop();
            }

            localStorage.setItem(
                'previouslyItems',
                JSON.stringify(previouslyItemsArray)
            );
        }

        removeItem(item);
    };

    return (
        <div className="relative flex items-stretch py-6 last:pb-0">
            <div
                className={cls('relative ml-6 h-64', {
                    'w-44':
                        storeConfig.catalog.productImageShape === 'rectangle',
                    'w-64': storeConfig.catalog.productImageShape === 'square'
                })}
            >
                <UiImage
                    className="h-full w-full rounded"
                    src={
                        item.productImage
                            ? `${item.productImage}?w=360&q=75`
                            : '/no-image.png'
                    }
                    alt={item.productName}
                    fill
                    fit="cover"
                    position="center"
                />
            </div>

            <div className="ml-6 flex flex-1 flex-col justify-between">
                <UiLink
                    className="block w-full text-sm"
                    href={item.productLink}
                >
                    <h2 className="font-semibold text-base">
                        {item.productName}
                    </h2>

                    {Array.isArray(item.productAttributes) &&
                        item.productAttributes.length > 0 && (
                            <div className="mt-1 flex items-center space-x-4">
                                {item.productAttributes.map(attribute => (
                                    <div
                                        className="flex items-center text-muted"
                                        key={attribute.value}
                                    >
                                        <div className="mr-0.5">
                                            {attribute.label}:
                                        </div>
                                        <div>{attribute.value}</div>
                                    </div>
                                ))}
                            </div>
                        )}

                    {item.isPCMProduct &&
                        !!item.pcmPayload &&
                        Object.keys(item.pcmPayload).length > 0 && (
                            <div className="mt-1.5 flex flex-col justify-center text-sm">
                                {item.pcmPayload.summary.items.map(
                                    (summaryItem: any) => (
                                        <div
                                            className="flex"
                                            key={summaryItem.code}
                                        >
                                            <div className="mr-2">
                                                {summaryItem.label}:
                                            </div>
                                            <div className="text-muted">
                                                {summaryItem.value}
                                            </div>
                                        </div>
                                    )
                                )}
                            </div>
                        )}

                    {item.isKitProduct &&
                        Array.isArray(item.subItems) &&
                        item.subItems.length > 0 && (
                            <ul className="mt-1.5 flex list-disc flex-col pl-3.5 text-[11px] font-medium leading-4">
                                {item.subItems.map(subItem => (
                                    <li key={subItem.productCode}>
                                        <div className="flex">
                                            <div className="mr-2">
                                                {subItem.productDefinition}:
                                            </div>
                                            <div className="text-muted">
                                                {subItem.quantity}
                                            </div>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        )}

                    <Price
                        className={cls('mt-4 block text-lg font-medium', {
                            '[&>span>span]:!ml-0 [&>span]:flex-col [&>span]:items-start [&>span]:text-primary-600':
                                typeof item.discountedPrice === 'number'
                        })}
                        price={item.price}
                        discountedPrice={
                            typeof item.discountedPrice === 'number'
                                ? item.discountedPrice
                                : undefined
                        }
                    />
                </UiLink>

                <div className="pt-5 text-sm">
                    {item.productStockQuantity > 5 ? (
                        <div className="flex items-center space-x-2 text-gray-600">
                            <CheckCircleIcon className="h-4 w-4 text-green-600" />
                            <div>{t('In Stock')}</div>
                        </div>
                    ) : item.productStockQuantity > 0 ? (
                        <div className="flex items-center space-x-2 text-gray-600">
                            <CheckCircleIcon className="h-4 w-4 text-green-600" />
                            <div>
                                {item.quantity > 1
                                    ? t('Last {count} products', {
                                          count: item.productStockQuantity
                                      })
                                    : t('Last {count} product', {
                                          count: item.productStockQuantity
                                      })}
                            </div>
                        </div>
                    ) : (
                        <div className="flex items-center space-x-2 text-gray-600">
                            <XCircleIcon className="h-4 w-4 text-red-600" />
                            <div>{t('Out Of Stock')}</div>
                        </div>
                    )}
                </div>
            </div>

            <div className="flex flex-col items-end justify-between">
                <Quantity
                    size="sm"
                    quantity={item.quantity}
                    availableQuantity={item.productStockQuantity}
                    onChange={quantity => updateItem({...item, quantity})}
                />

                <div
                    className="flex h-6 w-6 cursor-pointer select-none items-center text-muted hover:text-danger-600"
                    onClick={() => handleRemoveItem(item)}
                >
                    <TrashIcon className="h-5 w-5" />
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    Item.displayName = 'Item';
}

const Items: FC = memo(() => {
    const t = useTrans();
    const {cart} = useCart();

    return (
        <>
            {cart.items.length > 0 && (
                <section className="divide-y divide-gray-200 border-t border-gray-200">
                    {cart.items.map(item => (
                        <Item key={item.productId} item={item} />
                    ))}
                </section>
            )}

            {cart.items.length < 1 && (
                <div className="flex flex-1 flex-col items-center justify-center px-12 py-24">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                        <BagIcon className="h-7 w-7" />
                    </div>

                    <h2 className="pt-12 text-center text-2xl font-semibold">
                        {t('There are no items in your cart!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t(
                            'You can add products to your cart from the detail page of the products on our site.'
                        )}
                    </p>
                </div>
            )}
        </>
    );
});

if (isDev) {
    Items.displayName = 'Items';
}

export default Items;
