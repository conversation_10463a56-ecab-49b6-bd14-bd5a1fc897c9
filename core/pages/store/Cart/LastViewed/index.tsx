import {useCart, useTrans} from '@core/hooks';
import {BagIcon} from '@core/icons/outline';
import {CartItem} from '@core/types';
import React, {useEffect} from 'react';
import Item from './Item';

interface PropsPreviouslyAdded {
    lastViewedItems: CartItem[];
    setLastViewedItems: (lastViewedItems: CartItem[]) => void;
}

const LastViewed = (props: PropsPreviouslyAdded) => {
    const {lastViewedItems, setLastViewedItems} = props;
    const t = useTrans();
    const {cart} = useCart();

    useEffect(() => {
        const lastViewed = localStorage.getItem('lastViewed');

        if (lastViewed) {
            setLastViewedItems(JSON.parse(lastViewed));
        }
    }, [cart, setLastViewedItems]);

    return (
        <>
            {lastViewedItems.length > 0 && (
                <section className="grid grid-cols-1 gap-4 border-gray-200  xl:grid-cols-2">
                    {lastViewedItems?.map(item => (
                        <Item
                            key={item.productId}
                            item={item}
                            setLastViewedItems={setLastViewedItems}
                        />
                    ))}
                </section>
            )}

            {lastViewedItems?.length < 1 && (
                <div className="flex flex-1 flex-col items-center justify-center rounded border p-12 ">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                        <BagIcon className="h-7 w-7" />
                    </div>

                    <h2 className="pt-12 text-center text-2xl font-semibold">
                        {t('Your Last Viewed Product is Not Available!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t(
                            'You can view the products you have already reviewed here.'
                        )}
                    </p>
                </div>
            )}
        </>
    );
};

export default LastViewed;
