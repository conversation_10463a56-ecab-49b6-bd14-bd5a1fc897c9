import {
    FC,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import dynamic from 'next/dynamic';
import {cls, isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useIntersection, useStore, useTrans} from '@core/hooks';
import {Ui<PERSON><PERSON><PERSON>, UiSpinner} from '@core/components/ui';
import {
    ArrowRightArrowLeftIcon,
    BagIcon,
    BellIcon,
    BookmarkIcon,
    HeartIcon
} from '@core/icons/outline';
import {
    BellIcon as BellSolidIcon,
    BookmarkIcon as BookmarkSolidIcon,
    HeartIcon as HeartSolidIcon
} from '@core/icons/solid';
import Quantity from '@components/common/Quantity';
import useProduct from './useProduct';
import NotifyCustomer from './NotifyCustomer';
import {ProductListItem} from '@core/types';
import {Cookies} from 'react-cookie-consent';

const FixedProductDetails = dynamic(() => import('./FixedProductDetails'));

const Actions: FC = memo(() => {
    const t = useTrans();
    const {
        currency,
        addToFavorites,
        removeFromFavorites,
        updateProductCollections,
        addToCollection,
        removeFromCollection
    } = useStore();
    const {
        selectedProduct,
        setQuantity,
        isAddToCartInProgress,
        addToCart,
        availableQuantity,
        inStock,
        customerProductParams,
        comparisonList,
        setCustomerProductParams,
        addToComparisonList,
        openComparisonList
    } = useProduct();

    // Favorite.
    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);
    const onAddToFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await addToFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: true
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        addToFavorites,
        selectedProduct
    ]);
    const onRemoveFromFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await removeFromFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: false
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        removeFromFavorites,
        selectedProduct
    ]);

    // Collection.
    const [isCollectionUpdateInProgress, setIsCollectionUpdateInProgress] =
        useState(false);
    const onAddToCollection = useCallback(async () => {
        if (isCollectionUpdateInProgress) {
            return;
        }

        setIsCollectionUpdateInProgress(true);

        const result = await updateProductCollections({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        setCustomerProductParams({
            ...customerProductParams,
            isInCollection: result.length > 0,
            collectionIds: result
        });

        setIsCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isCollectionUpdateInProgress,
        updateProductCollections,
        selectedProduct
    ]);
    const [
        isAlarmCollectionUpdateInProgress,
        setIsAlarmCollectionUpdateInProgress
    ] = useState(false);
    const onAddToAlarmCollection = useCallback(async () => {
        if (isAlarmCollectionUpdateInProgress) {
            return;
        }

        setIsAlarmCollectionUpdateInProgress(true);

        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'add_to_wishlist',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: selectedProduct.salesPrice - selectedProduct.discount,
                items: [
                    {
                        item_id: selectedProduct.code,
                        item_name: selectedProduct.definition,
                        discount: selectedProduct.discount,
                        item_brand: selectedProduct.brandName,
                        item_category: selectedProduct.categoryName,
                        price: selectedProduct.salesPrice
                    }
                ]
            }
        });
        // ----------------------------------------

        const result = await addToCollection(
            {
                id: 'is-alarm',
                isAlarm: true
            },
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isInAlarmCollection: true
            });
        }
        setIsAlarmCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isAlarmCollectionUpdateInProgress,
        addToCollection,
        selectedProduct,
        currency.name
    ]);
    const onRemoveFromAlarmCollection = useCallback(async () => {
        if (isAlarmCollectionUpdateInProgress) {
            return;
        }

        setIsAlarmCollectionUpdateInProgress(true);

        const result = await removeFromCollection(
            {
                id: 'is-alarm',
                isAlarm: true
            },
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isInAlarmCollection: false
            });
        }
        setIsAlarmCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isAlarmCollectionUpdateInProgress,
        removeFromCollection,
        selectedProduct
    ]);

    const firstUpdate = useRef(true);
    const [showFixedDetails, setShowFixedDetails] = useState(false);
    const [ref, observer] = useIntersection();
    useEffect(() => {
        if (firstUpdate.current) {
            firstUpdate.current = false;
            return;
        }

        if (observer?.isIntersecting) {
            setShowFixedDetails(false);
        } else {
            setShowFixedDetails(true);
        }
    }, [observer?.isIntersecting]);

    const isInComparisonList = useMemo(
        () =>
            comparisonList.some(
                comparisonItem =>
                    comparisonItem.productId === selectedProduct.productId
            ),
        [comparisonList, selectedProduct.productId]
    );

    const handleRemoveItem = (items: ProductListItem | any) => {
        const previouslyItems = localStorage.getItem('previouslyItems');

        if (previouslyItems) {
            const newItems = (JSON.parse(previouslyItems) ?? []).filter(
                (item: any) => item.productId !== items.productId
            );

            localStorage.setItem('previouslyItems', JSON.stringify(newItems));
        }
    };

    const handleSubmit = () => {
        const previouslyItems = localStorage.getItem('previouslyItems');
        addToCart();
        if (previouslyItems && previouslyItems.length > 0) {
            handleRemoveItem(selectedProduct);
        }
    };

    return (
        <div ref={ref} className="border-b border-gray-200 pb-3">
            {showFixedDetails && <FixedProductDetails />}

            {inStock ? (
                <div className="flex select-none items-center xl:space-x-4">
                    <Quantity
                        className="hidden xl:flex"
                        quantity={selectedProduct.quantity}
                        availableQuantity={availableQuantity}
                        onChange={quantity => setQuantity(quantity)}
                    />

                    <UiButton
                        className="flex-1"
                        variant="solid"
                        color="primary"
                        size="xl"
                        leftIcon={<BagIcon className="mr-3 h-5 w-5" />}
                        loading={isAddToCartInProgress}
                        disabled={!inStock}
                        onClick={handleSubmit}
                    >
                        {t('ADD TO CART')}
                    </UiButton>
                </div>
            ) : (
                <NotifyCustomer />
            )}

            <div className="grid select-none grid-cols-2 gap-6 overflow-hidden pt-6 text-muted xl:gap-3">
                {customerProductParams.isFavorite ? (
                    !isFavoriteUpdateInProgress ? (
                        <button
                            className="flex items-center transition hover:text-primary-600"
                            onClick={onRemoveFromFavorites}
                        >
                            <HeartSolidIcon className="mr-2 h-4 w-4 text-primary-600" />
                            <div className="truncate text-xs font-medium uppercase">
                                {t('Remove From Favorites')}
                            </div>
                        </button>
                    ) : (
                        <button className="flex cursor-progress items-center text-gray-500">
                            <div className="mr-2 w-4">
                                <UiSpinner size="sm" />
                            </div>
                            <div className="truncate text-xs font-medium uppercase">
                                {t('Remove From Favorites')}
                            </div>
                        </button>
                    )
                ) : !isFavoriteUpdateInProgress ? (
                    <button
                        className="flex items-center transition hover:text-primary-600"
                        onClick={onAddToFavorites}
                    >
                        <HeartIcon className="mr-2 h-4 w-4" />
                        <div className="truncate text-xs font-medium uppercase">
                            {t('Add To Favorites')}
                        </div>
                    </button>
                ) : (
                    <button className="flex cursor-progress items-center text-gray-500">
                        <div className="mr-2 w-4">
                            <UiSpinner size="sm" />
                        </div>
                        <div className="truncate text-xs font-medium uppercase">
                            {t('Add To Favorites')}
                        </div>
                    </button>
                )}

                {!isCollectionUpdateInProgress ? (
                    <button
                        className="flex items-center transition hover:text-primary-600"
                        onClick={onAddToCollection}
                    >
                        {customerProductParams.isInCollection ? (
                            <BookmarkSolidIcon className="mr-2 h-4 w-4 text-primary-600" />
                        ) : (
                            <BookmarkIcon className="mr-2 h-4 w-4" />
                        )}
                        <div className="truncate text-xs font-medium uppercase">
                            {t('Add To Collection')}
                        </div>
                    </button>
                ) : (
                    <button className="flex cursor-progress items-center text-gray-500">
                        <div className="mr-2 w-4">
                            <UiSpinner size="sm" />
                        </div>
                        <div className="truncate text-xs font-medium uppercase">
                            {t('Add To Collection')}
                        </div>
                    </button>
                )}

                {customerProductParams.isInAlarmCollection ? (
                    !isAlarmCollectionUpdateInProgress ? (
                        <button
                            className="flex items-center transition hover:text-primary-600"
                            onClick={onRemoveFromAlarmCollection}
                        >
                            <BellSolidIcon className="mr-2 h-4 w-4 text-primary-600" />
                            <div className="truncate text-xs font-medium uppercase">
                                {t('Remove Alarm')}
                            </div>
                        </button>
                    ) : (
                        <button className="flex cursor-progress items-center text-gray-500">
                            <div className="mr-2 w-4">
                                <UiSpinner size="sm" />
                            </div>
                            <div className="truncate text-xs font-medium uppercase">
                                {t('Remove Alarm')}
                            </div>
                        </button>
                    )
                ) : !isAlarmCollectionUpdateInProgress ? (
                    <button
                        className="flex items-center transition hover:text-primary-600"
                        onClick={onAddToAlarmCollection}
                    >
                        <BellIcon className="mr-2 h-4 w-4" />
                        <div className="truncate text-xs font-medium uppercase">
                            {t('Discount Alarm')}
                        </div>
                    </button>
                ) : (
                    <button className="flex cursor-progress items-center text-gray-500">
                        <div className="mr-2 w-4">
                            <UiSpinner size="sm" />
                        </div>
                        <div className="truncate text-xs font-medium uppercase">
                            {t('Discount Alarm')}
                        </div>
                    </button>
                )}

                <button
                    className="flex items-center transition hover:text-primary-600"
                    onClick={() => {
                        addToComparisonList();
                        openComparisonList();
                    }}
                >
                    <ArrowRightArrowLeftIcon
                        className={cls('mr-2 h-4 w-4', {
                            'text-primary-600': isInComparisonList
                        })}
                    />
                    <div className="truncate text-xs font-medium uppercase">
                        {isInComparisonList
                            ? t('Added To Compare')
                            : t('Compare')}{' '}
                        {comparisonList.length > 0 && (
                            <span>({comparisonList.length})</span>
                        )}
                    </div>
                </button>
            </div>
        </div>
    );
});

if (isDev) {
    Actions.displayName = 'Actions';
}

export default Actions;
