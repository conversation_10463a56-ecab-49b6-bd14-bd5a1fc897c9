import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiButton} from '@core/components/ui';
import {BagIcon} from '@core/icons/outline';
import Price from '@components/common/Price';
import useProduct from '../useProduct';
import NotifyCustomer from '../NotifyCustomer';

const Summary: FC = memo(() => {
    const t = useTrans();
    const {selectedProduct, isAddToCartInProgress, addToCart, inStock} =
        useProduct();

    return (
        <div className="fixed bottom-0 left-0 z-10 flex h-mobile-tab-bar w-full items-center justify-between gap-4 border-t border-gray-200 bg-white px-4">
            {typeof selectedProduct.salesPriceAtCart === 'number' &&
            selectedProduct.salesPriceAtCart > 0 ? (
                <div className="flex flex-col items-center gap-1 overflow-hidden font-semibold">
                    <div className="flex w-full items-center justify-center gap-1.5 rounded bg-red-100 py-[1px]">
                        <svg
                            className="h-3 w-3 stroke-discount stroke-[1px] [&>path]:fill-discount"
                            viewBox="0 0 16 10"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path d="M10.278 9.469h4.607a.526.526 0 0 0 .218-.044.621.621 0 0 0 .37-.577v-5.83h-1.17v4.297L9.86 2.438a.55.55 0 0 0-.807-.024L5.97 5.504 1.47.681l-.822.884L5.53 6.809a.55.55 0 0 0 .81.02l3.108-3.085 4.074 4.479h-3.244v1.246Z" />
                        </svg>
                        <p className="text-center text-[10px] text-discount">
                            {t('Price on Cart')}
                        </p>
                    </div>
                    <Price
                        price={selectedProduct.salesPrice}
                        discountedPrice={selectedProduct.salesPriceAtCart}
                        dontWrapDiscountedPrice
                        className="text-xs [&>span]:text-discount"
                    />
                </div>
            ) : (
                <div className="flex items-center font-semibold">
                    <Price
                        price={
                            selectedProduct.hasDiscount
                                ? selectedProduct.unDiscountedSalesPrice
                                : selectedProduct.salesPrice
                        }
                        discountedPrice={
                            selectedProduct.hasDiscount
                                ? selectedProduct.salesPrice
                                : null
                        }
                        dontWrapDiscountedPrice
                    />
                </div>
            )}

            {inStock ? (
                <div className="flex items-center gap-2.5">
                    <UiButton
                        variant="solid"
                        color="primary"
                        size="md"
                        leftIcon={<BagIcon className="mr-2 h-3 w-3" />}
                        loading={isAddToCartInProgress}
                        disabled={!inStock}
                        onClick={addToCart}
                    >
                        {t('ADD TO CART')}
                    </UiButton>
                </div>
            ) : (
                <NotifyCustomer />
            )}
        </div>
    );
});

if (isDev) {
    Summary.displayName = 'Summary';
}

export default Summary;
