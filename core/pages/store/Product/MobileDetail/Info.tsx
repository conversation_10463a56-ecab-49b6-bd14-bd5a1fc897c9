import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiLink, UiRating} from '@core/components/ui';
import {CheckCircleIcon, XCircleIcon} from '@core/icons/outline';
import Price from '@components/common/Price';
import useProduct from '../useProduct';

type InfoProps = {
    onOpenReviews: () => void;
};

const Info: FC<InfoProps> = memo(({onOpenReviews}) => {
    const t = useTrans();
    const {availableQuantity, selectedProduct} = useProduct();
    const title = useMemo(() => {
        let title = '';

        if (!!selectedProduct.colorAttributeValue) {
            if (!!selectedProduct.brandName) {
                title = `${selectedProduct.brandName} ${selectedProduct.colorAttributeValue} ${selectedProduct.name} ${selectedProduct.code}`;
            } else {
                title = `${selectedProduct.colorAttributeValue} ${selectedProduct.name} ${selectedProduct.code}`;
            }
        } else {
            if (!!selectedProduct.brandName) {
                title = `${selectedProduct.brandName} ${selectedProduct.name} ${selectedProduct.code}`;
            } else {
                title = `${selectedProduct.name} ${selectedProduct.code}`;
            }
        }

        return title;
    }, [selectedProduct]);

    return (
        <div className="container mb-6 mt-6 border-b border-gray-200 pb-6">
            {/*{!!selectedProduct.brandName && selectedProduct.brandSlug && (*/}
            {/*    <UiLink*/}
            {/*        className="mb-2 inline-block text-sm text-muted underline"*/}
            {/*        href={`/${selectedProduct.brandSlug}`}*/}
            {/*    >*/}
            {/*        <h2>{selectedProduct.brandName}</h2>*/}
            {/*    </UiLink>*/}
            {/*)}*/}

            {/*<h1 className="mb-1 text-lg font-medium leading-6">*/}
            {/*    {selectedProduct.name}*/}
            {/*</h1>*/}

            {/*<div className="text-sm text-muted">{selectedProduct.code}</div>*/}
            <h1 className="mb-1 text-lg font-medium leading-6">{title}</h1>

            <div className="mt-2 text-sm">
                {availableQuantity > 5 ? (
                    <div className="flex items-center space-x-1 text-gray-600">
                        <CheckCircleIcon className="mr-1 h-4 w-4 text-green-600" />
                        <div>{t('In Stock')}</div>
                    </div>
                ) : availableQuantity > 0 ? (
                    <div className="flex items-center space-x-1 text-gray-600">
                        <CheckCircleIcon className="mr-1 h-4 w-4 text-green-600" />
                        <div>
                            {availableQuantity > 1
                                ? t('Last {count} products', {
                                      count: availableQuantity
                                  })
                                : t('Last {count} product', {
                                      count: availableQuantity
                                  })}
                        </div>
                    </div>
                ) : (
                    <div className="flex items-center space-x-1 text-gray-600">
                        <XCircleIcon className="mr-1 h-4 w-4 text-red-600" />
                        <div>{t('Out Of Stock')}</div>
                    </div>
                )}
            </div>

            <div className="mt-4 flex items-center space-x-3">
                <div className="flex items-center">
                    <UiRating
                        size="sm"
                        initialRating={selectedProduct.rating}
                        readonly
                    />
                </div>

                <button
                    className="flex items-center text-xs text-gray-500 underline transition duration-100"
                    onClick={onOpenReviews}
                >
                    {t('{reviewCount} reviews', {
                        reviewCount: selectedProduct.reviewCount
                    })}
                </button>
            </div>

            <div className="mt-4 flex items-center gap-3 text-xl font-medium">
                {selectedProduct.hasDiscount &&
                    selectedProduct.discount > 0 && (
                        <p className="rounded border-2 border-discount p-1 text-sm leading-4 text-discount">
                            %{Math.ceil(selectedProduct.discount)}
                        </p>
                    )}
                <Price
                    price={
                        selectedProduct.hasDiscount
                            ? selectedProduct.unDiscountedSalesPrice
                            : selectedProduct.salesPrice
                    }
                    discountedPrice={
                        selectedProduct.hasDiscount
                            ? selectedProduct.salesPrice
                            : null
                    }
                    dontWrapDiscountedPrice
                />
            </div>
        </div>
    );
});

if (isDev) {
    Info.displayName = 'Info';
}

export default Info;
