import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {CreditCardIcon} from '@core/icons/outline';
import {UiImage} from '@core/components/ui';
import Price from '@components/common/Price';
import {Installment} from '@core/types';

type InstallmentOptionsProps = {
    installments: Installment[];
};

const InstallmentOptions: FC<InstallmentOptionsProps> = memo(
    ({installments}) => {
        const t = useTrans();

        return (
            <div className="grid h-full w-full p-4">
                {installments.length > 0 ? (
                    <div className="grid gap-4 pb-4">
                        {installments.map(installment => (
                            <div
                                key={installment.cardBrandCode}
                                className="rounded border text-xs"
                            >
                                <div className="flex h-12 items-center justify-center bg-gray-100">
                                    {installment.cardBrandLogo.length > 0 ? (
                                        <UiImage
                                            src={installment.cardBrandLogo}
                                            alt=""
                                            width={96}
                                            height={24}
                                            className="mx-auto"
                                        />
                                    ) : (
                                        <p className="text-center font-semibold text-base">
                                            {installment.cardBrandName}
                                        </p>
                                    )}
                                </div>
                                <table className="w-full">
                                    <thead>
                                        <tr className="divide-x border-y align-top font-semibold [&>td]:py-2 [&>td]:text-center">
                                            <td>{t('Installment')}</td>
                                            <td>{t('Installment Amount')}</td>
                                            <td>{t('Total Amount')}</td>
                                        </tr>
                                    </thead>
                                    <tbody className="[&_tr:last-child]:border-0">
                                        {installment.installments.map(i => {
                                            return (
                                                <tr
                                                    key={i.id}
                                                    className="divide-x border-b [&>td]:py-2 [&>td]:text-center [&>td]:font-bold"
                                                >
                                                    <td>
                                                        <div className="flex items-center justify-center gap-2.5">
                                                            {i.installmentCount}
                                                            {i.plusInstallmentCount >
                                                                0 && (
                                                                <div className="rounded-md bg-gray-200 px-2 py-0.5">
                                                                    +
                                                                    {
                                                                        i.plusInstallmentCount
                                                                    }
                                                                </div>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        {i.installmentAmount >
                                                        0 ? (
                                                            <Price
                                                                price={
                                                                    i.installmentAmount
                                                                }
                                                            />
                                                        ) : (
                                                            '-'
                                                        )}
                                                    </td>
                                                    <td className="text-muted">
                                                        {i.total > 0 ? (
                                                            <Price
                                                                price={i.total}
                                                            />
                                                        ) : (
                                                            '-'
                                                        )}
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="flex h-full flex-col items-center justify-center">
                        <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                            <CreditCardIcon className="h-8 w-8" />
                        </div>

                        <h3 className="pt-8 text-center text-xl font-medium">
                            {t('No installment options available!')}
                        </h3>

                        <p className="px-10 pt-2 text-center text-muted">
                            {t(
                                'Unfortunately, there are no installment options available for this product at the moment.'
                            )}
                        </p>
                    </div>
                )}
            </div>
        );
    }
);

if (isDev) {
    InstallmentOptions.displayName = 'InstallmentOptions';
}

export default InstallmentOptions;
