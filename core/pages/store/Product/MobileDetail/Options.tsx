import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import ColorOptions from '../Options/ColorOptions';
import ImageOptions from '../Options/ImageOptions';
import OtherOptions from '../Options/OtherOptions';
import SizeOptions from '../Options/SizeOptions';
import useProduct from '../useProduct';

const Options: FC = memo(() => {
    const {productOptions} = useProduct();

    return (
        <div className="container mb-6 space-y-6 border-b border-gray-200 pb-6">
            {productOptions.map(option => (
                <div key={option.code}>
                    {option.type === 'color' && option.showVariantImage && (
                        <ImageOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'color' && !option.showVariantImage && (
                        <ColorOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'size' && (
                        <SizeOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'other' && (
                        <OtherOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                </div>
            ))}
        </div>
    );
});

if (isDev) {
    Options.displayName = 'Options';
}

export default Options;
