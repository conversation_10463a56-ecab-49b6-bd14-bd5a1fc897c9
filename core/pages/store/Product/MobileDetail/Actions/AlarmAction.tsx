import {FC, memo, useCallback, useState} from 'react';
import {useStore} from '@core/hooks';
import {UiSpinner} from '@core/components/ui';
import {BellIcon} from '@core/icons/outline';
import {BellIcon as BellSolidIcon} from '@core/icons/solid';
import useProduct from '../../useProduct';

const AlarmAction: FC = memo(() => {
    const {addToCollection, removeFromCollection} = useStore();
    const {selectedProduct, customerProductParams, setCustomerProductParams} =
        useProduct();

    const [
        isAlarmCollectionUpdateInProgress,
        setIsAlarmCollectionUpdateInProgress
    ] = useState(false);
    const onAddToAlarmCollection = useCallback(async () => {
        if (isAlarmCollectionUpdateInProgress) {
            return;
        }

        setIsAlarmCollectionUpdateInProgress(true);

        const result = await addToCollection(
            {
                id: 'is-alarm',
                isAlarm: true
            },
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isInAlarmCollection: true
            });
        }
        setIsAlarmCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isAlarmCollectionUpdateInProgress,
        addToCollection,
        selectedProduct
    ]);
    const onRemoveFromAlarmCollection = useCallback(async () => {
        if (isAlarmCollectionUpdateInProgress) {
            return;
        }

        setIsAlarmCollectionUpdateInProgress(true);

        const result = await removeFromCollection(
            {
                id: 'is-alarm',
                isAlarm: true
            },
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isInAlarmCollection: false
            });
        }
        setIsAlarmCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isAlarmCollectionUpdateInProgress,
        removeFromCollection,
        selectedProduct
    ]);

    return customerProductParams.isInAlarmCollection ? (
        !isAlarmCollectionUpdateInProgress ? (
            <button
                className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
                onClick={onRemoveFromAlarmCollection}
            >
                <BellSolidIcon className="h-3.5 w-3.5 text-primary-600" />
            </button>
        ) : (
            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow">
                <UiSpinner size="sm" />
            </div>
        )
    ) : !isAlarmCollectionUpdateInProgress ? (
        <button
            className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
            onClick={onAddToAlarmCollection}
        >
            <BellIcon className="h-3.5 w-3.5" />
        </button>
    ) : (
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow">
            <UiSpinner size="sm" />
        </div>
    );
});

AlarmAction.displayName = 'AlarmAction';

export default AlarmAction;
