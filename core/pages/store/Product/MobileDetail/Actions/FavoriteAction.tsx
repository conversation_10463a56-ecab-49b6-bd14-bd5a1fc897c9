import {FC, memo, useCallback, useState} from 'react';
import {useStore} from '@core/hooks';
import {UiSpinner} from '@core/components/ui';
import {HeartIcon} from '@core/icons/outline';
import {HeartIcon as HeartSolidIcon} from '@core/icons/solid';
import useProduct from '../../useProduct';

const FavoriteAction: FC = memo(() => {
    const {addToFavorites, removeFromFavorites} = useStore();
    const {selectedProduct, customerProductParams, setCustomerProductParams} =
        useProduct();

    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);
    const onAddToFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await addToFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: true
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        addToFavorites,
        selectedProduct
    ]);
    const onRemoveFromFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await removeFromFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: false
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        removeFromFavorites,
        selectedProduct
    ]);

    return customerProductParams.isFavorite ? (
        !isFavoriteUpdateInProgress ? (
            <button
                className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-200 transition active:opacity-30"
                onClick={onRemoveFromFavorites}
            >
                <HeartSolidIcon className="h-3.5 w-3.5 text-primary-600" />
            </button>
        ) : (
            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-200">
                <UiSpinner size="sm" />
            </div>
        )
    ) : !isFavoriteUpdateInProgress ? (
        <button
            className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-200 transition active:opacity-30"
            onClick={onAddToFavorites}
        >
            <HeartIcon className="h-3.5 w-3.5" />
        </button>
    ) : (
        <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-200">
            <UiSpinner size="sm" />
        </div>
    );
});

FavoriteAction.displayName = 'FavoriteAction';

export default FavoriteAction;
