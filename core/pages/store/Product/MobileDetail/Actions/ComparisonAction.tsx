import {FC, memo, useMemo} from 'react';
import {cls} from '@core/helpers';
import {ArrowRightArrowLeftIcon} from '@core/icons/outline';
import useProduct from '../../useProduct';

const ComparisonAction: FC = memo(() => {
    const {
        selectedProduct,
        comparisonList,
        addToComparisonList,
        openComparisonList
    } = useProduct();

    const isInComparisonList = useMemo(
        () =>
            comparisonList.some(
                comparisonItem =>
                    comparisonItem.productId === selectedProduct.productId
            ),
        [comparisonList, selectedProduct.productId]
    );

    return (
        <button
            className="relative flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 shadow transition active:opacity-30"
            onClick={() => {
                addToComparisonList();
                openComparisonList();
            }}
        >
            {comparisonList.length > 0 && (
                <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary-600 text-[10px] text-white">
                    {comparisonList.length}
                </span>
            )}
            <ArrowRightArrowLeftIcon
                className={cls('h-3.5 w-3.5', {
                    'text-primary-600': isInComparisonList
                })}
            />
        </button>
    );
});

ComparisonAction.displayName = 'ComparisonAction';

export default ComparisonAction;
