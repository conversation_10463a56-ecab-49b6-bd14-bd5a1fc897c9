import {FC, memo} from 'react';
import {SelectedProduct} from '@core/types';
import {isDev} from '@core/helpers';

type ProductInformationProps = {
    selectedProduct: SelectedProduct;
};

const ProductInformation: FC<ProductInformationProps> = memo(
    ({selectedProduct}) => {
        return (
            <div className="p-4">
                <div
                    className="prose"
                    dangerouslySetInnerHTML={{
                        __html: selectedProduct.description ?? ''
                    }}
                />
            </div>
        );
    }
);

if (isDev) {
    ProductInformation.displayName = 'ProductInformation';
}

export default ProductInformation;
