import {FC, memo, useEffect} from 'react';
import {isDev} from '@core/helpers';
import {useIntersection, useTrans} from '@core/hooks';
import ProductSlider from '@components/common/ProductSlider';
import useProduct from './useProduct';

const RelatedProducts: FC = memo(() => {
    const t = useTrans();
    const {relatedProducts, setActiveTab} = useProduct();

    const [ref, observer] = useIntersection({
        threshold: 0.5
    });
    useEffect(() => {
        if (observer?.isIntersecting) {
            setActiveTab('relatedProducts');
        }
    }, [observer, setActiveTab]);

    if (relatedProducts.length < 1) {
        return null;
    }

    return (
        <div ref={ref} id="relatedProducts" className="my-12">
            <h2 className="text-xl font-medium">{t('Related Products')}</h2>

            <div className="mt-4">
                <ProductSlider products={relatedProducts} />
            </div>
        </div>
    );
});

if (isDev) {
    RelatedProducts.displayName = 'RelatedProducts';
}

export default RelatedProducts;
