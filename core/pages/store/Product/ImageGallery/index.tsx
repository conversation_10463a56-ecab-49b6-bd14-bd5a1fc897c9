import {FC, memo, useEffect, useMemo, useRef, useState} from 'react';
import {Cookies} from 'react-cookie-consent';
import storeConfig from '~/store.config';
import {cls, isDev} from '@core/helpers';
import {useElementSize} from '@core//hooks';
import {UiImage, UiSlider} from '@core/components/ui';
import {Navigation, SliderInterface, Thumbs} from '@core/components/ui/Slider';
import useProduct from '../useProduct';
import SelectedImage from './SelectedImage';
import ShippingDetails from '../ProductDetails/ShippingDetails';

type GalleryProps = {
    images: string[];
    productName: string;
};

const Gallery: FC<GalleryProps> = memo(({productName, images}) => {
    const [isSliding, setIsSliding] = useState(false);
    const [thumbsSwiper, setThumbsSwiper] = useState<SliderInterface>();

    const {
        ref: containerRef,
        width: containerWidth,
        height: containerHeight
    } = useElementSize();

    const isMounted = useRef(false);
    useEffect(() => {
        isMounted.current = true;
        return () => {
            isMounted.current = false;
        };
    }, []);

    return (
        <>
            <div
                ref={containerRef}
                className="thumb-swiper-wrapper group relative rounded"
            >
                <UiSlider
                    modules={[Navigation, Thumbs]}
                    thumbs={{swiper: thumbsSwiper}}
                    className="h-full w-full"
                    navigation={{disabledClass: 'disable-slide-navigation'}}
                    watchOverflow
                    onSlideChangeTransitionStart={() => setIsSliding(true)}
                    onSlideChangeTransitionEnd={() => setIsSliding(false)}
                >
                    {images.map((image, index) => (
                        <UiSlider.Slide
                            key={image}
                            className={cls({
                                'aspect-h-3 aspect-w-2':
                                    storeConfig.catalog.productImageShape ===
                                    'rectangle',
                                'aspect-h-1 aspect-w-1':
                                    storeConfig.catalog.productImageShape !==
                                    'rectangle'
                            })}
                        >
                            <SelectedImage
                                src={image}
                                alt={productName}
                                preload={index === 0}
                                containerRef={containerRef}
                                containerWidth={containerWidth}
                                containerHeight={containerHeight}
                                isSliding={isSliding}
                                images={images}
                            />
                        </UiSlider.Slide>
                    ))}
                </UiSlider>

                <ShippingDetails />
            </div>

            <div className="mt-3 h-28">
                {isMounted.current && (
                    <UiSlider
                        className="thumb-swiper mt-3 h-28 w-full select-none"
                        slidesPerView={6}
                        spaceBetween={10}
                        watchSlidesProgress
                        threshold={2}
                        modules={[Navigation, Thumbs]}
                        onSwiper={setThumbsSwiper}
                    >
                        {images.map(image => (
                            <UiSlider.Slide
                                key={image}
                                className="h-28 cursor-pointer"
                            >
                                <UiImage
                                    className="rounded"
                                    src={`${image}?w=180&q=50`}
                                    alt={productName}
                                    fit="cover"
                                    position="center"
                                    fill
                                />
                            </UiSlider.Slide>
                        ))}
                    </UiSlider>
                )}
            </div>
        </>
    );
});

if (isDev) {
    Gallery.displayName = 'Gallery';
}

const ImageGallery: FC = memo(() => {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    const {selectedProduct, product} = useProduct();

    // Get product images.
    const images = useMemo(() => {
        let images = selectedProduct.images;

        if (product.isAdultProduct) {
            if (isMounted && Cookies.get('isAdultEligible') !== 'true') {
                images = ['/adult-image.png'];
            } else if (!isMounted) {
                images = ['/placeholder.png'];
            }
        }
        if (!Array.isArray(images) || images.length < 1) {
            images = ['/no-image.png'];
        }

        return images;
    }, [selectedProduct.images, product.isAdultProduct, isMounted]);

    return (
        <Gallery
            key={JSON.stringify(images)}
            images={images}
            productName={selectedProduct.name}
        />
    );
});

if (isDev) {
    ImageGallery.displayName = 'ImageGallery';
}

export default ImageGallery;
