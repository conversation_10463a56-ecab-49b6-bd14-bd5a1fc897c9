import {memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useProduct from '../useProduct';

const ShippingDetails = memo(() => {
    const t = useTrans();
    const {product} = useProduct();

    return (
        <div className="absolute left-2 top-16 z-[2] flex select-none flex-col gap-1 text-[8px] xl:top-2">
            {product.estimatedDeliveryDuration === 1 && (
                <p className="w-12 rounded border border-green-600 bg-green-600 text-center leading-[12px] text-white">
                    {t('FAST SHIPPING')}
                </p>
            )}
            {product.hasFreeShipping && (
                <p className="w-12 rounded border border-gray-600 bg-gray-600 text-center leading-[12px] text-white">
                    {t('FREE SHIPPING')}
                </p>
            )}
        </div>
    );
});

if (isDev) {
    ShippingDetails.displayName = 'ShippingDetails';
}

export default ShippingDetails;
