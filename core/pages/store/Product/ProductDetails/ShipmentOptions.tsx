import {memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {TruckFastIcon} from '@core/icons/solid';
import useProduct from '../useProduct';

const ShipmentOptions = memo(() => {
    const t = useTrans();
    const {locale} = useStore();
    const {
        product: {estimatedDeliveryDuration}
    } = useProduct();

    const getEstimatedShippingDate = useMemo(() => {
        const currentDate = new Date();
        currentDate.setDate(
            currentDate.getDate() + (estimatedDeliveryDuration ?? 2)
        );

        const formatter = new Intl.DateTimeFormat(locale, {
            day: 'numeric',
            month: 'long'
        });
        return formatter.format(currentDate);
    }, [locale, estimatedDeliveryDuration]);

    return typeof estimatedDeliveryDuration === 'number' ? (
        <div className="xl:rounded xl:border xl:shadow-sm">
            <p className="mt-3 border-b border-gray-200 px-4 pb-2 text-xs font-semibold xl:mt-0 xl:p-4">
                {t('SHIPPING OPTIONS')}
            </p>

            <div className="space-y-2 px-4 py-2 text-xs xl:px-2 xl:py-3">
                {estimatedDeliveryDuration === 1 && (
                    <div className="flex items-center gap-3 rounded bg-green-50 p-2 max-xl:border max-xl:border-green-600">
                        <TruckFastIcon className="h-6 w-6 text-green-600" />
                        <p className="w-full text-green-600">
                            {t('If you order now, we will ship it tomorrow!')}
                        </p>
                    </div>
                )}
                {estimatedDeliveryDuration >= 2 && (
                    <div className="flex items-center gap-3 rounded p-2 max-xl:border max-xl:border-primary-600 max-xl:bg-primary-50">
                        <TruckFastIcon className="h-6 w-6 text-primary-600" />
                        <p
                            dangerouslySetInnerHTML={{
                                __html: t(
                                    'We will ship it on {getEstimatedShippingDate}!',
                                    {getEstimatedShippingDate}
                                )
                            }}
                        />
                    </div>
                )}
            </div>
        </div>
    ) : null;
});

if (isDev) {
    ShipmentOptions.displayName = 'ShipmentOptions';
}

export default ShipmentOptions;
