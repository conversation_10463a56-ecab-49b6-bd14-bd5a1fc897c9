import {memo} from 'react';
import {useRouter} from 'next/router';
import {useTrans} from '@core/hooks';
import {UiButton, UiImage, UiLink, UiTransition} from '@core/components/ui';
import {isDev} from '@core/helpers';
import {XIcon} from '@core/icons/outline';
import useProduct from './useProduct';

const ComparisonList = memo(() => {
    const t = useTrans();
    const router = useRouter();
    const {
        showComparisonList,
        comparisonList,
        removeFromComparisonList,
        clearComparisonList,
        closeComparisonList
    } = useProduct();

    return (
        <UiTransition
            show={showComparisonList}
            appear
            enter="transform transition ease-in-out duration-300"
            enterFrom="translate-y-full"
            enterTo="translate-y-0"
            leave="transform transition ease-in-out duration-300"
            leaveFrom="translate-y-0"
            leaveTo="translate-y-full"
            className="fixed bottom-0 left-0 right-0 z-sticky"
        >
            <div className="relative flex h-64 flex-col overflow-y-scroll border-t bg-white xl:h-52">
                <div className="flex border-b">
                    <div className="container my-2 flex items-center">
                        <div className="min-w-0 flex-1">
                            <div className="w-full truncate font-semibold">
                                {t('Selected Products')} (
                                {comparisonList.length})
                            </div>
                        </div>
                        <div className="flex items-center">
                            <button
                                className="font-semibold text-primary-600 transition active:opacity-30"
                                onClick={closeComparisonList}
                            >
                                {t('Close')}
                            </button>
                        </div>
                    </div>
                </div>

                <div className="container flex h-full flex-col-reverse items-start justify-end gap-2 overflow-hidden xl:flex-row xl:items-center xl:justify-between xl:gap-4">
                    <div className="max-xl:scroller flex items-center gap-2 max-xl:w-full max-xl:overflow-x-scroll xl:gap-4">
                        {comparisonList.map(listItem => (
                            <UiLink
                                href={listItem.slug ?? ''}
                                key={listItem.productId}
                                className="group relative aspect-1 w-36 rounded border p-1"
                            >
                                <div className="relative mx-auto h-24 w-24">
                                    <UiImage
                                        src={
                                            Array.isArray(listItem.images) &&
                                            listItem.images[0]
                                                ? `${listItem.images[0]}?w-240&q=75`
                                                : '/no-image.png'
                                        }
                                        alt={listItem.definition ?? ''}
                                        fit="contain"
                                        position="center"
                                        fill
                                    />
                                </div>
                                <p className="mt-1.5 line-clamp-2 text-center text-sm font-medium group-hover:text-muted">
                                    {listItem.definition}
                                </p>
                                <button
                                    className="absolute right-1 top-1 flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition hover:bg-gray-200 hover:text-gray-500"
                                    onClick={e => {
                                        e.preventDefault();
                                        if (listItem.productId) {
                                            removeFromComparisonList(
                                                listItem.productId
                                            );
                                        }
                                    }}
                                >
                                    <XIcon
                                        className="h-4 w-4"
                                        aria-hidden="true"
                                    />
                                </button>
                            </UiLink>
                        ))}
                    </div>

                    <div className="flex gap-4 max-xl:mt-2 max-xl:w-full xl:flex-col">
                        <UiButton
                            className="max-xl:flex-1 xl:w-36"
                            variant="solid"
                            color="primary"
                            size="md"
                            disabled={comparisonList.length < 1}
                            onClick={() => {
                                const productIds = comparisonList
                                    .map(listItem => listItem.productId)
                                    .join(',');

                                router.push({
                                    pathname: '/compare',
                                    query: {
                                        q: encodeURIComponent(productIds)
                                    }
                                });
                            }}
                        >
                            {t('Compare')}
                        </UiButton>
                        <UiButton
                            className="max-xl:flex-1 xl:w-36"
                            variant="outline"
                            color="danger"
                            size="md"
                            disabled={comparisonList.length < 1}
                            onClick={() => {
                                clearComparisonList();
                                closeComparisonList();
                            }}
                        >
                            {t('Clear')}
                        </UiButton>
                    </div>
                </div>
            </div>
        </UiTransition>
    );
});

if (isDev) {
    ComparisonList.displayName = 'ComparisonList';
}

export default ComparisonList;
