const WomanShoes = ({...props}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 502.49 385.93"
            {...props}
        >
            <path d="M349.4,383.65c-19.67,0-39.33,.17-59-.04-24.19-.26-42.18-11.16-52.17-33.21C191.3,246.81,115.49,172.15,12.26,125.01c-7.97-3.64-10.18-8.38-10.25-16.64C1.66,69.92,15.91,37.57,40.91,8.95,48.51,.26,53.3-.34,62.39,7.49c50.5,43.5,94.56,92.7,133.04,146.96,23.37,32.96,46.81,65.88,69.47,99.33,29.36,43.36,82.26,57.92,128.5,33.25,10.63-5.67,19.39-6.13,29.77-1.74,28.61,12.1,52.81,29.73,69.47,56.27,13.78,21.95,9.96,30.6-15.67,35.54-28.17,5.43-56.8,6.26-85.42,6.54-14.05,.14-28.09,.02-42.14,.02Z" />
            <path d="M11.71,148.05c26.96,11.39,49.04,24.97,70.03,40.53,3.78,2.8,4.55,5.38,2.15,9.79-18.43,33.89-18.86,70.81-18.12,108,.43,21.84-.21,43.71,.22,65.55,.16,8.39-2.02,12.03-11.22,12.01-9.54-.02-11.06-4.56-11-12.49,.23-31.38-.58-62.77,.25-94.13,1.1-41.19-5.64-80.28-26.62-116.3-1.85-3.18-3-6.76-5.7-12.97Z" />
            <path d="M344.56,361.34c-17.97,0-35.95-.07-53.92,.03-6.77,.04-13.27-1.23-13.31-9.21-.04-8.08,5.66-11.42,13.61-11.41,37.07,.08,74.14,.01,111.21,.06,7.7,.01,13.71,3.17,13.31,11.59-.38,8-6.91,9.01-13.61,8.97-19.1-.1-38.19-.04-57.29-.04Z" />
            <path d="M447.96,340.96c6.08,1.74,10.31,4.9,9.63,11.74-.6,6.03-4.48,8.66-10.32,8.51-5.97-.15-9.55-3.39-9.58-9.27-.04-6.58,4.22-9.83,10.28-10.97Z" />
        </svg>
    );
};

export default WomanShoes;
