const WomanTop = ({...props}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 382.14 485.89"
            {...props}
        >
            <path d="M190.87,483.52c-44.2,0-88.41-.61-132.58,.36-13.91,.31-17.14-4.57-15.78-16.95,1.21-11.01,2.39-21.89,6.23-32.39,28.14-76.82,37.13-155.8,24.01-236.62-7.84-48.27-25.52-94.1-39.33-140.85-5.09-17.25-5.24-27.72,14.14-33.72,11.51-3.56,22.53-9.52,32.84-15.89C92.47,0,97.29,1.36,100.36,16.25c9.46,45.92,36.52,79.34,76.58,103.06,10.92,6.47,20.19,5.88,30.78-.51,39.9-24.05,66.13-58,75.66-103.85,2.53-12.18,6.24-15.17,17.72-8.53,14.54,8.41,29.77,15.74,45.13,22.57,8.21,3.65,9.34,7.67,6.74,16.05-10,32.17-18.49,64.81-28.63,96.93-30.77,97.46-24.27,193.67,8.77,289.07,4.43,12.78,6.8,25.65,7.69,39.11,.73,11-3.27,13.73-13.84,13.6-45.35-.54-90.72-.23-136.07-.23Z" />
            <path d="M115.67,12.03c-1.12-13.94,5.57-9.78,10.83-8.21,20.67,6.17,41.67,9.28,63.36,9.35,22.84,.08,45.19-2.17,66.79-9.51,9.42-3.2,13.38-1.94,10.79,8.7-9.71,39.84-32.09,70.25-67.46,91.12-4.26,2.52-7.95,3.46-12.99,1.62-30.09-10.93-69.99-64.15-71.32-93.08Z" />
            <path d="M15.5,54.49c8.58,28.91,15.62,52.62,22.66,76.33-.87,.57-1.74,1.14-2.6,1.71-10.69-12.83-23.35-24.54-31.5-38.83-7.16-12.54,6.23-22.8,11.45-39.22Z" />
            <path d="M345.08,130.73c7.05-23.72,14.09-47.43,21.32-71.78,18.13,22.79,18.19,28,.95,49.79-6.41,8.1-13,16.06-19.51,24.08-.92-.7-1.84-1.4-2.76-2.1Z" />
        </svg>
    );
};

export default WomanTop;
