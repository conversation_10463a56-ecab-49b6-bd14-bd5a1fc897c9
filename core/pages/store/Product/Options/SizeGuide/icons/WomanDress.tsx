const WomanDress = ({...props}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 255.01 682.82"
            {...props}
        >
            <path d="M2,376.54c1.86-40.81,2.65-84.52,18.68-126.21,3.63-9.45,8.01-11.9,18.42-11.01,21.15,1.8,164.12,1.98,180.01-.26,1.23-.17,5.6-.9,9.4,1.47,3.67,2.29,5,6.35,5.49,7.92,22.07,70.76,18.83,128.85,18.83,128.85-6.46,116.03-7.32,128.54-8.83,147.68-1.92,24.28-3.98,48.56-6.09,72.83-1.05,12.13-2.12,24.27-3.17,36.4-.84,9.62,1.77,47.8-19.63,34.64-4.45-2.74-6.43-9.08-10.32-21.44-1.38-4.39-8.01-33.56-15.69-28.68-2.21,1.4-3.02,4.18-3.76,6.69-5.98,20.23-18.74,40.63-38.18,50.35,0,0-24.36,14.26-113.34-5.15-1.77-.39-5.63-1.31-8.28-4.54-1.89-2.31-2.63-5.38-2.94-9.5-4.8-62.46-10.1-124.88-14.87-187.35-2.28-29.91-3.73-59.89-5.74-92.69Z" />
            <path d="M126.67,268.62c-30.39,.02-60.73-.89-91.15-.89-4.91,0-10.48,1.29-15.24-.46-2.03-.75-3.88-2.39-4.24-4.52-.29-1.69,.36-3.38,1.03-4.95,4.48-10.6,29.85-59.27,34.92-72.61,10.64-28.02-1.95-57.69-9.21-74.8C12.47,38.93,8.97,31.86,8.97,31.86c-.93-1.87-3.59-7.28-1.5-11.8,1.37-2.96,4.95-5.17,12.04-6.8,8.59-1.97,16.81-5.25,24.51-8.77,3.72-1.7,7.95-4.17,11.58-.86,1.98,1.8,2.97,4.4,3.93,6.9,4.4,11.49,14.38,20.78,22.5,29.72,9.92,10.94,19.89,22.05,27.62,34.7,17.65,28.87,14.91,53.36,20.57,53.09,5.82-.28-.24-25.67,15.93-54.42,7.39-13.15,17.57-24.56,27.54-35.78,8.34-9.38,17.7-18.08,22.1-30.14,.54-1.49,1.05-3.06,2.15-4.2,2.85-2.94,7.64-.92,10.76,.41,10.85,4.64,22.22,8.68,33.68,12.45,6.13,2.01,6.97,4.23,5.03,8.85-3.55,8.44-5.24,17.8-10.17,25.52-10.66,16.66-21.16,53.4-23.49,61.58-7.47,26.14-15.26,53.4-4.38,80.13,5.25,12.91,27.56,56.91,28.7,66.78,.12,1,.48,3.99-1.3,6.09-1.55,1.82-4.11,2.44-6.5,2.44-12.59,.02-25.12-1.29-37.76-.95-13.51,.36-27.01,1.25-40.52,1.55-8.44,.19-16.88,.26-25.31,.27Z" />
        </svg>
    );
};

export default WomanDress;
