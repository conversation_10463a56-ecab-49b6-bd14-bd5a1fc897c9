import {FC, memo, useEffect, useMemo, useState} from 'react';
import {Cookies} from 'react-cookie-consent';
import storeConfig from '~/store.config';
import {cls, isDev} from '@core/helpers';
import {useIntersection, useTrans} from '@core/hooks';
import {UiDivider, UiImage} from '@core/components/ui';
import useProduct from './useProduct';

const ProductInformation: FC = memo(() => {
    const t = useTrans();
    const {selectedProduct, product, setActiveTab} = useProduct();

    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    // Get product images.
    const imageSrc = useMemo(() => {
        let images = selectedProduct.images;

        if (product.isAdultProduct) {
            if (isMounted && Cookies.get('isAdultEligible') !== 'true') {
                images = ['/adult-image.png'];
            } else if (!isMounted) {
                images = ['/placeholder.png'];
            }
        }
        if (!Array.isArray(images) || images.length < 1) {
            images = ['/no-image.png'];
        }

        return images[0];
    }, [selectedProduct.images, product.isAdultProduct, isMounted]);

    const [ref, observer] = useIntersection({
        threshold: 0.5
    });
    useEffect(() => {
        if (observer?.isIntersecting) {
            setActiveTab('productInformation');
        }
    }, [observer, setActiveTab]);

    return (
        <div
            ref={ref}
            id="productInformation"
            className="my-12 xl:rounded xl:border xl:border-gray-200 xl:p-8 xl:shadow-sm"
        >
            <h2 className="text-xl font-medium">{t('Product Information')}</h2>

            <div className="mt-4 grid grid-cols-1 xl:grid-cols-12 xl:gap-8">
                <div className="col-span-4 hidden xl:block">
                    <div
                        className={cls('', {
                            'aspect-h-3 aspect-w-2':
                                storeConfig.catalog.productImageShape ===
                                'rectangle',
                            'aspect-h-1 aspect-w-1':
                                storeConfig.catalog.productImageShape !==
                                'rectangle'
                        })}
                    >
                        <div>
                            <UiImage
                                className="rounded"
                                src={`${imageSrc}?w=980&q=75`}
                                alt={selectedProduct.name}
                                fill
                                fit="cover"
                                position="center"
                            />
                        </div>
                    </div>
                </div>

                <div className="xl:col-span-8">
                    <div className="">
                        <div
                            className="prose"
                            dangerouslySetInnerHTML={{
                                __html: selectedProduct.description ?? ''
                            }}
                        />
                    </div>
                </div>
            </div>

            {selectedProduct.features.length > 0 && (
                <>
                    <UiDivider className="my-8 opacity-100" />

                    <h3 className="font-medium leading-4">
                        {t('Product Features')}
                    </h3>

                    <div className="mt-6 grid grid-cols-12 gap-6">
                        {selectedProduct.features.map((feature, index) => (
                            <div
                                className="col-span-6 flex items-center justify-between rounded border border-transparent bg-gray-100 px-4 py-3 text-sm shadow-sm transition hover:border-primary-600 hover:bg-white"
                                key={feature.code + index}
                            >
                                <div className="font-medium">
                                    {feature.label}
                                </div>
                                <div className="text-muted">
                                    {feature.value}
                                </div>
                            </div>
                        ))}
                    </div>
                </>
            )}
        </div>
    );
});

if (isDev) {
    ProductInformation.displayName = 'ProductInformation';
}

export default ProductInformation;
