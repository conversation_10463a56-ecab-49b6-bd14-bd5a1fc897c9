import {useCallback, useState} from 'react';
import {UiButton} from '@core/components/ui';
import {useScrollIntoView, useStore, useTrans} from '@core/hooks';
import useProduct from './useProduct';

const NotifyCustomer = () => {
    const t = useTrans();
    const {
        selectedProduct,
        relatedProducts,
        customerProductParams,
        setCustomerProductParams
    } = useProduct();
    const {addToCollection} = useStore();

    const [isNotifyCustomerInProgress, setIsNotfiyCustomerInProgress] =
        useState(false);
    const onNotifyCustomer = useCallback(async () => {
        if (isNotifyCustomerInProgress) return;

        setIsNotfiyCustomerInProgress(true);

        const result = await addToCollection(
            {id: 'is-notify-customer', isNotifyCustomer: true},
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isNotifyCustomer: true
            });
        }

        setIsNotfiyCustomerInProgress(false);
    }, [
        isNotifyCustomerInProgress,
        selectedProduct,
        customerProductParams,
        setCustomerProductParams,
        addToCollection
    ]);

    const {scrollIntoView} = useScrollIntoView({
        target: 'relatedProducts',
        offset: 108
    });

    return (
        <div className="flex select-none items-center gap-4">
            {
                <UiButton
                    className="btn-sm flex-1 text-xs disabled:btn-solid xl:btn-xl disabled:cursor-default disabled:!border-green-600 disabled:!bg-green-600 disabled:opacity-100 md:text-base"
                    variant="outline"
                    color="primary"
                    loading={isNotifyCustomerInProgress}
                    disabled={
                        isNotifyCustomerInProgress ||
                        customerProductParams.isNotifyCustomer
                    }
                    onClick={onNotifyCustomer}
                >
                    {customerProductParams.isNotifyCustomer
                        ? t('Received Your Request')
                        : t('Notify Me')}
                </UiButton>
            }

            {Array.isArray(relatedProducts) && relatedProducts.length > 0 && (
                <UiButton
                    className="hidden flex-1 text-base xl:block"
                    variant="solid"
                    color="primary"
                    size="xl"
                    onClick={() => scrollIntoView()}
                >
                    {t('See Related Products')}
                </UiButton>
            )}
        </div>
    );
};

export default NotifyCustomer;
