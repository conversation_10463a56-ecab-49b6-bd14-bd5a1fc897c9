import {FC, memo, useCallback, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiButton, UiRating} from '@core/components/ui';
import {StarIcon} from '@core/icons/solid';
import useProduct from '../useProduct';

const Report: FC = memo(() => {
    const t = useTrans();
    const {createReview} = useStore();
    const {selectedProduct} = useProduct();
    const [isLoading, setIsLoading] = useState(false);

    const onReviewCreate = useCallback(async () => {
        if (isLoading) return;

        setIsLoading(true);

        await createReview({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        setIsLoading(false);
    }, [
        createReview,
        isLoading,
        selectedProduct.images,
        selectedProduct.name,
        selectedProduct.productId,
        selectedProduct.salesPrice
    ]);

    return (
        <div className="xl:col-span-4">
            <h2 className="text-xl font-medium">{t('Product Reviews')}</h2>

            <div className="mt-3 flex items-center">
                <div className="flex items-center">
                    <div className="h-5">
                        <UiRating
                            initialRating={selectedProduct.rating}
                            size="sm"
                            readonly
                        />
                    </div>
                </div>
                <div className="ml-2.5 mt-0.5 text-sm">
                    {t('Based on {count} reviews', {
                        count: selectedProduct.reviewCount
                    })}
                </div>
            </div>

            <div className="mt-10">
                <dl className="space-y-3">
                    {selectedProduct.reviewsReport.map(report => (
                        <div
                            key={report.starCount}
                            className="flex items-center text-sm"
                        >
                            <dt className="flex flex-1 items-center">
                                <p className="w-3 font-medium">
                                    {report.starCount}
                                </p>

                                <div className="ml-1 flex flex-1 items-center">
                                    <StarIcon
                                        className={cls(
                                            report.count > 0
                                                ? 'text-yellow-400'
                                                : 'text-gray-300',
                                            'h-5 w-5 flex-shrink-0'
                                        )}
                                    />

                                    <div className="relative ml-3 flex-1">
                                        <div className="h-3 rounded-full border border-gray-200 bg-gray-100" />
                                        {report.count > 0 ? (
                                            <div
                                                className="absolute inset-y-0 rounded-full border border-yellow-400 bg-yellow-400"
                                                style={{
                                                    width: `${report.percentage}%`
                                                }}
                                            />
                                        ) : null}
                                    </div>
                                </div>
                            </dt>

                            <dd className="ml-3 w-10 text-right text-sm tabular-nums text-gray-900">
                                {Math.round(report.percentage)}%
                            </dd>
                        </div>
                    ))}
                </dl>
            </div>

            <div className="mt-10">
                <h3 className="text-lg font-medium">
                    {t('Share your thoughts')}
                </h3>
                <p className="mt-1 text-sm text-gray-600">
                    {t(
                        'If you’ve used this product, share your thoughts with other customers.'
                    )}
                </p>

                <UiButton
                    className="mt-6 w-full"
                    variant="outline"
                    loading={isLoading}
                    onClick={onReviewCreate}
                >
                    {t('Write a Review')}
                </UiButton>
            </div>
        </div>
    );
});

if (isDev) {
    Report.displayName = 'Report';
}

export default Report;
