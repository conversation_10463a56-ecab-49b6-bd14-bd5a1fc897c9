import {
    Product,
    ProductOption,
    ProductVariant,
    SelectedProduct
} from '@core/types';
import {clone, omit} from '@core/helpers';

export function getSelectedProduct({
    product,
    variant,
    selectedProduct: existingSelectedProduct
}: {
    product: Product;
    variant?: ProductVariant;
    selectedProduct?: SelectedProduct;
}) {
    const selectedProduct: SelectedProduct = clone(
        !!existingSelectedProduct
            ? existingSelectedProduct
            : ({
                  ...omit(product, 'variants')
              } as SelectedProduct)
    );

    if (!!variant) {
        selectedProduct.productId = variant.productId;
        selectedProduct.barcode = variant.barcode;
        selectedProduct.shortDescription = variant.shortDescription;
        selectedProduct.description = variant.description;
        selectedProduct.rating = variant.rating;
        selectedProduct.reviewCount = variant.reviewCount;
        selectedProduct.salesCount = variant.salesCount;
        selectedProduct.favoritesCount = variant.favoritesCount;
        selectedProduct.unitId = variant.unitId;
        selectedProduct.unitName = variant.unitName;
        selectedProduct.deliveryOptionIds = variant.deliveryOptionIds;
        selectedProduct.estimatedDeliveryDuration =
            variant.estimatedDeliveryDuration;
        selectedProduct.deliveryAtSpecifiedDate =
            variant.deliveryAtSpecifiedDate;
        selectedProduct.deliveryAtSpecifiedTime =
            variant.deliveryAtSpecifiedTime;
        selectedProduct.weight = variant.weight;
        selectedProduct.height = variant.height;
        selectedProduct.width = variant.width;
        selectedProduct.depth = variant.depth;
        selectedProduct.availableQuantity = variant.quantity;
        selectedProduct.quantity = 1;
        selectedProduct.salesPrice = variant.salesPrice;
        selectedProduct.unDiscountedSalesPrice = variant.unDiscountedSalesPrice;
        selectedProduct.discount = variant.discount;
        selectedProduct.hasDiscount = variant.hasDiscount;
        selectedProduct.attributes = variant.attributes;
        selectedProduct.colorAttributeCode = variant.colorAttributeCode;
        selectedProduct.colorAttributeValue = variant.colorAttributeValue;

        if (
            Array.isArray(product.variantImages) &&
            product.variantImages.length > 0 &&
            typeof selectedProduct.attributes === 'object'
        ) {
            const images: string[] = [];

            for (const attributeCode of Object.keys(variant.attributes)) {
                const attributeValue = variant.attributes[attributeCode];
                const variantImage = product.variantImages.find(
                    variantImage =>
                        variantImage.attributeCode === attributeCode &&
                        variantImage.attributeValue === attributeValue
                );

                if (
                    typeof variantImage === 'object' &&
                    Array.isArray(variantImage.images)
                ) {
                    for (const image of variantImage.images) {
                        if (!images.includes(image)) {
                            images.push(image);
                        }
                    }
                }
            }

            if (images.length > 0) {
                selectedProduct.images = images;
            } else if (
                Array.isArray(variant.images) &&
                variant.images.length > 0
            ) {
                selectedProduct.images = variant.images;
            }
        }
    } else {
        selectedProduct.availableQuantity = product.quantity;
        selectedProduct.quantity = 1;
    }

    selectedProduct.inStock = selectedProduct.availableQuantity > 0;

    return selectedProduct;
}

export function getInitialSelectedProduct(
    product: Product,
    selectedAttributes?: Record<string, any>
) {
    let variant: any = undefined;

    if (
        product.isConfigurable &&
        Array.isArray(product.variants) &&
        product.variants.length > 0
    ) {
        if (
            !!selectedAttributes &&
            typeof selectedAttributes === 'object' &&
            Object.keys(selectedAttributes).length > 0
        ) {
            let variants = product.variants;

            for (const attributeCode of Object.keys(selectedAttributes)) {
                const attributeValue = selectedAttributes[attributeCode];

                variants = variants.filter(
                    variantItem =>
                        variantItem.attributes[attributeCode] === attributeValue
                );
            }

            if (variants.length > 0) {
                variant = variants[0];
            } else {
                variant = product.variants[0];
            }
        } else {
            variant = product.variants[0];
        }
    }

    return getSelectedProduct({product, variant});
}

export function getProductOptions(
    product: Product,
    code?: string,
    value?: string
) {
    const originalOptions = JSON.parse(
        JSON.stringify(product.options)
    ) as ProductOption[];
    const options: ProductOption[] = [];

    for (const originalOption of originalOptions) {
        if (!!code && !!value && originalOption.code !== code) {
            const variants = (product.variants ?? []).filter(
                variant => variant.attributes[code] === value
            );
            const option = {...originalOption};
            const selections: {
                value: string;
                color?: string;
                image?: string;
                inStock?: boolean;
            }[] = [];

            for (const selection of option.selections) {
                const variant = variants.find(
                    variant =>
                        variant.attributes[option.code] === selection.value
                );

                if (!!variant) {
                    let image: string | undefined = undefined;
                    if (
                        Array.isArray(variant.images) &&
                        variant.images.length > 0
                    ) {
                        image = variant.images[0];
                    }

                    selections.push({
                        ...selection,
                        image,
                        inStock: variant.quantity > 0
                    });
                }
            }

            option.selections = selections;

            options.push(option);
        } else {
            options.push({
                ...originalOption,
                selections: originalOption.selections.map(selection => {
                    selection.inStock = true;

                    return selection;
                })
            });
        }
    }

    return options.filter(option => option.selections.length > 0);
}
