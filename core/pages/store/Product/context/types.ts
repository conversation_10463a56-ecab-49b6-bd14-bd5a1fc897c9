import {Dispatch, SetStateAction} from 'react';
import {
    Campaign,
    CustomerProductPrams,
    Installment,
    Product,
    ProductListItem,
    ProductOption,
    SelectedProduct
} from '@core/types';

export type ProductContextType = {
    product: Product;
    selectedProduct: SelectedProduct;
    productOptions: ProductOption[];
    seo: Record<string, any>;
    isAddToCartInProgress: boolean;
    availableQuantity: number;
    inStock: boolean;
    customerProductParams: CustomerProductPrams;
    activeTab: string;
    relatedProducts: ProductListItem[];
    currentPCMPayload?: Record<string, any>;
    campaigns: Campaign[];
    installments: Installment[];
    comparisonList: Partial<Product>[];
    showComparisonList: boolean;

    setProduct: (product: Product) => void;
    setSelectedProduct: (selectedProduct: SelectedProduct) => void;
    setProductOptions: (productOptions: ProductOption[]) => void;
    setAttribute: (code: string, value: string) => void;
    setQuantity: (quantity: number) => void;
    addToCart: () => void;
    setCustomerProductParams: Dispatch<
        SetStateAction<CustomerProductPrams | undefined>
    >;
    setActiveTab: Dispatch<SetStateAction<string>>;
    setCurrentPCMPayload: Dispatch<SetStateAction<Record<string, any>>>;
    refresh: () => Promise<void>;
    addToComparisonList: () => void;
    removeFromComparisonList: (productId: string) => void;
    clearComparisonList: () => void;
    openComparisonList: () => void;
    closeComparisonList: () => void;
};
