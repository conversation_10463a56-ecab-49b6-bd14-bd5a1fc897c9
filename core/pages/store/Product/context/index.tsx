import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {Cookies} from 'react-cookie-consent';
import storeConfig from '~/store.config';
import {
    Campaign,
    CartItem,
    CustomerProductPrams,
    Installment,
    Product,
    ProductListItem,
    ProductOption
} from '@core/types';
import {
    base64,
    cleanHtml,
    isDev,
    jsonRequest,
    pushIntoGTMDataLayer,
    trim
} from '@core/helpers';
import {
    useCart,
    useCustomer,
    useDebouncedCallback,
    useMobile,
    useStore,
    useTrans
} from '@core/hooks';
import ProductCartSummary from '@components/common/ProductCartSummary';
import {ProductContextType} from './types';
import {
    getInitialSelectedProduct,
    getProductOptions,
    getSelectedProduct
} from './utils';
import {notification} from '@core/components/ui';

export const ProductContext = createContext<ProductContextType>({} as any);

if (isDev) {
    ProductContext.displayName = 'ProductContext';
}

type ProductProviderProps = {
    slug: string;
    product: Product;
    selectedAttributes?: Record<string, any>;
    relatedProducts: ProductListItem[];
    campaigns: Campaign[];
};

export const ProductProvider: FC<PropsWithChildren<ProductProviderProps>> =
    memo(props => {
        const {
            slug,
            product: initialProduct,
            selectedAttributes,
            relatedProducts,
            campaigns: productCampaigns,
            ...rest
        } = props;
        const router = useRouter();
        const t = useTrans();
        const {isMobile} = useMobile();
        const {locale, currency} = useStore();
        const customer = useCustomer();
        const {
            cart,
            addItem: addCartItem,
            updateItem: updateCartItem
        } = useCart();
        const [campaigns, setCampaigns] = useState(() => productCampaigns);
        const [product, setProduct] = useState(() => initialProduct);
        const [selectedProduct, setSelectedProduct] = useState(() =>
            getInitialSelectedProduct(initialProduct, selectedAttributes)
        );
        const [productOptions, setProductOptions] = useState(() => {
            const initialSelectedProduct = getInitialSelectedProduct(
                initialProduct,
                selectedAttributes
            );

            let productOptions = [] as ProductOption[];

            if (
                Array.isArray(initialProduct.options) &&
                initialProduct.options.length > 0
            ) {
                const firstOption = initialProduct.options[0];

                if (firstOption.type === 'color') {
                    productOptions = getProductOptions(
                        initialProduct,
                        firstOption.code,
                        initialSelectedProduct.attributes![firstOption.code]
                    );
                } else {
                    productOptions = getProductOptions(initialProduct);
                }
            }

            return productOptions;
        });
        const isInitial = useRef(true);
        const [isAddToCartInProgress, setIsAddToCartInProgress] =
            useState(false);
        const [customerProductParams, setCustomerProductParams] =
            useState<CustomerProductPrams>({
                productId: initialProduct.productId,
                isFavorite: false,
                isInCollection: false,
                isInAlarmCollection: false,
                isNotifyCustomer: false
            });
        const [activeTab, setActiveTab] = useState('productInformation');
        const [currentPCMPayload, setCurrentPCMPayload] = useState<
            Record<string, any>
        >(() => {});

        const [showComparisonList, setShowComparisonList] = useState(false);
        const openComparisonList = useCallback(() => {
            setShowComparisonList(true);
        }, []);
        const closeComparisonList = useCallback(() => {
            setShowComparisonList(false);
        }, []);

        const [comparisonList, setComparisonList] = useState<
            Partial<Product>[]
        >([]);

        useEffect(() => {
            let comparisonList = localStorage.getItem('comparison-list');

            if (comparisonList) {
                try {
                    comparisonList = JSON.parse(comparisonList);
                } catch (error) {}

                if (
                    Array.isArray(comparisonList) &&
                    comparisonList.length > 0
                ) {
                    setComparisonList(comparisonList);
                }
            }
        }, []);

        const addToComparisonList = useCallback(() => {
            const product = {
                productId: selectedProduct.productId,
                brandName: selectedProduct.brandName,
                code: selectedProduct.code,
                definition: selectedProduct.definition,
                images: selectedProduct.images?.slice(0, 1) ?? [],
                slug: selectedProduct.slug
            };

            setComparisonList(prevComparisonList => {
                const isAlreadyInList = prevComparisonList.some(
                    item => item.productId === product.productId
                );

                if (!isAlreadyInList) {
                    if (comparisonList.length > 4) {
                        notification({
                            status: 'warning',
                            title: t('Comparison List Limit Reached'),
                            description: t(
                                'You have added more than 5 products to your comparison list. Please remove a product before adding another.'
                            )
                        });
                        return prevComparisonList;
                    }

                    const updatedComparisonList = [
                        ...prevComparisonList,
                        product
                    ];

                    localStorage.setItem(
                        'comparison-list',
                        JSON.stringify(updatedComparisonList)
                    );

                    return updatedComparisonList;
                }

                return prevComparisonList;
            });
        }, [selectedProduct, comparisonList.length, t]);

        const removeFromComparisonList = useCallback((productId: string) => {
            setComparisonList(prevComparisonList => {
                const updatedComparisonList = prevComparisonList.filter(
                    product => product.productId !== productId
                );

                localStorage.setItem(
                    'comparison-list',
                    JSON.stringify(updatedComparisonList)
                );

                return updatedComparisonList;
            });
        }, []);

        const clearComparisonList = useCallback(() => {
            Cookies.remove('comparison-list');
            setComparisonList([]);
        }, []);

        // Is initial.
        useEffect(() => {
            setTimeout(() => {
                isInitial.current = false;
            }, 250);
        }, []);

        const [installments, setInstallments] = useState<Installment[]>([]);
        useEffect(() => {
            (async () => {
                try {
                    const result: Installment[] = await jsonRequest({
                        url: '/api/checkout/installments',
                        method: 'POST',
                        data: {amount: product.salesPrice}
                    });

                    setInstallments(result);
                } catch (error) {}
            })();
        }, [product.salesPrice]);

        // Refresh.
        const refresh = useCallback(async () => {
            const result = await jsonRequest({
                url: '/api/catalog/product',
                method: 'POST',
                data: {
                    slug: initialProduct.slug
                }
            });

            setCampaigns(result.campaigns);
            setProduct(result.product);
            setSelectedProduct(
                getInitialSelectedProduct(result.product, selectedAttributes)
            );

            const initialSelectedProduct = getInitialSelectedProduct(
                result.product,
                selectedAttributes
            );
            let productOptions = [] as ProductOption[];
            if (
                Array.isArray(result.product.options) &&
                result.product.options.length > 0
            ) {
                const firstOption = result.product.options[0];

                if (firstOption.type === 'color') {
                    productOptions = getProductOptions(
                        result.product,
                        firstOption.code,
                        initialSelectedProduct.attributes![firstOption.code]
                    );
                } else {
                    productOptions = getProductOptions(result.product);
                }
            }
        }, [initialProduct.slug, selectedAttributes]);

        // Get SEO params.
        const seo = useMemo(() => {
            const seo: Record<string, any> = {};

            const params: Record<string, any> = {};
            params.title = selectedProduct.name;
            if (
                typeof selectedProduct.seoTitle === 'string' &&
                selectedProduct.seoTitle.length > 0
            ) {
                params.title = selectedProduct.seoTitle;
            }
            if (!!selectedProduct.colorAttributeValue) {
                params.title = `${selectedProduct.colorAttributeValue} ${params.title}`;
            }

            params.description = product.shortDescription;
            if (
                typeof product.seoDescription === 'string' &&
                product.seoDescription.length > 0
            ) {
                params.description = product.seoDescription;
            }
            if (
                typeof params.description !== 'string' ||
                params.description.trim().length < 1
            ) {
                params.description = t(
                    'Please visit our product page to get detailed information about the {title} product or to buy it.',
                    {title: params.title}
                );
            }

            if (
                !!selectedProduct.colorAttributeCode &&
                !!selectedProduct.colorAttributeValue
            ) {
                const colorVariants = (product.variants ?? []).filter(
                    variant =>
                        (variant.attributes ?? {})[
                            selectedProduct.colorAttributeCode!
                        ] === selectedProduct.colorAttributeValue
                );

                params.canonical = trim(
                    `${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/${
                        colorVariants[0].slug
                    }`,
                    '/'
                );
            }

            params.openGraph = {
                type: 'product',
                url: trim(
                    `${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/${
                        router.asPath
                    }`,
                    '/'
                ),
                title: `${params.title}`,
                description: params.description,
                images: selectedProduct?.images
                    ?.map(image => ({
                        url: image,
                        ...(storeConfig.catalog.productImageShape ===
                        'rectangle'
                            ? {
                                  width: 600,
                                  height: 800
                              }
                            : {width: 800, height: 800}),
                        alt: selectedProduct.name
                    }))
                    .slice(0, 1)
            };
            seo.params = params;

            const jsonLD: Record<string, any> = {};
            jsonLD.productName = product.name;
            jsonLD.sku = product.code;
            jsonLD.images = selectedProduct.images;
            jsonLD.description = params.description;
            jsonLD.brand = product.brandName;
            jsonLD.manufacturerName = product.manufacturer;
            jsonLD.reviews = (product.reviews ?? [])
                .map(review => ({
                    author: review.author,
                    datePublished: review.createdAt.toString(),
                    reviewBody: cleanHtml(review.content).slice(0, 300),
                    reviewRating: {
                        bestRating: '5',
                        ratingValue: review.rating.toString(),
                        worstRating: '1'
                    }
                }))
                .slice(0, 1);
            if (
                typeof product.reviewCount === 'number' &&
                product.reviewCount > 0
            ) {
                jsonLD.aggregateRating = {
                    '@type': 'AggregateRating',
                    ratingValue: product.rating.toString(),
                    reviewCount: product.reviewCount.toString(),
                    bestRating: 5,
                    worstRating: 1
                };
            }
            jsonLD.offers = {
                '@type': 'Offer',
                url: trim(
                    `${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/${
                        router.asPath
                    }`,
                    '/'
                ),
                priceCurrency: currency.name === 'TL' ? 'TRY' : currency.name,
                price: (selectedProduct.salesPrice ?? 0).toString(),
                priceValidUntil: `${new Date().getFullYear()}-12-31`,
                itemCondition: 'https://schema.org/UsedCondition',
                availability: 'https://schema.org/InStock'
            };
            seo.jsonLD = jsonLD;

            return seo;
        }, [
            router.asPath,
            product.brandName,
            product.shortDescription,
            product.seoDescription,
            product.name,
            product.code,
            product.manufacturer,
            product.reviews,
            product.reviewCount,
            product.variants,
            product.rating,
            selectedProduct.name,
            selectedProduct.seoTitle,
            selectedProduct.colorAttributeValue,
            selectedProduct.colorAttributeCode,
            selectedProduct.images,
            selectedProduct.salesPrice,
            currency.name,
            t
        ]);

        // Available quantity.
        const availableQuantity = useMemo(() => {
            let availableQuantity = selectedProduct.availableQuantity;

            for (const cartItem of cart.items ?? []) {
                if (cartItem.productId === selectedProduct.productId) {
                    availableQuantity -= cartItem.quantity;
                }
            }

            return availableQuantity;
        }, [cart, selectedProduct]);

        // In stock.
        const inStock = useMemo(
            () => availableQuantity > 0,
            [availableQuantity]
        );

        // Set attribute.
        const setAttribute = useCallback(
            (code: string, value: string) => {
                const newAttributes = {
                    ...selectedProduct.attributes,
                    [code]: value
                };
                const variants = (product.variants ?? []).filter(variant => {
                    return variant.attributes[code] === value;
                });

                let variant = variants.find(variant => {
                    for (const newAttributesCode of Object.keys(
                        newAttributes
                    )) {
                        if (
                            newAttributes[newAttributesCode] !==
                            variant.attributes[newAttributesCode]
                        ) {
                            return false;
                        }
                    }

                    return true;
                });
                if (!variant && variants.length > 0) {
                    variant = variants[0];
                }

                if (!!variant) {
                    const option = product.options.find(
                        option => option.code == code
                    );

                    setSelectedProduct(
                        getSelectedProduct({
                            product,
                            variant,
                            selectedProduct
                        })
                    );

                    if (option?.type === 'color') {
                        setProductOptions(
                            getProductOptions(product, code, value)
                        );

                        router.replace(
                            `${trim(product.slug, '/')}-${base64.encode(
                                new URLSearchParams({
                                    ...newAttributes,
                                    _es: 'true'
                                }).toString()
                            )}`,
                            undefined,
                            {
                                shallow: false,
                                locale: router.locale
                            }
                        );
                    } else {
                        router.replace(
                            `${trim(product.slug, '/')}-${base64.encode(
                                new URLSearchParams({
                                    ...newAttributes,
                                    _es: 'true'
                                }).toString()
                            )}`,
                            undefined,
                            {
                                shallow: false,
                                locale: router.locale
                            }
                        );
                    }
                }
            },
            [product, selectedProduct, router]
        );

        // Set quantity.
        const setQuantity = useCallback(
            (quantity: number) => {
                quantity = parseFloat(quantity as any);

                if (!isNaN(quantity)) {
                    setSelectedProduct({
                        ...selectedProduct,
                        quantity: Math.max(
                            1,
                            Math.min(
                                quantity,
                                selectedProduct.availableQuantity
                            )
                        )
                    });
                }
            },
            [selectedProduct]
        );

        // Add to cart.
        const addToCart = useCallback(() => {
            if (isAddToCartInProgress) {
                return;
            }

            setIsAddToCartInProgress(true);

            (async () => {
                const item: CartItem = {
                    productId: selectedProduct.productId,
                    productSlug: selectedProduct.slug,
                    productImage: (selectedProduct.images as string[])[0],
                    productName: selectedProduct.name,
                    brandName: product.brandName,
                    productCategory: product.categoryName,
                    productStockQuantity: selectedProduct.availableQuantity,
                    productRating: selectedProduct.rating,
                    productReviewCount: selectedProduct.reviewCount,
                    productLink: `/${product.slug}`,
                    isKitProduct: selectedProduct.isKitProduct,
                    price: selectedProduct.salesPrice,
                    unitId: selectedProduct.unitId,
                    unitName: selectedProduct.unitName,
                    quantity: selectedProduct.quantity,
                    weight: selectedProduct.weight,
                    width: selectedProduct.width,
                    height: selectedProduct.height,
                    depth: selectedProduct.depth,
                    volumetricWeight: 0,
                    deliveryType: 'standard',
                    deliveryOptionIds: selectedProduct.deliveryOptionIds ?? [],
                    deliveryPrice: 0,
                    estimatedDeliveryDuration:
                        selectedProduct.estimatedDeliveryDuration,
                    deliveryAtSpecifiedDate:
                        selectedProduct.deliveryAtSpecifiedDate,
                    deliveryAtSpecifiedTime:
                        selectedProduct.deliveryAtSpecifiedTime,
                    selected: true,
                    removed: false
                };

                if (
                    selectedProduct.isPCMProduct &&
                    !!currentPCMPayload &&
                    Object.keys(currentPCMPayload).length > 0
                ) {
                    item.isPCMProduct = true;
                    item.pcmPayload = currentPCMPayload;
                }

                if (
                    Array.isArray(
                        selectedProduct.pcmPayload?.finder?.cart?.items
                    ) &&
                    selectedProduct.pcmPayload.finder.cart.items?.length > 0
                ) {
                    item.subItems =
                        selectedProduct.pcmPayload.finder.cart.items;
                }

                if (
                    !!selectedProduct.attributes &&
                    Object.keys(selectedProduct.attributes).length > 0
                ) {
                    const attributes: {
                        code: string;
                        label: string;
                        value: string;
                        color?: string;
                    }[] = [];

                    for (const code of Object.keys(
                        selectedProduct.attributes
                    )) {
                        const value = selectedProduct.attributes[code];
                        const option = productOptions.find(
                            productOption => productOption.code === code
                        ) as ProductOption;
                        const selection = option.selections.find(
                            selection => selection.value === value
                        );

                        attributes.push({
                            code,
                            label: option.label as string,
                            value,
                            color: selection?.color
                        });
                    }

                    item.productLink = `/${trim(
                        trim(product.slug, '/')
                    )}-${base64.encode(
                        new URLSearchParams({
                            ...selectedProduct.attributes,
                            _es: 'true'
                        }).toString()
                    )}`;

                    item.productAttributes = attributes;
                }

                const inCartProduct = cart.items.find(
                    item => item.productId === selectedProduct.productId
                );

                let result = null;
                if (inCartProduct) {
                    result = await updateCartItem({
                        ...item,
                        quantity: item.quantity + inCartProduct.quantity
                    });
                } else {
                    result = await addCartItem(item);
                }

                if (result) {
                    setQuantity(1);

                    // ---------- Google Tag Manager ----------
                    pushIntoGTMDataLayer({
                        event: 'add_to_cart',
                        data: {
                            currency:
                                currency.name === 'TL' ? 'TRY' : currency.name,
                            value:
                                (selectedProduct.unDiscountedSalesPrice
                                    ? selectedProduct.unDiscountedSalesPrice
                                    : selectedProduct.salesPrice) *
                                selectedProduct.quantity,
                            items: [
                                {
                                    item_id: selectedProduct.code,
                                    item_name: selectedProduct.name,
                                    discount:
                                        selectedProduct.unDiscountedSalesPrice >
                                        0
                                            ? selectedProduct.unDiscountedSalesPrice -
                                              selectedProduct.salesPrice
                                            : 0,
                                    item_brand: selectedProduct.brandName,
                                    item_category: selectedProduct.categoryName,
                                    price: selectedProduct.unDiscountedSalesPrice
                                        ? selectedProduct.unDiscountedSalesPrice
                                        : selectedProduct.salesPrice,
                                    quantity: selectedProduct.quantity
                                }
                            ]
                        }
                    });
                    // ----------------------------------------

                    notification({
                        title: t('Added to Cart'),
                        description: t('Product has been added to your cart.'),
                        status: 'success',
                        detailRenderer: closeNotification => (
                            <ProductCartSummary
                                locale={locale}
                                currency={currency}
                                item={item}
                                onDetail={() => {
                                    closeNotification();

                                    if (isMobile) {
                                        router.push(
                                            `/mobile/my-cart?t=${Date.now()}`
                                        );
                                    } else {
                                        router.push(`/cart?t=${Date.now()}`);
                                    }
                                }}
                            />
                        )
                    });
                }

                setIsAddToCartInProgress(false);
            })();
        }, [
            isAddToCartInProgress,
            selectedProduct.productId,
            selectedProduct.slug,
            selectedProduct.images,
            selectedProduct.name,
            selectedProduct.availableQuantity,
            selectedProduct.rating,
            selectedProduct.reviewCount,
            selectedProduct.salesPrice,
            selectedProduct.unitId,
            selectedProduct.unitName,
            selectedProduct.quantity,
            selectedProduct.weight,
            selectedProduct.width,
            selectedProduct.height,
            selectedProduct.depth,
            selectedProduct.deliveryOptionIds,
            selectedProduct.estimatedDeliveryDuration,
            selectedProduct.deliveryAtSpecifiedDate,
            selectedProduct.deliveryAtSpecifiedTime,
            selectedProduct.isPCMProduct,
            selectedProduct.attributes,
            selectedProduct.code,
            selectedProduct.brandName,
            selectedProduct.categoryName,
            selectedProduct.isKitProduct,
            selectedProduct.pcmPayload,
            product.brandName,
            product.categoryName,
            selectedProduct.unDiscountedSalesPrice,
            product.slug,
            currentPCMPayload,
            addCartItem,
            productOptions,
            setQuantity,
            currency,
            t,
            locale,
            isMobile,
            router,
            updateCartItem,
            cart.items
        ]);

        // Get customer product params.
        const loadProductParamsForCustomer = () => {
            (async () => {
                try {
                    const params = await jsonRequest<CustomerProductPrams>({
                        url: '/api/customers/product-params-for-customer',
                        method: 'POST',
                        data: {
                            productId: selectedProduct.productId
                        }
                    });

                    setCustomerProductParams(params);
                } catch (error) {
                    notification({
                        title: t('Error'),
                        description: t(
                            'We encountered an issue processing your request. Please retry later.'
                        ),
                        status: 'error'
                    });
                }
            })();
        };
        const loadProductParamsForCustomerDebounced = useDebouncedCallback(
            loadProductParamsForCustomer,
            500,
            [selectedProduct, customer]
        );
        useEffect(() => {
            if (customer) {
                if (isInitial.current) {
                    loadProductParamsForCustomer();
                } else {
                    loadProductParamsForCustomerDebounced();
                }
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [selectedProduct, customer]);

        // Last visited products.
        useEffect(() => {
            let lastVisitedProductIds = JSON.parse(
                localStorage.getItem('lastVisitedProductIds') ?? '[]'
            );

            if (!lastVisitedProductIds.includes(product.productId)) {
                lastVisitedProductIds.unshift(product.productId);

                lastVisitedProductIds = lastVisitedProductIds.slice(0, 50);

                localStorage.setItem(
                    'lastVisitedProductIds',
                    JSON.stringify(lastVisitedProductIds)
                );
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [customer]);

        const value: any = useMemo(
            () => ({
                product,
                selectedProduct,
                productOptions,
                seo,
                isAddToCartInProgress,
                availableQuantity,
                inStock,
                customerProductParams,
                activeTab,
                relatedProducts,
                currentPCMPayload,
                campaigns,
                installments,
                comparisonList,
                showComparisonList,

                setProduct,
                setSelectedProduct,
                setProductOptions,
                setAttribute,
                setQuantity,
                addToCart,
                setCustomerProductParams,
                setActiveTab,
                setCurrentPCMPayload,
                refresh,
                addToComparisonList,
                removeFromComparisonList,
                clearComparisonList,
                openComparisonList,
                closeComparisonList
            }),
            [
                product,
                selectedProduct,
                productOptions,
                seo,
                isAddToCartInProgress,
                availableQuantity,
                inStock,
                customerProductParams,
                activeTab,
                relatedProducts,
                currentPCMPayload,
                campaigns,
                installments,
                comparisonList,
                showComparisonList,
                setAttribute,
                setQuantity,
                addToCart,
                refresh,
                addToComparisonList,
                removeFromComparisonList,
                clearComparisonList,
                openComparisonList,
                closeComparisonList
            ]
        );

        return <ProductContext.Provider value={value} {...rest} />;
    });

if (isDev) {
    ProductProvider.displayName = 'ProductProvider';
}
