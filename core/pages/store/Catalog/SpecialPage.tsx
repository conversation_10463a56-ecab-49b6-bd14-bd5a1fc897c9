import {FC, memo, useMemo} from 'react';
import {OrderedGroupedItems, SpecialPageProducts} from '@core/types';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import Collections from '@components/common/Collections';
import FeaturedProductSlider from '@components/common/FeaturedProductSlider';
import MainSlider from '@components/common/MainSlider';
import StorySlider from '@components/common/StorySlider';

type SpecialPageProps = {
    productCatalogMap: SpecialPageProducts;
};

const SpecialPage: FC<SpecialPageProps> = memo(({productCatalogMap}) => {
    const {navigation, slug, navigationItem} = useStore();

    const orderedGroupedItems = useMemo(() => {
        const subItems = navigation.filter(
            item =>
                slug !== undefined &&
                item.slug.startsWith(`${slug}/`) &&
                item.depth === navigationItem?.depth! + 1
        );
        const items: OrderedGroupedItems[] = [];
        let index = 0;

        for (const subItem of subItems) {
            if (subItem.type !== 'product-catalog') {
                const lastItem =
                    items.length > 0 ? items[items.length - 1] : undefined;

                if (typeof lastItem !== 'undefined') {
                    if (lastItem.type === subItem.type) {
                        lastItem.items.push(subItem);
                    } else {
                        items.push({
                            id: `${subItem.type}-${index}`,
                            type: subItem.type,
                            items: [subItem]
                        });
                    }
                } else {
                    items.push({
                        id: `${subItem.type}-${index}`,
                        type: subItem.type,
                        items: [subItem]
                    });
                }
            } else {
                items.push({
                    id: `${subItem.type}-${index}`,
                    type: subItem.type,
                    items: [subItem],
                    products: productCatalogMap[subItem.id]?.products,
                    detailPageLink:
                        productCatalogMap[subItem.id]?.detailPageLink,
                    catalogName: productCatalogMap[subItem.id]?.catalogName
                });
            }

            index++;
        }

        return items;
    }, [navigation, slug, navigationItem?.depth, productCatalogMap]);

    return (
        <>
            {orderedGroupedItems.map(item => {
                if (item.type === 'slide') {
                    return (
                        <MainSlider
                            key={item.id}
                            forSpecialPage
                            items={item.items}
                        />
                    );
                } else if (item.type === 'story') {
                    return (
                        <StorySlider
                            key={item.id}
                            forSpecialPage
                            items={item.items}
                        />
                    );
                } else if (item.type === 'collection') {
                    return (
                        <Collections
                            key={item.id}
                            forSpecialPage
                            items={item.items}
                        />
                    );
                } else if (item.type === 'product-catalog') {
                    return (
                        <FeaturedProductSlider
                            key={item.id}
                            forSpecialPage
                            products={item.products ?? []}
                            detailPageLink={item.detailPageLink ?? ''}
                            productSliderName={item.catalogName ?? ''}
                        />
                    );
                }
            })}
        </>
    );
});

if (isDev) {
    SpecialPage.displayName = 'SpecialPage';
}

export default SpecialPage;
