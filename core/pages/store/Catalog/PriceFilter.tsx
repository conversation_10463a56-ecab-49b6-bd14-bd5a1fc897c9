import {useEffect} from 'react';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {Filter} from '@core/types';
import {useStore, useTrans} from '@core/hooks';
import {SearchIcon} from '@core/icons/outline';
import {UiIconButton, UiInput, UiRadio} from '@core/components/ui';
import useCatalog from './useCatalog';
import {getQuery} from './context';

const PriceFilter = ({filter}: {filter: Filter}) => {
    const {register, watch, setValue, handleSubmit} = useForm();
    const {updateFilters, appliedFilters} = useCatalog();
    const {currency} = useStore();
    const router = useRouter();
    const t = useTrans();

    const minPrice = watch('minPrice');
    const maxPrice = watch('maxPrice');
    const hasAtLeastOnePriceValue = minPrice || maxPrice;

    const filters = appliedFilters.filter(filter => filter.type !== 'price');

    useEffect(() => {
        const query = getQuery(router);

        const prices = query['price'];
        if (typeof prices === 'string') {
            const [minPrice, maxPrice] = prices.trim().split('-');

            if (!isNaN(parseInt(minPrice))) {
                setValue('minPrice', minPrice);
            }
            if (!isNaN(parseInt(maxPrice))) {
                setValue('maxPrice', maxPrice);
            }
        } else {
            setValue('minPrice', '');
            setValue('maxPrice', '');
        }
    }, [router, setValue]);

    function onSubmit(data: Record<string, string>) {
        if (!data.minPrice && !data.maxPrice) return;

        const minPrice = data.minPrice || '*';
        const maxPrice = data.maxPrice || '*';

        updateFilters([
            ...filters,
            {
                type: filter.type,
                label: filter.label,
                field: filter.field,
                isColorAttribute: filter.isColorAttribute,
                selected: {
                    label: `${minPrice} - ${maxPrice}`,
                    value: `${minPrice}-${maxPrice}`
                }
            }
        ]);
    }

    return (
        <>
            <form
                onSubmit={handleSubmit(onSubmit)}
                className="mb-4 flex items-center gap-4"
            >
                <div className="flex items-center gap-2">
                    <UiInput
                        size="xs"
                        placeholder={t('Minimum')}
                        className="font-semibold"
                        maxLength={6}
                        {...register('minPrice', {
                            onChange(e) {
                                const numericValue = e.target.value.replace(
                                    /\D/,
                                    ''
                                );
                                setValue('minPrice', numericValue);
                            }
                        })}
                    />
                    <div>-</div>
                    <UiInput
                        size="xs"
                        placeholder={t('Maximum')}
                        className="font-semibold"
                        maxLength={6}
                        {...register('maxPrice', {
                            onChange(e) {
                                const numericValue = e.target.value.replace(
                                    /\D/,
                                    ''
                                );
                                setValue('maxPrice', numericValue);
                            }
                        })}
                    />
                </div>
                <UiIconButton
                    size="xs"
                    color="primary"
                    variant="solid"
                    type="submit"
                    disabled={!hasAtLeastOnePriceValue}
                    icon={<SearchIcon className="h-4 w-4" />}
                />
            </form>

            <div className="flex flex-col gap-1">
                {filter.items.map(item => (
                    <UiRadio
                        key={item.value}
                        checked={
                            appliedFilters.findIndex(
                                appliedFilter =>
                                    appliedFilter.field === filter.field &&
                                    appliedFilter.selected.value === item.value
                            ) !== -1
                        }
                        onChange={() => {
                            updateFilters([
                                ...filters,
                                {
                                    type: filter.type,
                                    label: filter.label,
                                    field: filter.field,
                                    isColorAttribute: filter.isColorAttribute,
                                    selected: item
                                }
                            ]);
                        }}
                    >
                        <span className="select-none text-sm transition duration-100 hover:text-gray-400">
                            {item.label} {currency.name}
                        </span>
                    </UiRadio>
                ))}
            </div>
        </>
    );
};

export default PriceFilter;
