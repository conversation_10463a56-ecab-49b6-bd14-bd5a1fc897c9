import {memo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {
    Breadcrumb,
    Filter,
    Page,
    ProductListItem,
    SpecialPageProducts
} from '@core/types';
import Breadcrumbs from '@components/common/Breadcrumbs';
import {CatalogProvider} from './context';
import Filters from './Filters';
import LoadingOverlay from './LoadingOverlay';
import Meta from './Meta';
import Products from './Products';
import SpecialPage from './SpecialPage';

type CatalogPageProps = {
    breadcrumbs: Breadcrumb[];
    filters: Filter[];
    search?: string;
    products: ProductListItem[];
    pageNumber?: number;
    hasNextPage: boolean;
    totalProductCountText: string;
    isSpecialPage: boolean;
    productCatalogMap: SpecialPageProducts;
};

const CatalogPage: Page<CatalogPageProps> = memo(props => {
    const {
        breadcrumbs,
        filters,
        search,
        products,
        pageNumber,
        hasNextPage,
        totalProductCountText,
        isSpecialPage,
        productCatalogMap
    } = props;
    const {navigationItem} = useStore();

    return (
        <CatalogProvider
            filters={filters}
            search={search}
            products={products}
            pageNumber={pageNumber}
            hasNextPage={hasNextPage}
            totalProductCountText={totalProductCountText}
        >
            <Meta />

            <div
                className={cls('relative xl:mt-0', {
                    'container mt-4': !isSpecialPage
                })}
            >
                {Array.isArray(breadcrumbs) && breadcrumbs.length > 0 && (
                    <div
                        className={cls('flex items-center', {
                            container: isSpecialPage
                        })}
                    >
                        <Breadcrumbs breadcrumbs={breadcrumbs} />
                    </div>
                )}

                {isSpecialPage && (
                    <SpecialPage productCatalogMap={productCatalogMap} />
                )}

                {!isSpecialPage && (
                    <>
                        <div className="flex pb-12">
                            {filters.length > 0 && <Filters />}

                            <Products />
                        </div>

                        <LoadingOverlay />
                    </>
                )}

                {typeof navigationItem?.content === 'string' &&
                    navigationItem.content.length > 0 && (
                        <div className="container overflow-x-hidden py-12">
                            <div
                                className="prose max-w-full"
                                dangerouslySetInnerHTML={{
                                    __html: navigationItem.content
                                }}
                            />
                        </div>
                    )}
            </div>
        </CatalogProvider>
    );
});

if (isDev) {
    CatalogPage.displayName = 'CatalogPage';
}

CatalogPage.initPageProps = async props => {
    return props;
};

export default CatalogPage;
