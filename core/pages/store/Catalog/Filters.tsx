import {FC, memo, useCallback, useMemo, useState} from 'react';
import {Filter as TFilter} from '@core/types';
import {isDev, toLower, trim} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiCheckbox, UiDisclosure, UiInput} from '@core/components/ui';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/outline';
import useCatalog from './useCatalog';
import PriceFilter from './PriceFilter';

const Filter: FC<{filter: TFilter}> = memo(({filter}) => {
    const t = useTrans();
    const [search, setSearch] = useState('');
    const {appliedFilters, addFilter, removeFilter} = useCatalog();

    const filteredItems = useMemo(
        () =>
            filter.items.filter(
                item =>
                    toLower(item.label).indexOf(toLower(trim(search))) !== -1
            ),
        [filter.items, search]
    );

    const scrollToTop = useCallback(() => {
        const container = document.querySelector('.content-wrapper');
        if (container === null || container === undefined) return;

        container.scrollTo({top: 0, behavior: 'smooth'});
    }, []);

    return (
        <UiDisclosure
            as="div"
            key={filter.label + filter.field}
            defaultOpen={
                filter.type === 'category' ||
                filter.type === 'brand' ||
                filter.type === 'attribute'
            }
            className="border-b border-gray-200 py-5"
        >
            {({open}) => (
                <>
                    <h3 className="-my-3 flow-root">
                        <UiDisclosure.Button className="flex w-full items-center justify-between bg-white py-3 text-sm">
                            <span className="font-semibold">
                                {t(filter.label)}
                            </span>

                            <span className="ml-6 flex items-center text-gray-500">
                                {open ? (
                                    <ChevronUpIcon
                                        className="h-4 w-4"
                                        aria-hidden="true"
                                    />
                                ) : (
                                    <ChevronDownIcon
                                        className="h-4 w-4"
                                        aria-hidden="true"
                                    />
                                )}
                            </span>
                        </UiDisclosure.Button>
                    </h3>

                    <UiDisclosure.Panel className="pt-6">
                        {filter.items.length > 10 && (
                            <div className="pb-3">
                                <UiInput
                                    className="border-gray-100 bg-gray-100 shadow-sm transition focus:bg-white"
                                    size="xs"
                                    placeholder={t(`{artifact} search`, {
                                        artifact: filter.label
                                    })}
                                    onChange={e => setSearch(e.target.value)}
                                />
                            </div>
                        )}

                        <div className="scroller max-h-56 space-y-1 overflow-auto p-1">
                            {filter.type === 'price' ? (
                                <PriceFilter filter={filter} />
                            ) : (
                                filteredItems.map(item => (
                                    <div
                                        key={item.value}
                                        className="group flex items-center"
                                    >
                                        <UiCheckbox
                                            checked={
                                                appliedFilters.findIndex(
                                                    appliedFilter =>
                                                        appliedFilter.field ===
                                                            filter.field &&
                                                        appliedFilter.selected
                                                            .value ===
                                                            item.value
                                                ) !== -1
                                            }
                                            onChange={e => {
                                                scrollToTop();
                                                // @ts-ignore
                                                e.target.checked
                                                    ? addFilter({
                                                          type: filter.type,
                                                          label: filter.label,
                                                          field: filter.field,
                                                          isColorAttribute:
                                                              filter.isColorAttribute,
                                                          selected: item
                                                      })
                                                    : removeFilter({
                                                          type: filter.type,
                                                          label: filter.label,
                                                          field: filter.field,
                                                          isColorAttribute:
                                                              filter.isColorAttribute,
                                                          selected: item
                                                      });
                                            }}
                                        >
                                            {filter.isColorAttribute ? (
                                                <div className="flex items-center">
                                                    <div
                                                        className="relative mr-1.5 h-4 w-4 overflow-hidden rounded border"
                                                        style={{
                                                            backgroundColor:
                                                                item.color
                                                        }}
                                                    >
                                                        {!item.color && (
                                                            <svg
                                                                className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                                                viewBox="0 0 100 100"
                                                                preserveAspectRatio="none"
                                                                stroke="currentColor"
                                                            >
                                                                <line
                                                                    x1={0}
                                                                    y1={100}
                                                                    x2={100}
                                                                    y2={0}
                                                                    vectorEffect="non-scaling-stroke"
                                                                />
                                                            </svg>
                                                        )}
                                                    </div>

                                                    <span className="select-none text-sm transition duration-100 group-hover:text-gray-400">
                                                        {item.label}
                                                    </span>
                                                </div>
                                            ) : (
                                                <span className="select-none text-sm transition duration-100 group-hover:text-gray-400">
                                                    {item.label}
                                                </span>
                                            )}
                                        </UiCheckbox>
                                    </div>
                                ))
                            )}
                        </div>
                    </UiDisclosure.Panel>
                </>
            )}
        </UiDisclosure>
    );
});

if (isDev) {
    Filter.displayName = 'Filter';
}

const Filters: FC = memo(() => {
    const {filters} = useCatalog();

    return (
        <aside className="sticky top-0 mr-8 hidden h-screen w-60 overflow-hidden xl:block">
            <div className="box-content h-screen w-60 overflow-y-scroll pr-4">
                {filters.map(filter => (
                    <Filter key={filter.label + filter.field} filter={filter} />
                ))}
            </div>
        </aside>
    );
});

if (isDev) {
    Filters.displayName = 'Filters';
}

export default Filters;
