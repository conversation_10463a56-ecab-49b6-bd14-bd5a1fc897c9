import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {NextRouter, useRouter} from 'next/router';
import storeConfig from '~/store.config';
import {CustomerProductPrams, Filter, ProductListItem} from '@core/types';
import {
    clone,
    escapeRegExp,
    isDev,
    jsonRequest,
    omit,
    orderBy,
    parseURLSearchParams,
    randomId,
    toLower,
    trim
} from '@core/helpers';
import {useCustomer, useStore, useTrans} from '@core/hooks';
import {notification} from '@core/components/ui';

const sortOptions = [
    {value: 'createdAt|desc', label: 'New products'},
    {value: 'salesPrice|asc', label: 'Price low'},
    {value: 'salesPrice|desc', label: 'Price high'},
    {value: 'salesCount|desc', label: 'Best selling'},
    {value: 'favoritesCount|desc', label: 'Most favorited'},
    {value: 'reviewCount|desc', label: 'Most reviewed'}
];

export type AppliedFilter = Pick<
    Filter,
    'type' | 'field' | 'label' | 'isColorAttribute'
> & {
    selected: {value: string; label: string; color?: string};
};

export type Sort = {
    field: string;
    direction: 'asc' | 'desc';
};

export type CatalogProviderProps = {
    filters: Filter[];
    search?: string;
    products: ProductListItem[];
    pageNumber?: number;
    hasNextPage: boolean;
    totalProductCountText: string;
};

type CatalogContextType = {
    filters: Filter[];
    search?: string;
    appliedFilters: AppliedFilter[];
    products: ProductListItem[];
    productsPerPage: number;
    initialSkip: number;
    sort: Sort;
    pageNumber?: number;
    hasNextPage: boolean;
    totalProductCountText: string;
    isLoading: boolean;
    isLoadingMore: boolean;
    isInitial: boolean;
    seo: Record<string, any>;
    sortOptions: {value: string; label: string}[];

    addFilter: (appliedFilter: AppliedFilter) => void;
    removeFilter: (appliedFilter: AppliedFilter) => void;
    updateFilters: (appliedFilters: AppliedFilter[]) => void;
    clearFilters: () => void;
    updateSort: (sort: Sort) => void;
    loadMore: () => void;
    loadPrevious: () => void;
};

export const CatalogContext = createContext<CatalogContextType>(null as any);

if (isDev) {
    CatalogContext.displayName = 'CatalogContext';
}

function parsePath(router: NextRouter) {
    let path = trim(router.asPath, '/');

    let queryString = undefined;
    if (path.indexOf('?') !== -1) {
        const parts = path.split('?');

        path = parts[0];
        queryString = parts[1];
    }

    return {path: `/${path}`, queryString};
}

export function getQuery(router: NextRouter) {
    const parsed = parsePath(router);
    const query: Record<string, any> = {};

    const searchParams = parseURLSearchParams(parsed.queryString ?? '');
    for (const field of Object.keys(searchParams)) {
        const value = searchParams[field];

        if (field !== 'slug') {
            query[field] = value;
        }
    }

    return query;
}

export const CatalogProvider: FC<PropsWithChildren<CatalogProviderProps>> =
    memo(props => {
        const {
            filters: initialFilters,
            search,
            products: initialProducts,
            hasNextPage: initialHasNextPage,
            totalProductCountText: initialTotalProductCountText,
            pageNumber,
            ...rest
        } = props;
        const router = useRouter();
        const customer = useCustomer();
        const {navigationItem} = useStore();
        const filterIndexes = useRef<Record<string, number>>({});
        const t = useTrans();

        // Get sorted filters.
        const getSortedFilters = (filters: any[]) => {
            const indexes = filterIndexes.current;

            const newFilters = filters.map((filter, i) => {
                const index = indexes[filter.field] ?? i;

                filter.index = indexes[filter.field] = index;

                if (filter.type === 'price') {
                    filter.index = 3;
                }

                return filter;
            });

            filterIndexes.current = indexes;

            return orderBy(newFilters, ['index'], ['asc']).map(filter => {
                delete filter.index;

                return filter;
            });
        };
        const [filters, setFilters] = useState<Filter[]>(() =>
            getSortedFilters(initialFilters)
        );
        const [appliedFilters, setAppliedFilters] = useState<AppliedFilter[]>(
            () => {
                const currentFilters: AppliedFilter[] = [];
                const query = getQuery(router);

                for (const key of Object.keys(omit(query, 'query'))) {
                    const filter = filters.find(filter => filter.field === key);
                    if (typeof filter !== 'object') {
                        continue;
                    }

                    const values = (
                        Array.isArray(query[key]) ? query[key] : [query[key]]
                    ) as string[];

                    for (const value of values) {
                        if (filter.type === 'price') {
                            currentFilters.push({
                                type: filter.type,
                                field: filter.field,
                                label: filter.label,
                                isColorAttribute: filter.isColorAttribute,
                                selected: {
                                    label: value,
                                    value: value
                                }
                            });
                        } else {
                            const item = filter.items.find(
                                item => item.value === value
                            );

                            if (typeof item !== 'object') {
                                continue;
                            }

                            currentFilters.push({
                                type: filter.type,
                                field: filter.field,
                                label: filter.label,
                                isColorAttribute: filter.isColorAttribute,
                                selected: {
                                    label: item.label,
                                    value: item.value,
                                    color: item.color
                                }
                            });
                        }
                    }
                }

                return currentFilters;
            }
        );
        const [sort, setSort] = useState(() => {
            const query = getQuery(router);
            const field = storeConfig.catalog.productSort
                ? Object.keys(storeConfig.catalog.productSort)[0]
                : 'createdAt';
            let s = {
                field,
                direction: 'desc'
            };

            if (Object.keys(omit(query, 'query')).length > 0) {
                const querySortStr = query['sort'];

                if (typeof querySortStr === 'string') {
                    const parts = querySortStr.split('|');

                    if (parts.length > 1) {
                        const field = parts[0];
                        const direction = parts[1];

                        if (direction === 'asc' || direction === 'desc') {
                            s = {field, direction};
                        }
                    }
                }
            }

            return s;
        });

        // General.
        const [products, setProducts] = useState(() => {
            const query = getQuery(router);

            if (Object.keys(omit(query, 'query')).length > 0) {
                return [];
            }

            return initialProducts;
        });
        const [hasNextPage, setHasNextPage] = useState(initialHasNextPage);
        const [totalProductCountText, setTotalProductCountText] = useState(
            initialTotalProductCountText
        );
        const [isLoading, setIsLoading] = useState(false);
        const inProgress = useRef(false);
        const loadMoreRequested = useRef(false);
        const [isLoadingMore, setIsLoadingMore] = useState(false);
        const isInitial = useRef(true);
        const limit = useRef(storeConfig.catalog.productsPerPage ?? 48);
        const initialSkip = useRef(
            (() => {
                const query = getQuery(router);

                if (typeof query.page === 'string') {
                    const page = parseInt(query.page, 10);

                    if (!isNaN(page)) {
                        return limit.current * (page - 1);
                    }
                }

                if (typeof pageNumber === 'number') {
                    return limit.current * (pageNumber - 1);
                }

                return 0;
            })()
        );
        const skip = useRef(0);

        // Is first load. This is required for no data content.
        const [isInitialReactive, setIsInitialReactive] = useState(true);
        useEffect(() => {
            if (isInitial.current) {
                setIsInitialReactive(false);
            }
        }, []);

        // Get initial customer params for products.
        const isInitialCustomerParamsEvaluated = useRef(false);
        useEffect(() => {
            if (
                products.length > 0 &&
                typeof customer !== 'undefined' &&
                !isInitialCustomerParamsEvaluated.current &&
                !isInitialReactive
            ) {
                isInitialCustomerParamsEvaluated.current = true;

                (async () => {
                    try {
                        const params = await jsonRequest<
                            CustomerProductPrams[]
                        >({
                            url: '/api/customers/product-params-for-customer',
                            method: 'POST',
                            data: {
                                customerId: customer?.id,
                                productId: products.map(
                                    product => product.productId
                                )
                            }
                        });

                        const newProducts = clone(products).map(product => {
                            const productParams = params.find(
                                productParams =>
                                    productParams.productId ===
                                        product.productId ||
                                    productParams.mainProductId ===
                                        product.productId
                            );
                            if (typeof productParams !== 'undefined') {
                                product.isFavorite = productParams.isFavorite;
                            }

                            return product;
                        });

                        setProducts(newProducts);
                    } catch (error) {
                        notification({
                            title: t('Error'),
                            description: t(
                                'We encountered an issue processing your request. Please retry later.'
                            ),
                            status: 'error'
                        });
                    }
                })();
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [customer, products, isInitialReactive]);

        // Get SEO params.
        const seo = useMemo(() => {
            const seo: Record<string, any> = {};

            seo.title = navigationItem?.seoTitle ?? navigationItem?.name;
            seo.description = navigationItem?.seoDescription;

            let currentPage = 1;
            if (typeof pageNumber !== 'number') {
                if (typeof router.query.page === 'string') {
                    currentPage = parseInt(router.query.page);
                }
            } else {
                currentPage = pageNumber;
            }

            let path = trim(router.asPath.split('?')[0], '/');
            if (storeConfig.defaultLocale !== router.locale) {
                path = `${router.locale}/${path}`;
            }
            if (path.includes('/page/')) {
                path = path.split('/page/')[0];
            }
            const baseUrl = trim(process.env.NEXT_PUBLIC_SITE_URL, '/');
            if (hasNextPage) {
                seo.nextUrl = `${baseUrl}/${path}?page=${currentPage + 1}`;
            }
            if (currentPage > 1) {
                let cp = currentPage - 1;

                if (cp > 1) {
                    seo.prevUrl = `${baseUrl}/${path}?page=${cp}`;
                } else {
                    seo.prevUrl = `${baseUrl}/${path}`;
                }

                seo.canonical = `${baseUrl}/${path}?page=${currentPage}`;
            } else {
                seo.canonical = `${baseUrl}/${path}`;
            }

            return seo;
        }, [navigationItem, router, pageNumber, hasNextPage]);

        // Prepare extra query.
        const extraQuery: Record<string, any> = useMemo(() => {
            const query: Record<string, any> = {$and: []};
            const categoryPaths: string[] = [];
            const brandIds: string[] = [];
            const dynamicFilters: {field: string; values: string[]}[] = [];

            for (const filter of appliedFilters) {
                if (filter.type === 'price') {
                    const prices = filter.selected.value?.trim()?.split('-');

                    if (Array.isArray(prices) && prices.length === 2) {
                        const minPrice = parseInt(prices[0]);
                        const maxPrice = parseInt(prices[1]);

                        const priceRange: Record<string, number> = {};

                        if (!isNaN(minPrice)) {
                            priceRange.$gte = minPrice;
                        }
                        if (!isNaN(maxPrice)) {
                            priceRange.$lte = maxPrice;
                        }

                        query.$and.push({
                            $or: [
                                {
                                    $and: [
                                        {discountedSalesPrice: {$ne: null}},
                                        {discountedSalesPrice: priceRange}
                                    ]
                                },
                                {
                                    $and: [
                                        {discountedSalesPrice: null},
                                        {salesPrice: priceRange}
                                    ]
                                }
                            ]
                        });
                    }
                } else if (filter.type === 'category') {
                    if (!categoryPaths.includes(filter.selected.value)) {
                        categoryPaths.push(filter.selected.value);
                    }
                } else if (filter.type === 'brand') {
                    if (!brandIds.includes(filter.selected.value)) {
                        brandIds.push(filter.selected.value);
                    }
                } else if (
                    filter.type === 'attribute' ||
                    filter.type === 'feature'
                ) {
                    const index = dynamicFilters.findIndex(
                        dynamicFilter => dynamicFilter.field === filter.field
                    );

                    if (index !== -1) {
                        if (
                            !dynamicFilters[index].values.includes(
                                filter.selected.value
                            )
                        ) {
                            dynamicFilters[index].values.push(
                                filter.selected.value
                            );
                        }
                    } else {
                        dynamicFilters.push({
                            field: filter.field,
                            values: [filter.selected.value]
                        });
                    }
                }
            }
            if (categoryPaths.length > 0) {
                let subQuery: Record<string, any> = {$or: []};

                for (const categoryPath of categoryPaths) {
                    subQuery.$or.push({
                        categoryPath: {
                            $regex: `^${toLower(escapeRegExp(categoryPath))}`,
                            $options: 'i'
                        }
                    });
                }

                if (subQuery.$or.length === 1) {
                    subQuery = subQuery.$or[0];
                }

                query.$and.push(subQuery);
            }
            if (brandIds.length > 0) {
                query.$and.push({
                    brandId: {$in: brandIds}
                });
            }
            if (dynamicFilters.length > 0) {
                for (const dynamicFilter of dynamicFilters) {
                    const subQuery: Record<string, any> = {$or: []};

                    subQuery.$or.push({
                        [dynamicFilter.field]: {$in: dynamicFilter.values}
                    });
                    subQuery.$or.push({
                        [`variants.${dynamicFilter.field}`]: {
                            $in: dynamicFilter.values
                        }
                    });

                    query.$and.push(subQuery);
                }
            }
            if (query.$and.length < 1) {
                delete query.$and;
            }

            return query;
        }, [appliedFilters]);

        // Get selected variant attributes.
        const selectedAttributes: Record<string, any> = useMemo(() => {
            const attributes: Record<string, any> = {};

            for (const filter of appliedFilters) {
                if (filter.type !== 'attribute') {
                    continue;
                }

                const field = filter.field.replace('attributes.', '');

                if (!!attributes[field]) {
                    attributes[field].push(filter.selected.value);
                } else {
                    attributes[field] = [filter.selected.value];
                }
            }

            return attributes;
        }, [appliedFilters]);

        // Watch applied filters and sort.
        const load = () => {
            const query = getQuery(router);

            if (
                isInitial.current &&
                Object.keys(omit(query, 'query')).length < 1
            ) {
                isInitial.current = false;
                return;
            }

            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            setIsLoading(true);
            skip.current = 0;
            setProducts(
                [...Array(limit.current)].map(
                    () =>
                        ({
                            productId: randomId(16),
                            isFake: true
                        } as any)
                )
            );

            const contentWrapper = document.querySelector(
                '.content-wrapper.overflow-y-auto'
            );
            if (!!contentWrapper) {
                contentWrapper.scrollTo({top: 0, behavior: 'smooth'});
            }

            (async () => {
                const result = await jsonRequest({
                    url: '/api/catalog/products',
                    method: 'POST',
                    data: {
                        categoryPaths:
                            navigationItem?.productCategoryPaths ?? [],
                        groupIds: navigationItem?.productGroupIds ?? [],
                        brandIds: navigationItem?.productBrandIds ?? [],
                        set: navigationItem?.productSet ?? null,
                        tagIds: navigationItem?.productTagIds ?? [],
                        advancedFilters:
                            navigationItem?.advancedFilters ?? null,
                        selectedAttributes,
                        extraQuery,
                        search,
                        skip: initialSkip.current,
                        limit: limit.current,
                        sort,
                        paginated: true
                    }
                });

                const filtersResult: Filter[] = await jsonRequest({
                    url: '/api/catalog/filters',
                    method: 'POST',
                    data: {
                        categoryPaths:
                            navigationItem?.productCategoryPaths ?? [],
                        groupIds: navigationItem?.productGroupIds ?? [],
                        brandIds: navigationItem?.productBrandIds ?? [],
                        set: navigationItem?.productSet ?? null,
                        extraQuery,
                        search
                    }
                });
                setFilters(previousFilters => {
                    const selectedFilters = previousFilters.filter(
                        previousFilter =>
                            appliedFilters.findIndex(
                                appliedFilter =>
                                    appliedFilter.field === previousFilter.field
                            ) !== -1
                    );
                    const newFilters = filtersResult.filter(
                        filter =>
                            appliedFilters.findIndex(
                                appliedFilter =>
                                    appliedFilter.field === filter.field
                            ) === -1
                    );

                    return getSortedFilters(selectedFilters.concat(newFilters));
                });

                setProducts(result.products);
                setHasNextPage(result.hasNextPage);
                setTotalProductCountText(result.totalProductCountText);
                setIsLoading(false);
                inProgress.current = false;
                isInitial.current = false;
            })();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
        useEffect(load, [navigationItem, extraQuery, sort, selectedAttributes]);

        // Context methods.
        const updateFilters = useCallback(
            (newAppliedFilters: AppliedFilter[]) => {
                (async () => {
                    setIsLoading(true);

                    try {
                        const {path, queryString} = parsePath(router);
                        const originalQ = parseURLSearchParams(
                            queryString ?? ''
                        );
                        const q: Record<string, any> = {};
                        const sort = originalQ['sort'];

                        if (!!originalQ.query) {
                            q['query'] = originalQ.query;
                        }

                        for (const appliedFilter of newAppliedFilters) {
                            if (Array.isArray(q[appliedFilter.field])) {
                                (q[appliedFilter.field] as string[]).push(
                                    appliedFilter.selected.value
                                );
                            } else if (
                                typeof q[appliedFilter.field] !== 'undefined'
                            ) {
                                q[appliedFilter.field] = [
                                    q[appliedFilter.field] as string,
                                    appliedFilter.selected.value
                                ];
                            } else {
                                q[appliedFilter.field] =
                                    appliedFilter.selected.value;
                            }
                        }
                        if (!!sort) {
                            q['sort'] = sort;
                        }

                        await router.replace(
                            `${path}?${new URLSearchParams(q).toString()}`,
                            undefined,
                            {
                                shallow: true,
                                locale: router.locale
                            }
                        );
                    } catch (error) {}

                    setAppliedFilters(() =>
                        getSortedFilters(newAppliedFilters)
                    );
                })();
            },
            [router]
        );
        const addFilter = useCallback(
            (appliedFilter: AppliedFilter) => {
                (async () => {
                    setIsLoading(true);

                    try {
                        const {path, queryString} = parsePath(router);
                        const q = parseURLSearchParams(queryString ?? '');

                        if (Array.isArray(q[appliedFilter.field])) {
                            (q[appliedFilter.field] as string[]).push(
                                appliedFilter.selected.value
                            );
                        } else if (
                            typeof q[appliedFilter.field] !== 'undefined'
                        ) {
                            q[appliedFilter.field] = [
                                q[appliedFilter.field] as string,
                                appliedFilter.selected.value
                            ];
                        } else {
                            q[appliedFilter.field] =
                                appliedFilter.selected.value;
                        }

                        delete q['page'];

                        await router.replace(
                            `${path}?${new URLSearchParams(q).toString()}`,
                            undefined,
                            {
                                shallow: true,
                                locale: router.locale
                            }
                        );
                    } catch (error) {}

                    setAppliedFilters(currentAppliedFilters =>
                        getSortedFilters(
                            currentAppliedFilters.concat(appliedFilter)
                        )
                    );
                })();
            },
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [router]
        );
        const removeFilter = useCallback(
            (appliedFilter: AppliedFilter) => {
                (async () => {
                    setIsLoading(true);

                    try {
                        const {path, queryString} = parsePath(router);
                        const q = parseURLSearchParams(queryString ?? '');

                        if (Array.isArray(q[appliedFilter.field])) {
                            q[appliedFilter.field] = (
                                q[appliedFilter.field] as string[]
                            ).filter(s => s !== appliedFilter.selected.value);
                        } else if (
                            typeof q[appliedFilter.field] !== 'undefined'
                        ) {
                            delete q[appliedFilter.field];
                        }

                        delete q['page'];

                        if (Object.keys(q).length > 0) {
                            await router.replace(
                                `${path}?${new URLSearchParams(q).toString()}`,
                                undefined,
                                {
                                    shallow: true,
                                    locale: router.locale
                                }
                            );
                        } else {
                            await router.replace(path, undefined, {
                                shallow: true,
                                locale: router.locale
                            });
                        }
                    } catch (error) {}

                    setAppliedFilters(currentAppliedFilters =>
                        getSortedFilters(
                            currentAppliedFilters.filter(
                                currentAppliedFilter =>
                                    !(
                                        currentAppliedFilter.field ===
                                            appliedFilter.field &&
                                        currentAppliedFilter.selected.value ===
                                            appliedFilter.selected.value
                                    )
                            )
                        )
                    );
                })();
            },
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [router]
        );
        const clearFilters = useCallback(() => {
            (async () => {
                setIsLoading(true);

                try {
                    const {path, queryString} = parsePath(router);
                    const q = parseURLSearchParams(queryString ?? '');
                    const sort = q['sort'];
                    const query = q['query'];

                    delete q['page'];

                    if (!!query) {
                        if (!!sort) {
                            await router.replace(
                                `${path}?${new URLSearchParams({
                                    query,
                                    sort
                                }).toString()}`,
                                undefined,
                                {
                                    shallow: true,
                                    locale: router.locale
                                }
                            );
                        } else {
                            await router.replace(
                                `${path}?${new URLSearchParams({
                                    query
                                }).toString()}`,
                                undefined,
                                {
                                    shallow: true,
                                    locale: router.locale
                                }
                            );
                        }
                    } else {
                        if (!!sort) {
                            await router.replace(
                                `${path}?${new URLSearchParams({
                                    sort
                                }).toString()}`,
                                undefined,
                                {
                                    shallow: true,
                                    locale: router.locale
                                }
                            );
                        } else {
                            await router.replace(path, undefined, {
                                shallow: true,
                                locale: router.locale
                            });
                        }
                    }
                } catch (error) {}

                setAppliedFilters([]);
            })();
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [router]);
        const updateSort = useCallback(
            (sort: Sort) => {
                setIsLoading(true);

                (async () => {
                    try {
                        const {path, queryString} = parsePath(router);
                        const q = parseURLSearchParams(queryString ?? '');

                        q['sort'] = `${sort.field}|${sort.direction}`;

                        delete q['page'];

                        await router.replace(
                            `${path}?${new URLSearchParams(q).toString()}`,
                            undefined,
                            {
                                shallow: true,
                                locale: router.locale
                            }
                        );
                    } catch (error) {}

                    setSort(sort);
                })();
            },
            [router]
        );
        const loadMore = useCallback(() => {
            if (inProgress.current) {
                loadMoreRequested.current = true;

                return;
            }

            function load() {
                inProgress.current = true;
                setIsLoadingMore(true);
                skip.current += limit.current;

                setProducts(currentProducts =>
                    currentProducts.concat(
                        [...Array(limit.current)].map(
                            () =>
                                ({
                                    productId: randomId(16),
                                    isFake: true
                                } as any)
                        )
                    )
                );

                (async () => {
                    const result = await jsonRequest({
                        url: '/api/catalog/products',
                        method: 'POST',
                        data: {
                            categoryPaths:
                                navigationItem?.productCategoryPaths ?? [],
                            groupIds: navigationItem?.productGroupIds ?? [],
                            brandIds: navigationItem?.productBrandIds ?? [],
                            set: navigationItem?.productSet ?? null,
                            tagIds: navigationItem?.productTagIds ?? [],
                            advancedFilters:
                                navigationItem?.advancedFilters ?? null,
                            selectedAttributes,
                            extraQuery,
                            search,
                            skip: initialSkip.current + skip.current,
                            limit: limit.current,
                            sort,
                            paginated: true
                        }
                    });

                    // if (Object.keys(selectedAttributes).length > 0) {
                    //     result.products = result.products.map(
                    //         (product: ProductListItem) => {
                    //             product.link = `/${trim(
                    //                 trim(product.slug, '/')
                    //             )}-${base64.encode(
                    //                 new URLSearchParams({
                    //                     ...selectedAttributes,
                    //                     _es: 'true'
                    //                 }).toString()
                    //             )}`;
                    //
                    //             return product;
                    //         }
                    //     );
                    // }

                    setProducts(currentProducts => {
                        currentProducts = currentProducts.filter(
                            // @ts-ignore
                            currentProduct => !currentProduct.isFake
                        );

                        return currentProducts.concat(result.products);
                    });
                    setHasNextPage(result.hasNextPage);
                    setIsLoadingMore(false);
                    inProgress.current = false;

                    try {
                        const {path, queryString} = parsePath(router);
                        const q = parseURLSearchParams(queryString ?? '');
                        const page = skip.current / limit.current + 1;

                        if (page > 1) {
                            q['page'] = page.toString();
                        } else {
                            delete q['page'];
                        }

                        if (Object.keys(q).length > 0) {
                            await router.replace(
                                `${path}?${new URLSearchParams(q).toString()}`,
                                undefined,
                                {
                                    shallow: true,
                                    locale: router.locale
                                }
                            );
                        } else {
                            await router.replace(path, undefined, {
                                shallow: true,
                                locale: router.locale
                            });
                        }
                    } catch (error) {}

                    if (loadMoreRequested.current) {
                        loadMoreRequested.current = false;

                        if (result.hasNextPage) {
                            load();
                        }
                    }
                })();
            }

            load();
        }, [
            search,
            router,
            navigationItem,
            extraQuery,
            sort,
            selectedAttributes
        ]);
        const loadPrevious = useCallback(() => {
            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            skip.current = 0;
            initialSkip.current -= limit.current;

            if (initialSkip.current < 0) {
                initialSkip.current = 0;

                return;
            }

            setProducts(currentProducts =>
                [...Array(limit.current)]
                    .map(
                        () =>
                            ({
                                productId: randomId(16),
                                isFake: true
                            } as any)
                    )
                    .concat(currentProducts)
            );

            (async () => {
                const result = await jsonRequest({
                    url: '/api/catalog/products',
                    method: 'POST',
                    data: {
                        categoryPaths:
                            navigationItem?.productCategoryPaths ?? [],
                        groupIds: navigationItem?.productGroupIds ?? [],
                        brandIds: navigationItem?.productBrandIds ?? [],
                        set: navigationItem?.productSet ?? null,
                        tagIds: navigationItem?.productTagIds ?? [],
                        advancedFilters:
                            navigationItem?.advancedFilters ?? null,
                        selectedAttributes,
                        extraQuery,
                        search,
                        skip: initialSkip.current + skip.current,
                        limit: limit.current,
                        sort,
                        paginated: true
                    }
                });

                // if (Object.keys(selectedAttributes).length > 0) {
                //     result.products = result.products.map(
                //         (product: ProductListItem) => {
                //             product.link = `/${trim(
                //                 trim(product.slug, '/')
                //             )}-${base64.encode(
                //                 new URLSearchParams({
                //                     ...selectedAttributes,
                //                     _es: 'true'
                //                 }).toString()
                //             )}`;
                //
                //             return product;
                //         }
                //     );
                // }

                setProducts(currentProducts => {
                    currentProducts = currentProducts.filter(
                        // @ts-ignore
                        currentProduct => !currentProduct.isFake
                    );

                    return result.products.concat(currentProducts);
                });
                setHasNextPage(result.hasNextPage);
                inProgress.current = false;

                try {
                    const {path, queryString} = parsePath(router);
                    const q = parseURLSearchParams(queryString ?? '');
                    const page = skip.current / limit.current - 1;

                    if (page > 1) {
                        q['page'] = page.toString();
                    } else {
                        delete q['page'];
                    }

                    if (Object.keys(q).length > 0) {
                        await router.replace(
                            `${path}?${new URLSearchParams(q).toString()}`,
                            undefined,
                            {
                                shallow: true,
                                locale: router.locale
                            }
                        );
                    } else {
                        await router.replace(path, undefined, {
                            shallow: true,
                            locale: router.locale
                        });
                    }
                } catch (error) {}
            })();
        }, [
            search,
            router,
            navigationItem,
            extraQuery,
            sort,
            selectedAttributes
        ]);

        const value: any = useMemo(
            () => ({
                filters,
                search,
                appliedFilters,
                products,
                productsPerPage: limit.current,
                initialSkip: initialSkip.current,
                sort,
                hasNextPage,
                totalProductCountText,
                isLoading,
                isLoadingMore,
                isInitial: isInitialReactive,
                seo,
                sortOptions,

                addFilter,
                removeFilter,
                updateFilters,
                clearFilters,
                updateSort,
                loadMore,
                loadPrevious
            }),
            [
                addFilter,
                appliedFilters,
                clearFilters,
                filters,
                search,
                hasNextPage,
                isInitialReactive,
                isLoading,
                isLoadingMore,
                loadMore,
                loadPrevious,
                products,
                removeFilter,
                seo,
                sort,
                totalProductCountText,
                updateFilters,
                updateSort
            ]
        );

        return <CatalogContext.Provider value={value} {...rest} />;
    });

if (isDev) {
    CatalogProvider.displayName = 'CatalogProvider';
}
