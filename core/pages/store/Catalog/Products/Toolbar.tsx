import {FC, Fragment, memo, useEffect, useMemo, useRef, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiListBox, UiTransition} from '@core/components/ui';
import {ChevronDownIcon, ChevronUpIcon, XIcon} from '@core/icons/outline';
import useCatalog from '@core/pages/store/Catalog/useCatalog';

const Toolbar: FC = memo(() => {
    const {navigationItem, currency} = useStore();
    const t = useTrans();
    const {
        sortOptions,
        sort,
        search,
        updateSort,
        appliedFilters,
        removeFilter,
        clearFilters
    } = useCatalog();

    const [selectedSort, setSelectedSort] = useState<string>(
        typeof sort === 'object'
            ? `${sort.field}|${sort.direction}`
            : sortOptions[0].value
    );
    const selectedSortLabel = useMemo(
        () => sortOptions.find(option => option.value === selectedSort)?.label,
        [selectedSort, sortOptions]
    );
    const isInitial = useRef(true);

    useEffect(() => {
        if (isInitial.current) {
            isInitial.current = false;

            return;
        }

        const [field, direction] = selectedSort.split('|');

        updateSort({field, direction: direction === 'desc' ? 'desc' : 'asc'});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedSort]);

    return (
        <div className="mb-4 hidden w-full border-b border-gray-200 py-4 xl:block">
            <div className="flex items-center justify-between">
                <div
                    className="text-sm font-bold"
                    dangerouslySetInnerHTML={{
                        __html: !!search
                            ? t('Showing results for "{search}" search.', {
                                  search
                              }).replace(
                                  `"${search}"`,
                                  `&#8220;<h1 class="inline">${search}</h1>&#8221;`
                              )
                            : !!navigationItem && navigationItem.name
                            ? t('Showing results for "{sectionName}".', {
                                  sectionName: navigationItem.name
                              }).replace(
                                  `"${navigationItem.name}"`,
                                  `&#8220;<h1 class="inline">${navigationItem.name}</h1>&#8221;`
                              )
                            : t('Showing results.')
                    }}
                ></div>

                {!!navigationItem && !navigationItem.productSet && (
                    <div>
                        <UiListBox
                            value={selectedSort}
                            onChange={setSelectedSort}
                            as="div"
                            className="relative space-y-1"
                            style={{minWidth: '210px'}}
                        >
                            {({open}) => (
                                <>
                                    <UiListBox.Button
                                        className={cls(
                                            'relative inline-flex h-8 w-full cursor-pointer appearance-none items-center rounded border border-gray-100 bg-gray-100 px-3 py-0 pr-6 text-sm text-default shadow-sm transition hover:border-gray-300 focus:outline-none',
                                            {
                                                'border-primary-600 !bg-white ring-1 ring-primary-600':
                                                    open
                                            }
                                        )}
                                    >
                                        {!selectedSort && (
                                            <span className="truncate text-sm text-muted">
                                                {t('Choose a sort criteria.')}
                                            </span>
                                        )}
                                        {!!selectedSort && (
                                            <span className="truncate text-sm">
                                                {t(selectedSortLabel as string)}
                                            </span>
                                        )}
                                        <span className="pointer-events-none absolute right-2 ml-3 flex items-center">
                                            {open ? (
                                                <ChevronUpIcon
                                                    className="h-3 w-3 text-primary-600"
                                                    aria-hidden="true"
                                                />
                                            ) : (
                                                <ChevronDownIcon
                                                    className="h-3 w-3 text-muted"
                                                    aria-hidden="true"
                                                />
                                            )}
                                        </span>
                                    </UiListBox.Button>

                                    <UiTransition
                                        show={open}
                                        as={Fragment}
                                        enter="transition"
                                        enterFrom="transform scale-95 opacity-0"
                                        enterTo="transform scale-100 opacity-100"
                                        leave="transition"
                                        leaveFrom="transform scale-100 opacity-100"
                                        leaveTo="transform scale-95 opacity-0"
                                    >
                                        <UiListBox.Options
                                            static
                                            className="absolute left-0 z-40 mt-2 max-h-64 w-full origin-top-left overflow-auto rounded border border-gray-200 bg-white p-1.5 shadow-sm outline-none"
                                        >
                                            {sortOptions.map(option => (
                                                <UiListBox.Option
                                                    className="relative"
                                                    key={option.value}
                                                    value={option.value}
                                                >
                                                    {({
                                                        active,
                                                        selected,
                                                        disabled
                                                    }) => (
                                                        <button
                                                            disabled={disabled}
                                                            aria-disabled={
                                                                disabled
                                                            }
                                                            className={cls(
                                                                'flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal focus:outline-none',
                                                                active &&
                                                                    'bg-gray-100'
                                                            )}
                                                        >
                                                            <span
                                                                className={cls(
                                                                    'block flex-1 truncate',
                                                                    selected
                                                                        ? 'font-medium'
                                                                        : 'font-normal'
                                                                )}
                                                            >
                                                                {t(
                                                                    option.label
                                                                )}
                                                            </span>
                                                            {selected && (
                                                                <span
                                                                    className="absolute -left-1 h-6 rounded-full bg-primary-600"
                                                                    style={{
                                                                        width: 2
                                                                    }}
                                                                ></span>
                                                            )}
                                                        </button>
                                                    )}
                                                </UiListBox.Option>
                                            ))}
                                        </UiListBox.Options>
                                    </UiTransition>
                                </>
                            )}
                        </UiListBox>
                    </div>
                )}
            </div>

            {appliedFilters.length > 0 && (
                <div className="flex w-full flex-wrap gap-y-2 space-x-3 pt-3">
                    {appliedFilters.map(appliedFilter => (
                        <div
                            key={`${appliedFilter.field}-${appliedFilter.selected.value}`}
                            className="
                            flex h-8 items-center rounded border border-gray-200 bg-white px-3
                            text-xs font-medium leading-3
                            "
                        >
                            <div className="flex items-center">
                                {appliedFilter.isColorAttribute ? (
                                    <div className="flex items-center">
                                        <div
                                            className="relative mr-1 h-3.5 w-3.5 overflow-hidden rounded border"
                                            style={{
                                                backgroundColor:
                                                    appliedFilter.selected.color
                                            }}
                                        >
                                            {!appliedFilter.selected.color && (
                                                <svg
                                                    className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                                    viewBox="0 0 100 100"
                                                    preserveAspectRatio="none"
                                                    stroke="currentColor"
                                                >
                                                    <line
                                                        x1={0}
                                                        y1={100}
                                                        x2={100}
                                                        y2={0}
                                                        vectorEffect="non-scaling-stroke"
                                                    />
                                                </svg>
                                            )}
                                        </div>

                                        {appliedFilter.selected.label}
                                    </div>
                                ) : (
                                    `${appliedFilter.selected.label} ${
                                        appliedFilter.type === 'price'
                                            ? currency.name
                                            : ''
                                    }`
                                )}
                            </div>

                            <button
                                className="group ml-2.5 flex h-[18px] w-[18px] items-center justify-center rounded-full bg-gray-200 transition duration-100 hover:bg-primary-600"
                                onClick={() => removeFilter(appliedFilter)}
                            >
                                <XIcon
                                    className="text-gray-800 transition duration-100 group-hover:text-white"
                                    width={12}
                                    height={12}
                                />
                            </button>
                        </div>
                    ))}

                    <button
                        onClick={clearFilters}
                        className="flex h-8 items-center rounded border border-gray-200 bg-gray-100 px-3 text-xs font-medium leading-3 transition duration-100 hover:bg-gray-200"
                    >
                        {t('Clear All Filters')}
                    </button>
                </div>
            )}
        </div>
    );
});

if (isDev) {
    Toolbar.displayName = 'Toolbar';
}

export default Toolbar;
