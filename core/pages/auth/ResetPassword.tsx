import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import ResetPassword from '@core/components/auth/ResetPassword';
import Seo from '@components/common/Seo';

type ResetPasswordProps = {
    token: string;
};

const ResetPasswordPage: FC<ResetPasswordProps> = memo(props => {
    const {token} = props;
    const t = useTrans();

    return (
        <>
            <Seo title={t('Reset Your Password')} />
            <div className="flex h-full w-full items-center justify-center ">
                <div className="relative flex w-full flex-col justify-center bg-white p-8 xl:w-[28rem] xl:rounded xl:p-16 xl:shadow-md">
                    <h2 className="mb-4 text-center text-2xl font-medium text-gray-600">
                        {t('Reset Your Password')}
                    </h2>

                    <p className="mb-8 text-center text-sm text-gray-800">
                        {t(
                            'Please enter your new password and password confirmation.'
                        )}
                    </p>

                    <ResetPassword token={token} />
                </div>
            </div>
        </>
    );
});

if (isDev) {
    ResetPasswordPage.displayName = 'ResetPasswordPage';
}

export default ResetPasswordPage;
