import {FC, memo, useCallback, useState} from 'react';
import {useRouter} from 'next/router';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import ForgotPassword from '@core/components/auth/ForgotPassword';
import SignIn from '@core/components/auth/SignIn';
import SignUp from '@core/components/auth/SignUp';
import {UiButton, UiTab} from '@core/components/ui';
import {ArrowLeftIcon, ChevronRightIcon} from '@core/icons/outline';
import Seo from '@components/common/Seo';

type AuthProps = {
    termsOfMembershipText?: string;
    clarificationText?: string;
    redirect?: string;
};

const Auth: FC<AuthProps> = memo(props => {
    const {termsOfMembershipText, clarificationText, redirect} = props;
    const [activeTab, setActiveTab] = useState('signIn');
    const [isForgotPasswordShown, setIsForgotPasswordShown] = useState(false);

    const t = useTrans();
    const router = useRouter();

    const isGuestCheckoutEnabled = router.asPath.includes('checkout');

    const redirectToGuestCheckout = useCallback(() => {
        router.push(`/checkout?t=${Date.now()}`);
    }, [router]);

    return (
        <>
            <Seo title={activeTab === 'signIn' ? t('Sign In') : t('Sign Up')} />
            <div className="flex h-full w-full items-center justify-center">
                <div className="relative flex w-full flex-col justify-center bg-white p-8 xl:w-[28rem] xl:rounded xl:p-16 xl:shadow-md">
                    {!isForgotPasswordShown ? (
                        <>
                            {activeTab === 'signIn' ? (
                                <>
                                    <h2 className="mb-4 text-center text-2xl font-medium text-gray-600">
                                        {t('Sign In')}
                                    </h2>

                                    <p className="mb-4 text-center text-sm text-gray-800">
                                        {t(
                                            'Please sign in using your e-mail address and password.'
                                        )}
                                    </p>

                                    {isGuestCheckoutEnabled && (
                                        <button
                                            className="mb-4 inline-flex items-center justify-center gap-2.5 text-sm"
                                            onClick={redirectToGuestCheckout}
                                        >
                                            <span>
                                                {t('Shop without membership')}
                                            </span>
                                            <ChevronRightIcon className="h-3 w-3" />
                                        </button>
                                    )}
                                </>
                            ) : (
                                <>
                                    <h2 className="mb-4 text-center text-2xl font-medium text-gray-600">
                                        {t('Sign Up')}
                                    </h2>

                                    <p className="mb-4 text-center text-sm text-gray-800">
                                        {t(
                                            'Please make sure your e-mail address and password are correct.'
                                        )}
                                    </p>

                                    {isGuestCheckoutEnabled && (
                                        <button
                                            className="mb-4 inline-flex items-center justify-center gap-2.5 text-sm"
                                            onClick={redirectToGuestCheckout}
                                        >
                                            <span>
                                                {t('Shop without membership')}
                                            </span>
                                            <ChevronRightIcon className="h-3 w-3" />
                                        </button>
                                    )}
                                </>
                            )}

                            <UiTab.Group
                                as="div"
                                className="flex flex-col"
                                defaultIndex={0}
                                // @ts-ignore
                                onChange={tabIndex =>
                                    setActiveTab(
                                        tabIndex === 0 ? 'signIn' : 'signUp'
                                    )
                                }
                            >
                                <UiTab.List
                                    aria-label="tabs"
                                    className="flex flex-row rounded border-2 border-gray-100 bg-gray-100"
                                >
                                    <UiTab
                                        className="
                                    flex flex-1 cursor-pointer justify-center whitespace-nowrap rounded
                                    bg-transparent py-2.5 font-medium
                                    text-base text-gray-600 hover:text-default
                                    focus:outline-none selected:bg-white selected:text-default
                                    "
                                    >
                                        {t('Sign In')}
                                    </UiTab>

                                    <UiTab
                                        className="
                                    flex flex-1 cursor-pointer justify-center whitespace-nowrap rounded
                                    bg-transparent py-2.5 font-medium
                                    text-base text-gray-600 hover:text-default
                                    focus:outline-none selected:bg-white selected:text-default
                                    "
                                    >
                                        {t('Sign Up')}
                                    </UiTab>
                                </UiTab.List>

                                <UiTab.Panels className="mt-6">
                                    <UiTab.Panel>
                                        <SignIn redirect={redirect} />

                                        <UiButton
                                            className="mt-4 w-full"
                                            variant="outline"
                                            color="primary"
                                            size="xl"
                                            onClick={() =>
                                                setIsForgotPasswordShown(true)
                                            }
                                        >
                                            {t('Forgot Password')}
                                        </UiButton>
                                    </UiTab.Panel>

                                    <UiTab.Panel>
                                        <SignUp
                                            termsOfMembershipText={
                                                termsOfMembershipText
                                            }
                                            clarificationText={
                                                clarificationText
                                            }
                                            redirect={redirect}
                                        />
                                    </UiTab.Panel>
                                </UiTab.Panels>
                            </UiTab.Group>
                        </>
                    ) : (
                        <>
                            <div
                                className="absolute left-6 top-6 hidden h-9 w-9 cursor-pointer items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 xl:flex"
                                onClick={() => setIsForgotPasswordShown(false)}
                            >
                                <ArrowLeftIcon className="h-4 w-4 text-muted" />
                            </div>

                            <h2 className="mb-4 text-center text-2xl font-medium text-gray-600">
                                {t('Forgot Password')}
                            </h2>

                            <p className="mb-8 text-center text-sm text-gray-800">
                                {t(
                                    'We need your e-mail address so we can send you the password reset link.'
                                )}
                            </p>

                            <ForgotPassword redirect={redirect} />

                            <UiButton
                                className="mt-4 block w-full xl:hidden"
                                variant="outline"
                                color="primary"
                                size="xl"
                                onClick={() => setIsForgotPasswordShown(false)}
                            >
                                {t('Sign In')}
                            </UiButton>
                        </>
                    )}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    Auth.displayName = 'Auth';
}

export default Auth;
