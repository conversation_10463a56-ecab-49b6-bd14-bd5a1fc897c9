import {FC, memo, useCallback, useEffect, useRef, useState} from 'react';
import {FormProvider, useForm, useWatch} from 'react-hook-form';
import {Contact} from '@core/types';
import {isDev, jsonRequest, regexp} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {
    UiAlert,
    UiButton,
    UiCheckbox,
    UiDivider,
    UiForm,
    notification
} from '@core/components/ui';
import AddressForm from '@components/common/AddressForm';

type AddressProps = {
    countries: Record<string, any>[];
    initialStates?: string[];
    initialCities: string[];
    type: 'delivery-address' | 'billing-address';
    contact?: Contact;
    onSave: () => Promise<void>;
};

const Address: FC<AddressProps> = memo(props => {
    const {countries, initialStates, initialCities, type, contact, onSave} =
        props;
    const methods = useForm();
    const {
        register,
        control,
        formState: {errors},
        setValue,
        getValues,
        setError
    } = methods;
    const t = useTrans();
    const {closeSideBar} = useUI();
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const inProgress = useRef(false);

    const useDeliveryAddressAsBillingAddress: string = useWatch({
        name: 'useDeliveryAddressAsBillingAddress',
        defaultValue: false,
        control
    });
    const invoiceType: string = useWatch({
        name: 'invoiceType',
        defaultValue: 'individual',
        control
    });

    useEffect(() => {
        if (typeof contact === 'undefined') return;

        if (typeof contact.name !== 'undefined') setValue('name', contact.name);

        if (typeof contact.relevantContact !== 'undefined') {
            if (typeof contact.relevantContact.name !== 'undefined')
                setValue('relevantContact-name', contact.relevantContact.name);
            if (typeof contact.relevantContact.email !== 'undefined')
                setValue(
                    'relevantContact-email',
                    contact.relevantContact.email
                );
            if (
                !!contact.relevantContact.phoneCountryCode &&
                !!contact.relevantContact.phoneCode &&
                !!contact.relevantContact.phoneNumber
            ) {
                setValue('relevantContact-phone', {
                    countryCode: contact.relevantContact.phoneCountryCode,
                    code: contact.relevantContact.phoneCode,
                    number: contact.relevantContact.phoneNumber
                });
            }
        }

        if (
            typeof contact.address === 'object' &&
            Object.keys(contact.address).length > 0
        ) {
            if (typeof contact.address.street !== 'undefined')
                setValue('address-street', contact.address.street);
            if (typeof contact.address.street2 !== 'undefined')
                setValue('address-street2', contact.address.street2);
            if (typeof contact.address.city !== 'undefined')
                setValue('address-city', contact.address.city);
            if (typeof contact.address.state !== 'undefined')
                setValue('address-state', contact.address.state);
            if (typeof contact.address.district !== 'undefined')
                setValue('address-district', contact.address.district);
            if (typeof contact.address.subDistrict !== 'undefined')
                setValue('address-subDistrict', contact.address.subDistrict);
            if (typeof contact.address.postalCode !== 'undefined')
                setValue('address-postalCode', contact.address.postalCode);
            if (typeof contact.address.countryId !== 'undefined')
                setValue('address-countryId', contact.address.countryId);
        }

        if (type === 'billing-address') {
            if (typeof contact.invoiceType !== 'undefined')
                setValue('invoiceType', contact.invoiceType);
            if (typeof contact.companyName !== 'undefined')
                setValue('companyName', contact.companyName);
            if (typeof contact.taxIdentificationNumber !== 'undefined')
                setValue(
                    'taxIdentificationNumber',
                    contact.taxIdentificationNumber
                );
            if (typeof contact.taxOffice !== 'undefined')
                setValue('taxOffice', contact.taxOffice);
            if (typeof contact.identityNumber !== 'undefined')
                setValue('identityNumber', contact.identityNumber);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [contact]);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            const phoneNumber = getValues(
                'relevantContact-phone.number'
            )?.replace(/[^\d]/g, '');
            if (phoneNumber?.length !== 10) {
                setError('relevantContact-phone', {type: 'required'});
                return;
            }
            if (
                phoneNumber?.[0] !== '5' &&
                data?.['relevantContact-phone']?.countryCode === 'TR'
            ) {
                setError('relevantContact-phone', {
                    type: 'required',
                    message: t(
                        'Turkey mobile numbers begin with +90 followed by a 5 in the second group. (e.g. +905XXXXXXXXX)'
                    )
                });
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                const relevantContact: Record<string, any> = {};
                relevantContact.name = data['relevantContact-name'];
                relevantContact.email = data['relevantContact-email'];
                if (
                    !!data['relevantContact-phone'] &&
                    typeof data['relevantContact-phone'] === 'object'
                ) {
                    relevantContact.phoneCountryCode =
                        data['relevantContact-phone'].countryCode;
                    relevantContact.phoneCode =
                        data['relevantContact-phone'].code;
                    relevantContact.phoneNumber =
                        data['relevantContact-phone'].number;
                }

                const c: Record<string, any> = {};
                c.id = contact?.id;
                c.type = type;
                c.name = data.name;
                c.address = {};
                c.address.street = data['address-street'];
                c.address.street2 = data['address-street2'];
                c.address.city = data['address-city'];
                c.address.state = data['address-state'];
                c.address.district = data['address-district'];
                c.address.subDistrict = data['address-subDistrict'];
                c.address.postalCode = data['address-postalCode'];
                c.address.countryId = data['address-countryId'];
                c.address.countryName = countries.find(
                    country => country.id === data['address-countryId']
                )?.name;
                c.relevantContact = relevantContact;

                if (type !== 'billing-address' && !contact?.id) {
                    c.useDeliveryAddressAsBillingAddress =
                        !!data.useDeliveryAddressAsBillingAddress;
                }

                if (
                    type === 'billing-address' ||
                    (!contact?.id && !!c.useDeliveryAddressAsBillingAddress)
                ) {
                    c.invoiceType = data.invoiceType;
                    c.companyName = data.companyName;
                    c.taxIdentificationNumber = data.taxIdentificationNumber;
                    c.taxOffice = data.taxOffice;
                    c.identityNumber = data.identityNumber;
                }

                await jsonRequest({
                    url: '/api/customers/save-contact',
                    method: 'POST',
                    data: {
                        contact: c
                    }
                });

                await onSave();
                notification({
                    title: t('Address Saved'),
                    description: t('The address has been successfully saved.'),
                    status: 'success'
                });
                closeSideBar();
            } catch (error: any) {
                setErrorMessage(error.message);
                setIsLoading(false);
            }

            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [type]
    );

    return (
        <div className="mt-6 flex flex-col px-4 pb-4 xl:mt-2 xl:px-6 xl:pb-6">
            <FormProvider {...methods}>
                <UiForm onSubmit={methods.handleSubmit(onSubmit)}>
                    {!!errorMessage && (
                        <UiAlert className="mb:6 xl:mb-8" color="danger">
                            {t(errorMessage)}
                        </UiAlert>
                    )}

                    <h2 className="mb-4 flex items-center text-lg font-medium xl:mb-6">
                        <div className="mr-3 flex h-7 w-7 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                            1
                        </div>
                        {t('Contact Information')}
                    </h2>
                    <div className="space-y-2 xl:space-y-4">
                        <UiForm.Field
                            label={t('Name')}
                            error={
                                errors['relevantContact-name'] &&
                                errors['relevantContact-name'].type ===
                                    'required'
                                    ? t('Name is required')
                                    : undefined
                            }
                            {...register('relevantContact-name', {
                                required: true
                            })}
                        />

                        <UiForm.Field
                            label={t('Email address')}
                            autoCorrect="off"
                            autoCapitalize="none"
                            error={
                                errors['relevantContact-email'] &&
                                errors['relevantContact-email'].type ===
                                    'required'
                                    ? t('Email address is required')
                                    : errors['relevantContact-email'] &&
                                      errors['relevantContact-email'].type ===
                                          'pattern'
                                    ? t('Email address is invalid')
                                    : undefined
                            }
                            {...register('relevantContact-email', {
                                required: true,
                                pattern: regexp.email
                            })}
                            // t('Phone number is required!')
                        />

                        <UiForm.Field
                            label={t('Phone')}
                            error={
                                errors['relevantContact-phone'] &&
                                errors['relevantContact-phone'].type ===
                                    'required'
                                    ? errors[
                                          'relevantContact-phone'
                                      ].message?.toString() ||
                                      t('Phone is required')
                                    : undefined
                            }
                            phone
                            countries={countries}
                            {...register('relevantContact-phone', {
                                required: true
                            })}
                        />
                    </div>

                    <UiDivider
                        orientation="horizontal"
                        className="my-6 block border-gray-200 xl:hidden"
                    />

                    <h2 className="mb-4 flex items-center text-lg font-medium xl:mb-6 xl:mt-12">
                        <div className="mr-3 flex h-7 w-7 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                            2
                        </div>
                        {t('Address')}
                    </h2>
                    <div className="space-y-2 xl:space-y-4">
                        <UiForm.Field
                            label={t('Address name')}
                            error={
                                errors.name && errors.name.type === 'required'
                                    ? t('Address name is required')
                                    : undefined
                            }
                            {...register('name', {
                                required: true
                            })}
                        />
                        <AddressForm
                            parentField="address"
                            countries={countries}
                            initialCountryId={contact?.address?.countryId}
                            initialStates={initialStates}
                            initialCities={initialCities}
                        />

                        {type !== 'billing-address' && !contact?.id && (
                            <UiForm.Control>
                                <UiCheckbox
                                    className="mt-1 self-start"
                                    {...register(
                                        'useDeliveryAddressAsBillingAddress',
                                        {required: false}
                                    )}
                                >
                                    {t(
                                        'Also use this address as billing address.'
                                    )}
                                    <div className="mt-1.5 text-xs text-muted">
                                        {t(
                                            'The address selected for the invoice is for information purposes only. Your products are delivered to the address entered in the "Delivery Address" section.'
                                        )}
                                    </div>
                                </UiCheckbox>
                            </UiForm.Control>
                        )}
                    </div>

                    {(type === 'billing-address' ||
                        useDeliveryAddressAsBillingAddress) && (
                        <>
                            <UiDivider
                                orientation="horizontal"
                                className="my-6 block border-gray-200 xl:hidden"
                            />

                            <h2 className="mb-4 flex items-center text-lg font-medium xl:mb-6 xl:mt-12">
                                <div className="mr-3 flex h-7 w-7 items-center justify-center rounded-full bg-gray-900 text-sm font-medium text-white">
                                    3
                                </div>
                                {t('Invoice Information')}
                            </h2>
                            <div className="space-y-2 xl:space-y-4">
                                <UiForm.Field
                                    label={t('Invoice type')}
                                    error={
                                        errors.invoiceType &&
                                        errors.invoiceType.type === 'required'
                                            ? t('{label} is required', {
                                                  label: t('Invoice type')
                                              })
                                            : undefined
                                    }
                                    {...register('invoiceType', {
                                        required: true
                                    })}
                                    defaultValue="individual"
                                    selection
                                >
                                    <option value="individual">
                                        {t('Individual')}
                                    </option>
                                    <option value="corporate">
                                        {t('Corporate')}
                                    </option>
                                </UiForm.Field>
                                {invoiceType === 'corporate' ? (
                                    <>
                                        <UiForm.Field
                                            label={t('Company name')}
                                            error={
                                                errors.invoiceType &&
                                                errors.invoiceType.type ===
                                                    'required'
                                                    ? t('{label} is required', {
                                                          label: t(
                                                              'Company name'
                                                          )
                                                      })
                                                    : undefined
                                            }
                                            {...register('companyName', {
                                                required: true
                                            })}
                                        />
                                        <UiForm.Field
                                            label={t(
                                                'Tax identification number'
                                            )}
                                            error={
                                                errors.taxIdentificationNumber &&
                                                errors.taxIdentificationNumber
                                                    .type === 'required'
                                                    ? t('{label} is required', {
                                                          label: t(
                                                              'Tax identification number'
                                                          )
                                                      })
                                                    : undefined
                                            }
                                            {...register(
                                                'taxIdentificationNumber',
                                                {
                                                    required: true
                                                }
                                            )}
                                        />
                                        <UiForm.Field
                                            label={t('Tax office')}
                                            error={
                                                errors.taxOffice &&
                                                errors.taxOffice.type ===
                                                    'required'
                                                    ? t('{label} is required', {
                                                          label: t('Tax office')
                                                      })
                                                    : undefined
                                            }
                                            {...register('taxOffice', {
                                                required: true
                                            })}
                                        />
                                    </>
                                ) : (
                                    <UiForm.Field
                                        label={t('Identity number (optional)')}
                                        maxLength={11}
                                        inputMode="numeric"
                                        type="text"
                                        {...register('identityNumber', {
                                            required: false,
                                            minLength: {
                                                value: 11,
                                                message: t(
                                                    'Identity number must be exactly 11 characters'
                                                )
                                            },
                                            maxLength: {
                                                value: 11,
                                                message: t(
                                                    'Identity number cannot exceed 11 characters'
                                                )
                                            },
                                            pattern: {
                                                value: /^[0-9]*$/,
                                                message: t(
                                                    'Identity number must be numeric'
                                                )
                                            }
                                        })}
                                        error={
                                            errors.identityNumber &&
                                            t(
                                                'The ID number must be 11 digits!'
                                            )
                                        }
                                    />
                                )}
                            </div>
                        </>
                    )}

                    <UiDivider
                        orientation="horizontal"
                        className="mb-4 mt-6 block border-gray-200 xl:hidden"
                    />

                    <UiButton
                        className="w-full xl:mt-6"
                        variant="solid"
                        color="primary"
                        size="xl"
                        loading={isLoading}
                        disabled={isLoading}
                    >
                        {t('Save Address')}
                    </UiButton>
                </UiForm>
            </FormProvider>
        </div>
    );
});

if (isDev) {
    Address.displayName = 'Address';
}

export default Address;
