import {FC, memo, useCallback, useRef, useState} from 'react';
import {Contact} from '@core/types';
import {isDev, jsonRequest} from '@core/helpers';
import {useCustomer, useTrans, useUI} from '@core/hooks';
import {UiDivider, notification} from '@core/components/ui';
import {PencilIcon, PlusIcon, TrashIcon} from '@core/icons/outline';
import Seo from '@components/common/Seo';
import Address from './Address';

type MyAddressesProps = {
    countries: Record<string, any>[];
    initialStates?: string[];
    initialCities: string[];
    contacts: Contact[];
};

const MyAddresses: FC<MyAddressesProps> = memo(props => {
    const {
        countries,
        initialStates,
        initialCities,
        contacts: initialContacts
    } = props;
    const t = useTrans();
    const {openSideBar, confirm} = useUI();
    const customer = useCustomer();
    const [contacts, setContacts] = useState(() => initialContacts);
    const isRemovingContactRef = useRef(false);

    const onAddressSave = useCallback(async () => {
        const newContacts = await jsonRequest({
            url: '/api/customers/contacts',
            method: 'POST',
            data: {}
        });

        setContacts(newContacts);
    }, []);
    const onAddressDetail = useCallback(
        (type: 'delivery-address' | 'billing-address', contact?: Contact) => {
            setTimeout(() => {
                if (isRemovingContactRef.current) return;

                if (type === 'delivery-address' && !contact) {
                    contact = {
                        relevantContact: {
                            name: customer?.name,
                            email: customer?.email,
                            phoneNumber: customer?.phoneNumber,
                            phoneCode: customer?.phoneCode,
                            phoneCountryCode: customer?.phoneCountryCode
                        }
                    } as any;
                }

                openSideBar(
                    type === 'delivery-address'
                        ? t('Delivery Address')
                        : t('Billing Address'),
                    <Address
                        contact={contact}
                        countries={countries}
                        initialStates={initialStates}
                        initialCities={initialCities}
                        type={type}
                        onSave={onAddressSave}
                    />
                );
            }, 50);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [countries, initialStates, initialCities, onAddressSave]
    );
    const onRemoveContact = useCallback(
        (contactId: string) => {
            isRemovingContactRef.current = true;

            confirm(
                t('Delete Address'),
                t(
                    'You are about to delete the address. This action will not be reversible.'
                ),
                t('Delete Address'),
                async () => {
                    try {
                        await jsonRequest({
                            url: '/api/customers/remove-contact',
                            method: 'POST',
                            data: {contactId}
                        });

                        await onAddressSave();
                    } catch (error) {
                        notification({
                            title: t('Error'),
                            description: t(
                                'We encountered an issue processing your request. Please retry later.'
                            ),
                            status: 'error'
                        });
                    }
                }
            );

            setTimeout(() => {
                isRemovingContactRef.current = false;
            }, 500);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [onAddressSave]
    );

    return (
        <>
            <Seo title={t('My Addresses')} />

            <div className="hidden xl:block">
                <h1 className="text-2xl font-semibold leading-6 text-default">
                    {t('My Addresses')}
                </h1>
                <p className="mt-3 text-sm text-gray-500">
                    {t('Manage your delivery and billing addresses.')}
                </p>
            </div>

            <UiDivider
                orientation="horizontal"
                className="mb-6 mt-4 hidden border-gray-200 xl:block"
            />

            <h2 className="mb-2 mt-4  text-lg font-medium xl:mb-3 xl:mt-0">
                {t('Delivery Addresses')}
            </h2>
            <div className="grid grid-cols-1 gap-2 xl:grid-cols-3 xl:gap-4">
                {contacts
                    .filter(contact => contact.type === 'delivery-address')
                    .map(contact => (
                        <div
                            key={contact.id}
                            className="relative flex cursor-pointer items-center rounded-lg border bg-white p-4 text-sm shadow-sm transition hover:border-primary-600 hover:bg-primary-50 hover:text-primary-600 focus:outline-none"
                            onClick={() =>
                                onAddressDetail('delivery-address', contact)
                            }
                        >
                            <div className="flex-1">
                                <div className="mb-2 font-medium">
                                    {contact.name}
                                </div>

                                <address className="not-italic">
                                    <div>{contact.address.street}</div>
                                    {!!contact.address.street2 && (
                                        <div>{contact.address.street2}</div>
                                    )}
                                    {contact.address.subDistrict && (
                                        <div>{contact.address.subDistrict}</div>
                                    )}
                                    <div className="flex items-center space-x-2">
                                        {contact.address.district && (
                                            <span>
                                                {contact.address.district}
                                            </span>
                                        )}
                                        <span>{contact.address.city}</span>
                                        {contact.address.state && (
                                            <span>{contact.address.state}</span>
                                        )}
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <span>
                                            {contact.address.postalCode}
                                        </span>
                                        <span>
                                            {contact.address.countryName}
                                        </span>
                                    </div>
                                </address>
                            </div>

                            <div className="ml-2 flex h-full flex-col justify-between">
                                <button className="group/inner cursor-pointer rounded-full p-1.5 transition hover:bg-warning-600">
                                    <PencilIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                </button>

                                <button
                                    className="group/inner cursor-pointer rounded-full p-1.5 transition hover:bg-danger-600"
                                    onClick={() => onRemoveContact(contact.id)}
                                >
                                    <TrashIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                </button>
                            </div>
                        </div>
                    ))}

                <div
                    key="new-address"
                    className="relative flex cursor-pointer select-none flex-col items-center justify-center rounded-lg border bg-white p-4 text-sm shadow-sm transition hover:border-green-600 hover:bg-green-50 hover:text-green-600 focus:outline-none"
                    onClick={() => onAddressDetail('delivery-address')}
                >
                    <PlusIcon className="mb-3 h-6 w-6" />
                    <div className="mb-0.5 font-medium">{t('New Address')}</div>
                    <div>{t('Add a new delivery address.')}</div>
                </div>
            </div>

            <h2 className="mb-2 mt-12 text-lg font-medium xl:mb-3">
                {t('Billing Addresses')}
            </h2>
            <div className="mb-4 grid grid-cols-1 gap-2 xl:mb-0 xl:grid-cols-3 xl:gap-4">
                {contacts
                    .filter(contact => contact.type === 'billing-address')
                    .map(contact => (
                        <div
                            key={contact.id}
                            className="relative flex cursor-pointer items-center rounded-lg border bg-white p-4 text-sm shadow-sm transition hover:border-primary-600 hover:bg-primary-50 hover:text-primary-600 focus:outline-none"
                            onClick={() =>
                                onAddressDetail('billing-address', contact)
                            }
                        >
                            <div className="flex-1">
                                <div className="mb-2 font-medium">
                                    {contact.name}
                                </div>

                                <address className="not-italic">
                                    <div>{contact.address.street}</div>
                                    {!!contact.address.street2 && (
                                        <div>{contact.address.street2}</div>
                                    )}
                                    {contact.address.subDistrict && (
                                        <div>{contact.address.subDistrict}</div>
                                    )}
                                    <div className="flex items-center space-x-2">
                                        {contact.address.district && (
                                            <span>
                                                {contact.address.district}
                                            </span>
                                        )}
                                        <span>{contact.address.city}</span>
                                        {contact.address.state && (
                                            <span>{contact.address.state}</span>
                                        )}
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <span>
                                            {contact.address.postalCode}
                                        </span>
                                        <span>
                                            {contact.address.countryName}
                                        </span>
                                    </div>
                                </address>
                            </div>

                            <div className="ml-2 flex h-full flex-col justify-between">
                                <button className="group/inner cursor-pointer rounded-full p-2 transition hover:bg-warning-600">
                                    <PencilIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                </button>

                                <button
                                    className="group/inner cursor-pointer rounded-full p-2 transition hover:bg-danger-600"
                                    onClick={() => onRemoveContact(contact.id)}
                                >
                                    <TrashIcon className="block h-4 w-4 transition group-hover/inner:text-white" />
                                </button>
                            </div>
                        </div>
                    ))}

                <div
                    key="new-address"
                    className="relative flex cursor-pointer flex-col items-center justify-center rounded-lg border bg-white p-4 text-sm shadow-sm transition hover:border-green-600 hover:bg-green-50 hover:text-green-600 focus:outline-none"
                    onClick={() => onAddressDetail('billing-address')}
                >
                    <PlusIcon className="mb-3 h-6 w-6" />
                    <div className="mb-0.5 font-medium">{t('New Address')}</div>
                    <div>{t('Add a new billing address.')}</div>
                </div>
            </div>
        </>
    );
});

if (isDev) {
    MyAddresses.displayName = 'MyAddresses';
}

export default MyAddresses;
