import {FC, Fragment, memo, useCallback, useState} from 'react';
import storeConfig from '~/store.config';
import {ProductListItem} from '@core/types';
import {cls, isDev, jsonRequest, trim} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {
    UiButton,
    UiDivider,
    UiImage,
    UiLink,
    UiRating,
    UiTab,
    notification
} from '@core/components/ui';
import {ReviewIcon} from '@core/icons/outline';
import Seo from '@components/common/Seo';

type Reviewed = ProductListItem & {
    review: Record<string, any>;
    dateFornatted: string;
};

type MyReviewsProps = {
    toReviewCount: number;
    reviewedCount: number;
    toReview: ProductListItem[];
    reviewed: Reviewed[];
};

const MyReviews: FC<MyReviewsProps> = memo(props => {
    const {
        toReviewCount: initialToReviewCount,
        reviewedCount: initialReviewedCount,
        toReview: initialToReview,
        reviewed: initialReviewed
    } = props;
    const t = useTrans();
    const {locale} = useStore();
    const [toReviewCount, setToReviewCount] = useState(initialToReviewCount);
    const [reviewedCount, setReviewedCount] = useState(initialReviewedCount);
    const [toReview, setToReview] = useState(() => initialToReview);
    const [reviewed, setReviewed] = useState(() => initialReviewed);
    const {createReview} = useStore();

    const onCreateReview = useCallback(
        async (item: ProductListItem) => {
            await createReview({
                id: item.productId,
                name: item.name,
                image:
                    Array.isArray(item.images) && item.images.length > 0
                        ? item.images[0]
                        : '/no-image.png',
                price: item.salesPrice
            });

            try {
                const result = await jsonRequest({
                    url: '/api/customers/reviews',
                    method: 'POST'
                });
                const toReview: Record<string, any>[] = [];
                const reviewed: Record<string, any>[] = [];

                for (const item of result) {
                    if (item.isReviewed) {
                        reviewed.push(item);
                    } else {
                        toReview.push(item);
                    }
                }

                setToReviewCount(toReview.length);
                setReviewedCount(reviewed.length);
                setToReview(toReview as any);
                setReviewed(
                    (reviewed as any[]).map((item: any) => {
                        const formatter = new Intl.DateTimeFormat(locale, {
                            day: 'numeric',
                            month: 'long',
                            year: 'numeric'
                        });
                        const date = new Date(Date.parse(item.date));

                        item.dateFornatted = formatter.format(date);

                        return item;
                    })
                );
            } catch (error) {
                notification({
                    title: t('Error'),
                    description: t(
                        'We encountered an issue processing your request. Please retry later.'
                    ),
                    status: 'error'
                });
            }
        },
        [createReview, locale, t]
    );

    return (
        <>
            <Seo title={t('My Reviews')} />

            <div className="hidden xl:block">
                <h1 className="text-2xl font-semibold leading-6 text-default">
                    {t('My Reviews')}
                </h1>
                <p className="mt-3 text-sm text-gray-500">
                    {t('Check your recent reviews.')}
                </p>
            </div>

            <UiDivider
                orientation="horizontal"
                className="mb-0 mt-4 hidden border-gray-200 xl:block"
            />

            <UiTab.Group as="div">
                <div className="border-b border-gray-200">
                    <UiTab.List className="-mb-px flex space-x-8 xl:space-x-10">
                        <UiTab
                            className={({selected}) =>
                                cls(
                                    selected
                                        ? 'border-primary-600 text-primary-600'
                                        : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                    'flex flex-1 items-stretch justify-center whitespace-nowrap border-b-2 py-3 text-sm font-medium xl:flex-shrink-0 xl:flex-grow-0 xl:justify-start'
                                )
                            }
                        >
                            <div className="hidden items-center leading-4 xl:flex">
                                {t('My Awaiting Reviews')}
                            </div>
                            <div className="flex items-center leading-4 xl:hidden">
                                {t('Waiting')}
                            </div>
                            <div
                                className="
                                ml-4 flex h-6 w-6 items-center justify-center rounded-full
                                bg-primary-100 text-xs text-primary-600 xl:ml-6
                                "
                            >
                                {toReviewCount}
                            </div>
                        </UiTab>

                        <UiTab
                            className={({selected}) =>
                                cls(
                                    selected
                                        ? 'border-primary-600 text-primary-600'
                                        : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                    'flex flex-1 items-stretch justify-center whitespace-nowrap border-b-2 py-3 text-sm font-medium xl:flex-shrink-0 xl:flex-grow-0 xl:justify-start'
                                )
                            }
                        >
                            <div className="hidden items-center leading-4 xl:flex">
                                {t('My Past Reviews')}
                            </div>
                            <div className="flex items-center leading-4 xl:hidden">
                                {t('History')}
                            </div>

                            <div
                                className="
                                ml-4 flex h-7 w-7 items-center justify-center rounded-full
                                bg-primary-100 text-xs text-primary-600 xl:ml-6
                                "
                            >
                                {reviewedCount}
                            </div>
                        </UiTab>
                    </UiTab.List>
                </div>

                <UiTab.Panels as={Fragment}>
                    <UiTab.Panel>
                        {toReviewCount > 0 && (
                            <div className="mb-4 mt-4 grid grid-cols-1 gap-2 xl:mb-6 xl:mt-6  xl:grid-cols-2 xl:gap-4">
                                {toReview.map(item => (
                                    <div
                                        key={item.productId}
                                        className="
                                        flex flex-col rounded-lg border border-gray-200
                                        p-4 shadow-sm
                                        "
                                    >
                                        <div className="flex">
                                            <div
                                                className={cls(
                                                    'h-24 flex-shrink-0 overflow-hidden rounded',
                                                    {
                                                        'w-18':
                                                            storeConfig.catalog
                                                                .productImageShape ===
                                                            'rectangle',
                                                        'w-24':
                                                            storeConfig.catalog
                                                                .productImageShape !==
                                                            'rectangle'
                                                    }
                                                )}
                                            >
                                                <UiImage
                                                    className="h-full w-full rounded"
                                                    src={
                                                        Array.isArray(
                                                            item.images
                                                        ) &&
                                                        item.images.length > 0
                                                            ? `${item.images[0]}?w=180&q=75`
                                                            : '/no-image.png'
                                                    }
                                                    alt={item.name}
                                                    width={
                                                        storeConfig.catalog
                                                            .productImageShape ===
                                                        'rectangle'
                                                            ? 72
                                                            : 96
                                                    }
                                                    height={96}
                                                    fit="cover"
                                                    position="center"
                                                    quality={75}
                                                />
                                            </div>

                                            <div className="ml-4 flex flex-1 flex-col items-start justify-between">
                                                <UiLink
                                                    href={
                                                        item.link
                                                            ? item.link
                                                            : `/${trim(
                                                                  trim(
                                                                      item.slug,
                                                                      '/'
                                                                  )
                                                              )}`
                                                    }
                                                    className="cursor-pointer text-xs font-medium text-default xl:text-sm"
                                                >
                                                    {!!item.brandName ? (
                                                        <span className="">
                                                            {item.brandName}{' '}
                                                        </span>
                                                    ) : null}
                                                    {item.name}
                                                </UiLink>

                                                <UiButton
                                                    color="primary"
                                                    variant="light"
                                                    onClick={() =>
                                                        onCreateReview(item)
                                                    }
                                                >
                                                    {t('Write a Review')}
                                                </UiButton>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {toReviewCount < 1 && (
                            <div className="flex flex-1 flex-col items-center justify-center py-12 xl:px-12 xl:py-24">
                                <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500 ">
                                    <ReviewIcon className="h-8 w-8" />
                                </div>

                                <h2 className="pt-8 text-center text-2xl font-semibold">
                                    {t('No products found!')}
                                </h2>

                                <p className="px-10 pt-2 text-center text-muted">
                                    {t(
                                        'No products awaiting review were found.'
                                    )}
                                </p>
                            </div>
                        )}
                    </UiTab.Panel>

                    <UiTab.Panel>
                        {reviewedCount > 0 && (
                            <div className="my-4 space-y-2 xl:my-6 xl:space-y-6">
                                {reviewed.map(item => (
                                    <div
                                        key={item.productId}
                                        className="
                                        flex flex-col rounded-lg border border-gray-200
                                        p-4 shadow-sm
                                        "
                                    >
                                        <div className="flex">
                                            <div
                                                className={cls(
                                                    'h-24 flex-shrink-0 overflow-hidden rounded',
                                                    {
                                                        'w-18':
                                                            storeConfig.catalog
                                                                .productImageShape ===
                                                            'rectangle',
                                                        'w-24':
                                                            storeConfig.catalog
                                                                .productImageShape !==
                                                            'rectangle'
                                                    }
                                                )}
                                            >
                                                <UiImage
                                                    className="h-full w-full rounded"
                                                    src={
                                                        Array.isArray(
                                                            item.images
                                                        ) &&
                                                        item.images.length > 0
                                                            ? `${item.images[0]}?w=180&q=75`
                                                            : '/no-image.png'
                                                    }
                                                    alt={item.name}
                                                    width={
                                                        storeConfig.catalog
                                                            .productImageShape ===
                                                        'rectangle'
                                                            ? 72
                                                            : 96
                                                    }
                                                    height={96}
                                                    fit="cover"
                                                    position="center"
                                                    quality={75}
                                                />
                                            </div>

                                            <div className="ml-4 flex flex-1 flex-col justify-between">
                                                <div className="flex flex-col font-medium text-base text-default xl:flex-row xl:justify-between">
                                                    <UiLink
                                                        href={
                                                            item.link
                                                                ? item.link
                                                                : `/${trim(
                                                                      trim(
                                                                          item.slug,
                                                                          '/'
                                                                      )
                                                                  )}`
                                                        }
                                                        className="cursor-pointer text-sm xl:text-base"
                                                    >
                                                        {!!item.brandName ? (
                                                            <span className="">
                                                                {item.brandName}{' '}
                                                            </span>
                                                        ) : null}
                                                        {item.name}
                                                    </UiLink>

                                                    <div className="mt-2 xl:mt-0">
                                                        {item.review.status ===
                                                            'approved' && (
                                                            <div className="inline-block rounded-full border border-success-600 px-2 text-[10px] text-success-600 xl:px-3 xl:py-1.5 xl:text-xs">
                                                                {t('Approved')}
                                                            </div>
                                                        )}
                                                        {item.review.status ===
                                                            'refused' && (
                                                            <div className="inline-block rounded-full border border-danger-600 px-2 text-[10px] text-danger-600 xl:px-3 xl:py-1.5 xl:text-xs">
                                                                {t('Refused')}
                                                            </div>
                                                        )}
                                                        {item.review.status ===
                                                            'draft' && (
                                                            <div className="inline-block rounded-full border border-gray-500 px-2 text-[10px] text-gray-500 xl:px-3 xl:py-1.5 xl:text-xs">
                                                                {t('Draft')}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>

                                                <div className="flex flex-col xl:flex-row xl:justify-between">
                                                    <div className="mt-4 h-5 xl:mt-0 xl:h-6">
                                                        <UiRating
                                                            size="md"
                                                            initialRating={
                                                                item.review
                                                                    .rating
                                                            }
                                                            readonly
                                                        />
                                                    </div>
                                                    <div className="mt-2 text-sm text-muted xl:mt-0">
                                                        {item.dateFornatted}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {item.review.hasContent && (
                                            <>
                                                <UiDivider
                                                    orientation="horizontal"
                                                    className="mb-5 mt-6 border-gray-200"
                                                />

                                                <div
                                                    className="space-y-3 text-base text-gray-600"
                                                    dangerouslySetInnerHTML={{
                                                        __html: item.review
                                                            .content
                                                    }}
                                                />
                                            </>
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}

                        {reviewedCount < 1 && (
                            <div className="flex flex-1 flex-col items-center justify-center py-12 xl:px-12 xl:py-24">
                                <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500 ">
                                    <ReviewIcon className="h-8 w-8" />
                                </div>

                                <h2 className="pt-8 text-center text-2xl font-semibold">
                                    {t('No reviews found!')}
                                </h2>

                                <p className="px-10 pt-2 text-center text-muted">
                                    {t('There are no reviews in your account.')}
                                </p>
                            </div>
                        )}
                    </UiTab.Panel>
                </UiTab.Panels>
            </UiTab.Group>
        </>
    );
});

if (isDev) {
    MyReviews.displayName = 'MyReviews';
}

export default MyReviews;
