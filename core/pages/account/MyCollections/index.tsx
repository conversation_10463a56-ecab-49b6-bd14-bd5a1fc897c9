import {FC, memo, useCallback, useMemo, useState} from 'react';
import storeConfig from '~/store.config';
import {Collection} from '@core/types';
import {clone, isDev, jsonRequest} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {UiButton, UiDivider, UiLink, notification} from '@core/components/ui';
import {
    BellIcon,
    BookmarkIcon,
    ClockIcon,
    EnvelopeIcon,
    PencilIcon,
    PlusIcon,
    TrashIcon
} from '@core/icons/outline';
import Seo from '@components/common/Seo';
import CollectionDetail from './CollectionDetail';

type MyCollectionsProps = {
    collections: Collection[];
};

const MyCollections: FC<MyCollectionsProps> = memo(props => {
    const {collections: initialCollections} = props;
    const t = useTrans();
    const {openSideBar, confirm} = useUI();
    const [collections, setCollections] = useState(() => initialCollections);

    const buyLaterCollection = useMemo(
        () => collections.find(collection => !!collection.isBuyLater),
        [collections]
    );
    const alarmCollection = useMemo(
        () => collections.find(collection => !!collection.isAlarm),
        [collections]
    );
    const notifyCustomerCollection = useMemo(
        () => collections.find(collection => !!collection.isNotifyCustomer),
        [collections]
    );
    const myCollections = useMemo(
        () =>
            collections.filter(
                collection =>
                    !collection.isBuyLater &&
                    !collection.isAlarm &&
                    !collection.isNotifyCustomer
            ),
        [collections]
    );

    const onCollectionSaved = useCallback(
        (collection: Collection) => {
            const newCollections = clone(collections);
            const index = newCollections.findIndex(c => c.id === collection.id);

            if (index !== -1) {
                newCollections[index] = collection;
            } else {
                newCollections.push(collection);
            }

            setCollections(newCollections);
        },
        [collections]
    );
    const onNewCollection = useCallback(() => {
        openSideBar(
            t('New Collection'),
            <CollectionDetail onSave={onCollectionSaved} />
        );
    }, [t, openSideBar, onCollectionSaved]);
    const onUpdateCollection = useCallback(
        (collection: Collection) => {
            openSideBar(
                t('Update Collection'),
                <CollectionDetail
                    collection={collection}
                    onSave={onCollectionSaved}
                />
            );
        },
        [t, openSideBar, onCollectionSaved]
    );
    const onDeleteCollection = useCallback(
        (collection: Collection) => {
            confirm(
                t('Delete Collection'),
                t(
                    'You are about to delete the collection. This action will not be reversible.'
                ),
                t('Delete Collection'),
                async () => {
                    try {
                        await jsonRequest({
                            url: '/api/customers/remove-collection',
                            method: 'POST',
                            data: {collectionId: collection.id}
                        });

                        setCollections(
                            collections.filter(c => c.id !== collection.id)
                        );
                    } catch (error) {
                        notification({
                            title: t('Error'),
                            description: t(
                                'We encountered an issue processing your request. Please retry later.'
                            ),
                            status: 'error'
                        });
                    }
                }
            );
        },
        [t, collections, confirm]
    );

    return (
        <>
            <Seo title={t('My Collections')} />

            <div className="hidden xl:block">
                <h1 className="text-2xl font-semibold leading-6 text-default">
                    {t('My Collections')}
                </h1>
                <p className="mt-3 text-sm text-gray-500">
                    {t(
                        'With the My Collections feature, all products are always at your disposal. Add the products that you like very much, that you buy when they are finished, that you expect to be discounted, to your collections, and you can reach them quickly whenever you want.'
                    )}
                </p>

                <UiButton
                    className="mt-6"
                    variant="solid"
                    color="primary"
                    leftIcon={<PlusIcon className="mr-2 h-4 w-4" />}
                    onClick={onNewCollection}
                >
                    {t('Create Collection')}
                </UiButton>
            </div>

            <UiDivider
                orientation="horizontal"
                className="mb-6 mt-4 hidden border-gray-200 xl:block"
            />

            <div className="mb-4 mt-4 xl:mb-0 xl:mt-0">
                <h2 className="mb-3 hidden text-lg font-medium xl:block">
                    {storeConfig.title}
                </h2>

                <div className="grid grid-cols-1 gap-2 xl:grid-cols-3 xl:gap-4">
                    <UiLink
                        className="group relative flex cursor-pointer select-none items-center rounded-lg border bg-white pb-4 pl-4 pr-9 pt-4 text-sm shadow-sm transition hover:border-primary-600 hover:bg-primary-50 hover:text-primary-600 focus:outline-none"
                        href={`/account/my-collections/${
                            buyLaterCollection!.id
                        }`}
                    >
                        <div className="mr-4 flex items-center">
                            <ClockIcon className="h-8 w-8 text-gray-400 transition group-hover:text-primary-600" />
                        </div>

                        <div className="flex-1 overflow-hidden">
                            <div className="mb-1 truncate font-medium">
                                {t(buyLaterCollection!.name)}
                            </div>
                            <div className="text-muted">
                                {t('{count} product(s)', {
                                    count: buyLaterCollection!.itemCount
                                })}
                            </div>
                        </div>
                    </UiLink>

                    <UiLink
                        className="group relative flex cursor-pointer select-none items-center rounded-lg border bg-white p-4 text-sm shadow-sm transition hover:border-primary-600 hover:bg-primary-50 hover:text-primary-600 focus:outline-none"
                        href={`/account/my-collections/${alarmCollection!.id}`}
                    >
                        <div className="mr-4 flex items-center">
                            <BellIcon className="h-8 w-8 text-gray-400 transition group-hover:text-primary-600" />
                        </div>

                        <div className="flex-1 overflow-hidden">
                            <div className="mb-1 truncate font-medium">
                                {t(alarmCollection!.name)}
                            </div>
                            <div className="text-muted">
                                {t('{count} product(s)', {
                                    count: alarmCollection!.itemCount
                                })}
                            </div>
                        </div>
                    </UiLink>

                    <UiLink
                        className="group relative flex cursor-pointer select-none items-center rounded-lg border bg-white p-4 text-sm shadow-sm transition hover:border-primary-600 hover:bg-primary-50 hover:text-primary-600 focus:outline-none"
                        href={`/account/my-collections/${
                            notifyCustomerCollection!.id
                        }`}
                    >
                        <div className="mr-4 flex items-center">
                            <EnvelopeIcon className="h-8 w-8 text-gray-400 transition group-hover:text-primary-600" />
                        </div>

                        <div className="flex-1 overflow-hidden">
                            <div className="mb-1 truncate font-medium">
                                {t(notifyCustomerCollection!.name)}
                            </div>
                            <div className="text-muted">
                                {t('{count} product(s)', {
                                    count: notifyCustomerCollection!.itemCount
                                })}
                            </div>
                        </div>
                    </UiLink>
                </div>

                {myCollections.length > 0 && (
                    <>
                        <h2 className="mb-3 mt-12 hidden text-lg font-medium xl:block">
                            {t('My Collections')}
                        </h2>
                        <div className="grid grid-cols-1 gap-2 xl:grid-cols-3 xl:gap-4">
                            {myCollections.map(collection => (
                                <UiLink
                                    key={collection.id}
                                    className="group relative flex cursor-pointer select-none items-center rounded-lg border bg-white p-4 text-sm shadow-sm transition hover:border-primary-600 hover:bg-primary-50 hover:text-primary-600 focus:outline-none"
                                    href={`/account/my-collections/${collection.id}`}
                                >
                                    <div className="mr-4 flex items-center">
                                        <BookmarkIcon
                                            className="
                                            h-8 w-8 text-gray-400 transition 
                                            group-hover:text-primary-600
                                            "
                                        />
                                    </div>

                                    <div className="flex-1 overflow-hidden">
                                        <div className="mb-1 truncate font-medium">
                                            {t(collection!.name)}
                                        </div>
                                        <div className="text-muted">
                                            {t('{count} product(s)', {
                                                count: collection!.itemCount
                                            })}
                                        </div>
                                    </div>

                                    <div className="ml-4 flex h-full items-center space-x-1">
                                        <button
                                            className="group/inner cursor-pointer rounded-full p-1.5 transition hover:bg-warning-600"
                                            onClick={e => {
                                                e.preventDefault();
                                                e.stopPropagation();

                                                onUpdateCollection(collection);
                                            }}
                                        >
                                            <PencilIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                        </button>

                                        <button
                                            className="group/inner cursor-pointer rounded-full p-1.5 transition hover:bg-danger-600"
                                            onClick={e => {
                                                e.preventDefault();
                                                e.stopPropagation();
                                                onDeleteCollection(collection);
                                            }}
                                        >
                                            <TrashIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                                        </button>
                                    </div>
                                </UiLink>
                            ))}
                        </div>
                    </>
                )}
            </div>
        </>
    );
});

if (isDev) {
    MyCollections.displayName = 'MyCollections';
}

export default MyCollections;
