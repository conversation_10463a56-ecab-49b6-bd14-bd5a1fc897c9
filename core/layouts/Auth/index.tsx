import {FC, memo, PropsWithChildren} from 'react';
import {isDev} from '@core/helpers';
import FooterPartial from './partials/Footer';
import HeaderPartial from './partials/Header';

const AuthLayout: FC<PropsWithChildren<unknown>> = memo(({children}) => {
    return (
        <div className=" flex h-full w-full flex-col items-center xl:h-screen xl:bg-gray-100">
            <HeaderPartial />

            <main className="w-full flex-1">{children}</main>

            <FooterPartial />
        </div>
    );
});

if (isDev) {
    AuthLayout.displayName = 'AuthLayout';
}

export default AuthLayout;
