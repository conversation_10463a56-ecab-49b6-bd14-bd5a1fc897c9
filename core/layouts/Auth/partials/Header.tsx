import {FC, memo} from 'react';
import siteLogo from '@assets/images/common/site-logo.png';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';
import HeaderUserNavPartial from '@core/layouts/Default/partials/Header/UserNav';

const HeaderPartial: FC = memo(() => {
    return (
        <header className="hidden h-account-header w-full border-b border-gray-200 bg-white xl:block">
            <div className="container flex h-full items-stretch">
                <div className="flex flex-1 items-center">
                    <UiLink
                        className="h-account-logo cursor-pointer"
                        href="/"
                        aria-label="Logo"
                    >
                        <UiImage
                            src={siteLogo}
                            alt={storeConfig.title}
                            width={parseFloat(
                                storeConfig.theme.accountLogoWidth.replace(
                                    'px',
                                    ''
                                )
                            )}
                            priority={true}
                        />
                    </UiLink>
                </div>

                <HeaderUserNavPartial />
            </div>
        </header>
    );
});

if (isDev) {
    HeaderPartial.displayName = 'HeaderPartial';
}

export default HeaderPartial;
