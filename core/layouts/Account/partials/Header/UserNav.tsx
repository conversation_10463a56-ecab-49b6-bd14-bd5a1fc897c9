import {FC, memo} from 'react';
import {useRouter} from 'next/router';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiButton} from '@core/components/ui';
import {HomeIcon} from '@core/icons/outline';

const HeaderUserNavPartial: FC = memo(() => {
    const router = useRouter();
    const t = useTrans();

    return (
        <nav className="flex items-center justify-end space-x-8">
            <UiButton
                variant="outline"
                leftIcon={<HomeIcon className="mr-2 h-4 w-4" />}
                onClick={() => router.push('/')}
            >
                {t('RETURN TO HOME PAGE')}
            </UiButton>
        </nav>
    );
});

if (isDev) {
    HeaderUserNavPartial.displayName = 'HeaderUserNavPartial';
}

export default HeaderUserNavPartial;
