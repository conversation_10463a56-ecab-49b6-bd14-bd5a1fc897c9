import {FC, memo, useMemo} from 'react';
import {useRouter} from 'next/router';
import {signOut} from 'next-auth/react';
import {Cookies} from 'react-cookie-consent';
import {cls, isDev} from '@core/helpers';
import {useCustomer, useTrans} from '@core/hooks';
import {UiAvatar, UiDivider, UiLink, UiStickyBox} from '@core/components/ui';
import {
    BookmarkIcon,
    HeartIcon,
    LocationIcon,
    PowerOffIcon,
    ReceiptIcon,
    ReviewIcon,
    UserIcon
} from '@core/icons/outline';
import CollectionSideBar from './CollectionSideBar';

type SideBarPartialProps = {
    pageProps: Record<string, any>;
};

const SideBarPartial: FC<SideBarPartialProps> = memo(({pageProps}) => {
    const router = useRouter();
    const t = useTrans();
    const customer = useCustomer();
    const collections = useMemo(() => pageProps.collections, [pageProps]);
    const collection = useMemo(() => pageProps.collection, [pageProps]);

    if (
        typeof collection !== 'undefined' &&
        typeof collections !== 'undefined'
    ) {
        return (
            <CollectionSideBar
                collections={collections}
                collection={collection}
            />
        );
    }

    return (
        <div className="hidden select-none xl:block">
            <UiStickyBox>
                <div className="rounded border border-gray-200 p-8 pb-6 shadow-sm">
                    <div className="flex items-center space-x-5">
                        <UiAvatar
                            className="bg-primary-600 text-white"
                            name={customer?.name}
                            size="xl"
                        />

                        <div className="flex-1 text-sm ">
                            <div className="font-medium">{customer?.name}</div>
                            <div className="text-muted">{customer?.email}</div>
                        </div>
                    </div>

                    <UiDivider
                        orientation="horizontal"
                        className="my-6 border-gray-200"
                    />

                    <div className="-mx-3 space-y-1">
                        <UiLink
                            href="/account/my-orders"
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        router.asPath.includes(
                                            '/account/my-orders'
                                        )
                                }
                            )}
                        >
                            <ReceiptIcon className="mr-3 h-4 w-4" />
                            {t('My Orders')}
                        </UiLink>

                        <UiLink
                            href="/account/my-favorites"
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        router.asPath ===
                                        '/account/my-favorites'
                                }
                            )}
                        >
                            <HeartIcon className="mr-3 h-4 w-4" />
                            {t('My Favorites')}
                        </UiLink>

                        <UiLink
                            href="/account/my-collections"
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        router.asPath ===
                                        '/account/my-collections'
                                }
                            )}
                        >
                            <BookmarkIcon className="mr-3 h-4 w-4" />
                            {t('My Collections')}
                        </UiLink>

                        <UiLink
                            href="/account/my-reviews"
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        router.asPath === '/account/my-reviews'
                                }
                            )}
                        >
                            <ReviewIcon className="mr-3 h-4 w-4" />
                            {t('My Reviews')}
                        </UiLink>

                        <UiLink
                            href="/account/my-addresses"
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        router.asPath ===
                                        '/account/my-addresses'
                                }
                            )}
                        >
                            <LocationIcon className="mr-3 h-4 w-4" />
                            {t('My Addresses')}
                        </UiLink>

                        <UiLink
                            href="/account/my-account"
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        router.asPath === '/account/my-account'
                                }
                            )}
                        >
                            <UserIcon className="mr-3 h-4 w-4" />
                            {t('My Account')}
                        </UiLink>

                        <button
                            className="flex w-full cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none"
                            onClick={() => {
                                Cookies.remove('cart-id');
                                signOut();
                            }}
                        >
                            <PowerOffIcon className="mr-3 h-4 w-4" />
                            {t('Sign Out')}
                        </button>
                    </div>
                </div>
            </UiStickyBox>
        </div>
    );
});

if (isDev) {
    SideBarPartial.displayName = 'SideBarPartial';
}

export default SideBarPartial;
