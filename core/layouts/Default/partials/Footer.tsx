import {FC, memo} from 'react';
import {UiImage, UiLink} from '@core/components/ui';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {
    FacebookIcon,
    InstagramIcon,
    LinkedinIcon,
    YoutubeIcon
} from '@core/icons/brand';

const footerNavigation = {
    popularPages: [
        {name: 'Woman', href: ''},
        {name: 'Man', href: ''},
        {name: 'Home & Life', href: ''},
        {name: 'Shoes & Bag', href: ''},
        {name: 'Watch & Accessories', href: ''},
        {name: 'Sport & Outdoor', href: ''}
    ],
    popularBrands: [
        {name: 'Trendy Tech', href: ''},
        {name: 'Cozy Home', href: ''},
        {name: 'Eco Essentials', href: ''},
        {name: 'Luxe Glow', href: ''},
        {name: 'Fresh Fusion', href: ''},
        {name: 'Moda Mix', href: ''},
        {name: 'Nova Nest', href: ''}
    ],
    company: [
        {name: 'Who we are', href: ''},
        {name: 'Blog', href: ''},
        {name: 'Careers', href: ''}
    ],
    legal: [
        {
            name: 'Frequently Asked Questions',
            href: 'cozum-merkezi/sikca-sorulan-sorular'
        },
        {
            name: 'Privacy and Payment Security',
            href: 'cozum-merkezi/gizlilik-ve-odeme-guvenligi'
        },
        {name: 'Cookie Policy', href: 'cozum-merkezi/cerez-politikasi'},
        {name: 'Return Policy', href: 'cozum-merkezi/iade-surecleri'},
        {name: 'Delivery Policy', href: 'cozum-merkezi/kargo-ve-teslimat'}
    ]
};

const FooterPartial: FC = memo(() => {
    const t = useTrans();

    return (
        <footer>
            <div className="container border-t">
                <div className="divide-y divide-gray-200 xl:flex xl:justify-center xl:divide-x xl:divide-y-0 xl:py-8">
                    <div className="py-8 xl:w-1/3 xl:flex-none xl:py-0">
                        <div className="mx-auto flex max-w-xs flex-col items-center px-4 xl:max-w-none xl:px-8">
                            <div className="h-24 w-24">
                                <UiImage
                                    src={require('@assets/images/footer/ssl.svg')}
                                    alt="SSL Icon"
                                />
                            </div>
                            <div className="mt-2 flex flex-auto flex-col-reverse text-center">
                                <p className="text-sm text-gray-500">
                                    {t(
                                        'To have a secure shopping experience enterstore.com uses SSL trust certificate'
                                    )}
                                </p>
                                <h3 className="font-medium text-gray-900">
                                    {t('Secure Shopping')}
                                </h3>
                            </div>
                        </div>
                    </div>
                    <div className="py-8 xl:w-1/3 xl:flex-none xl:py-0">
                        <div className="mx-auto flex max-w-xs flex-col items-center px-4 xl:max-w-none xl:px-8">
                            <div className="h-24 w-24">
                                <UiImage
                                    src={require('@assets/images/footer/reward.svg')}
                                    alt="Reward Icon"
                                />
                            </div>
                            <div className="mt-2 flex flex-auto flex-col-reverse text-center">
                                <p className="text-sm text-gray-500">
                                    {t(
                                        'All of our products have a 100% original product guarantee with the assurance of enterstore.com'
                                    )}
                                </p>
                                <h3 className="font-medium text-gray-900">
                                    {t('100% Original Product')}
                                </h3>
                            </div>
                        </div>
                    </div>
                    <div className="py-8 xl:w-1/3 xl:flex-none xl:py-0">
                        <div className="mx-auto flex max-w-xs flex-col items-center px-4 xl:max-w-none xl:px-8">
                            <div className="h-24 w-24">
                                <UiImage
                                    src={require('@assets/images/footer/delivery-box.svg')}
                                    alt="Delivery Box Icon"
                                />
                            </div>
                            <div className="mt-2 flex flex-auto flex-col-reverse text-center">
                                <p className="text-sm text-gray-500">
                                    {t(
                                        'With enterstore.com assurance, you can return all our products within 15 days'
                                    )}
                                </p>
                                <h3 className="font-medium text-gray-900">
                                    {t('Free Return')}
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="container">
                <div className="border-gary-200 grid grid-cols-1 gap-8 border-t py-8 xl:grid-cols-2 xl:py-20">
                    <div className="col-span-1 grid grid-cols-2 gap-8">
                        <div>
                            <h3 className="text-sm font-semibold">
                                {t('Popular Pages')}
                            </h3>

                            <ul
                                role="list"
                                className="mt-4 space-y-3.5 xl:mt-6"
                            >
                                {footerNavigation.popularPages.map(item => (
                                    <li key={item.name} className="text-sm">
                                        <UiLink
                                            href={item.href}
                                            className="hover:text-primary-600"
                                        >
                                            {t(item.name)}
                                        </UiLink>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        <div>
                            <h3 className="text-sm font-semibold">
                                {t('Contact Us')}
                            </h3>

                            <ul
                                role="list"
                                className="mt-4 space-y-3.5 xl:mt-6"
                            >
                                {footerNavigation.popularBrands.map(item => (
                                    <li key={item.name} className="text-sm">
                                        <UiLink
                                            href={item.href}
                                            className="hover:text-primary-600"
                                        >
                                            {t(item.name)}
                                        </UiLink>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>

                    <div className="col-span-1 grid grid-cols-2 gap-8">
                        <div>
                            <h3 className="text-sm font-semibold">
                                {t('demostore.entererp.com')}
                            </h3>

                            <ul
                                role="list"
                                className="mt-4 space-y-3.5 xl:mt-6"
                            >
                                {footerNavigation.company.map(item => (
                                    <li key={item.name} className="text-sm">
                                        <UiLink
                                            href={item.href}
                                            className="hover:text-primary-600"
                                        >
                                            {t(item.name)}
                                        </UiLink>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        <div>
                            <h3 className="text-sm font-semibold">
                                {t('Legal')}
                            </h3>

                            <ul
                                role="list"
                                className="mt-4 space-y-3.5 xl:mt-6"
                            >
                                {footerNavigation.legal.map(item => (
                                    <li key={item.name} className="text-sm">
                                        <UiLink
                                            href={item.href}
                                            className="hover:text-primary-600"
                                        >
                                            {t(item.name)}
                                        </UiLink>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div className="container">
                <div className="flex flex-col items-center justify-center space-y-6 pb-8 text-sm text-gray-700 xl:flex-row xl:justify-between xl:space-y-0">
                    <div className="flex items-center space-x-5">
                        <div className="relative h-6 w-11">
                            <UiImage
                                src={require('@core/assets/images/mastercard.png')}
                                alt="Mastercard logo"
                            />
                        </div>
                        <div className="relative h-6 w-11">
                            <UiImage
                                src={require('@core/assets/images/visa.png')}
                                alt="Visa logo"
                            />
                        </div>
                        <div className="relative h-6 w-11">
                            <UiImage
                                src={require('@core/assets/images/american-express.png')}
                                alt="American Express logo"
                            />
                        </div>
                        <div className="relative h-6 w-11">
                            <UiImage
                                src={require('@core/assets/images/troy.png')}
                                alt="Troy logo"
                            />
                        </div>
                    </div>

                    <div className="flex items-center space-x-4">
                        <a
                            href=""
                            rel="noopener noreferrer"
                            target="_blank"
                            title="Facebook"
                            className="flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 transition hover:border-primary-600 hover:bg-primary-600 hover:text-white"
                        >
                            <FacebookIcon className="h-4 w-4" />
                        </a>
                        <a
                            href=""
                            rel="noopener noreferrer"
                            target="_blank"
                            title="Instagram"
                            className="flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 transition hover:border-primary-600 hover:bg-primary-600 hover:text-white"
                        >
                            <InstagramIcon className="h-4 w-4" />
                        </a>
                        <a
                            href=""
                            rel="noopener noreferrer"
                            target="_blank"
                            title="LinkedIn"
                            className="flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 transition hover:border-primary-600 hover:bg-primary-600 hover:text-white"
                        >
                            <LinkedinIcon className="h-4 w-4" />
                        </a>
                        <a
                            href=""
                            rel="noopener noreferrer"
                            target="_blank"
                            title="Youtube"
                            className="flex h-10 w-10 items-center justify-center rounded-full border border-gray-200 transition hover:border-primary-600 hover:bg-primary-600 hover:text-white"
                        >
                            <YoutubeIcon className="h-4 w-4" />
                        </a>
                    </div>
                </div>
            </div>

            <div className="container">
                <div className="flex flex-col items-center justify-center border-t py-8 text-sm text-gray-700 xl:flex-row xl:items-center xl:justify-between xl:pb-8">
                    <div className="mb-3 whitespace-nowrap text-left xl:mb-0">
                        &copy; {new Date().getFullYear()}{' '}
                        {t('All Rights Reserved')}
                    </div>

                    <div className="flex flex-col items-center gap-4 md:flex-row">
                        <div className="space-x-4">
                            <UiLink
                                href="kurumsal/on-bilgilendirme-formu"
                                className="hover:text-primary-600"
                            >
                                {t('Preliminary Information Form')}
                            </UiLink>
                            <UiLink
                                href="kurumsal/mesafeli-satis-sozlesmesi"
                                className="hover:text-primary-600"
                            >
                                {t('Distance Sales Contract')}
                            </UiLink>
                        </div>
                        <a
                            href="https://entererp.com/"
                            rel="noopener noreferrer"
                            target="_blank"
                            title="EnterERP Logo"
                        >
                            <UiImage
                                className="h-auto w-36"
                                src={require('@assets/images/footer/entererp.svg')}
                                alt="EnterERP Logo"
                            />
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    );
});

if (isDev) {
    FooterPartial.displayName = 'FooterPartial';
}

export default FooterPartial;
