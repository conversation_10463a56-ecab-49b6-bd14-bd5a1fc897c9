import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';
import {LinkType} from './types';

const imageContainerWidths: Record<number, number> = {
    1: 300,
    2: 300,
    3: 630,
    4: 630,
    5: 960,
    6: 960
};

type NavBarSubLinksPartialProps = {
    activeLink: LinkType;
};

const NavBarSubLinksPartial: FC<NavBarSubLinksPartialProps> = memo(props => {
    const {activeLink} = props;

    const subImageLinks = useMemo(
        () =>
            (activeLink.children as LinkType[])
                .filter(
                    subLink =>
                        Array.isArray(subLink.images) &&
                        subLink.images.length > 0 &&
                        subLink.type !== 'story' &&
                        subLink.type !== 'collection' &&
                        subLink.type !== 'slide'
                )
                .slice(0, 6),
        [activeLink]
    );
    const subLinks = useMemo(
        () =>
            (activeLink.children as LinkType[]).filter(
                subLink =>
                    subLink.type !== 'story' &&
                    subLink.type !== 'collection' &&
                    subLink.type !== 'slide' &&
                    subImageLinks.findIndex(sil => sil.id === subLink.id) === -1
            ),
        [activeLink, subImageLinks]
    );
    const imagesContainerWidth = useMemo(
        () => imageContainerWidths[subImageLinks.length] || 0,
        [subImageLinks]
    );
    const imagesColumnCount = useMemo(
        () => Math.ceil(subImageLinks.length / 2),
        [subImageLinks]
    );
    const subLinksColumnCount = useMemo(() => {
        let count = 5;

        if (imagesColumnCount > 2) {
            count = 2;
        } else if (imagesColumnCount > 1) {
            count = 4;
        }

        return count;
    }, [imagesColumnCount]);

    return (
        <div className="flex flex-nowrap items-stretch justify-between p-8">
            <div
                className="flex-1"
                style={{
                    columnCount: subLinksColumnCount,
                    columnGap: '1rem'
                }}
            >
                {subLinks.map(subLink => (
                    <div
                        key={subLink.id}
                        className="column-breaks block w-full"
                    >
                        <div className="ml-4 flex w-full flex-col flex-nowrap pb-4 align-top text-sm">
                            <UiLink
                                className="font-semibold transition duration-100 hover:text-primary-600"
                                href={subLink.href}
                            >
                                {subLink.title}
                            </UiLink>

                            {Array.isArray(subLink.children) &&
                                subLink.children.length > 0 && (
                                    <div className="mt-1.5 flex flex-col flex-nowrap space-y-1">
                                        {subLink.children.map(subSubLink => {
                                            if (
                                                subSubLink.type === 'story' ||
                                                subSubLink.type ===
                                                    'collection' ||
                                                subSubLink.type === 'slide'
                                            )
                                                return;
                                            return (
                                                <UiLink
                                                    className="transition duration-100 hover:text-primary-600"
                                                    href={subSubLink.href}
                                                    key={subSubLink.id}
                                                >
                                                    {subSubLink.title}
                                                </UiLink>
                                            );
                                        })}
                                    </div>
                                )}
                        </div>
                    </div>
                ))}
            </div>

            {subImageLinks.length > 0 && (
                <div
                    className={cls('ml-6 gap-5 space-y-5', {
                        'columns-1': imagesColumnCount === 1,
                        'columns-2': imagesColumnCount === 2,
                        'columns-3': imagesColumnCount === 3
                    })}
                    style={{width: `${imagesContainerWidth}px`}}
                >
                    {subImageLinks.map((subLink, index) => (
                        <UiLink
                            className={cls('block break-inside-avoid', {
                                'row-span-2':
                                    subImageLinks.length === 3 && index === 2
                            })}
                            href={subLink.href}
                            key={subLink.id}
                        >
                            <UiImage
                                className="rounded-lg"
                                aspectW={16}
                                aspectH={9}
                                src={`${
                                    (subLink.images as string[])[0]
                                }?w=720&q=75`}
                                fill
                                position="center"
                                fit="cover"
                                alt=""
                            />
                        </UiLink>
                    ))}
                </div>
            )}
        </div>
    );
});

if (isDev) {
    NavBarSubLinksPartial.displayName = 'NavBarSubLinksPartial';
}

export default NavBarSubLinksPartial;
