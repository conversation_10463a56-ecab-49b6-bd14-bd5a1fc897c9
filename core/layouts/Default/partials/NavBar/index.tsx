import {FC, Fragment, memo, useEffect, useMemo, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev, orderBy} from '@core/helpers';
import {useElementSize, useStore} from '@core/hooks';
import {UiTransition} from '@core/components/ui';
import useLayout from '../../useLayout';
import NavBarMainLinksPartial from './MainLinks';
import NavBarSubLinksPartial from './SubLinks';
import {LinkType} from './types';

const NavBarPartial: FC = memo(() => {
    const router = useRouter();
    const {navigation} = useStore();
    const {ref: navRef, height: navHeight} = useElementSize();
    const {ref: containerRef, width: containerWidth} = useElementSize();
    const {isMegaMenuOpened, isMegaMenuCloseInProgress, setIsMegaMenuOpened} =
        useLayout();
    const [activeLink, setActiveLink] = useState<LinkType | undefined>(
        undefined
    );

    const links = useMemo(() => {
        const orderedItems = orderBy(
            (navigation ?? []).filter(item => item.showInMainMenu),
            ['order'],
            ['asc']
        );

        return (function recurse(items: any) {
            const links: LinkType[] = [];

            for (const item of items) {
                const link: LinkType = {
                    id: item.id,
                    href: item.href,
                    title: item.name,
                    svgIcon: item.svgIcon,
                    images: item.images,
                    type: item.type
                };

                const subItems = (orderedItems as any).filter(
                    (subItem: any) =>
                        subItem.path.startsWith(`${item.path}/`) &&
                        subItem.depth === item.depth + 1 &&
                        subItem.id !== item.id
                );
                if (subItems.length > 0) {
                    link.children = recurse(subItems);
                }

                links.push(link);
            }

            return links;
        })(
            (orderedItems as any)
                .map((item: any) => {
                    // @ts-ignore
                    item.depth = item.path.split('/').length - 1;

                    return item;
                })
                .filter((item: any) => !item.path.includes('/'))
        );
    }, [navigation]);

    // Watch router.
    useEffect(() => {
        const handleRouteChange = (
            url: string,
            {shallow}: {shallow: boolean}
        ) => {
            if (shallow) {
                return;
            }

            if (isMegaMenuOpened) {
                setIsMegaMenuOpened(false);
            }

            setActiveLink(undefined);
        };

        router.events.on('routeChangeStart', handleRouteChange);

        return () => {
            router.events.off('routeChangeStart', handleRouteChange);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isMegaMenuOpened]);

    const isMounted = useRef(false);
    useEffect(() => {
        isMounted.current = true;

        return () => {
            isMounted.current = false;
        };
    }, []);
    const isBackgroundLayerShown = useMemo(
        () =>
            !(
                isMounted.current &&
                !isMegaMenuOpened &&
                !isMegaMenuCloseInProgress
            ),
        [isMegaMenuOpened, isMegaMenuCloseInProgress]
    );

    const timerRef = useRef<any>(null);

    const activeLinkHandler = (link: LinkType) => {
        if (Array.isArray(link.children) && link.children.length > 0) {
            clearTimeout(timerRef.current);
            if (activeLink === undefined) {
                timerRef.current = setTimeout(() => {
                    setActiveLink(link);
                    setIsMegaMenuOpened(true);
                }, 300);
            } else {
                setActiveLink(link);
                setIsMegaMenuOpened(true);
            }
        } else {
            setActiveLink(link);
            setIsMegaMenuOpened(false);
        }
    };

    return (
        <>
            {links.length > 0 ? (
                <>
                    <nav
                        ref={navRef}
                        className={cls(
                            'hidden h-nav-bar border-b bg-white xl:block',
                            {
                                'z-[999]':
                                    isMegaMenuOpened ||
                                    isMegaMenuCloseInProgress
                            }
                        )}
                        onMouseLeave={() => {
                            setIsMegaMenuOpened(false);
                            setActiveLink(undefined);
                            clearTimeout(timerRef.current);
                        }}
                    >
                        <div ref={containerRef} className="container relative">
                            <NavBarMainLinksPartial
                                activeLink={activeLink}
                                links={links}
                                onMouseEnter={activeLinkHandler}
                            />

                            <UiTransition
                                as={Fragment}
                                enter="transition duration-300"
                                enterFrom="opacity-0"
                                enterTo="opacity-100"
                                leave="transition duration-200"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                                show={isMegaMenuOpened}
                            >
                                <div
                                    className="absolute left-0 right-0 z-dropdown origin-top rounded-b border border-t-0 border-gray-200 bg-white shadow-sm outline-none"
                                    style={{
                                        top: `${navHeight + 1}px`,
                                        width: `${containerWidth}px`
                                    }}
                                >
                                    {activeLink &&
                                        Array.isArray(activeLink.children) &&
                                        activeLink.children.length > 0 && (
                                            <NavBarSubLinksPartial
                                                activeLink={activeLink}
                                            />
                                        )}
                                </div>
                            </UiTransition>
                        </div>
                    </nav>
                </>
            ) : null}

            {isBackgroundLayerShown && (
                <UiTransition
                    show={isMegaMenuOpened}
                    as={Fragment}
                    enter="transition duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 z-20 bg-gray-900 bg-opacity-20 transition-opacity" />
                </UiTransition>
            )}
        </>
    );
});

if (isDev) {
    NavBarPartial.displayName = 'NavBarPartial';
}

export default NavBarPartial;
