import {FC, memo} from 'react';
import {cls, isDev} from '@core/helpers';
import useLayout from '../../useLayout';
import HeaderLogoPartial from './Logo';
import HeaderSearchBarPartial from './SearchBar';
import HeaderUserNavPartial from './UserNav';

const HeaderPartial: FC = memo(() => {
    const {isMegaMenuOpened, isMegaMenuCloseInProgress} = useLayout();

    return (
        <header
            className={cls('hidden h-header bg-white pb-4 xl:block', {
                'z-dropdown': isMegaMenuOpened || isMegaMenuCloseInProgress
            })}
        >
            <div className="container flex h-full items-stretch">
                <HeaderLogoPartial />
                <HeaderSearchBarPartial />
                <HeaderUserNavPartial />
            </div>
        </header>
    );
});

if (isDev) {
    HeaderPartial.displayName = 'HeaderPartial';
}

export default HeaderPartial;
