import {FC, memo, PropsWithChildren} from 'react';
import {isDev} from '@core/helpers';
import {BlogProvider} from './context';
import FooterPartial from './partials/Footer';
import HeaderPartial from './partials/Header';
import TopBarPartial from './partials/TopBar';

type BlogLayoutProps = {
    pageProps: Record<string, any>;
};

const BlogLayout: FC<PropsWithChildren<BlogLayoutProps>> = memo(
    ({pageProps, children}) => {
        return (
            <BlogProvider blogPosts={pageProps.blogPosts}>
                <div className="relative w-full bg-white xl:flex xl:min-h-screen xl:flex-col xl:flex-nowrap">
                    <TopBarPartial />
                    <HeaderPartial categories={pageProps.categories} />

                    <main className="xl:flex-1">{children}</main>

                    <FooterPartial />
                </div>
            </BlogProvider>
        );
    }
);

if (isDev) {
    BlogLayout.displayName = 'BlogLayout';
}

export default BlogLayout;
