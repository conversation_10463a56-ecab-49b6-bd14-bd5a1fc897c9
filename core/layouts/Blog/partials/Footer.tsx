import siteLogo from '@assets/images/common/site-logo.png';
import storeConfig from '~/store.config';
import {useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {
    FacebookIcon,
    InstagramIcon,
    LinkedinIcon,
    YoutubeIcon
} from '@core/icons/brand';

const FooterPartial = () => {
    const t = useTrans();

    return (
        <footer className="mt-auto border-t">
            <div className="container flex flex-col items-center justify-between gap-4 py-6 text-sm md:flex-row md:gap-0">
                <UiLink href="/blog" className="transition hover:opacity-70">
                    <UiImage
                        src={siteLogo}
                        alt={storeConfig.title}
                        height={20}
                    />
                </UiLink>
                <UiLink href="/" className="transition hover:opacity-70">
                    <UiImage
                        src={siteLogo}
                        alt={storeConfig.title}
                        height={20}
                    />
                </UiLink>
                <div className="flex items-center space-x-4">
                    <a
                        href="https://www.facebook.com/Baxanacom/"
                        rel="noopener noreferrer"
                        target="_blank"
                        title="Facebook"
                        className="flex h-10 w-10 items-center justify-center rounded-full border transition hover:border-primary-600 hover:bg-primary-600 hover:text-white"
                    >
                        <FacebookIcon className="h-4 w-4" />
                    </a>
                    <a
                        href="https://www.instagram.com/baxana_com/"
                        rel="noopener noreferrer"
                        target="_blank"
                        title="Instagram"
                        className="flex h-10 w-10 items-center justify-center rounded-full border transition hover:border-primary-600 hover:bg-primary-600 hover:text-white"
                    >
                        <InstagramIcon className="h-4 w-4" />
                    </a>
                    <a
                        href="https://www.linkedin.com/company/baxana-com"
                        rel="noopener noreferrer"
                        target="_blank"
                        title="LinkedIn"
                        className="flex h-10 w-10 items-center justify-center rounded-full border transition hover:border-primary-600 hover:bg-primary-600 hover:text-white"
                    >
                        <LinkedinIcon className="h-4 w-4" />
                    </a>
                    <a
                        href="https://www.youtube.com/@baxanacom"
                        rel="noopener noreferrer"
                        target="_blank"
                        title="Youtube"
                        className="flex h-10 w-10 items-center justify-center rounded-full border transition hover:border-primary-600 hover:bg-primary-600 hover:text-white"
                    >
                        <YoutubeIcon className="h-4 w-4" />
                    </a>
                </div>
            </div>
            <div className="bg-primary-600 p-6 text-center text-xs text-white">
                {t('Baxana bir Erler Grup kuruluşudur.')}
            </div>
        </footer>
    );
};

export default FooterPartial;
