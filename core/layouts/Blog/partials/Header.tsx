import {FC, memo, useMemo} from 'react';
import {useRouter} from 'next/router';
import {Category} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {UiLink} from '@core/components/ui';

type HeaderPartialProps = {
    categories: Category[];
};

const HeaderPartial: FC<HeaderPartialProps> = memo(({categories}) => {
    const router = useRouter();

    const isCategoryOrTagPage =
        router.asPath.includes('categories') || router.asPath.includes('tags');

    const categoryName = useMemo(() => {
        return categories.find(
            category => category.slug === router.asPath.split('/')[3]
        )?.name;
    }, [router, categories]);

    return (
        <>
            <ul className="hide-scrollbar container my-2 flex justify-between gap-5 overflow-x-auto text-lg font-semibold lg:justify-center lg:gap-10">
                {categories?.map(category => (
                    <li key={category.slug}>
                        <UiLink
                            className={cls(
                                'whitespace-nowrap transition hover:text-[#7bc377]',
                                {
                                    'text-[#7bc377]':
                                        router.asPath.split('/')[3] ===
                                        category.slug
                                }
                            )}
                            href={'categories/' + category.slug}
                        >
                            <h2>{category.name}</h2>
                        </UiLink>
                    </li>
                ))}
            </ul>
            {isCategoryOrTagPage && (
                <div className="flex h-12 items-center justify-center bg-[#7bc377]">
                    <h2 className="text-3xl text-white">{categoryName}</h2>
                </div>
            )}
        </>
    );
});

if (isDev) {
    HeaderPartial.displayName = 'HeaderPartial';
}

export default HeaderPartial;
