import {memo, useEffect, useState} from 'react';
import blogLogo from '@assets/images/common/site-logo.png';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {useDebounce} from '@core/hooks';
import {UiImage, UiInput, UiLink} from '@core/components/ui';
import useBlog from '../context';

const TopBarPartial = memo(() => {
    const [value, setValue] = useState('');

    const debouncedValue = useDebounce(value);

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setValue(event.target.value);
    };

    const {onSearchHandler} = useBlog();

    useEffect(() => {
        onSearchHandler(debouncedValue);
    }, [debouncedValue, onSearchHandler]);

    return (
        <div className="border-b-4 border-t-8 border-b-[#7bc377] border-t-primary-600 py-4 md:pt-8">
            <div className="container flex flex-col items-center justify-between gap-4 md:flex-row md:gap-8">
                <UiLink href="/blog">
                    <UiImage
                        src={blogLogo}
                        alt={storeConfig.title}
                        priority
                        width={200}
                        height={40}
                    />
                </UiLink>

                <UiInput
                    onChange={handleChange}
                    value={value}
                    className="w-full xl:w-1/4"
                    placeholder="Blog ara.."
                />
            </div>
        </div>
    );
});

if (isDev) {
    TopBarPartial.displayName = 'TopBarPartial';
}

export default TopBarPartial;
