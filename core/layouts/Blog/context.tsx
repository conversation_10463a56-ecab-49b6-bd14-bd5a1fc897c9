import {
    createContext,
    FC,
    memo,
    ReactNode,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState
} from 'react';
import {BlogPost} from '@core/types';
import {isDev, jsonRequest} from '@core/helpers';

type BlogContextType = {
    blogPosts: BlogPost[];
    setBlogPosts: React.Dispatch<React.SetStateAction<BlogPost[]>>;
    onSearchHandler: (value: string) => void;
};

export const BlogContext = createContext<BlogContextType>(null as any);

type BlogProviderProps = {
    children: ReactNode;
    blogPosts: BlogPost[];
};

export const BlogProvider: FC<BlogProviderProps> = memo(
    ({blogPosts: initialBlogPosts, children}) => {
        const [blogPosts, setBlogPosts] = useState(() => initialBlogPosts);

        useEffect(() => {
            setBlogPosts(initialBlogPosts);
        }, [initialBlogPosts]);

        const onSearchHandler = useCallback(
            async (value: string) => {
                if (value.length < 2) {
                    setBlogPosts(initialBlogPosts);
                    return;
                }

                try {
                    const posts = await jsonRequest({
                        url: '/api/blog/search',
                        method: 'POST',
                        data: value.trim()
                    });

                    setBlogPosts(posts);
                } catch (err) {
                    setBlogPosts(initialBlogPosts);
                }
            },
            [initialBlogPosts]
        );

        const value = useMemo(
            () => ({
                blogPosts,
                setBlogPosts,
                onSearchHandler
            }),
            [blogPosts, onSearchHandler]
        );

        return (
            <BlogContext.Provider value={value}>
                {children}
            </BlogContext.Provider>
        );
    }
);

if (isDev) {
    BlogProvider.displayName = 'BlogProvider';
}

export default function useBlog() {
    const context = useContext(BlogContext);

    if (context === undefined) {
        throw new Error(`useContext must be used within a BlogProvider!`);
    }

    return context;
}
