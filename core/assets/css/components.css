@layer components {
    .divider {
        @apply !border-gray-200 !opacity-100;
    }

    .scroller::-webkit-scrollbar {
        @apply xl:h-[8px] xl:w-[8px];
    }

    .scroller::-webkit-scrollbar-track {
        @apply xl:rounded xl:bg-gray-100 xl:transition;
    }

    .scroller::-webkit-scrollbar-track:hover {
        @apply xl:bg-gray-200;
    }

    .scroller::-webkit-scrollbar-thumb {
        @apply xl:rounded xl:bg-gray-400 xl:transition;
    }

    .scroller::-webkit-scrollbar-thumb:hover {
        @apply xl:bg-gray-600;
    }

    @property --transparent-scrollbar {
        syntax: '<color>';
        inherits: true;
        initial-value: transparent;
    }

    .scrollbar-fade {
        transition: --transparent-scrollbar 0.25s;
    }

    .scrollbar-fade:hover {
        --transparent-scrollbar: theme('backgroundColor.gray.400');
    }

    .scrollbar-fade::-webkit-scrollbar {
        @apply h-2 w-2 bg-transparent;
    }

    .scrollbar-fade::-webkit-scrollbar-thumb {
        background: var(--transparent-scrollbar);
        border-radius: 4px;
    }

    .scrollbar-fade::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400;
    }

    /* Single Product Slider */
    .thumb-swiper .swiper-slide-thumb-active {
        @apply rounded border !border-primary-600;
    }

    .thumb-swiper .swiper-slide {
        @apply rounded border border-transparent;
    }

    .thumb-swiper-wrapper .swiper-button-next,
    .thumb-swiper-wrapper .swiper-button-prev {
        @apply hidden h-10 w-10 bg-transparent text-white xl:flex;
        text-shadow: 0 0 3px rgb(221 224 228), 0 0 2px #000000;
    }

    .disable-slide-navigation {
        @apply !hidden;
    }

    /* Grouped Product Slider */
    .product-slider {
        --swiper-navigation-size: 16px;
    }

    .product-slider .swiper-wrapper {
        @apply items-stretch;
    }

    .product-slider .swiper-wrapper .swiper-slide {
        @apply h-[initial] px-1 pb-4;
    }

    .product-slider .swiper-button-next,
    .product-slider .swiper-button-prev {
        @apply top-[calc(50%-60px)] hidden h-10 w-10 origin-bottom scale-0 rounded-full bg-white bg-opacity-40 text-default opacity-0 transition hover:bg-primary-600 hover:text-white xl:flex;
    }

    .product-slider .swiper-button-next {
        @apply origin-left;
    }

    .product-slider .swiper-button-prev {
        @apply origin-right;
    }

    .product-slider:hover .swiper-button-next,
    .product-slider:hover .swiper-button-prev {
        @apply scale-100 opacity-100 shadow-lg;
    }

    .prose .ql-align-center {
        @apply text-right;
    }

    .prose .ql-align-right {
        @apply text-right;
    }

    .shipping-status-road {
        @apply h-5 rounded-full bg-gray-200;
    }

    .shipping-status-road::before {
        @apply absolute left-0 top-1/2 z-[2] h-1 w-full -translate-y-1/2 content-[''];
        background: linear-gradient(
            90deg,
            rgb(156 163 175 / 0.6) 0%,
            rgb(156 163 175 / 0.6) 70%,
            rgb(229 231 235 / var(--tw-bg-opacity)) 70%,
            rgb(229 231 235 / var(--tw-bg-opacity)) 100%
        );
        background-size: 64px;
    }

    .shipping-vertical-status-road {
        @apply h-full w-5 rounded-full bg-gray-200;
    }

    .shipping-vertical-status-road::before {
        @apply absolute left-1/2 top-0 z-[2] h-full w-1;

        content: ' ';
        transform: translateX(-50%);
        background: linear-gradient(
            0deg,
            rgb(156 163 175 / 0.6) 0%,
            rgb(156 163 175 / 0.6) 70%,
            rgb(229 231 235 / var(--tw-bg-opacity)) 70%,
            rgb(229 231 235 / var(--tw-bg-opacity)) 100%
        );
        background-size: 4px 64px;
    }

    .product-slider-button {
        @apply flex h-16 w-16 items-center justify-center rounded-full bg-white text-default transition hover:bg-primary-600 hover:text-white;
    }

    .swiper-padding .swiper {
        @apply p-3;
    }

    .swiper-story-padding .swiper {
        @apply p-[3px];
    }

    .shadow-card {
        @apply shadow-[0_0_7px_rgb(211,211,211)];
    }

    .skeleton-card {
        @apply relative isolate overflow-hidden rounded-md bg-gray-200 before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_1s_infinite] before:border-t before:border-gray-100/20 before:bg-gradient-to-r before:from-transparent before:via-gray-100/70 before:to-transparent;
    }

    @keyframes shimmer {
        to {
            transform: translateX(100%);
        }
    }

    .color-variant-slider .swiper {
        @apply px-1;
    }

    .home-main-slider {
        --swiper-navigation-size: 24px;
    }

    @media screen and (max-width: 768px) {
        .home-main-slider {
            --swiper-navigation-size: 18px;
        }
    }

    .home-main-slider .swiper-button-next,
    .home-main-slider .swiper-button-prev {
        @apply -mt-5 h-10 w-10 origin-bottom scale-0 rounded-full bg-white bg-opacity-40 text-default opacity-0 transition hover:bg-primary-600 hover:text-white md:-mt-8 md:h-16 md:w-16;
    }

    .home-main-slider .swiper-button-next {
        @apply right-6 origin-left;
    }

    .home-main-slider .swiper-button-prev {
        @apply left-6 origin-right;
    }

    .home-main-slider:hover .swiper-button-next,
    .home-main-slider:hover .swiper-button-prev {
        @apply scale-100 opacity-100 shadow-lg;
    }

    .product-slider-button {
        @apply flex h-16 w-16 items-center justify-center rounded-full bg-white text-default transition duration-150 ease-in-out hover:bg-primary-600 hover:text-white;
    }

    /* Frequently Asked Questions Accordion Class */
    .faq-accordion details {
        @apply isolate px-4 py-2;
    }

    .faq-accordion summary {
        @apply relative flex cursor-pointer select-none list-none items-center justify-between py-2;
    }

    .faq-accordion summary p {
        @apply w-10/12 text-sm font-medium xl:w-full xl:text-base;
    }

    .faq-accordion span {
        @apply block pb-4 pt-2 text-sm;
    }

    /* For Safari to not to show default list marker */
    .faq-accordion summary::-webkit-details-marker {
        @apply hidden;
    }

    .faq-accordion summary::before {
        @apply absolute right-0 !ml-4 h-8 w-8 rounded-full bg-gray-200 content-[''];
    }

    .faq-accordion summary::after {
        content: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' width='20px' height='20px' fill='gray'><path d='M432 256c0 17.69-14.33 32.01-32 32.01H256v144c0 17.69-14.33 31.99-32 31.99s-32-14.3-32-31.99v-144H48c-17.67 0-32-14.32-32-32.01s14.33-31.99 32-31.99H192v-144c0-17.69 14.33-32.01 32-32.01s32 14.32 32 32.01v144h144C417.7 224 432 238.3 432 256z' /></svg>");
        width: 26px;
        height: 20px;
        position: absolute;
        right: 0;
    }

    .faq-accordion details[open] summary::after {
        content: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' width='20px' height='20px' fill='gray'><path d='M400 288h-352c-17.69 0-32-14.32-32-32.01s14.31-31.99 32-31.99h352c17.69 0 32 14.3 32 31.99S417.7 288 400 288z' /></svg>");
        width: 26px;
        height: 20px;
        position: absolute;
        right: 0;
    }

    /* Hide Scrollbar for mobile navigation slider */
    .hide-scrollbar::-webkit-scrollbar {
        @apply hidden;
    }

    .hide-scrollbar {
        scrollbar-width: none;
        -webkit-overflow-scrolling: touch;
    }

    /* Fixes a bug due to column count property on chrome */
    .column-breaks {
        -webkit-column-break-inside: avoid;
        page-break-inside: avoid;
        break-inside: avoid;
    }

    .mobile-menu-underline {
        @apply relative;
    }

    .mobile-menu-underline:after {
        @apply absolute bottom-0 left-1/2 inline-block h-1 w-full -translate-x-1/2 bg-primary-600 content-[''];
    }

    .cookie-btn-accept {
        @apply ml-3 rounded-full border-2 border-transparent bg-primary-600 px-12 py-3 font-bold text-white transition;
    }

    .cookie-btn-accept:hover {
        @apply bg-primary-600/80;
    }

    .cookie-btn-decline {
        @apply ml-3 rounded-full border-2 border-primary-600 bg-white px-12 py-3 font-bold text-primary-600 transition;
    }

    .cookie-btn-decline:hover {
        @apply bg-primary-600/20;
    }

    .cookie-container {
        @apply fixed bottom-0 left-0 right-0 z-[1000] flex flex-col items-center gap-4 border-t border-primary-600 bg-white px-8 py-5 text-sm text-black shadow-[0_5px_20px_0px_rgba(0,0,0,0.1)] md:flex-row md:px-16 lg:px-32;
    }

    .cookie-btn-wrapper {
        @apply flex items-center whitespace-nowrap;
    }
}

/* React-image-lightbox classes */
.ril-outer.ril__outer {
    @apply z-[1000] bg-gray-900 bg-opacity-20 transition duration-300;
}

.ril-outer .ril__builtinButton {
    @apply transition;
}

.ril-outer .ril__navButtonPrev,
.ril-outer .ril__navButtonNext {
    @apply bg-gray-900 bg-opacity-20 transition;
}

.ril-outer .ril__navButtonPrev {
    @apply rounded-br rounded-tr;
}

.ril-outer .ril__navButtonNext {
    @apply rounded-bl rounded-tl;
}

.ril-outer .ril__caption,
.ril-outer .ril__toolbar {
    @apply bg-gray-900 bg-opacity-20;
}
