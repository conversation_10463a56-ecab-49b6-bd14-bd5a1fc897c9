import {useEffect, useState} from 'react';

const useIOSDevice = () => {
    const [isIOSDevice, setIsIOSDevice] = useState(false);

    useEffect(() => {
        if (
            /^((?!chrome|android).)*safari/i.test(navigator.userAgent) ||
            (window.matchMedia('(display-mode: standalone)').matches &&
                !!navigator.platform.match(/iPhone|iPod|iPad/) &&
                (window.screen.width / window.screen.height).toFixed(3) ===
                    '0.462')
        )
            setIsIOSDevice(true);
    }, []);

    return {isIOSDevice};
};

export default useIOSDevice;
