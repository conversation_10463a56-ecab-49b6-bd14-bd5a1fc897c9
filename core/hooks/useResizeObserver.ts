import {useEffect, useMemo, useRef, useState} from 'react';
import ResizeObserver from 'resize-observer-polyfill';

type ObserverRect = Omit<DOMRectReadOnly, 'toJSON'>;

const defaultState: ObserverRect = {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0
};

const browser = typeof window !== 'undefined';

export function useResizeObserver<T extends HTMLElement = any>() {
    const frameID = useRef(0);
    const ref = useRef<T>(null);

    const [rect, setRect] = useState<ObserverRect>(defaultState);

    const observer = useMemo(
        () =>
            browser
                ? new ResizeObserver((entries: any) => {
                      const entry = entries[0];

                      if (entry) {
                          cancelAnimationFrame(frameID.current);

                          frameID.current = requestAnimationFrame(() => {
                              if (ref.current) {
                                  setRect(entry.contentRect);
                              }
                          });
                      }
                  })
                : null,
        []
    );

    useEffect(() => {
        if (ref.current) {
            observer?.observe(ref.current);
        }

        return () => {
            observer?.disconnect();

            if (frameID.current) {
                cancelAnimationFrame(frameID.current);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [ref.current]);

    return [ref, rect] as const;
}

export function useElementSize<T extends HTMLElement = any>() {
    const [ref, {width, height}] = useResizeObserver<T>();

    return {ref, width, height};
}
