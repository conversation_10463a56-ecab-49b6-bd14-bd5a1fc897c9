import {useCallback, useEffect, useRef} from 'react';
import useReducedMotion from './useReducedMotion';
import useWindowEvent from './useWindowEvent';

const easeInOutQuad = (t: number) =>
    t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;

const getRelativePosition = ({
    axis,
    target,
    parent,
    alignment,
    offset,
    isList
}: Record<string, any>): number => {
    if (!target || (!parent && typeof document === 'undefined')) {
        return 0;
    }
    const isCustomParent = !!parent;
    const parentElement = parent || document.body;
    const parentPosition = parentElement.getBoundingClientRect();
    const targetPosition = target.getBoundingClientRect();

    const getDiff = (property: 'top' | 'left'): number =>
        targetPosition[property] - parentPosition[property];

    if (axis === 'y') {
        const diff = getDiff('top');

        if (diff === 0) return 0;

        if (alignment === 'start') {
            const distance = diff - offset;
            const shouldScroll =
                distance <= targetPosition.height * (isList ? 0 : 1) || !isList;

            return shouldScroll ? distance : 0;
        }

        const parentHeight = isCustomParent
            ? parentPosition.height
            : window.innerHeight;

        if (alignment === 'end') {
            const distance =
                diff + offset - parentHeight + targetPosition.height;
            const shouldScroll =
                distance >= -targetPosition.height * (isList ? 0 : 1) ||
                !isList;

            return shouldScroll ? distance : 0;
        }

        if (alignment === 'center') {
            return diff - parentHeight / 2 + targetPosition.height / 2;
        }

        return 0;
    }

    if (axis === 'x') {
        const diff = getDiff('left');

        if (diff === 0) return 0;

        if (alignment === 'start') {
            const distance = diff - offset;
            const shouldScroll = distance <= targetPosition.width || !isList;

            return shouldScroll ? distance : 0;
        }

        const parentWidth = isCustomParent
            ? parentPosition.width
            : window.innerWidth;

        if (alignment === 'end') {
            const distance = diff + offset - parentWidth + targetPosition.width;
            const shouldScroll = distance >= -targetPosition.width || !isList;

            return shouldScroll ? distance : 0;
        }

        if (alignment === 'center') {
            return diff - parentWidth / 2 + targetPosition.width / 2;
        }

        return 0;
    }

    return 0;
};

const getScrollStart = ({axis, parent}: Record<string, any>) => {
    if (!parent && typeof document === 'undefined') {
        return 0;
    }

    const method = axis === 'y' ? 'scrollTop' : 'scrollLeft';

    if (parent) {
        return parent[method];
    }

    const {body, documentElement} = document;

    // while one of it has a value the second is equal 0
    return body[method] + documentElement[method];
};

const setScrollParam = ({axis, parent, distance}: Record<string, any>) => {
    if (!parent && typeof document === 'undefined') {
        return;
    }

    const method = axis === 'y' ? 'scrollTop' : 'scrollLeft';

    if (parent) {
        // eslint-disable-next-line no-param-reassign
        parent[method] = distance;
    } else {
        const {body, documentElement} = document;

        // https://www.w3schools.com/jsref/prop_element_scrolltop.asp
        body[method] = distance;
        documentElement[method] = distance;
    }
};

interface ScrollIntoViewAnimation {
    /** target element alignment relatively to parent based on current axis */
    alignment?: 'start' | 'end' | 'center';
}

interface ScrollIntoViewParams {
    /** callback fired after scroll */
    onScrollFinish?: () => void;

    /** duration of scroll in milliseconds */
    duration?: number;

    /** axis of scroll */
    axis?: 'x' | 'y';

    /** custom mathematical easing function */
    easing?: (t: number) => number;

    /** additional distance between nearest edge and element */
    offset?: number;

    /** indicator if animation may be interrupted by user scrolling */
    cancelable?: boolean;

    /** prevents content jumping in scrolling lists with multiple targets */
    isList?: boolean;

    target?: string | HTMLElement;
}

export default function useScrollIntoView<
    Target extends HTMLElement,
    Parent extends HTMLElement | null = null
>({
    duration = 500,
    axis = 'y',
    onScrollFinish,
    easing = easeInOutQuad,
    offset = 0,
    cancelable = true,
    isList = false,
    target
}: ScrollIntoViewParams = {}) {
    const frameID = useRef(0);
    const startTime = useRef(0);
    const shouldStop = useRef(false);

    const scrollableRef = useRef<Parent>(null);
    const targetRef = useRef<Target>(null);

    const reducedMotion = useReducedMotion();

    const cancel = (): void => {
        if (frameID.current) {
            cancelAnimationFrame(frameID.current);
        }
    };

    const scrollIntoView = useCallback(
        ({alignment = 'start'}: ScrollIntoViewAnimation = {}) => {
            const contentWrapper = document.querySelector('.content-wrapper');

            shouldStop.current = false;

            if (frameID.current) {
                cancel();
            }

            const start =
                getScrollStart({
                    parent: scrollableRef.current ?? contentWrapper,
                    axis
                }) ?? 0;

            const change =
                getRelativePosition({
                    parent: scrollableRef.current ?? contentWrapper,
                    target: targetRef.current,
                    axis,
                    alignment,
                    offset,
                    isList
                }) - (scrollableRef.current ?? contentWrapper ? 0 : start);

            function animateScroll() {
                if (startTime.current === 0) {
                    startTime.current = performance.now();
                }

                const now = performance.now();
                const elapsed = now - startTime.current;

                // easing timing progress
                const t =
                    reducedMotion || duration === 0 ? 1 : elapsed / duration;

                const distance = start + change * easing(t);

                setScrollParam({
                    parent: scrollableRef.current ?? contentWrapper,
                    axis,
                    distance
                });

                if (!shouldStop.current && t < 1) {
                    frameID.current = requestAnimationFrame(animateScroll);
                } else {
                    typeof onScrollFinish === 'function' && onScrollFinish();
                    startTime.current = 0;
                    frameID.current = 0;
                    cancel();
                }
            }

            animateScroll();
        },
        // eslint-disable-next-line
        [scrollableRef.current]
    );

    const handleStop = () => {
        if (cancelable) {
            shouldStop.current = true;
        }
    };

    /**
     * detection of one of these events stops scroll animation
     * wheel - mouse wheel / touch pad
     * touchmove - any touchable device
     */

    useWindowEvent('wheel', handleStop, {
        passive: true
    });

    useWindowEvent('touchmove', handleStop, {
        passive: true
    });

    useEffect(() => {
        if (typeof target !== 'undefined') {
            if (typeof target === 'string') {
                // @ts-ignore
                targetRef.current = document.getElementById(target) ?? null;
            } else {
                // @ts-ignore
                targetRef.current = target;
            }
        }
    }, [target]);

    // cleanup requestAnimationFrame
    useEffect(() => cancel, []);

    return {
        scrollableRef,
        targetRef,
        scrollIntoView,
        cancel
    };
}
