import {useContext} from 'react';
import {UiContext} from '../context';

export default function useUI() {
    const context = useContext(UiContext);

    if (context === undefined) {
        throw new Error(`useUI must be used within a UiProvider!`);
    }

    return {
        openModal: context.openModal,
        closeModal: context.closeModal,
        openSideBar: context.openSideBar,
        closeSideBar: context.closeSideBar,
        sideBarTitle: context.sideBarTitle,
        setSideBarTitle: context.setSideBarTitle,
        confirm: context.confirm
    };
}
