import {DependencyList, useCallback, useRef} from 'react';

export default function useDebouncedCallback<T extends (...args: any) => any>(
    callback: T,
    wait: number,
    deps: DependencyList
) {
    const timeoutIdx = useRef<any>();

    return useCallback(
        (...args: any) => {
            const later = () => {
                clearTimeout(timeoutIdx.current);
                callback(...args);
            };

            clearTimeout(timeoutIdx.current);
            timeoutIdx.current = setTimeout(later, wait);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [callback, wait, ...deps]
    );
}
