import {useMemo} from 'react';
import {useSession} from 'next-auth/react';
import {Customer} from '@core/types';
import {useStore} from './';

function useCustomer() {
    const {data: session} = useSession();
    const {updatedCustomer} = useStore();

    return useMemo(() => {
        if (typeof updatedCustomer !== 'undefined') {
            return updatedCustomer;
        }

        if (!!session?.user) {
            return session?.user as Customer;
        }

        return;
    }, [session, updatedCustomer]);
}

export default useCustomer;
