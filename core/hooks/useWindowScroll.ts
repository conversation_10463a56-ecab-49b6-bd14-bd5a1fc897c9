import {useState} from 'react';
import useWindowEvent from './useWindowEvent';

interface ScrollPosition {
    x: number;
    y: number;
}

function getScrollPosition(): ScrollPosition {
    return typeof window !== 'undefined'
        ? {x: window.pageXOffset, y: window.pageYOffset}
        : {x: 0, y: 0};
}

function scrollTo({x, y}: Partial<ScrollPosition>) {
    if (typeof window !== 'undefined') {
        const scrollOptions: ScrollToOptions = {behavior: 'smooth'};

        if (typeof x === 'number') {
            scrollOptions.left = x;
        }

        if (typeof y === 'number') {
            scrollOptions.top = y;
        }

        window.scrollTo(scrollOptions);
    }
}

export default function useWindowScroll() {
    const [position, setPosition] = useState(getScrollPosition());

    useWindowEvent('scroll', () => setPosition(getScrollPosition()));
    useWindowEvent('resize', () => setPosition(getScrollPosition()));

    return [position, scrollTo] as const;
}
