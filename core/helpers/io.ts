import {io} from 'socket.io-client';
import * as parser from './parser';

const API_URL = (process.env.NEXT_PUBLIC_API_URL as string).replace(
    '/api/store',
    ''
);

export default function socketIO(storeId: string) {
    return io(API_URL, {
        path: '/ws/',
        transports: ['websocket'],
        upgrade: false,
        parser,
        query: {
            storeId: storeId,
            'x-ecommerce-client': 'yes'
        }
    });
}
