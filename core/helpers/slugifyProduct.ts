import base64 from './base64';
import parseURLSearchParams from './parseURLSearchParams';

export default function slugifyProduct(slug: string) {
    let processedSlug = slug;
    let selectedAttributes = null;
    if (slug.indexOf('-') !== -1) {
        const parts = processedSlug.split('-');
        const lastPart = parts[parts.length - 1];
        const decoded = base64.decode(lastPart);
        if (decoded.length > 0 && decoded.includes('=')) {
            const parsed = parseURLSearchParams(decoded);
            if (Object.keys(parsed).length > 0 && !!parsed._es) {
                delete parsed._es;
                processedSlug = parts.slice(0, -1).join('-');
                selectedAttributes = parsed;
            }
        }
    }
    return {processedSlug, selectedAttributes};
}
