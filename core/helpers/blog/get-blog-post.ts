import {BlogPost} from '@core/types';
import jsonRequest from '../jsonRequest';
import {BLOG_URL} from './blog-url';

export default async function getBlogPost(data: object): Promise<BlogPost> {
    try {
        return await jsonRequest({
            url: `${BLOG_URL}/post`,
            method: 'POST',
            data
        });
    } catch (err) {
        throw new Error('Something went wrong while fetching blog post', {
            cause: err
        });
    }
}
