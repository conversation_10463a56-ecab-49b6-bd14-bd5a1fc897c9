import {Category} from '@core/types';
import jsonRequest from '../jsonRequest';
import {BLOG_URL} from './blog-url';

export default async function getCategories(
    data?: object
): Promise<Category[]> {
    try {
        return await jsonRequest({
            url: `${BLOG_URL}/categories`,
            method: 'POST',
            data
        });
    } catch (err) {
        throw new Error('Something went wrong while fetching blog categories', {
            cause: err
        });
    }
}
