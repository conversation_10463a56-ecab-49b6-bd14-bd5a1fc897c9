import {Tag} from '@core/types';
import jsonRequest from '../jsonRequest';
import {BLOG_URL} from './blog-url';

export default async function getTags(data?: object): Promise<Tag[]> {
    try {
        return await jsonRequest({
            url: `${BLOG_URL}/tags`,
            method: 'POST',
            data
        });
    } catch (err) {
        throw new Error('Something went wrong while fetching blog tags', {
            cause: err
        });
    }
}
