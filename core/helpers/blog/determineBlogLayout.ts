import {BlogPost, Category, PageProps, Tag} from '@core/types';
import getBlogPost from './get-blog-post';
import getBlogPosts from './get-blog-posts';
import getCategories from './get-categories';
import getTags from './get-tags';

interface BlogPageProps extends PageProps {
    blogPosts: BlogPost[];
    popularBlogPosts: BlogPost[];
    featuredBlogPosts: BlogPost[];
    blogPost: BlogPost;
    tags: Tag[];
    categories: Category[];
}

interface BlogLayoutArgs {
    props: BlogPageProps;
    slug: string | string[] | undefined;
}

async function determineBlogLayout({props, slug}: BlogLayoutArgs) {
    props.tags = await getTags();
    props.categories = await getCategories();

    if (slug === undefined) {
        try {
            props.pageType = 'blog';
            props.blogPosts = await getBlogPosts();
            props.popularBlogPosts = await getBlogPosts({isPopular: true});
            props.featuredBlogPosts = await getBlogPosts({isFeatured: true});

            if (
                Array.isArray(props.blogPosts) &&
                props.blogPosts.length === 0
            ) {
                return {props, notFound: true};
            } else {
                return {props};
            }
        } catch (err) {
            return {
                props,
                notFound: true
            };
        }
    } else if (slug.length === 1) {
        try {
            props.pageType = 'content';
            props.blogPost = await getBlogPost({slug: slug[0]});
            return {
                props
            };
        } catch (err) {
            return {
                props,
                notFound: true
            };
        }
    } else if (slug.length === 2) {
        if (slug[0] === 'tags') {
            try {
                props.pageType = 'blog';
                props.blogPosts = await getBlogPosts({tagSlug: slug[1]});
                props.popularBlogPosts = await getBlogPosts({
                    tagSlug: slug[1],
                    isPopular: true
                });
                props.featuredBlogPosts = await getBlogPosts({
                    tagSlug: slug[1],
                    isFeatured: true
                });

                if (
                    Array.isArray(props.blogPosts) &&
                    props.blogPosts.length === 0
                ) {
                    return {props, notFound: true};
                } else {
                    return {props};
                }
            } catch (err) {
                return {
                    props,
                    notFound: true
                };
            }
        } else if (slug[0] === 'categories') {
            try {
                props.pageType = 'blog';
                props.blogPosts = await getBlogPosts({categorySlug: slug[1]});
                props.popularBlogPosts = await getBlogPosts({
                    categorySlug: slug[1],
                    isPopular: true
                });
                props.featuredBlogPosts = await getBlogPosts({
                    categorySlug: slug[1],
                    isFeatured: true
                });

                if (
                    Array.isArray(props.blogPosts) &&
                    props.blogPosts.length === 0
                ) {
                    return {props, notFound: true};
                } else {
                    return {props};
                }
            } catch (err) {
                return {
                    props,
                    notFound: true
                };
            }
        } else {
            return {
                props,
                notFound: true
            };
        }
    } else {
        return {
            props,
            notFound: true
        };
    }
}

export default determineBlogLayout;
