import {trim} from '@core/helpers/str';

export default function parseURLSearchParams(queryString = '') {
    const parsed: Record<string, any> = {};
    let normalized = '';

    new URLSearchParams(queryString).forEach((value, name) => {
        if (value.includes(',')) {
            for (const part of value.split(',')) {
                normalized = `${normalized}&${name}=${part}`;
            }
        } else {
            normalized = `${normalized}&${name}=${value}`;
        }
    });

    new URLSearchParams(trim(normalized, '&')).forEach((value, name) => {
        if (Array.isArray(parsed[name])) {
            parsed[name].push(value);
        } else if (typeof parsed[name] !== 'undefined') {
            parsed[name] = [parsed[name], value];
        } else {
            parsed[name] = value;
        }
    });

    return parsed;
}
