// @ts-ignore
import notepack from 'notepack.io';
// @ts-ignore
import Emitter from 'component-emitter';

export const CONNECT = 0;
export const DISCONNECT = 1;
export const EVENT = 2;
export const ACK = 3;
export const ERROR = 4;
export const BINARY_EVENT = 5;
export const BINARY_ACK = 6;

const errorPacket = {
    type: ERROR,
    data: 'parser error'
};

export class Encoder {
    encode(packet: any) {
        switch (packet.type) {
            case CONNECT:
            case DISCONNECT:
            case ERROR:
                return [JSON.stringify(packet)];
            default:
                return [notepack.encode(packet)];
        }
    }
}

export class Decoder extends Emitter {
    add(obj: any) {
        if (typeof obj === 'string') {
            this.parseJSON(obj);
        } else {
            this.parseBinary(obj);
        }
    }

    parseJSON(obj: any) {
        try {
            const decoded = JSON.parse(obj);
            // @ts-ignore
            this.emit('decoded', decoded);
        } catch (e) {
            // @ts-ignore
            this.emit('decoded', errorPacket);
        }
    }

    parseBinary(obj: any) {
        try {
            const decoded = notepack.decode(obj);
            // @ts-ignore
            this.emit('decoded', decoded);
        } catch (e) {
            // @ts-ignore
            this.emit('decoded', errorPacket);
        }
    }

    destroy() {}
}
