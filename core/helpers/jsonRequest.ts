type JsonRequestOptions = {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    headers?: Record<string, string>;
    data?: any;
};

export class JsonRequestError extends Error {
    code: number;

    constructor(error: {code: number; message: string}) {
        super();

        this.code = error.code;
        this.message = error.message;
    }
}

export default async function jsonRequest<T = any>(
    payload: JsonRequestOptions
): Promise<T> {
    const {url, method = 'GET', data, headers = {}} = payload;

    let response;
    let result;
    try {
        response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...(headers || {})
            },
            ...(!!data ? {body: JSON.stringify(data)} : {})
        });

        result = await response.json();
    } catch (error: any) {
        throw new JsonRequestError({
            code: error.code || 500,
            message: error.message || 'Internal server error'
        });
    }

    if (!response.ok) {
        if (typeof result === 'object') {
            throw new JsonRequestError({
                code: result.code || response.status,
                message: result.message || response.statusText
            });
        }

        throw new JsonRequestError({
            code: response.status,
            message: response.statusText
        });
    }

    return result;
}
