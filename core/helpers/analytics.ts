type DataLayer = {
    event:
        | 'view_item'
        | 'add_to_cart'
        | 'add_to_wishlist'
        | 'view_cart'
        | 'remove_from_cart'
        | 'begin_checkout'
        | 'add_payment_info'
        | 'purchase';
    data: Record<string, any>;
};

export const pushIntoGTMDataLayer = ({event, data}: DataLayer) => {
    if ('dataLayer' in window && Array.isArray(window.dataLayer)) {
        window.dataLayer.push({ecommerce: null});
        window.dataLayer.push({
            event,
            ecommerce: {...data}
        });
    }
};
