import {NextApiRequest, NextApiResponse} from 'next';
import {getServerSession} from 'next-auth';
import nc from 'next-connect';
import {Customer} from '@core/types';
import {authOptions} from '@core/api/auth/common';
import erpClient from './erpClient';

type Context = {
    erpClient: typeof erpClient;
    customer?: Customer;
};

type Handler = (
    ctx: Context,
    req: NextApiRequest,
    res: NextApiResponse
) => void | Promise<void>;

class Endpoint {
    private readonly ctx!: Context;

    constructor() {
        this.ctx = {
            erpClient
        };
    }

    get(handler: Handler, isSecure = false) {
        return this.getHandler().get(
            async (req: NextApiRequest, res: NextApiResponse) => {
                const session = await getServerSession(req, res, authOptions);

                if (!!session) {
                    this.ctx.customer = session.user;
                } else if (isSecure) {
                    return res.status(401).json({
                        code: 401,
                        message: 'Unauthorized'
                    });
                } else {
                    this.ctx.customer = undefined;
                }

                return handler(this.ctx, req, res);
            }
        );
    }

    post(handler: Handler, isSecure = false) {
        return this.getHandler().post(
            async (req: NextApiRequest, res: NextApiResponse) => {
                const session = await getServerSession(req, res, authOptions);

                if (!!session) {
                    this.ctx.customer = session.user;
                } else if (isSecure) {
                    return res.status(401).json({
                        code: 401,
                        message: 'Unauthorized'
                    });
                } else {
                    this.ctx.customer = undefined;
                }

                return handler(this.ctx, req, res);
            }
        );
    }

    put(handler: Handler, isSecure = false) {
        return this.getHandler().put(
            async (req: NextApiRequest, res: NextApiResponse) => {
                const session = await getServerSession(req, res, authOptions);

                if (!!session) {
                    this.ctx.customer = session.user;
                } else if (isSecure) {
                    return res.status(401).json({
                        code: 401,
                        message: 'Unauthorized'
                    });
                } else {
                    this.ctx.customer = undefined;
                }

                return handler(this.ctx, req, res);
            }
        );
    }

    delete(handler: Handler, isSecure = false) {
        return this.getHandler().delete(
            async (req: NextApiRequest, res: NextApiResponse) => {
                const session = await getServerSession(req, res, authOptions);

                if (!!session) {
                    this.ctx.customer = session.user;
                } else if (isSecure) {
                    return res.status(401).json({
                        code: 401,
                        message: 'Unauthorized'
                    });
                } else {
                    this.ctx.customer = undefined;
                }

                return handler(this.ctx, req, res);
            }
        );
    }

    private getHandler() {
        return nc({
            onError(err, req: NextApiRequest, res: NextApiResponse, next) {
                if (typeof err === 'object') {
                    const result: Record<string, any> = {};

                    result.code =
                        typeof err.code !== 'undefined' ? err.code : 400;
                    result.message =
                        typeof err.message !== 'undefined'
                            ? err.message
                            : 'Unknown error!';
                    if (typeof err.field !== 'undefined') {
                        result.field = err.field;
                    }

                    return res
                        .status(
                            typeof result.code === 'number' &&
                                result.code >= 400 &&
                                result.code < 600
                                ? result.code
                                : 400
                        )
                        .json(result);
                }

                res.status(500).end(err.toString());
            }
        });
    }
}

export default new Endpoint();
