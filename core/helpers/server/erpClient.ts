import {jsonRequest, trim} from '../';

class ERPClient {
    private readonly apiUrl: string;
    private readonly apiSecret: string;

    constructor() {
        this.apiUrl = process.env.NEXT_PUBLIC_API_URL as string;
        this.apiSecret = process.env.API_SECRET as string;
    }

    get<T = any>(path: string): Promise<T> {
        return this.jsonRequest<T>({
            path,
            method: 'GET'
        });
    }

    post<T = any>(path: string, data: Record<string, any> = {}): Promise<T> {
        return this.jsonRequest<T>({
            path,
            method: 'POST',
            data
        });
    }

    put<T = any>(path: string, data: Record<string, any> = {}): Promise<T> {
        return this.jsonRequest<T>({
            path,
            method: 'PUT',
            data
        });
    }

    patch<T = any>(path: string, data: Record<string, any> = {}): Promise<T> {
        return this.jsonRequest<T>({
            path,
            method: 'PATCH',
            data
        });
    }

    delete<T = any>(path: string, data: Record<string, any> = {}): Promise<T> {
        return this.jsonRequest<T>({
            path,
            method: 'DELETE',
            data
        });
    }

    private async jsonRequest<T = any>(payload: {
        path: string;
        method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
        data?: Record<string, any>;
    }): Promise<T> {
        const {path, method = 'GET', data} = payload;
        const url = `${trim(trim(this.apiUrl, '/'), ' ')}/${trim(
            trim(path, '/'),
            ' '
        )}`;

        return await jsonRequest({
            url,
            method,
            headers: {
                'api-secret': this.apiSecret
            },
            data
        });
    }
}

export default new ERPClient();
