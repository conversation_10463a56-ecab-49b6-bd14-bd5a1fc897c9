import {GetServerSidePropsContext, Redirect} from 'next';
import {getServerSession, Session} from 'next-auth';
import storeConfig from '~/store.config';
import {authOptions} from '@core/api/auth/common';
import {erpClient, loadTranslations} from '.';
import {NavigationItem, StoreInfo} from '../../types';

type ParsedUrlQuery = Record<string, string | string[] | undefined>;

type Options = {
    isSecure?: boolean;
};

type PageProps = {
    storeInfo: StoreInfo;
    session: Session | null;
    slug?: string;
    pageNumber?: number;
    navigationItem?: NavigationItem;
    [key: string]: any;
};

async function initDynamicPageParams(
    ctx: GetServerSidePropsContext<ParsedUrlQuery>,
    options: Options = {} as Options
) {
    const locale = ctx.locale ?? storeConfig.defaultLocale;
    const {isSecure = true} = options;
    const session = await getServerSession(ctx.req, ctx.res, authOptions);
    const props: PageProps = {
        storeInfo: await erpClient.post('common/info', {
            locale
        }),
        session: JSON.parse(JSON.stringify(session)),
        ...(await loadTranslations(locale))
    };
    let redirect: Redirect | boolean = false;

    // Check session.
    if (isSecure && !session) {
        redirect = {destination: '/auth', permanent: false};
    }

    // Get slug and page number.
    const {slug, pageNumber} = processParams(ctx.params);

    // Set slug.
    if (typeof slug === 'object') {
        props.slug = slug;
    }

    // Set page number.
    if (typeof pageNumber === 'number') {
        props.pageNumber = pageNumber;
    }

    // Get navigation.
    const navigation = props.storeInfo.navigation;

    // Get navigation item.
    const navigationItem = navigation.find(
        navigationItem => navigationItem.slug === slug
    );

    // Set navigation item.
    if (typeof navigationItem === 'object') {
        props.navigationItem = navigationItem;
    }

    return {props, notFound: false, redirect};
}

function processParams(params: ParsedUrlQuery | undefined) {
    if (
        typeof params !== 'undefined' &&
        Array.isArray(params.slug) &&
        params.slug.length > 0
    ) {
        const slug = params.slug;

        if (slug[slug.length - 2] === 'page') {
            const page = parseInt(slug[slug.length - 1]);

            if (!isNaN(page)) {
                return {
                    slug: slug.slice(0, params.slug.length - 2).join('/'),
                    pageNumber: page
                };
            }
        }

        return {slug: slug.join('/')};
    }

    return {};
}

export default initDynamicPageParams;
