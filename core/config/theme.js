const colors = require('./colors');

module.exports = {
    colors: {
        ...colors,
        primary: colors.orange,
        success: colors.green,
        warning: colors.amber,
        danger: colors.red
    },

    fontFamily:
        '-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji',
    textColor: colors.gray[900],
    textMutedColor: colors.gray[500],

    // Desktop Layout
    topBarHeight: '48px',
    headerHeight: '96px',
    logoWidth: '158px',
    logoHeight: '36px',
    navBarHeight: '48px',
    accountHeaderHeight: '96px',
    accountLogoWidth: '158px',
    accountLogoHeight: '36px',
    accountFooterHeight: '54px',

    // Mobile Layout.
    mobileHeaderHeight: '60px',
    mobileTabBarHeight: '55px',
    iosTabBarHeight: '68px',
    mobileLogoWidth: '118px',
    mobileLogoHeight: '27px'
};
