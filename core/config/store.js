const theme = require('./theme');

module.exports = {
    title: 'EnterStore',
    description: 'New generation e-commerce platform',
    titleTemplate: '%s - EnterStore',
    locales: ['tr'],
    defaultLocale: 'tr',
    defaultCurrency: {
        name: 'TL',
        symbol: '₺',
        template: '{{price}} {{name}}'
    },
    defaultCountry: {
        code: 'TR',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        phoneCode: '+90'
    },
    seo: {},
    theme,
    catalog: {
        productListItemFields: [
            'productId',
            'code',
            'name',
            'categoryName',
            'productCategory',
            'brandName',
            'slug',
            'shortDescription',
            'rating',
            'reviewCount',
            'images',
            'salesPrice',
            'unDiscountedSalesPrice',
            'discount',
            'hasDiscount',
            'isBestSellingProduct',
            'isDiscountedProduct',
            'isNewProduct',
            'isSuggestedProduct',
            'estimatedDeliveryDuration',
            'isKitProduct',
            'description'
        ],
        productsPerPage: 12,
        productImageShape: 'rectangle',
        productSort: {createdAt: -1}
    }
};
