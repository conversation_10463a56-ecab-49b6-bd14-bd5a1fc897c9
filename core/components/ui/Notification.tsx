import {ReactNode} from 'react';
import {toast, ExternalToast} from 'sonner';
import {
    CheckCircleIcon,
    ExclanationCircleIcon,
    InfoCircleIcon,
    XCircleIcon
} from '@core/icons/outline';
import {UiButton} from '.';

const statusIcons = {
    info: <InfoCircleIcon className="h-5 w-5 text-blue-600" />,
    success: <CheckCircleIcon className="h-5 w-5 text-green-600" />,
    error: <XCircleIcon className="h-5 w-5 text-red-600" />,
    warning: <ExclanationCircleIcon className="h-5 w-5 text-amber-600" />
};

type NotificationProps = {
    title: string;
    description: string;
    options?: ExternalToast;
    undoText?: string;
    onUndo?: () => void;
    detailRenderer?: (close: () => void) => ReactNode;
} & (
    | {status?: never; icon: JSX.Element}
    | {status: keyof typeof statusIcons; icon?: never}
) &
    (
        | {undoText?: never; onUndo?: never}
        | {undoText: string; onUndo: () => void}
    );

const notification = ({
    description,
    status,
    title,
    options,
    icon,
    undoText,
    onUndo,
    detailRenderer
}: NotificationProps) => {
    return toast(
        <div className="flex w-full flex-col">
            <div className="flex items-center gap-3">
                {icon || statusIcons[status]}
                <div className="flex-1 text-sm">
                    <p className="font-semibold">{title}</p>
                    <p className="mt-1">{description}</p>
                </div>
                {onUndo && (
                    <UiButton
                        variant="link"
                        color="primary"
                        className="notification-action-undo"
                        onClick={onUndo}
                    >
                        {undoText}
                    </UiButton>
                )}
            </div>
            {detailRenderer && detailRenderer(() => toast.dismiss())}
        </div>,
        options
    );
};

export default notification;
