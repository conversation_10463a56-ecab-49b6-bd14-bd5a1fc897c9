import {FC, Fragment, PropsWithChildren} from 'react';
import {cls, isDev} from '@core/helpers';
import {useMobile, useTrans} from '@core/hooks';
import {ChevronLeftIcon, XIcon} from '@core/icons/outline';
import {UiButton, UiDialog, UiTransition} from '.';

export type UiSideBarProps = {
    isShown: boolean;
    title?: string | null;
    size: 'normal' | 'large' | number;
    onClose: () => void;
};

const UiSideBar: FC<PropsWithChildren<UiSideBarProps>> = ({
    isShown = false,
    title,
    size,
    onClose,
    children
}) => {
    const t = useTrans();
    const {isMobile, setSelectedFilter, selectedFilter} = useMobile();

    return (
        <UiTransition.Root show={isShown} as={Fragment}>
            <UiDialog as="div" className="relative z-modal" onClose={onClose}>
                <UiTransition.Child
                    as={Fragment}
                    enter="transition duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition duration-300"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-gray-900 bg-opacity-20 transition-opacity" />
                </UiTransition.Child>

                <div className="fixed inset-0 overflow-hidden">
                    <div className="absolute inset-0 overflow-hidden">
                        <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full xl:pl-10">
                            <UiTransition.Child
                                as={Fragment}
                                enter="transform transition duration-500"
                                enterFrom={
                                    isMobile
                                        ? 'translate-y-full'
                                        : 'translate-x-full'
                                }
                                enterTo={
                                    isMobile ? 'translate-y-0' : 'translate-x-0'
                                }
                                leave="transform transition duration-500"
                                leaveFrom={
                                    isMobile ? 'translate-y-0' : 'translate-x-0'
                                }
                                leaveTo={
                                    isMobile
                                        ? 'translate-y-full'
                                        : 'translate-x-full'
                                }
                            >
                                <UiDialog.Panel
                                    className={cls(
                                        'pointer-events-auto w-screen',
                                        {
                                            'xl:max-w-2xl': size === 'large',
                                            'xl:max-w-md': size === 'normal'
                                        }
                                    )}
                                    style={{
                                        ...(typeof size === 'number'
                                            ? {
                                                  maxWidth: `${size}px`
                                              }
                                            : {})
                                    }}
                                >
                                    <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                                        <div className="hidden p-6 xl:block">
                                            <div className="flex items-center justify-between">
                                                <UiDialog.Title className="flex select-none items-center text-lg font-medium">
                                                    {title ? title : ' '}
                                                </UiDialog.Title>
                                                <div className="ml-3 flex h-8 items-center">
                                                    <button
                                                        type="button"
                                                        className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition hover:bg-gray-200 hover:text-gray-500"
                                                        onClick={onClose}
                                                    >
                                                        <XIcon
                                                            className="h-5 w-5"
                                                            aria-hidden="true"
                                                        />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex h-mobile-header w-full select-none border-b border-gray-200 bg-white xl:hidden">
                                            <div className="container">
                                                <div className="flex h-full items-center">
                                                    <div className="flex min-w-0 flex-1 items-center ">
                                                        {selectedFilter !==
                                                            undefined && (
                                                            <div className="">
                                                                <UiButton
                                                                    variant="link"
                                                                    className="focus:border-0 focus:ring-0"
                                                                    onClick={() =>
                                                                        setSelectedFilter(
                                                                            undefined
                                                                        )
                                                                    }
                                                                >
                                                                    <ChevronLeftIcon className="h-4 w-4 stroke-primary-600 stroke-[20px]" />
                                                                </UiButton>
                                                            </div>
                                                        )}
                                                        <div className="w-full truncate font-semibold">
                                                            {title
                                                                ? title
                                                                : ' '}
                                                        </div>
                                                    </div>

                                                    <div className="flex items-center">
                                                        <button
                                                            className="font-semibold text-primary-600 transition active:opacity-30"
                                                            onClick={onClose}
                                                        >
                                                            {t('Close')}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="scroller flex-1 overflow-y-auto overflow-x-hidden">
                                            {children}
                                        </div>
                                    </div>
                                </UiDialog.Panel>
                            </UiTransition.Child>
                        </div>
                    </div>
                </div>
            </UiDialog>
        </UiTransition.Root>
    );
};

if (isDev) {
    UiSideBar.displayName = 'UiSideBar';
}

export default UiSideBar;
