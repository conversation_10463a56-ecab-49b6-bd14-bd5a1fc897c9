import {FC, Fragment, PropsWithChildren} from 'react';
import {cls, isDev} from '@core/helpers';
import {XIcon} from '@core/icons/outline';
import {UiDialog, UiTransition} from '.';

export type UiModalProps = {
    isShown: boolean;
    title?: string | null;
    isLarge?: boolean;
    isClosable?: boolean;
    onClose: () => void;
};

const UiModal: FC<PropsWithChildren<UiModalProps>> = ({
    isShown,
    title,
    isLarge,
    isClosable = true,
    onClose,
    children
}) => {
    return (
        <UiTransition.Root show={isShown} as={Fragment}>
            <UiDialog
                as="div"
                className="fixed inset-0 z-10 overflow-y-auto"
                onClose={() => {
                    if (isClosable) {
                        onClose();
                    }
                }}
            >
                <div
                    className="flex min-h-screen text-center sm:block sm:px-6 xl:px-8"
                    style={{fontSize: 0}}
                >
                    <UiTransition.Child
                        as={Fragment}
                        enter="transition duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="transition duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <UiDialog.Overlay className="hidden sm:fixed sm:inset-0 sm:block sm:bg-gray-900 sm:bg-opacity-20 sm:transition-opacity" />
                    </UiTransition.Child>

                    {/* This element is to trick the browser into centering the modal contents. */}
                    <span
                        className="hidden sm:inline-block sm:h-screen sm:align-middle"
                        aria-hidden="true"
                    >
                        &#8203;
                    </span>

                    <UiTransition.Child
                        as={Fragment}
                        enter="transition duration-300"
                        enterFrom="opacity-0 scale-105"
                        enterTo="opacity-100 scale-100"
                        leave="transition duration-200"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-105"
                    >
                        <div
                            className={cls(
                                'flex w-full transform text-left text-base transition sm:my-8 sm:inline-block sm:align-middle',
                                {
                                    'max-w-5xl': isLarge,
                                    'max-w-3xl': !isLarge
                                }
                            )}
                        >
                            <div className="relative flex w-full flex-col items-stretch overflow-hidden bg-white sm:rounded-lg">
                                <div className="flex items-center justify-between px-3 py-2 xl:px-6 xl:py-4">
                                    <h2 className="text-lg font-medium text-gray-900">
                                        {title ? title : ' '}
                                    </h2>

                                    {isClosable && (
                                        <button
                                            type="button"
                                            className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition duration-75 ease-out hover:bg-gray-200 hover:text-gray-500"
                                            onClick={onClose}
                                        >
                                            <span className="sr-only">
                                                Close
                                            </span>
                                            <XIcon
                                                className="h-5 w-5"
                                                aria-hidden="true"
                                            />
                                        </button>
                                    )}
                                </div>

                                {children}
                            </div>
                        </div>
                    </UiTransition.Child>
                </div>
            </UiDialog>
        </UiTransition.Root>
    );
};

if (isDev) {
    UiModal.displayName = 'UiModal';
}

export default UiModal;
