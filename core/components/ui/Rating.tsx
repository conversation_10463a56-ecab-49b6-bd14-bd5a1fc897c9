import {FC, memo} from 'react';
import dynamic from 'next/dynamic';
import type {RatingComponentProps} from 'react-rating';
import {cls, isDev} from '@core/helpers';
import {StarIcon} from '@core/icons/solid';

// @ts-ignore
const Rating = dynamic(() => import('react-rating'));

export type UiRatingProps = RatingComponentProps & {
    size?: 'xs' | 'sm' | 'md' | 'lg';
};

const UiRating: FC<UiRatingProps> = memo(props => {
    const {className, size = 'sm', ...rest} = props;

    return (
        <div
            className={cls('inline-block', {
                'h-3 leading-3': size === 'xs',
                'h-4 leading-4': size === 'sm',
                'h-5 leading-5': size === 'md',
                'h-6 leading-6': size === 'lg'
            })}
        >
            {/* @ts-ignore */}
            <Rating
                className={cls('inline-flex items-center', className, {
                    'h-3 space-x-0.5': size === 'xs',
                    'h-4 space-x-0.5': size === 'sm',
                    'h-5 space-x-1': size === 'md',
                    'h-6 space-x-2': size === 'lg'
                })}
                emptySymbol={
                    <StarIcon
                        className={cls('flex-shrink-0 text-gray-300', {
                            'h-3 w-3': size === 'xs',
                            'h-4 w-4': size === 'sm',
                            'h-5 w-5': size === 'md',
                            'h-6 w-6': size === 'lg'
                        })}
                    />
                }
                fullSymbol={
                    <StarIcon
                        className={cls('flex-shrink-0 text-yellow-400', {
                            'h-3 w-3': size === 'xs',
                            'h-4 w-4': size === 'sm',
                            'h-5 w-5': size === 'md',
                            'h-6 w-6': size === 'lg'
                        })}
                    />
                }
                {...rest}
            />
        </div>
    );
});

if (isDev) {
    UiRating.displayName = 'UiRating';
}

export default UiRating;
