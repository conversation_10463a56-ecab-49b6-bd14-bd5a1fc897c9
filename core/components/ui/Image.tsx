import {FC, useMemo} from 'react';
import NextImage, {ImageProps as NextImageProps} from 'next/image';
import {cls, isDev} from '@core/helpers';

export type UiImageProps = NextImageProps & {
    aspectW?: number;
    aspectH?: number;
    fit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
    position?:
        | 'center'
        | 'top'
        | 'bottom'
        | 'left'
        | 'right'
        | 'left-bottom'
        | 'left-top'
        | 'right-bottom'
        | 'right-top';
    raw?: boolean;
};

const aspectWClasses: Record<number, string> = {
    1: 'aspect-w-1',
    2: 'aspect-w-2',
    3: 'aspect-w-3',
    4: 'aspect-w-4',
    5: 'aspect-w-5',
    6: 'aspect-w-6',
    7: 'aspect-w-7',
    8: 'aspect-w-8',
    9: 'aspect-w-9',
    10: 'aspect-w-10',
    11: 'aspect-w-11',
    12: 'aspect-w-12',
    13: 'aspect-w-13',
    14: 'aspect-w-14',
    15: 'aspect-w-15',
    16: 'aspect-w-16'
};

const aspectHClasses: Record<number, string> = {
    1: 'aspect-h-1',
    2: 'aspect-h-2',
    3: 'aspect-h-3',
    4: 'aspect-h-4',
    5: 'aspect-h-5',
    6: 'aspect-h-6',
    7: 'aspect-h-7',
    8: 'aspect-h-8',
    9: 'aspect-h-9',
    10: 'aspect-h-10',
    11: 'aspect-h-11',
    12: 'aspect-h-12',
    13: 'aspect-h-13',
    14: 'aspect-h-14',
    15: 'aspect-h-15',
    16: 'aspect-h-16'
};

const UiImage: FC<UiImageProps> = props => {
    const {aspectW, aspectH, fit, position, raw, className, ...rest} = props;

    const isUnoptimized = rest.src?.toString().startsWith('https');

    const calculatedClassName = useMemo(
        () =>
            cls(
                {
                    'object-cover': fit === 'cover',
                    'object-contain': fit === 'contain',
                    'object-fill': fit === 'fill',
                    'object-none': fit === 'none',
                    'object-scale-down': fit === 'scale-down',
                    'object-center': position === 'center',
                    'object-top': position === 'top',
                    'object-bottom': position === 'bottom',
                    'object-left': position === 'left',
                    'object-right': position === 'right',
                    'object-left-bottom': position === 'left-bottom',
                    'object-left-top': position === 'left-top',
                    'object-right-bottom': position === 'right-bottom',
                    'object-right-top': position === 'right-top'
                },
                className
            ),
        [fit, position, className]
    );

    if (typeof aspectW === 'number' && typeof aspectH === 'number') {
        return (
            <div
                className={`${aspectWClasses[aspectW]} ${aspectHClasses[aspectH]}`}
            >
                <div>
                    <div className="relative h-full w-full">
                        <NextImage
                            fill
                            className={cls([
                                calculatedClassName,
                                'object-cover',
                                'object-center'
                            ])}
                            unoptimized={isUnoptimized}
                            {...rest}
                        />
                    </div>
                </div>
            </div>
        );
    }

    return raw ? (
        // eslint-disable-next-line @next/next/no-img-element
        <img
            src={rest.src as string}
            alt={rest.alt}
            className={calculatedClassName}
            style={rest.style}
            width={rest.width}
            height={rest.width}
        />
    ) : (
        <NextImage
            className={calculatedClassName}
            unoptimized={isUnoptimized}
            {...rest}
        />
    );
};

if (isDev) {
    UiImage.displayName = 'UiImage';
}

export default UiImage;
