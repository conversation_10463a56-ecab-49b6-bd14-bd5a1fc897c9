import {forwardRef, ReactNode, useId} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiInput, UiInputProps} from '../..';
import {default as UiFormControl} from '../FormControl';
import {default as UiFormErrorMessage} from '../FormErrorMessage';
import {default as UiFormLabel} from '../FormLabel';

type UiTextFieldProps = Omit<UiInputProps, 'placeholder'> & {
    name: string;
    label?: string;
    error?: string;
    disabled?: boolean;
    readOnly?: boolean;
    leftElement?: ReactNode;
    rightElement?: ReactNode;
    leftAddon?: ReactNode;
    rightAddon?: ReactNode;
};

const UiTextField = forwardRef<HTMLInputElement, UiTextFieldProps>(
    (props, ref) => {
        const {
            name,
            label,
            error,
            leftElement,
            rightElement,
            leftAddon,
            rightAddon,
            className,
            children,
            ...rest
        } = props;
        const id = useId();

        return (
            <UiFormControl
                className={className}
                invalid={typeof error === 'string' && error.length > 0}
            >
                <UiInput.Group className="relative" size="xl">
                    {leftAddon && (
                        <UiInput.LeftAddon>{leftAddon}</UiInput.LeftAddon>
                    )}
                    {leftElement && (
                        <UiInput.LeftElement>{leftElement}</UiInput.LeftElement>
                    )}

                    <UiInput
                        {...rest}
                        id={id}
                        name={name}
                        className={cls(
                            'peer px-4 pt-4 text-sm font-medium leading-4',
                            'placeholder-transparent peer-placeholder-shown:pt-0',
                            {
                                'pl-12': !!leftElement,
                                'pr-12': !!rightElement
                            }
                        )}
                        placeholder={label}
                        ref={ref}
                    />

                    <UiFormLabel
                        htmlFor={id}
                        className={cls(
                            'absolute top-1.5 select-none text-xs text-gray-500 transition-all',
                            'peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400',
                            'peer-placeholder-shown:top-3 peer-focus:top-1.5 peer-focus:text-gray-500',
                            'cursor-text peer-focus:text-xs',
                            {
                                'left-4': !leftElement,
                                'left-12': !!leftElement
                            }
                        )}
                    >
                        {label}
                    </UiFormLabel>

                    {rightElement && (
                        <UiInput.RightElement>
                            {rightElement}
                        </UiInput.RightElement>
                    )}
                    {rightAddon && (
                        <UiInput.RightAddon>{rightAddon}</UiInput.RightAddon>
                    )}
                </UiInput.Group>

                {typeof error === 'string' && error.length > 0 && (
                    <UiFormErrorMessage>{error}</UiFormErrorMessage>
                )}
            </UiFormControl>
        );
    }
);

if (isDev) {
    UiTextField.displayName = 'UiTextField';
}

export default UiTextField;
