import {forwardRef, memo, ReactNode, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {UiInputProps} from '../..';
import UiCheckBoxField from './CheckboxField';
import UiFormattedTextField from './FormattedTextField';
import Ui<PERSON><PERSON><PERSON>ield from './PhoneField';
import UiSelect<PERSON>ield from './SelectField';
import UiTextareaField from './TextareaField';
import UiTextField from './TextField';

type UiFieldProps = Omit<UiInputProps, 'placeholder'> & {
    name: string;
    label?: string | ReactNode;
    error?: string;
    disabled?: boolean;
    readOnly?: boolean;
    selection?: boolean;
    phone?: boolean;
    format?: string;
    mask?: any;
    textarea?: boolean;
    checkbox?: boolean;
    defaultChecked?: boolean;
    countries?: Record<string, any>[];
    leftElement?: ReactNode;
    rightElement?: ReactNode;
    leftAddon?: ReactNode;
    rightAddon?: ReactNode;
};

const UiField = memo(
    forwardRef<HTMLInputElement, UiFieldProps>((props, ref) => {
        const {selection, phone, format, textarea, checkbox, ...rest} = props;

        const inputType = useMemo(() => {
            let type = 'text';

            if (selection) {
                type = 'select';
            } else if (phone) {
                type = 'phone';
            } else if (textarea) {
                type = 'textarea';
            } else if (checkbox) {
                type = 'checkbox';
            }

            return type;
        }, [textarea, checkbox, selection, phone]);

        if (inputType === 'select') {
            // @ts-ignore
            return <UiSelectField ref={ref} {...rest} />;
        } else if (inputType === 'phone') {
            // @ts-ignore
            return <UiPhoneField ref={ref} {...rest} />;
        } else if (inputType === 'text') {
            if (typeof format !== 'undefined') {
                return (
                    // @ts-ignore
                    <UiFormattedTextField format={format} ref={ref} {...rest} />
                );
            }

            // @ts-ignore
            return <UiTextField ref={ref} {...rest} />;
        } else if (inputType === 'textarea') {
            // @ts-ignore
            return <UiTextareaField ref={ref} {...rest} />;
        } else if (inputType === 'checkbox') {
            // @ts-ignore
            return <UiCheckBoxField ref={ref} {...rest} />;
        }

        return null;
    })
);

if (isDev) {
    UiField.displayName = 'UiField';
}

export default UiField;
