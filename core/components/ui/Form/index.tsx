import {FormHTMLAttributes, forwardRef, memo} from 'react';
import {isDev} from '@core/helpers';
import {default as UiFormField} from './Field';
import {default as UiFormControl} from './FormControl';
import {default as UiFormErrorMessage} from './FormErrorMessage';
import {default as UiFormHelperText} from './FormHelperText';
import {default as UiFormLabel} from './FormLabel';

export type UiFormProps = FormHTMLAttributes<HTMLFormElement> & {};

const UiForm = forwardRef<HTMLFormElement, UiFormProps>((props, ref) => {
    const {...rest} = props;

    return <form ref={ref} noValidate {...rest} />;
});

if (isDev) {
    UiForm.displayName = 'UiForm';
}

export default Object.assign(UiForm, {
    Control: UiFormControl,
    Label: UiFormLabel,
    ErrorMessage: UiFormErrorMessage,
    HelperText: UiFormHelperText,
    Field: UiFormField
});

export {useUiFormControl} from './FormControl';
export type {UiFormControlProps} from './FormControl';
export type {UiFormLabelProps} from './FormLabel';
export type {UiFormErrorMessageProps} from './FormErrorMessage';
export type {UiFormHelperTextProps} from './FormHelperText';
