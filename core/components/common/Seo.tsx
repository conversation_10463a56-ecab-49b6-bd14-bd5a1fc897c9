import {FC, memo, useEffect} from 'react';
import {NextSeo, NextSeoProps} from 'next-seo';
import {isDev} from '@core/helpers';
import {useMobile} from '@core/hooks';

export type SeoProps = NextSeoProps;

const Seo: FC<SeoProps> = memo(props => {
    const {setMobileTitle} = useMobile();

    useEffect(() => {
        setMobileTitle(props.title ?? '');

        return () => {
            setMobileTitle('');
        };
    }, [props.title, setMobileTitle]);

    return <NextSeo {...props} />;
});

if (isDev) {
    Seo.displayName = 'Seo';
}

export default Seo;
