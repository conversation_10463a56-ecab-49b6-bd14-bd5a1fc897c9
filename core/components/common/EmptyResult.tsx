import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiLink} from '@core/components/ui';

type EmptyResultProps = {
    onPopularSearchClick: (term: string) => void;
    onPopularCategoryClick: () => void;
};

const EmptyResult: FC<EmptyResultProps> = memo(
    ({onPopularCategoryClick, onPopularSearchClick}) => {
        const t = useTrans();
        const {popularSearches, popularCategories} = useStore();

        return (
            <div className="flex w-full flex-col items-center justify-center space-y-6 p-6">
                {popularSearches.length > 0 && (
                    <div className="w-full">
                        <h3 className="mb-3 w-full text-sm">
                            {t('Popular Searches')}
                        </h3>
                        <div className="flex w-full flex-wrap gap-3">
                            {popularSearches.map(item => (
                                <button
                                    key={item.title + item.id}
                                    className="cursor-pointer rounded-full border border-gray-200 px-3 py-1.5 text-xs transition hover:border-primary-600 hover:bg-primary-100 hover:text-primary-600"
                                    onClick={() =>
                                        onPopularSearchClick(item.title)
                                    }
                                >
                                    {item.title}
                                </button>
                            ))}
                        </div>
                    </div>
                )}

                {popularCategories.length > 0 && (
                    <div className="w-full">
                        <h3 className="mb-3 w-full text-sm">
                            {t('Popular Categories')}
                        </h3>
                        <div className="flex w-full flex-wrap gap-3">
                            {popularCategories.map(item => (
                                <UiLink
                                    key={item.title + item.id}
                                    className="cursor-pointer rounded-full border border-gray-200 px-3 py-1.5 text-xs transition hover:border-primary-600 hover:bg-primary-100 hover:text-primary-600"
                                    href={item.href}
                                    onClick={onPopularCategoryClick}
                                >
                                    {item.title}
                                </UiLink>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        );
    }
);

if (isDev) {
    EmptyResult.displayName = 'SearchEmptyResult';
}

export default EmptyResult;
