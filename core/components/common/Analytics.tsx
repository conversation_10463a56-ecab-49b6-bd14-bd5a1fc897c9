import {useEffect, useState} from 'react';
import Script from 'next/script';
import {isDev, isVercelEnvironment, jsonRequest} from '@core/helpers';

const getCookie = (name: string): string | undefined => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift();
};

const Analytics = () => {
    const [tagManagerId, setTagManagerId] = useState<string>('');
    const [consentData, setConsentData] = useState({
        adPersonalization: '',
        adStorage: '',
        analyticsStorage: '',
        adUserData: ''
    });

    useEffect(() => {
        (async () => {
            try {
                const result = await jsonRequest({
                    url: '/api/common/analytics',
                    method: 'POST'
                });
                if (typeof result.tagManagerId === 'string') {
                    setTagManagerId(result.tagManagerId);
                }
            } catch (error) {}
        })();

        setConsentData({
            adPersonalization: getCookie('ad_personalization') || '',
            adStorage: getCookie('ad_storage') || '',
            analyticsStorage: getCookie('analytics_storage') || '',
            adUserData: getCookie('ad_user_data') || ''
        });
    }, []);

    if (!tagManagerId || isDev || isVercelEnvironment) return null;

    return (
        <>
            <Script
                id="google-tag-manager-init"
                dangerouslySetInnerHTML={{
                    __html: `
            window.dataLayer = window.dataLayer || [];
          `
                }}
            />

            <Script
                id="cookie-consent"
                dangerouslySetInnerHTML={{
                    __html: `

                        dataLayer.push({ consent: null });
                        dataLayer.push({
                        event: "consentUpdate",
                        consent: {
                            ad_user_data: "${consentData.adUserData}",
                            ad_storage: "${consentData.adStorage}",
                            ad_personalization: "${consentData.adPersonalization}",
                            analytics_storage: "${consentData.analyticsStorage}",
                        },
                        });
                        `
                }}
            />

            <Script
                id="google-tag-manager"
                dangerouslySetInnerHTML={{
                    __html: `
                        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                        })(window,document,'script','dataLayer','${tagManagerId.trim()}');
                        `
                }}
            />
        </>
    );
};

export default Analytics;
