import {FC, memo, useEffect, useMemo, useState} from 'react';
import NextHead from 'next/head';
import {useRouter} from 'next/router';
import Script from 'next/script';
import {
    DefaultSeo,
    DefaultSeoProps,
    SiteLinksSearchBoxJsonLd,
    SiteLinksSearchBoxJsonLdProps
} from 'next-seo';
import storeConfig from '~/store.config';
import {isDev, trim} from '@core/helpers';
import {useTrans, useViewportSize} from '@core/hooks';

const Head: FC = memo(() => {
    const t = useTrans();
    const router = useRouter();
    const {title, description, titleTemplate, locales, seo} = storeConfig;

    // Default seo settings.
    const defaultSeoSettings = useMemo(() => {
        const settings: DefaultSeoProps = {
            openGraph: {
                type: 'website',
                url: `${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/`,
                title: t(title),
                description: t(description),
                site_name: title
            },
            ...(seo ?? {})
        };

        // Language alternates.
        if (locales.length > 1) {
            const alternates = locales.filter(
                locale => locale !== router.locale
            );

            if (alternates.length > 0) {
                settings.languageAlternates = [];

                for (const alternate of alternates) {
                    // @ts-ignore
                    settings.languageAlternates.push({
                        hrefLang: alternate,
                        href: `${trim(
                            `${trim(
                                process.env.NEXT_PUBLIC_SITE_URL,
                                '/'
                            )}/${alternate}${router.asPath}`,
                            '/'
                        )}/`
                    });
                }
            }
        }

        return settings;
    }, [locales, router, title, description, seo, t]);

    const searchBoxJsonLdSettings: SiteLinksSearchBoxJsonLdProps =
        useMemo(() => {
            return {
                url: `${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/`,
                potentialActions: [
                    {
                        target: `${trim(
                            process.env.NEXT_PUBLIC_SITE_URL,
                            '/'
                        )}/search?query`,
                        queryInput: 'search_term'
                    }
                ]
            };
        }, []);

    const {width: viewportWidth} = useViewportSize();
    const [isMobile, setIsMobile] = useState(false);
    const isMobileViewport = useMemo(
        () => viewportWidth > 0 && viewportWidth < 1024,
        [viewportWidth]
    );
    useEffect(() => {
        if (isMobileViewport) {
            setIsMobile(true);
        }
    }, [isMobileViewport]);

    return (
        <>
            <DefaultSeo
                defaultTitle={t(title)}
                titleTemplate={titleTemplate}
                description={t(description)}
                {...defaultSeoSettings}
            />

            <NextHead>
                {isMobile ? (
                    <meta
                        name="viewport"
                        content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no, user-scalable=no, viewport-fit=cover"
                    />
                ) : (
                    <meta
                        name="viewport"
                        content="width=device-width, initial-scale=1"
                    />
                )}
                <meta name="robots" content="max-image-preview:large" />
                <meta name="format-detection" content="telephone=no" />

                <meta name="application-name" content={title} />
                <meta name="apple-mobile-web-app-capable" content="yes" />
                <meta
                    name="apple-mobile-web-app-status-bar-style"
                    content="default"
                />
                <meta name="apple-mobile-web-app-title" content={title} />
                <meta name="mobile-web-app-capable" content="yes" />
                <meta name="theme-color" content="#FFFFFF" />
                <link rel="apple-touch-icon" href="/icons/icon-512x512.png" />
                <link
                    rel="apple-touch-icon"
                    sizes="192x192"
                    href="/icons/icon-192x192.png"
                />
                <link
                    rel="apple-touch-icon"
                    sizes="256x256"
                    href="/icons/icon-256x256.png"
                />
                <link
                    rel="apple-touch-icon"
                    sizes="384x384"
                    href="/icons/icon-384x384.png"
                />
                <link
                    rel="apple-touch-icon"
                    sizes="512x512"
                    href="/icons/icon-512x512.png"
                />
                <link
                    rel="mask-icon"
                    href="/icons/icon-512x512.png"
                    color="#f97316"
                />
                <link rel="manifest" href="/manifest.json" />
                {/* Tell the browser to never restore the scroll position on load */}
                <Script
                    id="scroll-restoration"
                    dangerouslySetInnerHTML={{
                        __html: `history.scrollRestoration = "manual"`
                    }}
                />
            </NextHead>

            <SiteLinksSearchBoxJsonLd {...searchBoxJsonLdSettings} />
        </>
    );
});

if (isDev) {
    Head.displayName = 'Head';
}

export default Head;
