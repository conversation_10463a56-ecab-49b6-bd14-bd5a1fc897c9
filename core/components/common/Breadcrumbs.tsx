import {FC, memo} from 'react';
import {UiLink} from '@core/components/ui';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {Breadcrumb} from '@core/types';

export type BreadcrumbsProps = {
    breadcrumbs: Breadcrumb[];
    border?: boolean;
};

const Breadcrumbs: FC<BreadcrumbsProps> = memo(props => {
    const {breadcrumbs, border = true} = props;
    const t = useTrans();

    return (
        <nav
            className={cls(
                'breadcrumbs hidden w-full py-4 xl:block',
                border && 'border-b'
            )}
            aria-label="Breadcrumb"
        >
            <ol
                className="breadcrumbs-list flex items-center space-x-2 overflow-x-auto"
                role="list"
            >
                {breadcrumbs.map(breadcrumb =>
                    !!breadcrumb.href ? (
                        <li className="breadcrumbs-item" key={breadcrumb.slug}>
                            <div className="flex items-center">
                                <UiLink
                                    href={breadcrumb.href}
                                    className="breadcrumbs-link mr-2 text-sm font-medium text-muted transition hover:text-current"
                                >
                                    {breadcrumb.name === 'Home'
                                        ? t('Home')
                                        : breadcrumb.name}
                                </UiLink>

                                <svg
                                    width={16}
                                    height={20}
                                    viewBox="0 0 16 20"
                                    fill="currentColor"
                                    xmlns="http://www.w3.org/2000/svg"
                                    aria-hidden="true"
                                    className="h-5 w-4 text-gray-300"
                                >
                                    <path d="M5.697 4.34L8.98 16.532h1.327L7.025 4.341H5.697z" />
                                </svg>
                            </div>
                        </li>
                    ) : (
                        <li className="breadcrumbs-item" key={breadcrumb.slug}>
                            <div
                                aria-current="page"
                                className="breadcrumbs-current text-sm font-medium text-current"
                            >
                                {breadcrumb.name}
                            </div>
                        </li>
                    )
                )}
            </ol>
        </nav>
    );
});

if (isDev) {
    Breadcrumbs.displayName = 'Breadcrumbs';
}

export default Breadcrumbs;
