import {useCallback, useEffect, useState} from 'react';
import {useTrans, useViewportSize} from '@core/hooks';
import {ArrowUpIcon} from '@core/icons/solid';

const ScrollToTop = () => {
    const [isVisible, setIsVisible] = useState(false);
    const t = useTrans();

    const {height} = useViewportSize();

    const scrollToTop = useCallback(() => {
        const container = document.querySelector('.content-wrapper');
        if (container === null || container === undefined) return;

        container.scrollTo({top: 0, behavior: 'smooth'});
    }, []);

    useEffect(() => {
        const container = document.querySelector('.content-wrapper');
        if (container === null || container === undefined) return;

        const toggleVisibility = () => {
            if (container?.scrollTop > height) {
                setIsVisible(true);
            } else {
                setIsVisible(false);
            }
        };

        container.addEventListener('scroll', toggleVisibility);

        return () => container.removeEventListener('scroll', toggleVisibility);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return isVisible ? (
        <button
            onClick={scrollToTop}
            className="fixed bottom-6 right-10 z-40 hidden items-center justify-center gap-2 rounded-full border-2 bg-white px-4 py-2 text-sm transition-shadow hover:shadow-md xl:inline-flex"
        >
            <ArrowUpIcon className="h-4 w-4" />
            <span>{t('Go to Top')}</span>
        </button>
    ) : null;
};

export default ScrollToTop;
