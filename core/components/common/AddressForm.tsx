import {
    FC,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useFormContext, useWatch} from 'react-hook-form';
import {isDev, jsonRequest} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiForm} from '@core/components/ui';

type AddressFormProps = {
    parentField?: string;
    countries: Record<string, any>[];
    initialStates?: string[];
    initialCities: string[];
    initialCountryId?: string;
};

const AddressForm: FC<AddressFormProps> = memo(props => {
    const {parentField, initialStates, initialCities, initialCountryId} = props;
    const countries = useMemo(
        () => (Array.isArray(props.countries) ? props.countries : []),
        [props.countries]
    );
    const {
        register,
        formState: {errors},
        setValue
    } = useFormContext();
    const t = useTrans();
    const [states, setStates] = useState<string[]>([]);
    const [cities, setCities] = useState<string[]>([]);
    const [districts, setDistricts] = useState<string[]>([]);
    const [subDistricts, setSubDistricts] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const defaultCountry = useMemo(
        () =>
            countries.find(country => !!country.isDefault) as Record<
                string,
                any
            >,
        [countries]
    );
    const hasStates = useMemo(
        () => Array.isArray(states) && states.length > 0,
        [states]
    );
    const fieldName = useCallback(
        (name: string) =>
            typeof parentField === 'string' ? `${parentField}-${name}` : name,
        [parentField]
    );
    const isInitial = useRef(true);

    useEffect(() => {
        setTimeout(() => {
            isInitial.current = false;
        }, 250);
    }, []);

    const countryId = useWatch({
        name: fieldName('countryId'),
        defaultValue: initialCountryId || defaultCountry.id
    });
    const state = useWatch({
        name: fieldName('state'),
        defaultValue: null
    });
    const city = useWatch({
        name: fieldName('city'),
        defaultValue: null
    });
    const district = useWatch({
        name: fieldName('district'),
        defaultValue: null
    });

    useEffect(() => {
        if (isInitial.current) return;

        (async () => {
            if (defaultCountry.id !== countryId) {
                setStates(
                    await jsonRequest({
                        url: '/api/common/states',
                        method: 'POST',
                        data: {countryId}
                    })
                );
            } else {
                setStates(() => initialStates ?? []);
            }
            setValue(fieldName('state'), null);
            setValue(fieldName('city'), null);
            setValue(fieldName('district'), null);
            setValue(fieldName('subDistrict'), null);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [countryId, defaultCountry, initialStates]);

    useEffect(() => {
        if (isInitial.current) return;

        (async () => {
            if (defaultCountry.id !== countryId) {
                setCities(
                    await jsonRequest({
                        url: '/api/common/cities',
                        method: 'POST',
                        data: {countryId, state}
                    })
                );
            } else {
                setCities(() => initialCities);
            }

            setValue(fieldName('city'), null);
            setValue(fieldName('district'), null);
            setValue(fieldName('subDistrict'), null);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [countryId, state, hasStates, defaultCountry, initialCities]);

    useEffect(() => {
        if (isInitial.current) return;

        (async () => {
            if (!!city) {
                setDistricts(
                    await jsonRequest({
                        url: '/api/common/districts',
                        method: 'POST',
                        data: {countryId, city}
                    })
                );
            } else {
                setDistricts([]);
            }

            setValue(fieldName('district'), null);
            setValue(fieldName('subDistrict'), null);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [countryId, city]);

    useEffect(() => {
        if (isInitial.current) return;

        (async () => {
            if (!!city && !!district) {
                setSubDistricts(
                    await jsonRequest({
                        url: '/api/common/sub-districts',
                        method: 'POST',
                        data: {countryId, city, district}
                    })
                );
            } else {
                setSubDistricts([]);
            }

            setValue(fieldName('subDistrict'), null);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [countryId, city, district]);

    useEffect(() => {
        if (!isInitial.current) return;

        (async () => {
            setIsLoading(true);

            if (defaultCountry.id !== countryId) {
                setStates(
                    await jsonRequest({
                        url: '/api/common/states',
                        method: 'POST',
                        data: {countryId}
                    })
                );
            }

            setCities(
                await jsonRequest({
                    url: '/api/common/cities',
                    method: 'POST',
                    data: {countryId, state}
                })
            );

            if (!!city) {
                setDistricts(
                    await jsonRequest({
                        url: '/api/common/districts',
                        method: 'POST',
                        data: {countryId, city}
                    })
                );
            }

            if (!!city && !!district) {
                setSubDistricts(
                    await jsonRequest({
                        url: '/api/common/sub-districts',
                        method: 'POST',
                        data: {countryId, city, district}
                    })
                );
            }

            setIsLoading(false);
        })();
    }, [countryId, city, district, state, defaultCountry]);

    return (
        <>
            <UiForm.Field
                className="hidden"
                label={t('Apartment, suite, etc. (optional)')}
                {...register(fieldName('street2'), {required: false})}
            />

            <div className="flex space-x-4">
                {hasStates && (
                    <UiForm.Field
                        label={t('State')}
                        error={
                            errors[fieldName('state')] &&
                            errors[fieldName('state')]?.type === 'required'
                                ? t('{label} is required', {
                                      label: t('State')
                                  })
                                : undefined
                        }
                        {...register(fieldName('state'), {
                            required: true
                        })}
                        selection
                    >
                        {(states as string[]).map(state => (
                            <option key={state} value={state}>
                                {state}
                            </option>
                        ))}
                    </UiForm.Field>
                )}

                {cities.length > 0 ? (
                    <UiForm.Field
                        label={t('City')}
                        error={
                            errors[fieldName('city')] &&
                            errors[fieldName('city')]?.type === 'required'
                                ? t('{label} is required', {label: t('City')})
                                : undefined
                        }
                        {...register(fieldName('city'), {required: true})}
                        selection
                    >
                        {cities.map(city => (
                            <option key={city} value={city}>
                                {city}
                            </option>
                        ))}
                    </UiForm.Field>
                ) : (
                    <UiForm.Field
                        label={t('City')}
                        error={
                            errors[fieldName('city')] &&
                            errors[fieldName('city')]?.type === 'required'
                                ? t('{label} is required', {label: t('City')})
                                : undefined
                        }
                        {...register(fieldName('city'), {required: true})}
                        disabled={isLoading}
                    />
                )}

                {districts.length > 0 ? (
                    <UiForm.Field
                        label={t('District')}
                        error={
                            errors[fieldName('district')] &&
                            errors[fieldName('district')]?.type === 'required'
                                ? t('{label} is required', {
                                      label: t('District')
                                  })
                                : undefined
                        }
                        {...register(fieldName('district'), {
                            required: true
                        })}
                        selection
                    >
                        {districts.map(district => (
                            <option key={district} value={district}>
                                {district}
                            </option>
                        ))}
                    </UiForm.Field>
                ) : (
                    <UiForm.Field
                        label={t('District')}
                        error={
                            errors[fieldName('district')] &&
                            errors[fieldName('district')]?.type === 'required'
                                ? t('{label} is required', {
                                      label: t('District')
                                  })
                                : undefined
                        }
                        {...register(fieldName('district'), {
                            required: true
                        })}
                        disabled={isLoading}
                    />
                )}
            </div>

            {subDistricts.length > 0 ? (
                <UiForm.Field
                    label={t('Sub-District')}
                    error={
                        errors[fieldName('subDistrict')] &&
                        errors[fieldName('subDistrict')]?.type === 'required'
                            ? t('{label} is required', {
                                  label: t('Sub-District')
                              })
                            : undefined
                    }
                    {...register(fieldName('subDistrict'), {
                        required: true
                    })}
                    selection
                >
                    {subDistricts.map(city => (
                        <option key={city} value={city}>
                            {city}
                        </option>
                    ))}
                </UiForm.Field>
            ) : (
                <UiForm.Field
                    label={t('Sub-District')}
                    error={
                        errors[fieldName('subDistrict')] &&
                        errors[fieldName('subDistrict')]?.type === 'required'
                            ? t('{label} is required', {
                                  label: t('Sub-District')
                              })
                            : undefined
                    }
                    {...register(fieldName('subDistrict'), {
                        required: true
                    })}
                    disabled={isLoading}
                />
            )}

            <UiForm.Field
                label={t('Address')}
                error={
                    errors[fieldName('street')] &&
                    errors[fieldName('street')]?.type === 'required'
                        ? t('{label} is required', {label: t('Address')})
                        : undefined
                }
                {...register(fieldName('street'), {required: true})}
            />

            <div className="flex space-x-4">
                <UiForm.Field
                    label={t('Postal code')}
                    error={
                        errors[fieldName('postalCode')] &&
                        errors[fieldName('postalCode')]?.type === 'required'
                            ? t('{label} is required', {
                                  label: t('Postal code')
                              })
                            : undefined
                    }
                    {...register(fieldName('postalCode'), {required: true})}
                />

                <UiForm.Field
                    label={t('Country')}
                    error={
                        errors[fieldName('countryId')] &&
                        errors[fieldName('countryId')]?.type === 'required'
                            ? t('{label} is required', {
                                  label: t('Country')
                              })
                            : undefined
                    }
                    {...register(fieldName('countryId'), {
                        required: true
                    })}
                    defaultValue={initialCountryId || defaultCountry.id}
                    selection
                >
                    {countries.map(country => (
                        <option key={country.id} value={country.id}>
                            {country.name}
                        </option>
                    ))}
                </UiForm.Field>
            </div>
        </>
    );
});

if (isDev) {
    AddressForm.displayName = 'AddressForm';
}

export default AddressForm;
