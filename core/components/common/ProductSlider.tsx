import {FC, memo, useEffect, useState} from 'react';
import {ProductListItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiLink, UiSlider} from '@core/components/ui';
import {Autoplay, Navigation} from '@core/components/ui/Slider';
import ProductCard from './ProductCard';

type ProductSliderProps = {
    title?: string;
    detailPageLink?: string;
    products: ProductListItem[];
};

const ProductSlider: FC<ProductSliderProps> = memo(props => {
    const {title, detailPageLink, products} = props;
    const t = useTrans();
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    return (
        <div className="select-none">
            {title && (
                <div className="mb-4 flex items-center justify-between">
                    <h2 className="text-xl font-medium">{title}</h2>

                    {detailPageLink && (
                        <UiLink
                            href={detailPageLink}
                            className="text-sm font-semibold text-primary-600 hover:underline"
                        >
                            {t('Show All Products')}
                        </UiLink>
                    )}
                </div>
            )}

            {(products ?? []).length > 0 && isMounted && (
                <UiSlider
                    className="product-slider"
                    modules={[Autoplay, Navigation]}
                    autoplay={{
                        delay: 5000
                    }}
                    spaceBetween={8}
                    slidesPerView={2}
                    slidesPerGroup={2}
                    navigation
                    threshold={2}
                    breakpoints={{
                        1280: {
                            spaceBetween: 16,
                            slidesPerView: 5,
                            slidesPerGroup: 5
                        }
                    }}
                >
                    {(products ?? []).map(product => (
                        <UiSlider.Slide key={product.productId}>
                            <ProductCard
                                className="h-full hover:shadow-none"
                                product={product}
                                // @ts-ignore
                                isFake={product.isFake}
                            />
                        </UiSlider.Slide>
                    ))}
                </UiSlider>
            )}
        </div>
    );
});

if (isDev) {
    ProductSlider.displayName = 'ProductSlider';
}

export default ProductSlider;
