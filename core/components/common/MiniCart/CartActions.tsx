import {FC, memo, useCallback, useMemo, useState} from 'react';
import {useRouter} from 'next/router';
import {useSession} from 'next-auth/react';
import {Cart, CartItem} from '@core/types';
import {isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useStore, useTrans, useUI} from '@core/hooks';
import {UiButton, notification} from '@core/components/ui';
import {ArrowRightIcon} from '@core/icons/solid';
import CouponApplicationForm from '../CouponApplicationForm';
import Price from '../Price';

type CartActionsProps = {
    cart: Cart;
};

const CartActions: FC<CartActionsProps> = memo(({cart}) => {
    const t = useTrans();
    const router = useRouter();
    const {closeSideBar} = useUI();
    const [isProceedToCheckOutInProgress, setIsProceedToCheckOutInProgress] =
        useState(false);
    const {currency} = useStore();

    const {data: session} = useSession();

    const isProceedToCheckOutEnabled = useMemo(
        () =>
            cart.items.filter(
                item => item.selected && !item.removed && item.quantity > 0
            ).length > 0 && cart.subTotal > 0,
        [cart]
    );

    const onProceedToCheckout = useCallback(async () => {
        for (const item of cart.items) {
            if (item.productStockQuantity < item.quantity) {
                notification({
                    title: t('Error'),
                    description: t(
                        'Please remove out of stock items from your cart!'
                    ),
                    status: 'error'
                });

                return;
            }
        }

        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'begin_checkout',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: cart.items
                    .map((item: CartItem) => item.price * item.quantity)
                    .reduce((a, b) => a + b, 0),
                items: cart.items.map(item => ({
                    item_id: item.productCode,
                    item_name: item.productName,
                    discount: item.discountedPrice
                        ? item.price - item.discountedPrice
                        : 0,
                    price: item.price,
                    item_brand: item.brandName,
                    item_category: item.productCategory,
                    quantity: item.quantity
                }))
            }
        });
        // ----------------------------------------

        setIsProceedToCheckOutInProgress(true);

        if (!!session) {
            await router.push(`/checkout?t=${Date.now()}`);
        } else {
            await router.push('/auth?redirect=checkout');
        }

        closeSideBar();

        setIsProceedToCheckOutInProgress(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router, cart]);

    const onGoToCart = useCallback(() => {
        closeSideBar();
        router.push(`/cart?t=${Date.now()}`);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router]);

    return (
        <div className="flow-root border-t border-gray-200 p-4 xl:p-6">
            <div className="flex justify-between font-medium">
                <div className="mr-4">
                    <p>{t('Products total')}</p>

                    <p className="mt-0.5 text-xs text-gray-500 xl:text-sm">
                        {t(
                            'Shipping and other additional charges will be calculated at checkout.'
                        )}
                    </p>
                </div>

                <Price price={cart.grandTotal} />
            </div>

            <CouponApplicationForm disabled={!isProceedToCheckOutEnabled} />

            <UiButton
                className="mt-3 w-full xl:mt-6"
                variant="solid"
                color="primary"
                size="lg"
                disabled={!isProceedToCheckOutEnabled}
                leftIcon={<ArrowRightIcon className="mr-2 h-4 w-4" />}
                loading={isProceedToCheckOutInProgress}
                onClick={onProceedToCheckout}
            >
                {t('Proceed to Checkout')}
            </UiButton>

            <UiButton
                className="mt-3 hidden w-full xl:block"
                variant="light"
                color="primary"
                size="md"
                onClick={onGoToCart}
            >
                {t('Go to Cart')}
            </UiButton>

            <div className="mt-6 hidden justify-center text-center text-sm text-gray-500 xl:block">
                <p>
                    {t('or')}{' '}
                    <button
                        type="button"
                        className="font-medium text-primary-600 hover:text-primary-700"
                        onClick={() => closeSideBar()}
                    >
                        {t('Continue Shopping')}
                        <span aria-hidden="true"> &rarr;</span>
                    </button>
                </p>
            </div>
        </div>
    );
});

if (isDev) {
    CartActions.displayName = 'CartActions';
}

export default CartActions;
