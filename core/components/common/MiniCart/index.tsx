import {FC, memo, useEffect} from 'react';
import {isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useCart, useStore, useTrans} from '@core/hooks';
import {TrashIcon} from '@core/icons/outline';
import CartActions from './CartActions';
import CartItems from './CartItems';
import EmptyCart from './EmptyCart';
import {CartItem} from '@core/types';

const MiniCart: FC = memo(() => {
    const {cart, refreshCart, isLoading, productCount, removeItems} = useCart();
    const {currency} = useStore();
    const t = useTrans();

    useEffect(() => {
        refreshCart();

        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'view_cart',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: cart.items
                    .map((item: CartItem) => item.price * item.quantity)
                    .reduce((a, b) => a + b, 0),
                items: cart.items.map(item => ({
                    item_id: item.productCode,
                    item_name: item.productName,
                    discount: item.discountedPrice
                        ? item.price - item.discountedPrice
                        : 0,
                    price: item.price,
                    item_brand: item.brandName,
                    item_category: item.productCategory,
                    quantity: item.quantity
                }))
            }
        });
        // ----------------------------------------
        // eslint-disable-next-line
    }, []);

    return (
        <div className="relative flex h-full w-full flex-col">
            {isLoading && (
                <div className="absolute inset-0 z-50 h-full w-full bg-white bg-opacity-30"></div>
            )}

            {productCount > 0 && (
                <div className="container mt-4 flex items-center justify-between gap-4 xl:hidden">
                    <h1 className="font-medium">
                        {t('My Cart')} (
                        {productCount > 1
                            ? t('{count} Products', {
                                  count: productCount
                              })
                            : t('{count} Product', {count: 1})}
                        )
                    </h1>
                    <button
                        onClick={removeItems}
                        className="flex items-center gap-2 text-sm font-medium text-muted hover:text-danger-600"
                    >
                        <span>{t('Remove Products')}</span>
                        <TrashIcon className="h-4 w-4" />
                    </button>
                </div>
            )}

            {cart.items.length > 0 ? (
                <>
                    <CartItems cart={cart} />
                    <CartActions cart={cart} />
                </>
            ) : (
                <EmptyCart />
            )}
        </div>
    );
});

if (isDev) {
    MiniCart.displayName = 'MiniCart';
}

export default MiniCart;
