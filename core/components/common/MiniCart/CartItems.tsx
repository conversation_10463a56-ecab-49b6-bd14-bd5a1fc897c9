import {FC, memo, useCallback} from 'react';
import {useRouter} from 'next/router';
import storeConfig from '~/store.config';
import {Cart, CartItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useCart, useMobile, useTrans, useUI} from '@core/hooks';
import {UiImage} from '@core/components/ui';
import {CheckCircleIcon, TrashIcon, XCircleIcon} from '@core/icons/outline';
import Price from '../Price';
import Quantity from '../Quantity';

type CartItemsProps = {
    cart: Cart;
};

const CartItems: FC<CartItemsProps> = memo(({cart}) => {
    const t = useTrans();
    const router = useRouter();
    const {closeSideBar} = useUI();
    const {isMobile} = useMobile();
    const {removeItem, updateItem} = useCart();

    const onDetail = useCallback(
        (item: CartItem) => {
            closeSideBar();
            router.push(item.productLink);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [router]
    );

    const onRemoveItem = useCallback(
        (item: CartItem) => removeItem({...item}),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const onQuantityChange = useCallback(
        (item: CartItem, quantity: number) => updateItem({...item, quantity}),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return (
        <div className="flex-1 overflow-hidden">
            <div className="scroller h-full w-full overflow-y-auto p-4 xl:px-6 xl:pb-6 xl:pt-0">
                <ul role="list" className="-my-4 divide-y xl:-my-6">
                    {cart.items.map(item => {
                        const hasDiscount =
                            typeof item.discountedPrice === 'number' &&
                            item.discountedPrice > 0;

                        const hasProductAttributes =
                            Array.isArray(item.productAttributes) &&
                            item.productAttributes.length > 0;

                        const isPCMProduct =
                            item.isPCMProduct &&
                            !!item.pcmPayload &&
                            Object.keys(item.pcmPayload).length > 0;

                        return (
                            <li
                                className="flex py-4 xl:py-6"
                                key={item.productId}
                            >
                                <div
                                    className={cls(
                                        'h-24 flex-shrink-0 overflow-hidden rounded',
                                        {
                                            'w-18':
                                                storeConfig.catalog
                                                    .productImageShape ===
                                                'rectangle',
                                            'w-24':
                                                storeConfig.catalog
                                                    .productImageShape !==
                                                'rectangle'
                                        }
                                    )}
                                >
                                    <UiImage
                                        className="rounded"
                                        src={
                                            item.productImage
                                                ? item.productImage
                                                : '/no-image.png'
                                        }
                                        alt={item.productName}
                                        width={
                                            storeConfig.catalog
                                                .productImageShape ===
                                            'rectangle'
                                                ? 72
                                                : 96
                                        }
                                        height={96}
                                        fit="cover"
                                        position="center"
                                    />
                                </div>

                                <div className="ml-4 flex flex-1 flex-col overflow-hidden">
                                    <div className="flex items-start justify-between">
                                        <div className="flex w-8/12 flex-col items-start">
                                            <h3 className="w-full">
                                                <button
                                                    className="w-full text-left text-sm font-medium"
                                                    onClick={() =>
                                                        onDetail(item)
                                                    }
                                                >
                                                    <span className="inline-block w-full truncate xl:overflow-auto xl:whitespace-normal xl:text-left">
                                                        {item.productName}
                                                    </span>
                                                </button>
                                            </h3>

                                            {hasProductAttributes && (
                                                <div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-xs xl:-mt-0.5 xl:text-sm">
                                                    {item.productAttributes?.map(
                                                        attribute => (
                                                            <div
                                                                className="inline-flex items-center gap-0.5 text-muted"
                                                                key={
                                                                    attribute.value
                                                                }
                                                            >
                                                                <span>
                                                                    {
                                                                        attribute.label
                                                                    }
                                                                    :
                                                                </span>
                                                                <span>
                                                                    {
                                                                        attribute.value
                                                                    }
                                                                </span>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            )}

                                            {isPCMProduct && (
                                                <div
                                                    className={cls(
                                                        '-mt-0.5 grid text-xs xl:text-sm',
                                                        {
                                                            'grid-cols-2 gap-3':
                                                                Object.keys(
                                                                    item.pcmPayload!
                                                                ).length < 3
                                                        }
                                                    )}
                                                >
                                                    {item.pcmPayload?.summary.items.map(
                                                        /* @ts-ignore */
                                                        summaryItem => (
                                                            <div
                                                                className="flex"
                                                                key={
                                                                    summaryItem.code
                                                                }
                                                            >
                                                                <p className="mr-2">
                                                                    {
                                                                        summaryItem.label
                                                                    }
                                                                    :
                                                                </p>
                                                                <p className="text-muted">
                                                                    {
                                                                        summaryItem.value
                                                                    }
                                                                </p>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            )}
                                        </div>

                                        <Price
                                            className={cls(
                                                'text-sm font-semibold xl:ml-4 xl:text-base',
                                                {
                                                    '[&>span]:flex-col [&>span]:items-end [&>span]:text-primary-600':
                                                        hasDiscount
                                                }
                                            )}
                                            price={item.price}
                                            discountedPrice={
                                                hasDiscount
                                                    ? item.discountedPrice
                                                    : undefined
                                            }
                                        />
                                    </div>

                                    {isMobile ? (
                                        <div className="mt-1.5 grid select-none text-sm">
                                            <div className="flex items-center justify-between">
                                                <Quantity
                                                    size="sm"
                                                    quantity={item.quantity}
                                                    availableQuantity={
                                                        item.productStockQuantity
                                                    }
                                                    onChange={quantity =>
                                                        onQuantityChange(
                                                            item,
                                                            quantity
                                                        )
                                                    }
                                                />

                                                <button
                                                    type="button"
                                                    className="inline-flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 transition active:opacity-50"
                                                    onClick={() =>
                                                        onRemoveItem(item)
                                                    }
                                                >
                                                    <TrashIcon className="h-3.5 w-3.5" />
                                                </button>
                                            </div>

                                            <div className="mt-1.5 text-xs">
                                                {item.productStockQuantity >
                                                5 ? (
                                                    <div className="flex items-center space-x-1.5 text-gray-700">
                                                        <CheckCircleIcon className="h-3 w-3 text-green-600" />
                                                        <div>
                                                            {t('In Stock')}
                                                        </div>
                                                    </div>
                                                ) : item.productStockQuantity >
                                                  0 ? (
                                                    <div className="flex items-center space-x-1.5 text-gray-700">
                                                        <CheckCircleIcon className="h-3 w-3 text-green-600" />
                                                        <div>
                                                            {item.productStockQuantity >
                                                            1
                                                                ? t(
                                                                      'Last {count} products',
                                                                      {
                                                                          count: item.productStockQuantity
                                                                      }
                                                                  )
                                                                : t(
                                                                      'Last {count} product',
                                                                      {
                                                                          count: item.productStockQuantity
                                                                      }
                                                                  )}
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <div className="flex items-center space-x-1.5 text-gray-700">
                                                        <XCircleIcon className="h-3.5 w-3.5 text-red-600" />
                                                        <div>
                                                            {t('Out Of Stock')}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="mt-1.5 flex flex-1 items-end justify-between text-sm">
                                            <div className="flex items-center gap-4">
                                                <p className="text-gray-500">
                                                    {t('Quantity')}{' '}
                                                    {item.quantity}
                                                </p>

                                                {item.productStockQuantity >
                                                5 ? (
                                                    <div className="flex items-center space-x-1.5 text-gray-700">
                                                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                                                        <div>
                                                            {t('In Stock')}
                                                        </div>
                                                    </div>
                                                ) : item.productStockQuantity >
                                                  0 ? (
                                                    <div className="flex items-center space-x-1.5 text-gray-700">
                                                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                                                        <div>
                                                            {item.productStockQuantity >
                                                            1
                                                                ? t(
                                                                      'Last {count} products',
                                                                      {
                                                                          count: item.productStockQuantity
                                                                      }
                                                                  )
                                                                : t(
                                                                      'Last {count} product',
                                                                      {
                                                                          count: item.productStockQuantity
                                                                      }
                                                                  )}
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <div className="flex items-center space-x-1.5 text-gray-700">
                                                        <XCircleIcon className="h-4 w-4 text-red-600" />
                                                        <div>
                                                            {t('Out Of Stock')}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>

                                            <button
                                                type="button"
                                                className="h-5 w-5 cursor-pointer select-none text-gray-700 transition hover:text-danger-600"
                                                onClick={() =>
                                                    onRemoveItem(item)
                                                }
                                            >
                                                <TrashIcon className="h-5 w-5" />
                                            </button>
                                        </div>
                                    )}
                                </div>
                            </li>
                        );
                    })}
                </ul>
            </div>
        </div>
    );
});

if (isDev) {
    CartItems.displayName = 'CartItems';
}

export default CartItems;
