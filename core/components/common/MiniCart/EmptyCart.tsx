import {memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {BagIcon} from '@core/icons/outline';

const EmptyCart = memo(() => {
    const t = useTrans();

    return (
        <div className="flex flex-1 flex-col items-center justify-center px-6 py-12">
            <div className="flex h-20 w-20 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                <BagIcon className="h-6 w-6" />
            </div>

            <h2 className="pt-12 text-center text-lg font-semibold">
                {t('There are no items in your cart!')}
            </h2>

            <p className="px-10 pt-2 text-center text-muted">
                {t(
                    'You can add products to your cart from the detail page of the products on our site.'
                )}
            </p>
        </div>
    );
});

if (isDev) {
    EmptyCart.displayName = 'EmptyCart';
}

export default EmptyCart;
