import {useEffect} from 'react';
import {useCustomer, useStore} from '@core/hooks';
import {io} from '@core/helpers';

const Socket = () => {
    const customer = useCustomer();
    const {storeId} = useStore();

    useEffect(() => {
        const socket = io(storeId);

        socket.emit('user-activity', {
            customerId: customer?.id
        });

        return () => {
            if (socket.connected) socket.disconnect();
        };
    }, [storeId, customer?.id]);

    return null;
};

export default Socket;
