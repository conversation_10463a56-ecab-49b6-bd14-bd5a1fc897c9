import {FC, memo, useMemo} from 'react';
import {NavigationItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay, EffectFade, Navigation} from '@core/components/ui/Slider';

interface MainSliderProps {
    forSpecialPage?: boolean;
    items?: NavigationItem[];
}

const MainSlider: FC<MainSliderProps> = memo(
    ({forSpecialPage = false, items}) => {
        const {navigation} = useStore();

        const slides = useMemo(() => {
            if (forSpecialPage) {
                return (items ?? []).map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href
                }));
            }

            return navigation
                .filter(
                    navigationItem =>
                        navigationItem.type === 'slide' &&
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0 &&
                        navigationItem.depth === 0
                )
                .map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href
                }));
        }, [forSpecialPage, items, navigation]);

        return slides.length > 0 ? (
            <div className="home-main-slider container">
                <div className="aspect-h-3 aspect-w-8 my-4 w-full">
                    <div>
                        <UiSlider
                            className="h-full"
                            modules={[Autoplay, EffectFade, Navigation]}
                            autoplay={{
                                delay: 5000
                            }}
                            effect="fade"
                            fadeEffect={{
                                crossFade: true
                            }}
                            loop
                            navigation
                        >
                            {slides.map((slide, index) => (
                                <UiSlider.Slide key={index}>
                                    <UiLink
                                        className="relative block h-full w-full"
                                        href={slide.link ?? ''}
                                    >
                                        <UiImage
                                            className="rounded-lg"
                                            src={`${slide.src}?w=1280&q=90`}
                                            alt={slide.title}
                                            fit="cover"
                                            position="center"
                                            priority={index === 0}
                                            fill
                                        />
                                    </UiLink>
                                </UiSlider.Slide>
                            ))}
                        </UiSlider>
                    </div>
                </div>
            </div>
        ) : null;
    }
);

if (isDev) {
    MainSlider.displayName = 'MainSlider';
}

export default MainSlider;
