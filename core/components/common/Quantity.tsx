import {
    ChangeEvent<PERSON><PERSON><PERSON>,
    FC,
    FocusEventHandler,
    memo,
    useCallback,
    useEffect,
    useState
} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiInput} from '@core/components/ui';
import {MinusIcon, PlusIcon} from '@core/icons/outline';

export type QuantityProps = {
    className?: string;
    size?: 'sm' | 'md';
    quantity: number;
    availableQuantity: number;
    onChange: (quantity: number) => void;
};

const Quantity: FC<QuantityProps> = memo(props => {
    const {
        size = 'md',
        quantity = 1,
        availableQuantity = 1,
        className,
        onChange
    } = props;
    const [qty, setQty] = useState(quantity.toString());

    useEffect(() => {
        if (qty !== quantity.toString()) {
            setQty(quantity.toString());
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [quantity]);

    const increaseQty = useCallback(() => {
        const parsed = parseFloat(qty);

        if (isNaN(parsed)) {
            return qty;
        }

        let newQty = Math.min(parsed + 1, availableQuantity);

        if (newQty < 1) {
            newQty = 1;
        }

        onChange(newQty);
        setQty(newQty.toString());
    }, [availableQuantity, qty, onChange]);
    const decreaseQty = useCallback(() => {
        const parsed = parseFloat(qty);

        if (isNaN(parsed)) {
            return qty;
        }

        const newQty = Math.max(1, parsed - 1);

        onChange(newQty);
        setQty(newQty.toString());
    }, [qty, onChange]);
    const onInputChange: ChangeEventHandler<HTMLInputElement> = useCallback(
        e => setQty(e.target.value),
        []
    );
    const onInputBlur: FocusEventHandler<HTMLInputElement> = useCallback(() => {
        let newQty = parseFloat(qty);

        if (isNaN(newQty)) {
            setQty(quantity.toString());

            return;
        }

        newQty = Math.max(1, Math.min(newQty, availableQuantity));

        onChange(newQty);
        setQty(newQty.toString());
    }, [availableQuantity, qty, quantity, onChange]);

    return (
        <UiInput.Group className={className} size={size === 'md' ? 'xl' : 'md'}>
            <UiInput.LeftAddon
                className={cls(
                    'flex items-center border-gray-200 bg-white px-4 text-primary-600',
                    quantity <= 1
                        ? 'cursor-not-allowed text-gray-300'
                        : 'cursor-pointer'
                )}
                onClick={decreaseQty}
            >
                <MinusIcon
                    className={cls('h-4 w-4', {'h-3 w-3': size === 'sm'})}
                />
            </UiInput.LeftAddon>

            <UiInput
                className={cls(
                    'w-14 border-gray-200 border-l-transparent border-r-transparent text-center text-primary-600',
                    {
                        'w-11': size === 'sm',
                        'bg-primary-50 ': availableQuantity > 0,
                        'bg-red-100 text-red-700': availableQuantity < 1
                    }
                )}
                value={qty}
                onChange={onInputChange}
                onBlur={onInputBlur}
            />

            <UiInput.RightAddon
                className={cls(
                    'flex items-center border-gray-200 bg-white px-4 text-primary-600',
                    quantity >= availableQuantity
                        ? 'cursor-not-allowed text-gray-300'
                        : 'cursor-pointer'
                )}
                onClick={increaseQty}
            >
                <PlusIcon
                    className={cls('h-4 w-4', {'h-3 w-3': size === 'sm'})}
                />
            </UiInput.RightAddon>
        </UiInput.Group>
    );
});

if (isDev) {
    Quantity.displayName = 'Quantity';
}

export default Quantity;
