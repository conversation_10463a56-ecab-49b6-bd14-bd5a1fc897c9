import {Dispatch, ReactNode, SetStateAction} from 'react';
import {Product, ProductListItem, ProductVariant} from '@core/types';

export type Status = 'pending' | 'resolved' | 'rejected';

export type ColorVariantPicker = 'desktop' | 'mobile' | undefined;

export type ProductCardContext = {
    product: ProductListItem;
    setColorVariantPicker: Dispatch<SetStateAction<ColorVariantPicker>>;
    status: Status;
    setStatus: Dispatch<SetStateAction<Status>>;
    productItem: Product | undefined;
    colorVariantPicker: ColorVariantPicker;
    isFake: boolean;
    isFavoriteShown: boolean;
    isUnDiscountedPriceShown: boolean;
    preloadImage: boolean;
    isImageLoading: boolean;
    setIsImageLoading: Dispatch<SetStateAction<boolean>>;
    hasColorPicker: boolean;
    hasQuickLook: boolean;
    hasSellingOptions: boolean;
    hasAddToCart: boolean;
    hasRating: boolean;
    selectedVariant: ProductVariant | undefined;
    setSelectedVariant: Dispatch<SetStateAction<ProductVariant | undefined>>;
    productVariants: ProductVariant[];
    onRemove: ((product: ProductListItem) => void | Promise<void>) | undefined;
};

export type ProductCardProviderProps = {
    product: ProductListItem;
    preloadImage: boolean;
    isFavoriteShown: boolean;
    isUnDiscountedPriceShown: boolean;
    isFake: boolean;
    hasColorPicker: boolean;
    hasQuickLook: boolean;
    hasSellingOptions: boolean;
    hasAddToCart: boolean;
    hasRating: boolean;
    children: ReactNode;
    onRemove: ((product: ProductListItem) => void | Promise<void>) | undefined;
};
