import {useTrans} from '@core/hooks';
import {UiLink, UiTransition} from '@core/components/ui';
import {useProductCard} from '../context';
import VariantSlider from './VariantSlider';

const slidesPerViewCount = 4;

const Desktop = () => {
    const {
        setColorVariantPicker,
        colorVariantPicker,
        setSelectedVariant,
        product
    } = useProductCard();

    const t = useTrans();

    return (
        <UiTransition
            show={colorVariantPicker === 'desktop'}
            onMouseLeave={() => {
                setColorVariantPicker(undefined);
                setSelectedVariant(undefined);
            }}
            className="absolute bottom-0 left-0 right-0 z-10 translate-y-24 opacity-0"
            enter="transition ease duration-300 transform"
            enterFrom="opacity-0 translate-y-24"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease duration-300 transform"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 translate-y-24"
        >
            <div className="z-20 h-24 w-full overflow-x-auto overflow-y-hidden rounded-t-xl border-t bg-white px-2 pt-2">
                <div className="flex items-start justify-between gap-2 text-[11px] font-medium">
                    <p>{t('Different Color Options')}</p>
                    {product.colorVariantCount &&
                        product.colorVariantCount > 4 && (
                            <UiLink href={product.slug} className="underline">
                                +
                                {product.colorVariantCount - slidesPerViewCount}{' '}
                                {t('More Color')}
                            </UiLink>
                        )}
                </div>
                <VariantSlider slidesPerViewCount={slidesPerViewCount} />
            </div>
        </UiTransition>
    );
};

export default Desktop;
