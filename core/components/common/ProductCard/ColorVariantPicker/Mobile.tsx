import {Fragment} from 'react';
import {useTrans} from '@core/hooks';
import {UiDialog, UiTransition} from '@core/components/ui';
import {useProductCard} from '../context';
import VariantSlider from './VariantSlider';

const Mobile = () => {
    const {setColorVariantPicker, colorVariantPicker, productVariants} =
        useProductCard();

    const t = useTrans();

    return (
        <UiTransition.Root show={colorVariantPicker === 'mobile'} as={Fragment}>
            <UiDialog
                as="div"
                className="relative z-modal"
                onClose={() => setColorVariantPicker(undefined)}
            >
                <UiTransition.Child
                    as={Fragment}
                    enter="transition ease-in-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition ease-in-out duration-300"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-gray-900 bg-opacity-40 transition-opacity" />
                </UiTransition.Child>

                <div className="pointer-events-none fixed bottom-0 right-0 flex max-w-full xl:pl-10">
                    <UiTransition.Child
                        as={Fragment}
                        enter="transform transition ease-in-out duration-500"
                        enterFrom="translate-y-full"
                        enterTo="translate-y-0"
                        leave="transform transition ease-in-out duration-500"
                        leaveFrom="translate-y-0"
                        leaveTo="translate-y-full"
                    >
                        <UiDialog.Panel className="pointer-events-auto w-screen">
                            <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                                <div className="flex h-mobile-header w-full select-none border-b bg-white xl:hidden">
                                    <div className="container flex h-full items-center">
                                        <div className="min-w-0 flex-1">
                                            <div className="w-full truncate font-semibold">
                                                {t('Different Color Options')} (
                                                {productVariants.length})
                                            </div>
                                        </div>

                                        <div className="flex items-center">
                                            <button
                                                className="font-semibold text-primary-600 transition active:opacity-30"
                                                onClick={() =>
                                                    setColorVariantPicker(
                                                        undefined
                                                    )
                                                }
                                            >
                                                {t('Close')}
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div className="scroller container flex-1 overflow-y-auto overflow-x-hidden">
                                    <VariantSlider />
                                </div>
                            </div>
                        </UiDialog.Panel>
                    </UiTransition.Child>
                </div>
            </UiDialog>
        </UiTransition.Root>
    );
};

export default Mobile;
