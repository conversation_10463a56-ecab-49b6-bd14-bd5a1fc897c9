import dynamic from 'next/dynamic';
import {cls} from '@core/helpers';
import {useProductCard} from './context';
import ImagePartial from './ImagePartial';
import NamePartial from './NamePartial';
import PricePartial from './PricePartial';
import ProductSellingOptions from './ProductSellingOptions';
import Rating from './Rating';

const QuickLook = dynamic(() => import('../QuickLook'), {ssr: false});
const FavoriteAction = dynamic(() => import('./FavoriteAction'), {ssr: false});
const AddToCartAction = dynamic(() => import('./AddToCartAction'), {
    ssr: false
});

const ProductCardConsumer = ({className}: {className?: string}) => {
    const {
        product,
        hasQuickLook,
        hasSellingOptions,
        hasAddToCart,
        hasRating,
        setColorVariantPicker
    } = useProductCard();

    return (
        <div
            data-product=""
            data-id={product.code}
            data-name={product.name}
            data-price={product.salesPrice}
            data-brand={product.brandName}
            data-category={product.categoryName}
            onMouseLeave={() => setColorVariantPicker(undefined)}
            className={cls(
                'group relative flex flex-col rounded-lg border border-gray-100 bg-white transition xl:hover:shadow-lg',
                className
            )}
        >
            <div className="absolute right-1 top-1 isolate z-10 flex flex-col gap-1.5">
                {hasQuickLook && !product.isAdultProduct && (
                    <QuickLook productSlug={product.slug} />
                )}
                {!product.isAdultProduct && <FavoriteAction />}
            </div>

            <ImagePartial />

            <div className="flex flex-1 flex-col justify-start px-3 py-1.5 xl:justify-between xl:py-3">
                <NamePartial />

                {hasRating && <Rating />}

                <PricePartial />

                <div className="mb-1.5 mt-auto h-7 xl:mb-0">
                    {hasAddToCart &&
                        !product.isAdultProduct &&
                        (product.hasVariants ? (
                            <QuickLook
                                productSlug={product.slug}
                                variant="button"
                            />
                        ) : (
                            <AddToCartAction product={product} />
                        ))}
                </div>

                {hasSellingOptions && <ProductSellingOptions />}
            </div>
        </div>
    );
};

export default ProductCardConsumer;
