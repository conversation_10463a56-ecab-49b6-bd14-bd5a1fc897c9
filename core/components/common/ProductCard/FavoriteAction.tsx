import {useCallback, useEffect, useState} from 'react';
import {useStore, useTrans} from '@core/hooks';
import {UiSpinner, notification} from '@core/components/ui';
import {HeartIcon, TrashIcon} from '@core/icons/outline';
import {HeartIcon as HeartSolidIcon} from '@core/icons/solid';
import {useProductCard} from './context';

const FavoriteAction = () => {
    const {product, isFavoriteShown, onRemove} = useProductCard();
    const {addToFavorites, removeFromFavorites} = useStore();
    const t = useTrans();

    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);

    const [isFavorite, setIsFavorite] = useState(() => !!product.isFavorite);

    useEffect(() => setIsFavorite(!!product.isFavorite), [product]);

    const onFavoriteBtnClick = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        if (!isFavorite) {
            await addToFavorites({
                id: product.productId,
                name: product.name,
                image: product.images![0],
                price: product.salesPrice
            });
            setIsFavorite(true);
        } else {
            await removeFromFavorites({
                id: product.productId,
                name: product.name,
                image: product.images![0],
                price: product.salesPrice
            });
            setIsFavorite(false);
        }

        setIsFavoriteUpdateInProgress(false);
    }, [
        product,
        isFavorite,
        isFavoriteUpdateInProgress,
        addToFavorites,
        removeFromFavorites
    ]);

    const [isRemoveInProgress, setIsRemoveInProgress] = useState(false);
    const onRemoveBtnClick = useCallback(async () => {
        if (isRemoveInProgress) {
            return;
        }

        setIsRemoveInProgress(true);

        try {
            await onRemove!(product);
        } catch (error: any) {
            notification({
                title: t('Error'),
                description: error.message,
                status: 'error'
            });
        }

        setIsRemoveInProgress(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isRemoveInProgress, product, onRemove]);

    return (
        <>
            {isFavoriteShown && (
                <button
                    className="group/inner cursor-pointer rounded-full border bg-white p-1.5 shadow-sm transition hover:border-primary-600 hover:bg-primary-600"
                    onClick={onFavoriteBtnClick}
                >
                    {!isFavoriteUpdateInProgress ? (
                        isFavorite ? (
                            <HeartSolidIcon className="h-4 w-4 text-primary-600 transition group-hover/inner:text-white" />
                        ) : (
                            <HeartIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                        )
                    ) : (
                        <div className="flex h-4 w-4 items-center justify-center">
                            <UiSpinner className="group-hover/inner:text-white" />
                        </div>
                    )}
                </button>
            )}

            {typeof onRemove !== 'undefined' && (
                <button
                    className="group/inner cursor-pointer rounded-full border bg-white p-1.5 shadow-sm transition hover:border-danger-600 hover:bg-danger-600"
                    onClick={onRemoveBtnClick}
                >
                    {!isRemoveInProgress ? (
                        <TrashIcon className="h-4 w-4 transition group-hover/inner:text-white" />
                    ) : (
                        <div className="flex h-4 w-4 items-center justify-center">
                            <UiSpinner className="group-hover/inner:text-white" />
                        </div>
                    )}
                </button>
            )}
        </>
    );
};

export default FavoriteAction;
