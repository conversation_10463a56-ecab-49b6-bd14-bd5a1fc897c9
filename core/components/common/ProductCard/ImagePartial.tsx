import {useEffect, useMemo, useState} from 'react';
import dynamic from 'next/dynamic';
import storeConfig from '~/store.config';
import {cls} from '@core/helpers';
import {useMobile} from '@core/hooks';
import {UiImage} from '@core/components/ui';
import {useProductCard} from './context';

const ColorVariantPicker = dynamic(() => import('./ColorVariantPicker'), {
    ssr: false
});

const ImagePartial = () => {
    const {
        isFake,
        product,
        preloadImage,
        isImageLoading,
        setIsImageLoading,
        hasColorPicker,
        selectedVariant
    } = useProductCard();

    const {isMobile} = useMobile();

    const [isMounted, setIsMounted] = useState(false);
    useEffect(() => {
        setIsMounted(true);
    }, []);

    const image = useMemo(() => {
        if (product.isAdultProduct) {
            return '/adult-image.png';
        } else if (isFake) {
            return '';
        } else if (
            hasColorPicker &&
            selectedVariant &&
            Array.isArray(selectedVariant.images) &&
            selectedVariant.images.length > 0
        ) {
            return `${selectedVariant.images[0]}?w=480&q=60`;
        } else if (Array.isArray(product.images) && product.images.length > 0) {
            return `${product.images[0]}?w=480&q=60`;
        } else {
            return '/no-image.png';
        }
    }, [product, isFake, selectedVariant, hasColorPicker]);

    const imageAlt = useMemo(() => {
        if (isFake || product.isAdultProduct) {
            return null;
        } else if (Array.isArray(product.images) && product.images.length > 1) {
            return `${product.images[1]}?w=480&q=60`;
        } else {
            return null;
        }
    }, [product, isFake]);

    return (
        <div
            className={
                storeConfig.catalog.productImageShape === 'rectangle'
                    ? 'aspect-h-3 aspect-w-2'
                    : 'aspect-h-1 aspect-w-1'
            }
        >
            {(isImageLoading || isFake) && (
                <div className="absolute inset-0 z-20 h-full w-full bg-white">
                    <div className="skeleton-card h-full w-full rounded-b-none" />
                </div>
            )}

            <div className="overflow-hidden">
                <div
                    className={cls(
                        'relative h-full w-full transition',
                        !selectedVariant && 'group-hover:opacity-75'
                    )}
                >
                    {!!image && (
                        <UiImage
                            className="rounded-t-lg"
                            src={image}
                            alt={product.name}
                            fit="cover"
                            position="center"
                            fill
                            priority={preloadImage}
                            onLoadingComplete={() => setIsImageLoading(false)}
                        />
                    )}
                    {!!imageAlt &&
                        isMounted &&
                        !isMobile &&
                        !selectedVariant && (
                            <UiImage
                                className="rounded-t-lg opacity-0 transition duration-300 group-hover:opacity-100"
                                src={imageAlt}
                                alt={product.name}
                                fit="cover"
                                position="center"
                                fill
                            />
                        )}

                    {product.hasDiscount && product.discount > 0 && (
                        <p className="absolute bottom-1 left-1 rounded-md bg-discount px-1 py-0.5 text-xs text-white xl:hidden">
                            %{Math.ceil(product.discount)}
                        </p>
                    )}
                </div>

                {hasColorPicker && !product.isAdultProduct && (
                    <ColorVariantPicker />
                )}
            </div>
        </div>
    );
};

export default ImagePartial;
