import {useTrans} from '@core/hooks';
import {TruckFastIcon} from '@core/icons/solid';
import {useProductCard} from './context';

const ProductSellingOptions = () => {
    const {isImageLoading, product} = useProductCard();

    const t = useTrans();

    return (
        <>
            <div className="absolute left-1 top-1 z-[2] hidden select-none flex-col gap-1 text-[8px] xl:flex">
                {product.estimatedDeliveryDuration === 1 && !isImageLoading && (
                    <p className="w-12 rounded border border-green-600 bg-green-600 text-center leading-[10px] text-white">
                        {t('FAST SHIPPING')}
                    </p>
                )}
                {product.hasFreeShipping && !isImageLoading && (
                    <p className="w-12 rounded border border-gray-600 bg-gray-600 text-center leading-[10px] text-white">
                        {t('FREE SHIPPING')}
                    </p>
                )}
            </div>

            <div className="mt-auto grid grid-cols-2 gap-1 md:grid-cols-3 xl:hidden">
                {product.estimatedDeliveryDuration === 1 && !isImageLoading && (
                    <div className="flex flex-col items-center justify-center gap-1 overflow-hidden rounded bg-green-50 p-1 leading-[10px]">
                        <TruckFastIcon className="mx-auto h-4 w-4 text-green-600" />
                        <p className="max-w-fit text-center text-[8px] text-green-600">
                            {t('FAST SHIPPING')}
                        </p>
                    </div>
                )}
                {product.hasFreeShipping && !isImageLoading && (
                    <div className="flex flex-col items-center justify-center gap-1 overflow-hidden rounded bg-primary-50 p-1 leading-[10px]">
                        <TruckFastIcon className="mx-auto h-4 w-4 text-primary-600" />
                        <p className="max-w-fit text-center text-[8px] text-primary-600">
                            {t('FREE SHIPPING')}
                        </p>
                    </div>
                )}
            </div>
        </>
    );
};

export default ProductSellingOptions;
