import {memo, useState, useCallback} from 'react';
import {useRouter} from 'next/router';
import {CartItem, ProductListItem} from '@core/types';
import {cls, isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useCart, useMobile, useStore, useTrans} from '@core/hooks';
import {UiButton, notification} from '@core/components/ui';

import ProductCartSummary from '../ProductCartSummary';

import {Cookies} from 'react-cookie-consent';
import {BagIcon, XCircleIcon} from '@core/icons/outline';

type AddToCartActionProps = {
    product: ProductListItem;
    disabled?: boolean;
};

const CartAddToCartAction = memo(
    ({product, disabled}: AddToCartActionProps) => {
        const [isLoading, setIsLoading] = useState(false);
        const [isAddToCartInProgress, setIsAddToCartInProgress] =
            useState(false);

        const {addItem, cart, updateItem} = useCart();
        const {locale, currency} = useStore();
        const router = useRouter();
        const {isMobile} = useMobile();
        const t = useTrans();

        const removeItemFromCookies = (productId: string) => {
            const previouslyItems = localStorage.getItem('previouslyItems');
            if (previouslyItems) {
                const items = JSON.parse(previouslyItems) as CartItem[];
                const updatedItems = items.filter(
                    item => item.productId !== productId
                );

                localStorage.setItem(
                    'previouslyItems',
                    JSON.stringify(updatedItems)
                );
            }
        };

        const addToCart = useCallback(async () => {
            if (isAddToCartInProgress) {
                return;
            }

            setIsAddToCartInProgress(true);

            const item: CartItem = {
                productId: product.productId,
                productSlug: product.slug,
                productImage: (product.images as string[])[0],
                productName: product.name,
                brandName: product.brandName,
                productCategory: product.categoryName,
                productStockQuantity: product.quantity ?? 0,
                productRating: product.rating,
                productReviewCount: product.reviewCount,
                productLink: `/${product.slug}`,
                isKitProduct: product.isKitProduct,
                price: product.salesPrice,
                unitId: product.unitId,
                unitName: product.unitName,
                quantity: 1,
                weight: product.weight,
                width: product.width,
                height: product.height,
                depth: product.depth,
                volumetricWeight: 0,
                deliveryType: 'standard',
                deliveryOptionIds: product.deliveryOptionIds ?? [],
                deliveryPrice: 0,
                estimatedDeliveryDuration: product.estimatedDeliveryDuration,
                deliveryAtSpecifiedDate: product.deliveryAtSpecifiedDate,
                deliveryAtSpecifiedTime: product.deliveryAtSpecifiedTime,
                selected: true,
                removed: false
            };

            const inCartProduct = cart.items.find(
                cartItem => cartItem.productId === product.productId
            );

            let result = null;
            if (inCartProduct) {
                result = await updateItem({
                    ...item,
                    quantity: item.quantity + inCartProduct.quantity
                });
            } else {
                result = await addItem(item);
            }

            if (result) {
                // Google Tag Manager
                pushIntoGTMDataLayer({
                    event: 'add_to_cart',
                    data: {
                        currency:
                            currency.name === 'TL' ? 'TRY' : currency.name,
                        value: product.unDiscountedSalesPrice
                            ? product.unDiscountedSalesPrice
                            : product.salesPrice,
                        items: [
                            {
                                item_id: product.code,
                                item_name: product.name,
                                discount:
                                    product.unDiscountedSalesPrice > 0
                                        ? product.unDiscountedSalesPrice -
                                          product.salesPrice
                                        : 0,
                                item_brand: product.brandName,
                                item_category: product.categoryName,
                                price: product.unDiscountedSalesPrice
                                    ? product.unDiscountedSalesPrice
                                    : product.salesPrice,
                                quantity: 1
                            }
                        ]
                    }
                });

                notification({
                    title: t('Added to Cart'),
                    description: t('Product has been added to your cart.'),
                    status: 'success',
                    detailRenderer: closeNotification => (
                        <ProductCartSummary
                            locale={locale}
                            currency={currency}
                            item={item}
                            onDetail={() => {
                                closeNotification();
                                if (isMobile) {
                                    router.push(
                                        `/mobile/my-cart?t=${Date.now()}`
                                    );
                                } else {
                                    router.push(`/cart?t=${Date.now()}`);
                                }
                            }}
                        />
                    )
                });
            }
            if (
                router.pathname !== '/cart' &&
                'slug' in product &&
                typeof product.slug === 'string' &&
                product.quantity > 0
            ) {
                removeItemFromCookies(product.productId);
            } else if (
                router.pathname === '/cart' &&
                'slug' in product &&
                typeof product.slug === 'string' &&
                product.quantity > 0
            ) {
                removeItemFromCookies(product.productId);
            }

            setIsAddToCartInProgress(false);
        }, [
            isAddToCartInProgress,
            product,
            cart.items,
            currency,
            t,
            router,
            isMobile,
            updateItem,
            addItem,
            locale
        ]);

        const cartCheck = cart.items.find(
            cartItem => cartItem.productId === product.productId
        );

        const cartCheckQuantity = cartCheck?.quantity ?? 0;

        return (
            <div
                className={cls(
                    'relative isolate z-[5] xl:opacity-0 xl:transition xl:group-hover:opacity-100',
                    {
                        'xl:opacity-100': router.pathname === '/cart'
                    }
                )}
                onClick={() => {
                    if (
                        router.pathname !== '/cart' &&
                        'slug' in product &&
                        typeof product.slug === 'string' &&
                        product.quantity > 0
                    ) {
                        removeItemFromCookies(product.productId);
                    } else if (
                        router.pathname === '/cart' &&
                        'slug' in product &&
                        typeof product.slug === 'string' &&
                        product.quantity > 0
                    ) {
                        removeItemFromCookies(product.productId);
                    }
                }}
            >
                <UiButton
                    className={cls(
                        'inline-flex w-full gap-3 transition duration-300 hover:bg-primary-500 hover:text-white focus:!bg-primary-500 focus-visible:ring-2 focus-visible:ring-offset-2',
                        {
                            'h-8': router.pathname === '/cart',
                            '!border-gray-200 !bg-gray-100 !text-muted hover:!bg-gray-200 hover:!text-muted':
                                disabled
                        }
                    )}
                    variant="solid"
                    color={product.quantity > 0 ? 'primary' : 'outline'}
                    size="xs"
                    leftIcon={
                        !disabled ? (
                            <BagIcon className="hidden h-4 w-4 sm:inline-block" />
                        ) : (
                            <XCircleIcon className="hidden h-4 w-4 sm:inline-block" />
                        )
                    }
                    loading={isLoading || isAddToCartInProgress}
                    onClick={!disabled ? addToCart : () => {}}
                    disabled={disabled}
                >
                    {!disabled ? t('Add To Cart') : t('Out Of Stock')}
                </UiButton>
            </div>
        );
    }
);

if (isDev) {
    CartAddToCartAction.displayName = 'CartAddToCartAction';
}

export default CartAddToCartAction;
