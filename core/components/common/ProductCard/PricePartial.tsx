import {cls} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import Price from '../Price';
import {useProductCard} from './context';

const PricePartial = () => {
    const {currency} = useStore();
    const {product, isUnDiscountedPriceShown} = useProductCard();

    const t = useTrans();

    return (
        <div className="my-1.5 xl:mt-2">
            <div className="flex items-center justify-center gap-2 text-center text-sm font-semibold xl:text-base">
                {product.hasDiscount && product.discount > 0 && (
                    <p className="hidden rounded-md border-2 border-discount px-1 py-0.5 text-xs text-discount xl:block">
                        %{Math.ceil(product.discount)}
                    </p>
                )}
                {!!product.salesPrice ? (
                    isUnDiscountedPriceShown ? (
                        <Price
                            price={
                                product.hasDiscount
                                    ? product.unDiscountedSalesPrice
                                    : product.salesPrice
                            }
                            discountedPrice={
                                product.hasDiscount ? product.salesPrice : null
                            }
                            className={cls({
                                'xl:[&>span]:flex-col':
                                    product.salesPrice?.toFixed().length > 3
                            })}
                        />
                    ) : (
                        <Price price={product.salesPrice} />
                    )
                ) : (
                    `--- ${currency.name}`
                )}
            </div>

            {typeof product.salesPriceAtCart === 'number' &&
                product.salesPriceAtCart > 0 && (
                    <p className="mt-1.5 flex flex-col items-center justify-center rounded-md bg-red-50 p-1 xl:mt-2 xl:flex-row xl:gap-1">
                        <span className="text-xs text-discount">
                            {t('Price on Cart')}:
                        </span>
                        <Price
                            price={product.salesPriceAtCart}
                            className="text-sm font-semibold xl:-mt-[2px] [&>span]:text-discount"
                        />
                    </p>
                )}
        </div>
    );
};

export default PricePartial;
