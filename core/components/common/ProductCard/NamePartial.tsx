import {trim} from '@core/helpers';
import {UiLink} from '@core/components/ui';
import {useProductCard} from './context';

const NamePartial = () => {
    const {product} = useProductCard();

    return (
        <h3 className="text-center text-xs font-medium xl:text-sm">
            <UiLink
                href={
                    product.link
                        ? product.link
                        : `/${trim(trim(product.slug, '/'))}`
                }
            >
                <span className="absolute inset-0 z-[8]" />
                {!!product.brandName ? <span>{product.brandName} </span> : null}
                {product.name}
            </UiLink>
        </h3>
    );
};

export default NamePartial;
