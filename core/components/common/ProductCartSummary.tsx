import {FC, memo} from 'react';
import {CartItem} from '@core/types';
import {isDev} from '@core/helpers';
import {UiImage, UiPrice} from '@core/components/ui';
import {XIcon} from '@core/icons/outline';

type ProductCartSummaryProps = {
    locale: string;
    currency: {
        name: string;
        symbol: string;
        template: string;
    };
    item: CartItem;
    onDetail: () => void;
};

const ProductCartSummary: FC<ProductCartSummaryProps> = memo(props => {
    const {locale, currency, item, onDetail} = props;

    return (
        <div className="mt-4 w-full border-t pt-4 text-sm">
            <div className="flex cursor-pointer" onClick={onDetail}>
                <div className="h-18 relative w-12 flex-shrink-0">
                    <UiImage
                        src={
                            item.productImage
                                ? `${item.productImage}?w=120&q=75`
                                : '/no-image.png'
                        }
                        alt={item.productName}
                        fit="cover"
                        position="center"
                        fill
                    />
                </div>

                <div className="ml-4 flex-1 flex-col">
                    <div className="w-full">
                        <h2 className="font-medium">{item.productName}</h2>

                        {Array.isArray(item.productAttributes) &&
                            item.productAttributes.length > 0 && (
                                <div className="mt-0.5 flex items-center space-x-4">
                                    {item.productAttributes.map(attribute => (
                                        <div
                                            className="flex items-center text-muted"
                                            key={attribute.value}
                                        >
                                            <div className="mr-0.5">
                                                {attribute.label}:
                                            </div>
                                            <div>{attribute.value}</div>
                                        </div>
                                    ))}
                                </div>
                            )}
                    </div>

                    <div className="flex items-center gap-2 pt-2">
                        <UiPrice
                            locale={locale}
                            currency={currency}
                            price={item.price}
                            decimal={0}
                        />
                        <XIcon className="h-4 w-4" />
                        <div>{item.quantity}</div>
                    </div>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    ProductCartSummary.displayName = 'ProductCartSummary';
}

export default ProductCartSummary;
