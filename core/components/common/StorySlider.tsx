import {FC, memo, useMemo} from 'react';
import {NavigationItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay} from '@core/components/ui/Slider';

interface StorySliderProps {
    forSpecialPage?: boolean;
    items?: NavigationItem[];
}

const StorySlider: FC<StorySliderProps> = memo(
    ({forSpecialPage = false, items}) => {
        const {navigation} = useStore();

        const storyItems = useMemo(() => {
            if (forSpecialPage) {
                return (items ?? []).map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href
                }));
            }

            return navigation
                .filter(
                    navigationItem =>
                        navigationItem.type === 'story' &&
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0 &&
                        navigationItem.depth === 0
                )
                .map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href
                }));
        }, [forSpecialPage, items, navigation]);

        return storyItems.length > 0 ? (
            <div className="container mt-4 select-none">
                <div className="swiper-story-padding mx-auto w-full select-none rounded-lg bg-white xl:mr-8">
                    <div>
                        <UiSlider
                            className="h-full w-full"
                            modules={[Autoplay]}
                            autoplay={{delay: 2000}}
                            slidesPerView={3}
                            spaceBetween={10}
                            breakpoints={{
                                320: {
                                    slidesPerView: 4,
                                    spaceBetween: 15
                                },
                                480: {
                                    slidesPerView: 5,
                                    spaceBetween: 20
                                },
                                768: {
                                    slidesPerView: 8,
                                    spaceBetween: 25
                                },
                                1200: {
                                    slidesPerView: 11,
                                    spaceBetween: 40
                                }
                            }}
                        >
                            {storyItems?.map(story => {
                                return (
                                    <UiSlider.Slide
                                        className="group"
                                        key={story.src}
                                    >
                                        <UiLink
                                            className="relative block h-full max-w-[76px]"
                                            href={story.link ?? '#'}
                                        >
                                            <div className="flex flex-col">
                                                <div className="aspect-1 rounded-full p-1 ring-2 ring-gray-200 transition group-hover:ring-primary-600">
                                                    <UiImage
                                                        className="rounded-full transition group-hover:opacity-70"
                                                        src={`${story.src}?w=180&q=90`}
                                                        alt={story.title}
                                                        aspectH={1}
                                                        aspectW={1}
                                                        priority
                                                    />
                                                </div>
                                                <div className="mx-auto cursor-pointer pt-1 text-center text-[10px] transition group-hover:text-primary-600 lg:whitespace-nowrap lg:text-sm">
                                                    {story.title}
                                                </div>
                                            </div>
                                        </UiLink>
                                    </UiSlider.Slide>
                                );
                            })}
                        </UiSlider>
                    </div>
                </div>
            </div>
        ) : null;
    }
);

if (isDev) {
    StorySlider.displayName = 'StorySlider';
}

export default StorySlider;
