import {FC, memo, useEffect, useRef, useState} from 'react';
import {ProductListItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay, SliderInterface} from '@core/components/ui/Slider';
import {ChevronLeftIcon, ChevronRightIcon} from '@core/icons/outline';
import Price from './Price';

interface SliderProps {
    forSpecialPage?: boolean;
    products: ProductListItem[];
    detailPageLink: string;
    productSliderName?: string;
}

const FeaturedProductSlider: FC<SliderProps> = memo(
    ({products, detailPageLink, productSliderName}) => {
        const t = useTrans();

        const swiperRef = useRef<SliderInterface>();

        let link = '';

        if (detailPageLink.startsWith('/')) {
            link = detailPageLink;
        } else {
            link = `/${detailPageLink}`;
        }

        const [isSwiperInitialized, setIsSwiperInitialized] = useState(false);

        useEffect(() => {
            setIsSwiperInitialized(true);
        }, []);

        return products.length > 0 ? (
            <section className="container">
                <div className="my-4 flex flex-col gap-4 rounded-lg bg-[#FFF7ED] xl:flex-row xl:gap-8">
                    <div className="mt-4 flex flex-col justify-between xl:my-6 xl:ml-8 xl:w-3/12">
                        <div className="mx-4 flex items-center justify-between xl:mx-0 xl:flex-col xl:items-start xl:gap-5">
                            <p className="text-xl font-semibold leading-tight xl:text-4xl">
                                {t(productSliderName ?? 'Featured Products')}
                            </p>
                            <UiLink
                                className="w-fit text-sm text-primary-600 hover:text-primary-600 xl:text-lg xl:text-default"
                                href={link}
                            >
                                {t('All Products')}
                            </UiLink>
                        </div>
                        <div className="hidden gap-6 xl:flex">
                            <button
                                className="product-slider-button"
                                onClick={() => swiperRef.current?.slidePrev()}
                            >
                                <ChevronLeftIcon className="h-7 w-7" />
                            </button>
                            <button
                                className="product-slider-button"
                                onClick={() => swiperRef.current?.slideNext()}
                            >
                                <ChevronRightIcon className="h-7 w-7" />
                            </button>
                        </div>
                    </div>

                    <div className="shadow-card swiper-padding mx-auto mb-5 w-[95%] select-none rounded-lg bg-white xl:my-5 xl:mr-8 xl:w-9/12">
                        <div className="aspect-h-7 aspect-w-9 md:aspect-h-5 xl:aspect-h-4">
                            {isSwiperInitialized && (
                                <div>
                                    <UiSlider
                                        className="h-full w-full"
                                        modules={[Autoplay]}
                                        autoplay={{delay: 3000}}
                                        spaceBetween={12}
                                        onSwiper={swiper => {
                                            swiperRef.current = swiper;
                                        }}
                                        threshold={2}
                                        breakpoints={{
                                            300: {
                                                slidesPerView: 2
                                            },
                                            768: {
                                                slidesPerView: 3
                                            },
                                            1280: {
                                                slidesPerView: 4
                                            }
                                        }}
                                    >
                                        {products.map(product => (
                                            <UiSlider.Slide
                                                className="group"
                                                key={product.productId}
                                            >
                                                <UiLink
                                                    className="relative block h-full w-full"
                                                    href={'/' + product.slug}
                                                >
                                                    <UiImage
                                                        className="rounded-lg transition-opacity duration-200 group-hover:opacity-70"
                                                        src={
                                                            Array.isArray(
                                                                product.images
                                                            ) &&
                                                            product.images
                                                                .length > 0
                                                                ? `${product.images[0]}?w=480&q=90`
                                                                : '/no-image.png'
                                                        }
                                                        alt={product.name}
                                                        fit="cover"
                                                        position="center"
                                                        fill
                                                    />
                                                </UiLink>
                                                <div className="shadow-card absolute bottom-2 left-1/2 w-11/12 -translate-x-1/2 cursor-pointer rounded-lg bg-white p-2 text-xs will-change-transform md:text-sm xl:text-base">
                                                    <p className="w-full truncate">
                                                        {product.name}
                                                    </p>
                                                    <Price
                                                        price={
                                                            product.hasDiscount
                                                                ? product.unDiscountedSalesPrice
                                                                : product.salesPrice
                                                        }
                                                        discountedPrice={
                                                            product.hasDiscount
                                                                ? product.salesPrice
                                                                : null
                                                        }
                                                        dontWrapDiscountedPrice
                                                        className={cls(
                                                            'font-semibold max-sm:[&>span>span]:text-[2.5vw] [&>span]:space-x-1',
                                                            {
                                                                '[&>span]:text-discount':
                                                                    product.hasDiscount
                                                            }
                                                        )}
                                                    />
                                                </div>
                                            </UiSlider.Slide>
                                        ))}
                                    </UiSlider>
                                </div>
                            )}
                            <ul className="sr-only">
                                {products.map(product => (
                                    <li key={product.productId}>
                                        {product.name}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        ) : null;
    }
);

if (isDev) {
    FeaturedProductSlider.displayName = 'FeaturedProductSlider';
}

export default FeaturedProductSlider;
