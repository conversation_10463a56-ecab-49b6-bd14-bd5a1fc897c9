import {FC, memo, useMemo} from 'react';
import {NavigationItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {ChevronRightIcon} from '@core/icons/outline';

type CollectionProps = {
    title: string;
    src: string[];
    link?: string;
};

const Collection: FC<CollectionProps> = ({src, title, link}) => {
    const t = useTrans();

    const Component = link ? UiLink : 'div';

    return (
        <Component
            href={link!}
            className="shadow-card group relative block h-full w-full overflow-hidden rounded-lg"
        >
            <picture>
                <source
                    media="(max-width: 1280px)"
                    srcSet={`${src[1] ?? src[0]}?w=580&q=75`}
                />
                <source
                    media="(min-width: 1281px)"
                    srcSet={`${src[0]}?w=980&q=75`}
                />
                <UiImage
                    className="transition duration-500 group-hover:scale-105"
                    src={`${src[0]}?w=980&q=75`}
                    fill
                    fit="cover"
                    position="center"
                    alt={title}
                />
            </picture>

            <div className="shadow-card absolute bottom-2 left-1/2 hidden w-11/12 -translate-x-1/2 items-center justify-between rounded-[0.25rem] bg-white px-4 py-1 text-sm font-medium text-black transition duration-200 group-hover:border-primary-600 group-hover:bg-primary-600 xl:flex xl:py-2">
                <div className="text-[0.675rem] uppercase leading-5 group-hover:text-white">
                    {title}
                </div>
                <div className="hidden items-center text-[0.675rem] leading-5 text-white transition xl:group-hover:flex">
                    {t('Start Shopping')}
                    <ChevronRightIcon className="ml-0.5 h-4 w-4" />
                </div>
            </div>
        </Component>
    );
};

interface CollectionsProps {
    forSpecialPage?: boolean;
    items?: NavigationItem[];
}

const Collections: FC<CollectionsProps> = memo(
    ({forSpecialPage = false, items}) => {
        const {navigation} = useStore();

        const collections = useMemo(() => {
            if (forSpecialPage) {
                return (items ?? [])
                    .filter(
                        navigationItem =>
                            Array.isArray(navigationItem.images) &&
                            navigationItem.images.length > 0
                    )
                    .map(navigationItem => ({
                        title: navigationItem.name,
                        src: navigationItem.images as string[],
                        link: navigationItem.href
                    }));
            }

            return navigation
                .filter(
                    navigationItem =>
                        navigationItem.type === 'collection' &&
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0 &&
                        navigationItem.depth === 0
                )
                .map(navigationItem => ({
                    title: navigationItem.name,
                    src: navigationItem.images as string[],
                    link: navigationItem.href
                }));
        }, [forSpecialPage, items, navigation]);

        return collections.length > 0 ? (
            <div className="container my-4 grid grid-cols-1 gap-4 sm:grid sm:grid-cols-2 md:grid md:grid-cols-3">
                {collections.map((collection, index) => (
                    <div
                        className="aspect-h-3 aspect-w-5 xl:aspect-w-4"
                        key={index}
                    >
                        {/* This empty div is required for aspect ratio and next/image to work together. */}
                        <div>
                            <Collection
                                title={collection.title}
                                src={collection.src}
                                link={collection.link}
                            />
                        </div>
                    </div>
                ))}
            </div>
        ) : null;
    }
);

if (isDev) {
    Collections.displayName = 'Collections';
}

export default Collections;
