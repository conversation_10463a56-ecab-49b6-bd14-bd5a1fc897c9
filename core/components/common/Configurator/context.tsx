import {
    createContext,
    FC,
    memo,
    PropsWithChildren,
    ReactNode,
    useCallback,
    useMemo,
    useState
} from 'react';
import {isDev, jsonRequest} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {ConfiguratorContextType, Payload, Step} from './types';
import {notification} from '@core/components/ui';

export const ConfiguratorContext = createContext<ConfiguratorContextType>(
    null as any
);

if (isDev) {
    ConfiguratorContext.displayName = 'ConfiguratorContext';
}

export const ConfiguratorProvider: FC<
    PropsWithChildren<{
        initialPayload: Payload;
        initialValues: Record<string, any>;
        availableQuantity?: number;
        AddToCartButton: ReactNode;
        onChange: (payload: Payload) => void;
    }>
> = memo(
    ({
        initialPayload,
        initialValues,
        availableQuantity,
        AddToCartButton,
        onChange,
        children
    }) => {
        const t = useTrans();

        const [payload, setPayload] = useState(() => {
            if (typeof initialPayload.deliveryDate === 'string') {
                initialPayload.deliveryDate = new Date(
                    Date.parse(initialPayload.deliveryDate)
                );
            }

            return initialPayload;
        });
        const [isLoading, setIsLoading] = useState(false);

        // Steps.
        const steps = useMemo(
            () => payload.steps.filter(step => !step.isHidden),
            [payload.steps]
        );

        // Current step
        const currentStep = useMemo(() => {
            const steps = payload.steps ?? [];
            const stepId = payload.stepId ?? steps[0].id;

            return steps.find(step => step.id === stepId);
        }, [payload.stepId, payload.steps]);

        // Values.
        const values = useMemo(() => payload.values, [payload.values]);

        // Methods.
        const updatePayload = useCallback(
            (payload: Payload) => {
                if (typeof payload.deliveryDate === 'string') {
                    payload.deliveryDate = new Date(
                        Date.parse(payload.deliveryDate)
                    );
                }

                onChange(payload);
                setPayload(payload);
            },
            [onChange]
        );
        const changeStep = useCallback(
            async (step: Step) => {
                setIsLoading(true);

                try {
                    const newPayload = await jsonRequest({
                        url: '/api/pcm/get-payload',
                        method: 'POST',
                        data: {
                            targetId: payload.model.id,
                            initialValues,
                            currentPayload: {
                                ...payload,
                                stepId: step.id
                            }
                        }
                    });

                    updatePayload(newPayload);
                } catch (error: any) {
                    notification({
                        title: t('Error'),
                        description: error.message,
                        status: 'error'
                    });
                }

                setIsLoading(false);
            },
            [initialValues, payload, t, updatePayload]
        );
        const changeFieldValue = useCallback(
            async (name: string, value: any) => {
                setIsLoading(true);

                try {
                    const newPayload = await jsonRequest({
                        url: '/api/pcm/get-payload',
                        method: 'POST',
                        data: {
                            targetId: payload.model.id,
                            initialValues,
                            currentPayload: {
                                ...payload,
                                values: {
                                    ...(payload.values || {}),
                                    [name]: value
                                }
                            },
                            changePayload: {
                                field: name,
                                value
                            }
                        }
                    });

                    updatePayload(newPayload);
                } catch (error: any) {
                    notification({
                        title: t('Error'),
                        description: error.message,
                        status: 'error'
                    });
                }

                setIsLoading(false);
            },
            [initialValues, payload, t, updatePayload]
        );
        const changeQuantity = useCallback(
            async (quantity: number) => {
                setIsLoading(true);

                try {
                    const newPayload = await jsonRequest({
                        url: '/api/pcm/get-payload',
                        method: 'POST',
                        data: {
                            targetId: payload.model.id,
                            initialValues,
                            currentPayload: {
                                ...payload,
                                quantity
                            }
                        }
                    });

                    updatePayload(newPayload);
                } catch (error: any) {
                    notification({
                        title: t('Error'),
                        description: error.message,
                        status: 'error'
                    });
                }

                setIsLoading(false);
            },
            [initialValues, payload, t, updatePayload]
        );

        const value: any = useMemo(
            () => ({
                payload,
                isLoading,
                currentStep,
                steps,
                values,
                availableQuantity,
                AddToCartButton,

                changeStep,
                changeFieldValue,
                changeQuantity,
                updatePayload
            }),
            [
                AddToCartButton,
                availableQuantity,
                changeFieldValue,
                changeQuantity,
                changeStep,
                currentStep,
                isLoading,
                payload,
                steps,
                updatePayload,
                values
            ]
        );

        return (
            <ConfiguratorContext.Provider value={value}>
                {children}
            </ConfiguratorContext.Provider>
        );
    }
);

if (isDev) {
    ConfiguratorProvider.displayName = 'ConfiguratorProvider';
}
