import {FC, memo, ReactNode} from 'react';
import {isDev} from '@core/helpers';
import Manager from './components/Manager';
import {ConfiguratorProvider} from './context';
import {Payload} from './types';

type ConfiguratorProps = {
    payload: Payload;
    initialValues: Record<string, any>;
    availableQuantity?: number;
    AddToCartButton: ReactNode;
    onChange: (payload: Payload) => void;
};

const Configurator: FC<ConfiguratorProps> = memo(
    ({
        payload: initialPayload,
        initialValues,
        availableQuantity,
        AddToCartButton,
        onChange
    }) => {
        return (
            <ConfiguratorProvider
                initialPayload={initialPayload}
                initialValues={initialValues}
                availableQuantity={availableQuantity}
                AddToCartButton={AddToCartButton}
                onChange={onChange}
            >
                <Manager />
            </ConfiguratorProvider>
        );
    }
);

if (isDev) {
    Configurator.displayName = 'Configurator';
}

export default Configurator;
