import {ReactNode} from 'react';

export type Step = {
    id: string;
    code: string;
    name: string;
    isActive: boolean;
    isCompleted: boolean;
    isHidden: boolean;
    isSummary: boolean;
    canBeShown: boolean;
};

export type Field = {
    id: string;
    name: string;
    label: string;
    min?: any;
    max?: any;
    stepCode: string;
    description?: string;
    isHidden: boolean;
    isRequired: boolean;
    isReadOnly: boolean;
    component:
        | 'configuration-field-text'
        | 'configuration-field-select'
        | 'configuration-field-select-list'
        | 'configuration-field-select-color'
        | 'configuration-field-select-image'
        | 'configuration-field-integer'
        | 'configuration-field-decimal'
        | 'configuration-field-date'
        | 'configuration-field-datetime'
        | 'configuration-field-boolean'
        | 'configuration-field-yes-no';
    displayType?: string;
    options?: {
        value: string;
        label: string;
        shortDescription?: string;
        description?: string;
        color?: string;
        images?: string[];
    }[];
};

export type Payload = {
    model: {
        id: string;
        name: string;
    };
    price: number;
    additionalPrice: number;
    quantity: number;
    deliveryDate: string | Date;
    steps: Step[];
    stepId?: string;
    fields: Field[];
    values: Record<string, any>;
    images: string[];
    covered: string[];
    products: Record<string, any>[];
    summary: {
        code: string;
        name: string;
        description?: string;
        items: {
            code: string;
            value: string;
            label: string;
        }[];
    };
};

export type ConfiguratorContextType = {
    payload: Payload;
    steps: Step[];
    currentStep?: Step;
    values: Record<string, any>;
    isLoading: boolean;
    availableQuantity?: number;
    AddToCartButton: ReactNode;

    changeStep: (step: Step) => Promise<void>;
    changeFieldValue: (name: string, value: any) => Promise<void>;
    changeQuantity: (quantity: number) => Promise<void>;
    updatePayload: (payload: Payload) => void;
};
