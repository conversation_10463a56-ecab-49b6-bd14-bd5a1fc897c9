import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {Field} from '../../../types';

import Boolean from './Boolean';
import DateTime from './DateTime';
import Number from './Number';
import Select from './Select';
import SelectColor from './SelectColor';
import SelectImage from './SelectImage';
import SelectList from './SelectList';
import Text from './Text';
import YesNo from './YesNo';

type FieldProps = {
    field: Field;
};

const Field: FC<FieldProps> = memo(({field}) => {
    const FieldComponent = useMemo(() => {
        if (field.component === 'configuration-field-boolean') {
            return Boolean;
        } else if (field.component === 'configuration-field-select') {
            return Select;
        } else if (field.component === 'configuration-field-select-color') {
            return SelectColor;
        } else if (field.component === 'configuration-field-select-image') {
            return SelectImage;
        } else if (field.component === 'configuration-field-select-list') {
            return SelectList;
        } else if (
            field.component === 'configuration-field-integer' ||
            field.component === 'configuration-field-decimal'
        ) {
            return Number;
        } else if (
            field.component === 'configuration-field-date' ||
            field.component === 'configuration-field-datetime'
        ) {
            return DateTime;
        } else if (field.component === 'configuration-field-yes-no') {
            return YesNo;
        }

        return Text;
    }, [field.component]);

    return (
        <div className="">
            <div className="mb-1.5 text-sm font-medium">{field.label}</div>

            {field.description && (
                <div className="-mt-1.5 mb-1.5 text-sm text-gray-700">
                    {field.description}
                </div>
            )}

            <FieldComponent field={field} />
        </div>
    );
});

if (isDev) {
    Field.displayName = 'Field';
}

export default Field;
