import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiRadioGroup} from '@core/components/ui';
import {useConfigurator} from '../../../helpers';
import {Field} from '../../../types';

function isColorLight(color: string) {
    const hex = color.replace('#', '');
    const c_r = parseInt(hex.substr(0, 2), 16);
    const c_g = parseInt(hex.substr(2, 2), 16);
    const c_b = parseInt(hex.substr(4, 2), 16);
    const brightness = (c_r * 299 + c_g * 587 + c_b * 114) / 1000;

    return brightness > 240;
}

type SelectColorProps = {
    field: Field;
};

const SelectColor: FC<SelectColorProps> = memo(({field}) => {
    const {values, changeFieldValue} = useConfigurator();
    const options = useMemo(() => field.options ?? [], [field.options]);
    const value = useMemo(() => values[field.name], [field.name, values]);

    return (
        <UiRadioGroup
            value={value}
            onChange={selectedValue =>
                changeFieldValue(field.name, selectedValue)
            }
            disabled={field.isReadOnly}
        >
            <div className="-m-1.5 mt-1.5 flex select-none flex-wrap items-center">
                {options.map(option => (
                    <UiRadioGroup.Option
                        key={option.value}
                        value={option.value}
                        className={({checked, active}) =>
                            cls(
                                'relative m-1.5 flex items-center justify-center p-0.5',
                                'cursor-pointer rounded-full transition focus:outline-none',
                                {
                                    'ring ring-gray-200 ring-offset-1': checked
                                }
                            )
                        }
                        style={
                            !!option.color && !isColorLight(option.color)
                                ? {
                                      // @ts-ignore
                                      '--tw-ring-color': `${option.color}`
                                  }
                                : {}
                        }
                    >
                        {!!option.color ? (
                            <div
                                aria-hidden="true"
                                className="h-8 w-8 rounded-full border border-black border-opacity-10"
                                style={{
                                    backgroundColor: option.color
                                }}
                            />
                        ) : (
                            <div
                                aria-hidden="true"
                                className="relative h-8 w-8 overflow-hidden rounded-full border border-black border-opacity-10"
                            >
                                <svg
                                    className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                    viewBox="0 0 100 100"
                                    preserveAspectRatio="none"
                                    stroke="currentColor"
                                >
                                    <line
                                        x1={0}
                                        y1={100}
                                        x2={100}
                                        y2={0}
                                        vectorEffect="non-scaling-stroke"
                                    />
                                </svg>
                            </div>
                        )}
                    </UiRadioGroup.Option>
                ))}
            </div>
        </UiRadioGroup>
    );
});

if (isDev) {
    SelectColor.displayName = 'SelectColor';
}

export default SelectColor;
