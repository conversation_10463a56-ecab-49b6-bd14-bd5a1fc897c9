import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiSelect} from '@core/components/ui';
import {useConfigurator} from '../../../helpers';
import {Field} from '../../../types';

type SelectProps = {
    field: Field;
};

const Select: FC<SelectProps> = memo(({field}) => {
    const t = useTrans();
    const {values, changeFieldValue} = useConfigurator();

    const options = useMemo(() => field.options ?? [], [field.options]);
    const value = useMemo(() => values[field.name], [field.name, values]);

    return (
        <UiSelect
            value={value}
            onChange={e => changeFieldValue(field.name, e.target.value)}
            disabled={field.isReadOnly}
            placeholder={t('Choose an option.')}
            className="h-12"
            size="xl"
        >
            {options.map((option, index) => (
                <option key={option.value + index} value={option.value}>
                    {option.label}
                </option>
            ))}
        </UiSelect>
    );
});

if (isDev) {
    Select.displayName = 'Select';
}

export default Select;
