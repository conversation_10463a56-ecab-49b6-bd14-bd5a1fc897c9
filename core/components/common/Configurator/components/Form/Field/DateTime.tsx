import {FC, memo, useMemo, useState} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiInput} from '@core/components/ui';
import {useConfigurator} from '../../../helpers';
import {Field} from '../../../types';

type DateTimeProps = {
    field: Field;
};

const DateTime: FC<DateTimeProps> = memo(({field}) => {
    const t = useTrans();
    const {values, changeFieldValue} = useConfigurator();
    const value = useMemo(() => {
        const v = values[field.name];

        if (typeof v === 'string' && v.length > 0) {
            const date = new Date(Date.parse(v));
            let y: any = date.getFullYear();
            let m: any = date.getMonth() + 1;
            m = m > 9 ? m : `0${m}`;
            let d: any = date.getDate();
            d = d > 9 ? d : `0${d}`;

            return `${y}-${m}-${d}`;
        }

        return '';
    }, [field.name, values]);
    const [currentValue, setCurrentValue] = useState(() => value);

    return (
        <UiInput
            className="h-12"
            size="xl"
            type="date"
            value={currentValue ?? ''}
            disabled={field.isReadOnly}
            onChange={e => setCurrentValue(e.target.value)}
            onBlur={e => changeFieldValue(field.name, e.target.value)}
        />
    );
});

if (isDev) {
    DateTime.displayName = 'DateTime';
}

export default DateTime;
