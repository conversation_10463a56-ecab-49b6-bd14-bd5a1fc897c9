import {FC, memo, useMemo, useState} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiInput} from '@core/components/ui';
import {useConfigurator} from '../../../helpers';
import {Field} from '../../../types';

type TextProps = {
    field: Field;
};

const Text: FC<TextProps> = memo(({field}) => {
    const t = useTrans();
    const {values, changeFieldValue} = useConfigurator();
    const value = useMemo(() => values[field.name], [field.name, values]);
    const [currentValue, setCurrentValue] = useState(() => value);

    return (
        <UiInput
            className="h-12"
            size="xl"
            value={currentValue ?? ''}
            disabled={field.isReadOnly}
            onChange={e => setCurrentValue(e.target.value)}
            onBlur={e => changeFieldValue(field.name, e.target.value)}
        />
    );
});

if (isDev) {
    Text.displayName = 'Text';
}

export default Text;
