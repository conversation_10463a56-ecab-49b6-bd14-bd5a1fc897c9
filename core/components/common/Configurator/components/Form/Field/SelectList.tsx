import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiRadioGroup} from '@core/components/ui';
import {CheckCircleIcon} from '@core/icons/solid';
import {useConfigurator} from '../../../helpers';
import {Field} from '../../../types';

type SelectListProps = {
    field: Field;
};

const SelectList: FC<SelectListProps> = memo(({field}) => {
    const {values, changeFieldValue} = useConfigurator();
    const options = useMemo(() => field.options ?? [], [field.options]);
    const value = useMemo(() => values[field.name], [field.name, values]);

    return (
        <UiRadioGroup
            value={value}
            onChange={selectedValue =>
                changeFieldValue(field.name, selectedValue)
            }
            disabled={field.isReadOnly}
        >
            <div className="space-y-2">
                {options.map(option => (
                    <UiRadioGroup.Option
                        key={option.value}
                        value={option.value}
                        className={({checked, active}) =>
                            cls(
                                checked
                                    ? 'border-transparent'
                                    : 'border-gray-300',
                                'relative cursor-pointer rounded border bg-white px-4 py-3.5 shadow-sm transition focus:outline-none'
                            )
                        }
                    >
                        {({checked, active}) => (
                            <div className="flex items-center">
                                <div className="mr-2.5 w-5">
                                    {checked ? (
                                        <CheckCircleIcon
                                            className="h-5 w-5 text-primary-600"
                                            aria-hidden="true"
                                        />
                                    ) : (
                                        <div className="h-5 w-5 rounded-full border border-gray-300"></div>
                                    )}
                                </div>

                                <div className="flex flex-1 flex-col">
                                    <UiRadioGroup.Label
                                        as="span"
                                        className="block text-sm font-medium"
                                    >
                                        {option.label}
                                    </UiRadioGroup.Label>

                                    {option.shortDescription && (
                                        <UiRadioGroup.Description
                                            as="span"
                                            className="mt-0.5 flex items-center text-xs text-gray-700"
                                        >
                                            {option.shortDescription}
                                        </UiRadioGroup.Description>
                                    )}
                                </div>

                                <div
                                    className={cls(
                                        checked
                                            ? 'border-primary-600'
                                            : 'border-transparent',
                                        'pointer-events-none absolute -inset-px rounded-lg border-2'
                                    )}
                                    aria-hidden="true"
                                />
                            </div>
                        )}
                    </UiRadioGroup.Option>
                ))}
            </div>
        </UiRadioGroup>
    );
});

if (isDev) {
    SelectList.displayName = 'SelectList';
}

export default SelectList;
