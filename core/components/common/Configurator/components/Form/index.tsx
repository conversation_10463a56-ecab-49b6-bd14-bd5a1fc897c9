import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useConfigurator} from '../../helpers';
import Field from './Field';

const Form: FC = memo(() => {
    const {payload} = useConfigurator();

    return (
        <div
            className="
            scroller flex max-h-[480px] w-full flex-col space-y-6 overflow-auto
            bg-gray-100 p-4 xl:max-h-[640px] xl:min-h-[480px] xl:w-[480px] xl:space-y-8 xl:p-6
            "
        >
            {payload.fields
                .filter(field => !field.isHidden)
                .map(field => (
                    <Field key={field.id} field={field} />
                ))}
        </div>
    );
});

if (isDev) {
    Form.displayName = 'Form';
}

export default Form;
