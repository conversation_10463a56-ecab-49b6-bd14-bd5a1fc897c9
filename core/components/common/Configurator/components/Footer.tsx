import {FC, memo, useCallback, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiButton} from '@core/components/ui';
import {ArrowLeftIcon, ArrowRightIcon} from '@core/icons/solid';
import {useConfigurator} from '../helpers';

const Footer: FC = memo(() => {
    const t = useTrans();
    const {payload, steps, currentStep, AddToCartButton, changeStep} =
        useConfigurator();

    const isPrevDisabled = useMemo(() => {
        const stepIndex = steps.findIndex(step => step.id === currentStep?.id);

        return stepIndex === 0;
    }, [currentStep?.id, steps]);
    const isNextDisabled = useMemo(() => {
        const stepIndex = steps.findIndex(step => step.id === currentStep?.id);

        return steps.length - 1 === stepIndex;
    }, [currentStep?.id, steps]);

    const onPrev = useCallback(() => {
        if (isPrevDisabled) return;

        const stepIndex = steps.findIndex(step => step.id === currentStep?.id);

        // noinspection JSIgnoredPromiseFromCall
        changeStep(steps[stepIndex - 1]);
    }, [changeStep, currentStep?.id, isPrevDisabled, steps]);
    const onNext = useCallback(() => {
        if (isNextDisabled || !currentStep!.isCompleted) return;

        const stepIndex = steps.findIndex(step => step.id === currentStep?.id);

        // noinspection JSIgnoredPromiseFromCall
        changeStep(steps[stepIndex + 1]);
    }, [changeStep, currentStep, isNextDisabled, steps]);

    return (
        <div className="hidden border-t border-gray-200 p-6 xl:flex">
            <UiButton
                variant="solid"
                color="primary"
                disabled={isPrevDisabled}
                onClick={onPrev}
                leftIcon={<ArrowLeftIcon className="mr-2 h-4 w-4" />}
            >
                {t('Previous Step')}
            </UiButton>

            <div className="flex flex-1 items-center justify-center">
                <div className="w-full truncate px-8 text-center text-xl font-semibold">
                    {payload.model.name}
                </div>
            </div>

            {isNextDisabled ? (
                AddToCartButton
            ) : (
                <UiButton
                    variant="solid"
                    color="primary"
                    disabled={isNextDisabled || !currentStep!.isCompleted}
                    onClick={onNext}
                    rightIcon={<ArrowRightIcon className="ml-2 h-4 w-4" />}
                >
                    {t('Next Step')}
                </UiButton>
            )}
        </div>
    );
});

if (isDev) {
    Footer.displayName = 'Footer';
}

export default Footer;
