import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import MobileHeader from '@core/context/MobileContext/MobileHeader';
import {useConfigurator} from '../helpers';
import Content from './Content';
import Footer from './Footer';
import Form from './Form';
import MobileFooter from './MobileFooter';
import Steps from './Steps';
import Summary from './Summary';

const Manager: FC = memo(() => {
    const {
        payload: {model},
        currentStep,
        isLoading
    } = useConfigurator();

    return (
        <div className="relative  pb-mobile-tab-bar pt-mobile-header xl:pb-0 xl:pt-0">
            <MobileHeader title={model.name} />

            <Steps />

            <div className="flex flex-col xl:mt-8 xl:rounded xl:border xl:border-gray-200 xl:shadow-sm">
                <div className="xl:flex">
                    {currentStep?.isSummary ? <Summary /> : <Form />}

                    <Content />
                </div>

                <Footer />
            </div>

            <MobileFooter />

            {isLoading && (
                <div className="absolute inset-0 z-10 bg-whiteAlpha-600"></div>
            )}
        </div>
    );
});

if (isDev) {
    Manager.displayName = 'Manager';
}

export default Manager;
