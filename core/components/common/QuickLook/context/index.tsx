import {
    createContext,
    memo,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {
    Campaign,
    CustomerProductPrams,
    Product,
    ProductOption,
    SelectedProduct
} from '@core/types';
import {isDev, jsonRequest, slugifyProduct} from '@core/helpers';
import {useCart, useCustomer, useMobile, useStore, useTrans} from '@core/hooks';
import ProductCartSummary from '../../ProductCartSummary';
import {QuickLookContext as ContextType, Status} from './types';
import {
    getInitialSelectedProduct,
    getProductOptions,
    getSelectedProduct,
    prepareCartItem
} from './utils';
import {notification} from '@core/components/ui';

export const QuickLookContext = createContext<ContextType | undefined>(
    undefined
);

type QuickLookProviderProps = {
    productSlug: string | undefined;
    children: React.ReactNode;
};

export const QuickLookProvider = memo(
    ({children, productSlug = ''}: QuickLookProviderProps) => {
        const [product, setProduct] = useState<Product>();
        const [campaigns, setCampaigns] = useState<Campaign[]>([]);
        const [status, setStatus] = useState<Status>('resolved');
        const [isModalActive, setIsModalActive] = useState(false);
        const [isAddToCartInProgress, setIsAddToCartInProgress] =
            useState(false);
        const [productOptions, setProductOptions] = useState<ProductOption[]>(
            []
        );
        const [selectedProduct, setSelectedProduct] = useState<SelectedProduct>(
            {} as SelectedProduct
        );
        const [customerProductParams, setCustomerProductParams] =
            useState<CustomerProductPrams>({} as CustomerProductPrams);

        const t = useTrans();
        const {locale, currency} = useStore();
        const router = useRouter();
        const {isMobile} = useMobile();
        const {cart, addItem, updateItem} = useCart();
        const customer = useCustomer();

        const {processedSlug, selectedAttributes} = slugifyProduct(productSlug);

        useEffect(() => {
            (async () => {
                try {
                    if (!isModalActive) return;
                    setStatus('pending');

                    const result = await jsonRequest({
                        url: '/api/catalog/product',
                        method: 'POST',
                        data: {slug: processedSlug}
                    });

                    const product = result.product as Product;

                    setProduct(product);
                    setCampaigns(result.campaigns);
                    setCustomerProductParams({
                        productId: product.productId,
                        isFavorite: false,
                        isInCollection: false,
                        isInAlarmCollection: false,
                        isNotifyCustomer: false
                    });
                    setStatus('resolved');
                } catch (err) {
                    setStatus('rejected');
                    notification({
                        title: t('Error'),
                        description: t(
                            'An error occurred while opening the product detail!'
                        ),
                        status: 'error'
                    });
                }
            })();
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [processedSlug, isModalActive]);

        const availableQuantity = useMemo(() => {
            if (selectedProduct === undefined) return 0;

            let availableQuantity = selectedProduct.availableQuantity;

            for (const cartItem of cart.items ?? []) {
                if (cartItem.productId === selectedProduct.productId) {
                    availableQuantity -= cartItem.quantity;
                }
            }

            return availableQuantity;
        }, [cart, selectedProduct]);

        const inStock = useMemo(() => {
            return (availableQuantity ?? 0) > 0;
        }, [availableQuantity]);

        useEffect(() => {
            if (!customerProductParams?.productId || !customer) return;
            (async () => {
                try {
                    const params = await jsonRequest<CustomerProductPrams>({
                        url: '/api/customers/product-params-for-customer',
                        method: 'POST',
                        data: {
                            productId: selectedProduct.productId
                        }
                    });
                    setCustomerProductParams(params);
                } catch (error) {}
            })();
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [selectedProduct, customer]);

        const setQuantity = useCallback(
            (quantity: number) => {
                quantity = parseFloat(quantity as any);

                if (selectedProduct === undefined) return;

                if (!isNaN(quantity)) {
                    setSelectedProduct({
                        ...selectedProduct,
                        quantity: Math.max(
                            1,
                            Math.min(
                                quantity,
                                selectedProduct?.availableQuantity
                            )
                        )
                    });
                }
            },
            [selectedProduct]
        );

        useEffect(() => {
            if (product === undefined) return;

            setSelectedProduct(
                getInitialSelectedProduct(product, selectedAttributes)
            );

            setProductOptions(() => {
                const initialSelectedProduct = getInitialSelectedProduct(
                    product,
                    selectedAttributes
                );

                let productOptions: ProductOption[] = [];

                if (
                    Array.isArray(product.options) &&
                    product.options.length > 0
                ) {
                    const firstOption = product.options[0];

                    if (firstOption.type === 'color') {
                        productOptions = getProductOptions(
                            product,
                            firstOption.code,
                            initialSelectedProduct.attributes![firstOption.code]
                        );
                    } else {
                        productOptions = getProductOptions(product);
                    }
                }
                return productOptions;
            });
            // eslint-disable-next-line
        }, [product]);

        const setAttribute = useCallback(
            (code: string, value: string) => {
                if (product === undefined) return;

                const newAttributes = {
                    ...selectedProduct?.attributes,
                    [code]: value
                };
                const variants = (product?.variants ?? []).filter(variant => {
                    return variant.attributes[code] === value;
                });

                let variant = variants.find(variant => {
                    for (const newAttributesCode of Object.keys(
                        newAttributes
                    )) {
                        if (
                            newAttributes[newAttributesCode] !==
                            variant.attributes[newAttributesCode]
                        ) {
                            return false;
                        }
                    }

                    return true;
                });
                if (!variant && variants.length > 0) {
                    variant = variants[0];
                }

                if (!!variant) {
                    setSelectedProduct(
                        getSelectedProduct({
                            product,
                            variant,
                            selectedProduct
                        })
                    );
                }
            },
            [product, selectedProduct]
        );

        const addToCart = useCallback(() => {
            if (isAddToCartInProgress || selectedProduct === undefined) {
                return;
            }

            setIsAddToCartInProgress(true);

            (async () => {
                const cartItem = prepareCartItem({
                    selectedProduct,
                    productOptions
                });

                const inCartProduct = cart.items.find(
                    cartItem => cartItem.productId === selectedProduct.productId
                );

                let result;
                if (inCartProduct) {
                    result = await updateItem({
                        ...cartItem,
                        quantity: cartItem.quantity + inCartProduct.quantity
                    });
                } else {
                    result = await addItem(cartItem);
                }

                if (result) {
                    notification({
                        title: t('Added to Cart'),
                        description: t('Product has been added to your cart.'),
                        status: 'success',
                        detailRenderer: closeNotification => (
                            <ProductCartSummary
                                locale={locale}
                                currency={currency}
                                item={cartItem}
                                onDetail={() => {
                                    closeNotification();
                                    if (isMobile) {
                                        router.push(
                                            `/mobile/my-cart?t=${Date.now()}`
                                        );
                                    } else {
                                        router.push(`/cart?t=${Date.now()}`);
                                    }
                                }}
                            />
                        )
                    });
                }

                setQuantity(1);
                setIsAddToCartInProgress(false);
            })();
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [
            isAddToCartInProgress,
            selectedProduct,
            setQuantity,
            addItem,
            updateItem,
            cart,
            productOptions
        ]);

        const value = useMemo(
            () => ({
                product,
                status,
                selectedProduct,
                isAddToCartInProgress,
                availableQuantity,
                inStock,
                productOptions,
                campaigns,
                isModalActive,
                customerProductParams,

                addToCart,
                setQuantity,
                setStatus,
                setProduct,
                setAttribute,
                setIsModalActive,
                setCustomerProductParams
            }),
            [
                product,
                status,
                selectedProduct,
                isAddToCartInProgress,
                availableQuantity,
                inStock,
                productOptions,
                campaigns,
                isModalActive,
                customerProductParams,

                addToCart,
                setQuantity,
                setAttribute
            ]
        );

        return (
            <QuickLookContext.Provider value={value}>
                {children}
            </QuickLookContext.Provider>
        );
    }
);

if (isDev) {
    QuickLookProvider.displayName = 'OrderProvider';
}

export function useQuickLook() {
    const quickLookContext = useContext(QuickLookContext);

    if (quickLookContext === undefined) {
        throw new Error('useQuickLook must be used inside QuickLookProvider.');
    }

    return quickLookContext;
}
