import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useQuickLook} from '../../context';

type SizeOptionsProps = {
    code: string;
    label: string;
    selections: {
        value: string;
        color?: string;
        inStock?: boolean;
    }[];
};

const SizeOptions: FC<SizeOptionsProps> = memo(props => {
    const {code, label, selections} = props;
    const {selectedProduct, setAttribute} = useQuickLook();
    const value = useMemo(
        () => (selectedProduct.attributes ?? {})[code],
        [selectedProduct, code]
    );

    return (
        <>
            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <h3 className="text-sm font-medium text-default">
                        {label}:
                    </h3>
                    <div className="ml-1.5 text-sm text-muted">{value}</div>
                </div>
            </div>

            <div className="-m-1.5 mt-2.5 select-none">
                <div className="flex flex-wrap items-center">
                    {selections.map(selection => (
                        <button
                            key={selection.value}
                            onClick={() => setAttribute(code, selection.value)}
                            className={cls(
                                'group relative m-1.5 flex items-center justify-center rounded border px-3 py-3',
                                'cursor-pointer bg-white text-sm font-medium uppercase hover:bg-primary-600 focus:outline-none',
                                selection.inStock
                                    ? 'text-default shadow-sm'
                                    : 'text-gray-700'
                            )}
                        >
                            <div>{selection.value}</div>
                            <div
                                className={cls(
                                    selection.value === value
                                        ? 'border-2 border-primary-600'
                                        : 'border-2 border-transparent',
                                    'pointer-events-none absolute -inset-px rounded'
                                )}
                                aria-hidden="true"
                            >
                                {!selection.inStock && (
                                    <svg
                                        className="absolute inset-0 h-full w-full stroke-2 text-gray-200"
                                        viewBox="0 0 100 100"
                                        preserveAspectRatio="none"
                                        stroke="currentColor"
                                    >
                                        <line
                                            x1={0}
                                            y1={100}
                                            x2={100}
                                            y2={0}
                                            vectorEffect="non-scaling-stroke"
                                        />
                                    </svg>
                                )}
                            </div>
                        </button>
                    ))}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    SizeOptions.displayName = 'SizeOptions';
}

export default SizeOptions;
