import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {useQuickLook} from '../../context';

const ProductCampaigns: FC = memo(() => {
    const t = useTrans();
    const {campaigns} = useQuickLook();

    return (
        <div className="rounded border shadow-sm">
            <p className="border-b p-4 text-xs font-semibold">
                {t('PRODUCT CAMPAIGNS')}
            </p>

            {Array.isArray(campaigns) && campaigns.length > 0 ? (
                <div className="space-y-2 p-2 text-xs">
                    {campaigns.map(campaign => (
                        <div
                            key={campaign.id}
                            className="flex items-center gap-3 rounded bg-primary-50 p-2"
                        >
                            <svg
                                className="h-5 w-5"
                                viewBox="0 0 18 18"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <g mask="url(#a)" className="fill-primary-600">
                                    <path
                                        d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                        fillOpacity=".55"
                                    />
                                    <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                </g>
                            </svg>
                            <p className="font-semibold text-primary-600">
                                {campaign.description}
                            </p>
                        </div>
                    ))}
                </div>
            ) : (
                <p className="p-4 text-xs">{t('No campaign found!')}</p>
            )}
        </div>
    );
});

if (isDev) {
    ProductCampaigns.displayName = 'ProductCampaigns';
}

export default ProductCampaigns;
