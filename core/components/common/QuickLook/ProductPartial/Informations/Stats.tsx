import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {CheckCircleIcon, HeartIcon, XCircleIcon} from '@core/icons/outline';
import {useQuickLook} from '../../context';

const Stats: FC = memo(() => {
    const t = useTrans();
    const {selectedProduct, availableQuantity} = useQuickLook();

    return (
        <div className="mt-4 flex select-none items-center justify-between text-sm">
            {availableQuantity > 5 ? (
                <div className="flex items-center space-x-1 text-muted">
                    <CheckCircleIcon className="mr-1 h-4 w-4 text-green-600" />
                    <div>{t('In Stock')}</div>
                </div>
            ) : availableQuantity > 0 ? (
                <div className="flex items-center space-x-1 text-muted">
                    <CheckCircleIcon className="mr-1 h-4 w-4 text-green-600" />
                    <div>
                        {availableQuantity > 1
                            ? t('Last {count} products', {
                                  count: availableQuantity
                              })
                            : t('Last {count} product', {
                                  count: availableQuantity
                              })}
                    </div>
                </div>
            ) : (
                <div className="flex items-center space-x-1 text-muted">
                    <XCircleIcon className="mr-1 h-4 w-4 text-red-600" />
                    <div>{t('Out Of Stock')}</div>
                </div>
            )}

            <div className="flex items-center space-x-1 text-muted">
                <HeartIcon className="mr-1 h-4 w-4" />
                <div>
                    {t('{count} Favorite(s)', {
                        count: selectedProduct?.favoritesCount
                    })}
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    Stats.displayName = 'Stats';
}

export default Stats;
