import {FC, Fragment, memo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiTab} from '@core/components/ui';
import {useQuickLook} from '../../context';

const ProductInformation: FC = memo(() => {
    const t = useTrans();
    const {selectedProduct} = useQuickLook();

    const hasDescripton =
        selectedProduct.description && selectedProduct.description.length > 0;

    const hasFeature =
        Array.isArray(selectedProduct.features) &&
        selectedProduct.features.length > 0;

    return hasDescripton || hasFeature ? (
        <div className="mt-8">
            <UiTab.Group>
                <UiTab.List className="flex items-center justify-center gap-2 border-b font-medium">
                    {hasDescripton && (
                        <UiTab as={Fragment}>
                            {({selected}) => (
                                <button
                                    className={cls(
                                        'border-b-2 px-4 py-2 transition',
                                        selected
                                            ? 'border-primary-600'
                                            : 'border-transparent hover:border-gray-300'
                                    )}
                                >
                                    {t('Product Information')}
                                </button>
                            )}
                        </UiTab>
                    )}
                    {hasFeature && (
                        <UiTab as={Fragment}>
                            {({selected}) => (
                                <button
                                    className={cls(
                                        'border-b-2 px-4 py-2 transition',
                                        selected
                                            ? 'border-primary-600'
                                            : 'border-transparent hover:border-gray-300'
                                    )}
                                >
                                    {t('Product Features')}
                                </button>
                            )}
                        </UiTab>
                    )}
                </UiTab.List>
                <UiTab.Panels>
                    {hasDescripton && (
                        <UiTab.Panel>
                            <div
                                className="prose mt-4 max-w-none text-sm"
                                dangerouslySetInnerHTML={{
                                    __html: selectedProduct.description ?? ''
                                }}
                            />
                        </UiTab.Panel>
                    )}
                    {hasFeature && (
                        <UiTab.Panel>
                            <div className="mt-4 grid gap-2 md:grid-cols-2 md:gap-4">
                                {selectedProduct.features.map(
                                    (feature, index) => (
                                        <div
                                            className="flex cursor-default items-center justify-between rounded border border-transparent bg-gray-100 px-4 py-3 text-sm shadow-sm transition hover:border-primary-600 hover:bg-white"
                                            key={feature.code + index}
                                        >
                                            <div className="font-medium">
                                                {feature.label}
                                            </div>
                                            <div className="text-muted">
                                                {feature.value}
                                            </div>
                                        </div>
                                    )
                                )}
                            </div>
                        </UiTab.Panel>
                    )}
                </UiTab.Panels>
            </UiTab.Group>
        </div>
    ) : null;
});

if (isDev) {
    ProductInformation.displayName = 'ProductInformation';
}

export default ProductInformation;
