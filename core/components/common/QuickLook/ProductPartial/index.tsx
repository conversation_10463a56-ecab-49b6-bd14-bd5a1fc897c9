import {useMobile, useTrans} from '@core/hooks';
import {UiModal, UiSideBar} from '@core/components/ui';
import {useQuickLook} from '../context';
import Actions from './Actions';
import ImageGallery from './ImageGallery';
import Info from './Informations/Info';
import ProductInformation from './Informations/ProductInformation';
import Stats from './Informations/Stats';
import Options from './Options';
import SideBar from './Sidebar';

const ProductPartial = () => {
    const {setIsModalActive, isModalActive, status, product} = useQuickLook();

    const {isMobile} = useMobile();

    const t = useTrans();

    return (
        <>
            <UiSideBar
                size="normal"
                title={t('Product Detail')}
                isShown={isModalActive && status === 'resolved' && isMobile}
                onClose={() => setIsModalActive(false)}
            >
                <div className="p-4">
                    <ImageGallery />
                    <Info />
                    <Actions />
                    <Stats />
                    <SideBar />
                    <ProductInformation />
                </div>
            </UiSideBar>

            <UiModal
                title={t('Product Detail')}
                isClosable
                isLarge
                isShown={isModalActive && status === 'resolved' && !isMobile}
                onClose={() => setIsModalActive(false)}
            >
                <div className="border-t px-8 py-4">
                    <div className="flex">
                        <div className="flex-1">
                            <div className="grid grid-cols-12 gap-8">
                                <div className="col-span-6">
                                    <ImageGallery />
                                </div>
                                <div className="col-span-6">
                                    <Info />
                                    {(product?.variants ?? []).length > 0 && (
                                        <Options />
                                    )}
                                    <Actions />
                                    <Stats />
                                </div>
                            </div>
                        </div>
                        <div className="ml-8 w-60">
                            <SideBar />
                        </div>
                    </div>

                    <ProductInformation />
                </div>
            </UiModal>
        </>
    );
};

export default ProductPartial;
