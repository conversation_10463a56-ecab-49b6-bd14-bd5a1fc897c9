import {FC, memo, useCallback, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {signIn} from 'next-auth/react';
import {useForm} from 'react-hook-form';
import {isDev, regexp} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiAlert, UiButton, UiForm} from '@core/components/ui';
import {
    EnvelopeIcon,
    EyeIcon,
    EyeSlashIcon,
    KeyIcon
} from '@core/icons/outline';

type SignInProps = {
    redirect?: string;
};

const SignIn: FC<SignInProps> = memo(props => {
    const {redirect} = props;
    const {
        register,
        formState: {errors},
        handleSubmit,
        watch
    } = useForm();
    const router = useRouter();
    const t = useTrans();
    const password: string = watch('password', '');
    const [isLoading, setIsLoading] = useState(false);
    const [isPasswordShown, setIsPasswordShown] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const inProgressRef = useRef(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;
            setIsLoading(true);
            setErrorMessage('');

            try {
                const result: any = await signIn('credentials', {
                    redirect: false,
                    email: data.email,
                    password: data.password
                });

                if (!!result?.error) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new Error(result.error);
                }

                if (typeof redirect === 'string') {
                    await router.push(
                        !!redirect && redirect !== ''
                            ? redirect
                            : '/account/my-orders'
                    );
                    return;
                }
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            inProgressRef.current = false;
            setIsLoading(false);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [router, redirect]
    );

    return (
        <UiForm onSubmit={handleSubmit(onSubmit)}>
            {!!errorMessage && (
                <UiAlert className="mb-4" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <UiForm.Field
                label={t('Email address')}
                autoCorrect="off"
                autoCapitalize="none"
                leftElement={
                    <EnvelopeIcon className="-mt-1 h-5 w-5 text-gray-400" />
                }
                error={
                    errors.email && errors.email.type === 'required'
                        ? t('Email address is required')
                        : errors.email && errors.email.type === 'pattern'
                        ? t('Email address is invalid')
                        : undefined
                }
                {...register('email', {
                    required: true,
                    pattern: regexp.email
                })}
            />

            <UiForm.Field
                className="mt-4"
                label={t('Password')}
                leftElement={<KeyIcon className="h-5 w-5 text-gray-400" />}
                rightElement={
                    !!password &&
                    password.length > 0 && (
                        <div
                            className="cursor-pointer"
                            onClick={() => setIsPasswordShown(!isPasswordShown)}
                        >
                            {isPasswordShown && (
                                <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                            )}
                            {!isPasswordShown && (
                                <EyeIcon className="h-5 w-5 text-gray-400" />
                            )}
                        </div>
                    )
                }
                type={isPasswordShown ? 'text' : 'password'}
                error={
                    errors.password && errors.password.type === 'required'
                        ? t('Password is required')
                        : undefined
                }
                {...register('password', {required: true})}
            />

            <UiButton
                className="mt-4 w-full"
                type="submit"
                variant="solid"
                size="xl"
                color="primary"
                loading={isLoading}
            >
                {t('Sign In')}
            </UiButton>
        </UiForm>
    );
});

if (isDev) {
    SignIn.displayName = 'SignIn';
}

export default SignIn;
