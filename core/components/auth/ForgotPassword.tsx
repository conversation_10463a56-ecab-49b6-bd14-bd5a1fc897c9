import {FC, memo, useCallback, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {isDev, jsonRequest, regexp} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiAlert, UiButton, UiForm, notification} from '@core/components/ui';
import {EnvelopeIcon} from '@core/icons/outline';

type ForgotPasswordProps = {
    redirect?: string;
};

const ForgotPassword: FC<ForgotPasswordProps> = memo(({redirect}) => {
    const {
        register,
        formState: {errors},
        handleSubmit
    } = useForm();
    const router = useRouter();
    const t = useTrans();
    const {locale} = useStore();
    const [isLoading, setIsLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const inProgressRef = useRef(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;
            setIsLoading(true);
            setErrorMessage('');

            try {
                await jsonRequest({
                    url: '/api/auth/send-reset-password-link',
                    method: 'POST',
                    data: {
                        email: data.email,
                        locale
                    }
                });

                notification({
                    title: t('Reset Your Password'),
                    description: t(
                        'A password reset link has been sent to your email address.'
                    ),
                    status: 'success'
                });

                if (typeof redirect === 'string') {
                    await router.push(
                        !!redirect && redirect !== '' ? redirect : '/'
                    );
                    return;
                } else {
                    await router.push('/');
                }
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            inProgressRef.current = false;
            setIsLoading(false);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [router, redirect]
    );

    return (
        <UiForm onSubmit={handleSubmit(onSubmit)}>
            {!!errorMessage && (
                <UiAlert className="mb-4" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <UiForm.Field
                label={t('Email address')}
                autoCorrect="off"
                autoCapitalize="none"
                leftElement={
                    <EnvelopeIcon className="-mt-1 h-5 w-5 text-gray-400" />
                }
                error={
                    errors.email && errors.email.type === 'required'
                        ? t('Email address is required')
                        : errors.email && errors.email.type === 'pattern'
                        ? t('Email address is invalid')
                        : undefined
                }
                {...register('email', {
                    required: true,
                    pattern: regexp.email
                })}
            />

            <UiButton
                className="mt-4 w-full"
                type="submit"
                variant="solid"
                size="xl"
                color="primary"
                loading={isLoading}
            >
                {t('Continue')}
            </UiButton>
        </UiForm>
    );
});

if (isDev) {
    ForgotPassword.displayName = 'ForgotPassword';
}

export default ForgotPassword;
