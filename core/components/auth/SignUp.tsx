import {
    <PERSON>,
    memo,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    use<PERSON>allback,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {signIn} from 'next-auth/react';
import {useForm} from 'react-hook-form';
import {isDev, jsonRequest, regexp} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {UiAlert, UiButton, UiCheckbox, UiForm} from '@core/components/ui';
import {EyeIcon, EyeSlashIcon} from '@core/icons/outline';

// eslint-disable-next-line react/display-name
const StaticContent: FC<{content?: string}> = ({content = ''}) => (
    <div className="w-full px-6 pb-6 pt-6 xl:pt-0">
        <div
            className="prose"
            dangerouslySetInnerHTML={{
                __html: content
            }}
        />
    </div>
);

type SignUpProps = {
    termsOfMembershipText?: string;
    clarificationText?: string;
    redirect?: string;
};

const SignUp: FC<SignUpProps> = memo(props => {
    const {termsOfMembershipText, clarificationText, redirect} = props;
    const {
        register,
        formState: {errors},
        handleSubmit,
        watch
    } = useForm();
    const router = useRouter();
    const t = useTrans();
    const {openSideBar} = useUI();
    const password: string = watch('password', '');
    const [isLoading, setIsLoading] = useState(false);
    const [isPasswordShown, setIsPasswordShown] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const inProgressRef = useRef(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;
            setIsLoading(true);
            setErrorMessage('');

            try {
                await jsonRequest({
                    url: '/api/auth/signup',
                    method: 'POST',
                    data: {
                        ...data
                    }
                });

                const result: any = await signIn('credentials', {
                    redirect: false,
                    email: data.email,
                    password: data.password
                });

                if (!!result?.error) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new Error(result.error);
                }

                if (typeof redirect === 'string') {
                    await router.push(
                        !!redirect && redirect !== '' ? redirect : '/'
                    );
                    return;
                }
            } catch (error: any) {
                if (error.code === 'already_exists') {
                    setErrorMessage(
                        t(
                            'The e-mail address you entered is in use by another customer. Please try again with a different e-mail address.'
                        )
                    );
                } else if (error.code === 'clarification_text_not_accepted') {
                    setErrorMessage(
                        t(
                            'The clarification text for the processing of personal data must be accepted.'
                        )
                    );
                } else {
                    setErrorMessage(error.message);
                }
            }

            inProgressRef.current = false;
            setIsLoading(false);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [router, redirect]
    );

    const onClarificationTextClick: MouseEventHandler<HTMLDivElement> =
        useCallback(
            e => {
                // @ts-ignore
                const id = e.target.id;

                if (id === 'clarificationText') {
                    e.preventDefault();

                    openSideBar(
                        t('Clarification Text'),
                        <StaticContent content={clarificationText} />,
                        'large'
                    );
                }
            },
            [clarificationText, t, openSideBar]
        );
    const onMembershipAgreementTextClick: MouseEventHandler<HTMLDivElement> =
        useCallback(
            e => {
                // @ts-ignore
                const id = e.target.id;

                if (id === 'termsOfMembershipText') {
                    e.preventDefault();

                    openSideBar(
                        t('Membership Agreement'),
                        <StaticContent content={termsOfMembershipText} />,
                        'large'
                    );
                }
            },
            [termsOfMembershipText, t, openSideBar]
        );

    return (
        <UiForm onSubmit={handleSubmit(onSubmit)}>
            {!!errorMessage && (
                <UiAlert className="mb-2" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <div className="flex space-x-4">
                <UiForm.Field
                    label={t('First name')}
                    size="sm"
                    error={
                        errors.firstName && errors.firstName.type === 'required'
                            ? t('First name is required')
                            : undefined
                    }
                    {...register('firstName', {required: true})}
                />

                <UiForm.Field
                    label={t('Last name')}
                    error={
                        errors.lastName && errors.lastName.type === 'required'
                            ? t('Last name is required')
                            : undefined
                    }
                    {...register('lastName', {required: true})}
                />
            </div>

            <UiForm.Field
                className="mt-3"
                label={t('Email address')}
                autoCorrect="off"
                autoCapitalize="none"
                error={
                    errors.email && errors.email.type === 'required'
                        ? t('Email address is required')
                        : errors.email && errors.email.type === 'pattern'
                        ? t('Email address is invalid')
                        : undefined
                }
                {...register('email', {
                    required: true,
                    pattern: regexp.email
                })}
            />

            <UiForm.Field
                className="mt-3"
                label={t('Password')}
                rightElement={
                    !!password &&
                    password.length > 0 && (
                        <div
                            className="cursor-pointer"
                            onClick={() => setIsPasswordShown(!isPasswordShown)}
                        >
                            {isPasswordShown && (
                                <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                            )}
                            {!isPasswordShown && (
                                <EyeIcon className="h-5 w-5 text-gray-400" />
                            )}
                        </div>
                    )
                }
                type={isPasswordShown ? 'text' : 'password'}
                error={
                    errors.password && errors.password.type === 'required'
                        ? t('Password is required')
                        : undefined
                }
                {...register('password', {required: true})}
            />

            <UiForm.Control className="mt-3">
                <UiCheckbox
                    className="mt-1 self-start"
                    {...register('isSubscribedToNewsletter', {required: false})}
                >
                    <div className="text-xs">
                        {t(
                            'I accept the processing of my personal data and the sending of electronic messages so that I can be informed about the campaigns.'
                        )}
                    </div>
                </UiCheckbox>
            </UiForm.Control>

            <UiForm.Control className="mt-3">
                <UiCheckbox
                    className="mt-1 self-start"
                    {...register('acceptedClarificationText', {
                        required: false
                    })}
                >
                    <div
                        className="text-xs"
                        onClick={onClarificationTextClick}
                        dangerouslySetInnerHTML={{
                            __html: t(
                                'I have read and understood the <span id="clarificationText" class="text-primary-600">clarification text</span> for the processing of my personal data.'
                            )
                        }}
                    ></div>
                </UiCheckbox>
            </UiForm.Control>

            <UiButton
                className="mt-3 w-full"
                type="submit"
                variant="solid"
                size="xl"
                color="primary"
                loading={isLoading}
            >
                {t('Sign Up')}
            </UiButton>

            <div
                className="mt-3 w-full text-center text-xs"
                onClick={onMembershipAgreementTextClick}
                dangerouslySetInnerHTML={{
                    __html: t(
                        'I accept the <span id="termsOfMembershipText" class="cursor-pointer text-primary-600">Terms of Membership</span> by clicking Sign Up.'
                    )
                }}
            />
        </UiForm>
    );
});

if (isDev) {
    SignUp.displayName = 'SignUp';
}

export default SignUp;
