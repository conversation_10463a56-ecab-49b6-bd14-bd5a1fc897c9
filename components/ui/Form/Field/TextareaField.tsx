import {forwardRef, ReactNode, useId} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiTextarea, UiTextareaProps} from '@core/components/ui';
import {default as UiFormControl} from '@core/components/ui/Form/FormControl';
import {default as UiFormErrorMessage} from '@core/components/ui/Form/FormErrorMessage';
import {default as UiFormLabel} from '@core/components/ui/Form/FormLabel';

type UiTextareaFieldProps = Omit<UiTextareaProps, 'placeholder'> & {
    name: string;
    label?: string;
    error?: string;
    disabled?: boolean;
    readOnly?: boolean;
    leftElement?: ReactNode;
    rightElement?: ReactNode;
    leftAddon?: ReactNode;
    rightAddon?: ReactNode;
};

const UiTextareaField = forwardRef<HTMLTextAreaElement, UiTextareaFieldProps>(
    (props, ref) => {
        const {
            name,
            label,
            error,
            leftElement,
            rightElement,
            leftAddon,
            rightAddon,
            className,
            children,
            ...rest
        } = props;
        const id = useId();

        return (
            <UiFormControl
                className={className}
                invalid={typeof error === 'string' && error.length > 0}
            >
                <UiTextarea
                    {...rest}
                    id={id}
                    name={name}
                    className={cls(
                        'peer h-40 px-4 pt-6 text-sm font-medium leading-4',
                        'placeholder-transparent peer-placeholder-shown:pt-0',
                        {
                            'pl-12': !!leftElement,
                            'pr-12': !!rightElement
                        }
                    )}
                    placeholder={label}
                    ref={ref}
                />

                <UiFormLabel
                    htmlFor={id}
                    className={cls(
                        'absolute top-1.5 select-none text-xs text-gray-500 transition-all',
                        'peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400',
                        'peer-placeholder-shown:top-3 peer-focus:top-1.5 peer-focus:text-gray-500',
                        'cursor-text peer-focus:text-xs',
                        {
                            'left-4': !leftElement,
                            'left-12': !!leftElement
                        }
                    )}
                >
                    {label}
                </UiFormLabel>

                {typeof error === 'string' && error.length > 0 && (
                    <UiFormErrorMessage>{error}</UiFormErrorMessage>
                )}
            </UiFormControl>
        );
    }
);

if (isDev) {
    UiTextareaField.displayName = 'UiTextareaField';
}

export default UiTextareaField;
