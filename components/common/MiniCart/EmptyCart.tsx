import {memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';

const EmptyCart = memo(() => {
    const t = useTrans();

    return (
        <div className="mt-[-30px] flex flex-1 flex-col items-start justify-start  px-6 py-2 ">
            <h2 className="pt-12 text-center text-sm ">
                {t('There are no items in your cart!')}
            </h2>
            <div className="mt-5 w-full border-b border-gray-300"></div>
        </div>
    );
});

if (isDev) {
    EmptyCart.displayName = 'EmptyCart';
}

export default EmptyCart;
