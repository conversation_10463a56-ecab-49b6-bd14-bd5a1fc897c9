import {FC, memo, useCallback, useEffect, useState} from 'react';
import {useRouter} from 'next/router';
import storeConfig from '~/store.config';
import {Cart, CartItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useCart, useTrans, useUI} from '@core/hooks';
import {UiImage} from '@core/components/ui';
import Price from '../Price';
import MobileQuantity from '../MobileQuantity';
type CartItemsProps = {
    cart: Cart;
};

const CartItems: FC<CartItemsProps> = memo(({cart}) => {
    const t = useTrans();
    const router = useRouter();
    const {closeSideBar} = useUI();

    const {removeItem, updateItem} = useCart();

    const [deletedCampaingsProducts, setDeletedCampaingsProducts] = useState<
        string | null
    >(null);

    useEffect(() => {
        const isDeleted = localStorage.getItem('deleteCampaingsProduct');
        setDeletedCampaingsProducts(isDeleted ? isDeleted : null);
    }, [deletedCampaingsProducts]);

    const handleRemoveItem = (item: CartItem) => {
        if (item.productId === deletedCampaingsProducts) {
            localStorage.setItem(
                'isDeleteCampaingsProduct',
                JSON.stringify(true)
            );
        }

        const previouslyItems = localStorage.getItem('previouslyItems');
        let previouslyItemsArray: {
            productId: string;
            productName: string;
            price: number;
            discountedPrice: number | undefined;
            quantity: number;
            productSlug: string;
            productImage: string;
            link: string;
            code?: string;
            productStockQuantity?: number;
        }[] = previouslyItems ? JSON.parse(previouslyItems) : [];

        const itemExists = previouslyItemsArray.some(
            (previoulyItem: {productId: string}) =>
                previoulyItem.productId === item.productId
        );

        if (!itemExists) {
            previouslyItemsArray.unshift({
                productId: item.productId,
                productName: item.productName,
                price: item.price,
                discountedPrice: item.discountedPrice,
                quantity: item.quantity,
                productSlug: item.productSlug,
                productImage: item.productImage,
                link: item.productLink,
                code: item.productCode,
                productStockQuantity: item.productStockQuantity
            });

            if (previouslyItemsArray.length > 10) {
                previouslyItemsArray.pop();
            }

            localStorage.setItem(
                'previouslyItems',
                JSON.stringify(previouslyItemsArray)
            );
        }

        removeItem(item);
    };

    const onDetail = useCallback(
        (item: CartItem) => {
            closeSideBar();
            router.push(item.productLink);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const onQuantityChange = useCallback(
        (item: CartItem, quantity: number) => updateItem({...item, quantity}),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return (
        <div className=" pt-3">
            <div className=" h-full w-full p-4 xl:px-5 xl:pt-0">
                <ul role="list" className="overflow-y-auto lg:pb-24">
                    {cart.items.map(item => {
                        const hasDiscount =
                            typeof item.discountedPrice === 'number' &&
                            item.discountedPrice > 0;
                        return (
                            <li
                                className="flex border-b-2 border-brand-buttongray pt-2"
                                key={item.productId}
                            >
                                <div
                                    className={cls(
                                        'h-24 flex-shrink-0 overflow-hidden ',
                                        {
                                            'w-18':
                                                storeConfig.catalog
                                                    .productImageShape ===
                                                'rectangle',
                                            'w-24':
                                                storeConfig.catalog
                                                    .productImageShape !==
                                                'rectangle'
                                        }
                                    )}
                                >
                                    <UiImage
                                        src={
                                            item.productImage
                                                ? item.productImage
                                                : '/no-image.png'
                                        }
                                        alt={item.productName}
                                        width={
                                            storeConfig.catalog
                                                .productImageShape ===
                                            'rectangle'
                                                ? 89.94
                                                : 89.94
                                        }
                                        height={89.94}
                                        fit="cover"
                                        position="center"
                                    />
                                </div>

                                <div className="ml-4 flex flex-1 flex-col overflow-hidden ">
                                    <div className=" ">
                                        <div className="flex  flex-col">
                                            <div className="h-auto w-[90%] text-sm xl:text-lg">
                                                <p
                                                    className="inline-block w-full cursor-pointer truncate font-dm-serif text-sm text-brand-clr xl:overflow-auto xl:whitespace-normal xl:text-left xl:text-base"
                                                    onClick={() =>
                                                        onDetail(item)
                                                    }
                                                >
                                                    {item.productName}
                                                </p>
                                            </div>
                                            <div className="mb-2 flex w-full items-center justify-between">
                                                <div className="flex items-start justify-start !text-left text-xs font-semibold text-secondary-100 md:text-lg xl:text-base">
                                                    <Price
                                                        className=" font-semibold text-base [&>span]:!flex-col [&>span]:!items-start [&>span]:!space-x-0 [&>span]:!font-bold [&>span]:!text-secondary-100"
                                                        price={
                                                            item.price *
                                                            item.quantity
                                                        }
                                                        discountedPrice={
                                                            typeof item.discountedPrice ===
                                                            'number'
                                                                ? item.discountedPrice *
                                                                  item.quantity
                                                                : undefined
                                                        }
                                                        decimal={0}
                                                    />
                                                </div>
                                                <MobileQuantity
                                                    className=" h-fit  border-none"
                                                    size="md"
                                                    quantity={item.quantity}
                                                    availableQuantity={
                                                        item.productStockQuantity
                                                    }
                                                    item={item}
                                                    onChange={quantity =>
                                                        onQuantityChange(
                                                            item,
                                                            quantity
                                                        )
                                                    }
                                                    handleRemoveItem={
                                                        handleRemoveItem
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        );
                    })}
                </ul>
            </div>
        </div>
    );
});

if (isDev) {
    CartItems.displayName = 'CartItems';
}

export default CartItems;
