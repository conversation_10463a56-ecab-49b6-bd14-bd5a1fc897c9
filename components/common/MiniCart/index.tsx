import {
    FC,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {cls, isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useCart, useMobile, useStore, useTrans, useUI} from '@core/hooks';
import CartActions from './CartActions';
import CartItems from './CartItems';
import EmptyCart from './EmptyCart';
import {UiDivider, UiSlider, UiTab, notification} from '@core/components/ui';
import {Autoplay, SliderInterface} from '@core/components/ui/Slider';
import {Campaign, CartItem} from '@core/types';
import {ConfettiIcon, LuPackage} from '@components/icons';
import {useSession} from 'next-auth/react';

import PreviouslyAdded from '@core/pages/store/Cart/PreviouslyAdded';
import LastViewed from '@core/pages/store/Cart/LastViewed';
import MyFavorites from '@core/pages/store/Cart/MyFavorites';
import CampaignProducts from '@components/pages/store/Cart/CampaignProducts';

const MiniCart: FC = memo(() => {
    const {
        cart,
        refreshCart,
        isLoading,
        addItem,
        updateItem: updateCartItem,
        cart: mainCart,
        campaignProducts,
        campaingsCart
    } = useCart();
    const swiperRef = useRef<SliderInterface>();
    const t = useTrans();

    const {currency, navigation} = useStore();
    const {isMobile} = useMobile();
    const {openSideBar} = useUI();

    const [campaignAmount, setCampaignAmount] = useState(0);
    const [progress, setProgress] = useState(0);

    const {data: session} = useSession();

    const [previouslyAddedItems, setPreviouslyAddedItems] = useState<
        CartItem[]
    >([]);
    const [lastViewedItems, setLastViewedItems] = useState<CartItem[]>([]);

    const [activeTab, setActiveTab] = useState(
        mainCart?.showCampaignProducts ? 'campaings' : 'lastViewed'
    );

    useEffect(() => {
        if (mainCart?.showCampaignProducts) {
            setActiveTab('campaings');
        } else {
            setActiveTab('lastViewed');
        }
    }, [mainCart?.showCampaignProducts]);

    useEffect(() => {
        campaingsCart();
    }, [campaingsCart]);

    const [isDeletedCampaingsProducts, setIsDeletedCampaingsProducts] =
        useState<boolean | null>(null);

    useEffect(() => {
        try {
            const isDeleted = JSON.parse(
                localStorage.getItem('isDeleteCampaingsProduct') as string
            );
            if (typeof isDeleted === 'boolean') {
                setIsDeletedCampaingsProducts(isDeleted);
            } else {
                setIsDeletedCampaingsProducts(false);
            }
        } catch (error) {
            setIsDeletedCampaingsProducts(null);
        }
    }, []);

    const installmentText = useMemo(() => {
        return navigation
            ?.filter(
                navigationItem =>
                    navigationItem.type === 'collection' &&
                    navigationItem.section === 'installment'
            )
            .map(navigationItem => ({
                title: navigationItem.name
            }))?.[0];
    }, [navigation]);

    const linkData = [
        {text: 'FAST SHIPPING'},
        installmentText?.title ? {text: installmentText?.title || ''} : {},
        {text: 'FAST SHIPPING'},
        installmentText?.title ? {text: installmentText?.title || ''} : {},
        {text: 'FAST SHIPPING'}
    ];

    useEffect(() => {
        const campaignMissingAmount =
            mainCart?.subTotal + mainCart?.taxTotal >
            mainCart?.minimumPurchaseAmountForFreeShipping
                ? mainCart?.minimumPurchaseAmountForFreeShipping -
                  mainCart.grandTotal
                : mainCart?.minimumPurchaseAmountForFreeShipping -
                  (mainCart.grandTotal - mainCart.deliveryTotal);

        setCampaignAmount(campaignMissingAmount);

        const progressValue =
            mainCart?.subTotal + mainCart?.taxTotal >
            mainCart?.minimumPurchaseAmountForFreeShipping
                ? (mainCart.grandTotal /
                      mainCart?.minimumPurchaseAmountForFreeShipping) *
                  100
                : ((mainCart.grandTotal - mainCart.deliveryTotal) /
                      mainCart?.minimumPurchaseAmountForFreeShipping) *
                  100;
        setProgress(progressValue > 100 ? 100 : progressValue);
    }, [mainCart, mainCart?.minimumPurchaseAmountForFreeShipping]);

    useEffect(() => {
        refreshCart();

        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'view_cart',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: cart.items
                    .map((item: CartItem) => item.price * item.quantity)
                    .reduce((a, b) => a + b, 0),
                items: cart.items.map(item => ({
                    item_id: item.productCode,
                    item_name: item.productName,
                    discount: item.discountedPrice
                        ? item.price - item.discountedPrice
                        : 0,
                    price: item.price,
                    item_brand: item.brandName,
                    item_category: item.productCategory,
                    quantity: item.quantity
                }))
            }
        });
        // ----------------------------------------
        // eslint-disable-next-line
    }, []);

    const isCampaignProduct = mainCart?.campaigns?.find(
        (campaing: Campaign) =>
            campaing.campaignType === campaignProducts[0]?.productId
    );

    const addToCart = useCallback(
        async (product: CartItem) => {
            const item: CartItem = {
                productId: product.productId,
                campaigns: product.campaigns,
                productSlug: product.productSlug,
                productImage: product.productImage,
                productName: product.productName,
                brandName: product.brandName,
                productCategory: product.productCategory,
                productStockQuantity: product.quantity ?? 0,
                productRating: product.productRating,
                productReviewCount: product.productReviewCount,
                productLink: `/${product.productSlug}`,
                isKitProduct: product.isKitProduct,
                price: product.salesPrice || 0,
                unitId: product.unitId,
                unitName: product.unitName,
                quantity: 1,
                weight: product.weight,
                width: product.width,
                height: product.height,
                depth: product.depth,
                volumetricWeight: 0,
                deliveryType: 'standard',
                deliveryOptionIds: product.deliveryOptionIds ?? [],
                deliveryPrice: 0,
                estimatedDeliveryDuration: product.estimatedDeliveryDuration,
                deliveryAtSpecifiedDate: product.deliveryAtSpecifiedDate,
                deliveryAtSpecifiedTime: product.deliveryAtSpecifiedTime,
                selected: true,
                removed: false
            };

            const inCartProduct = mainCart.items.find(
                cartItem => cartItem.productId === product.productId
            );

            let result = null;
            if (inCartProduct) {
                result = await updateCartItem({
                    ...item,
                    quantity: item.quantity + inCartProduct.quantity
                });
            } else {
                result = await addItem(item);
            }
        },
        [mainCart.items, addItem, updateCartItem]
    );

    useEffect(() => {
        if (mainCart.items.length === 0) {
            localStorage.removeItem('isDeleteCampaingsProduct');
        }
    }, [mainCart.items]);

    let isCampaingsControl = false;

    if (mainCart.discounts.find(discount => discount.type === 'buy-x-get-y')) {
        isCampaingsControl = true;
    }

    useEffect(() => {
        if (
            Array.isArray(campaignProducts) &&
            campaignProducts.length > 0 &&
            !isCampaignProduct &&
            mainCart.hasAutomaticRewardAddition &&
            !isDeletedCampaingsProducts &&
            isDeletedCampaingsProducts !== null &&
            !isCampaingsControl
        ) {
            const updatedItem = {
                ...campaignProducts?.[0],
                quantity: 1,
                price: campaignProducts?.[0].price
            };
            notification({
                title: t('Kampanyalı Ürün Sepete Eklendi!'),
                description: t(
                    'Kampanya Kapsamında Ürününüz Sepete Eklenmiştir.'
                ),
                status: 'success',
                options: {
                    duration: 4000
                }
            });
            addToCart(updatedItem);
            localStorage.setItem(
                'isDeleteCampaingsProduct',
                JSON.stringify(true)
            );
            localStorage.setItem(
                'deleteCampaingsProduct',
                JSON.stringify(updatedItem.productId)
            );
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isDeletedCampaingsProducts, campaignProducts]);

    return (
        <div className="relative flex h-full w-full flex-col">
            {isLoading && (
                <div className="absolute inset-0 z-50 h-full w-full bg-white bg-opacity-30"></div>
            )}
            <div className="w-full bg-brand-budget">
                <div
                    className={cls(
                        'border-b-2 border-secondary-100/20  text-center text-sm text-secondary-100',
                        {
                            'bg-green-100': campaignAmount < 0,
                            'bg-secondary-50': campaignAmount > 0
                        }
                    )}
                >
                    {mainCart?.minimumPurchaseAmountForFreeShipping &&
                        mainCart.minimumPurchaseAmountForFreeShipping > 0 && (
                            <>
                                <div
                                    className="flex h-1.5 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-neutral-700"
                                    role="progressbar"
                                    aria-valuenow={progress}
                                    aria-valuemin={0}
                                    aria-valuemax={100}
                                    aria-label={`Progress: ${progress}%`}
                                >
                                    <div
                                        className={cls(
                                            'flex w-[70%] flex-col justify-center overflow-hidden whitespace-nowrap rounded-full  text-center text-xs text-white',
                                            {
                                                'bg-green-600':
                                                    campaignAmount < 0,
                                                'bg-secondary-100':
                                                    campaignAmount > 0
                                            }
                                        )}
                                        style={{
                                            width: `${progress}%`
                                        }}
                                    ></div>
                                </div>

                                <div>
                                    {campaignAmount > 0 ? (
                                        <div className="py-2">
                                            <span> Sepetine</span>
                                            <strong>
                                                &nbsp;
                                                {campaignAmount.toFixed(1)} TL
                                                &nbsp;
                                            </strong>
                                            <span>
                                                daha ekle, ücretsiz kargo
                                                kampanyasından yararlan!
                                            </span>
                                        </div>
                                    ) : (
                                        <div className="flex items-center justify-center gap-2 py-2">
                                            <LuPackage className=" stroke-green-600" />
                                            <span className="text-green-600">
                                                Kargo Bedava
                                            </span>
                                            <ConfettiIcon className="h-4 w-4 stroke-green-600" />
                                        </div>
                                    )}
                                </div>
                            </>
                        )}
                </div>
                <UiSlider
                    className="cart-slider"
                    spaceBetween={10}
                    slidesPerView={2}
                    loop
                    modules={[Autoplay]}
                    autoplay={{
                        delay: 0
                    }}
                    direction="horizontal"
                    speed={7000}
                    onSwiper={swiper => {
                        swiperRef.current = swiper;
                    }}
                >
                    {linkData.map(
                        (link, index) =>
                            link.text && (
                                <UiSlider.Slide key={index}>
                                    <div className="flex h-12 items-center justify-center text-xs font-extrabold">
                                        <span className="flex items-center justify-center gap-x-10">
                                            <p className="whitespace-nowrap text-xs font-bold text-secondary-100 xl:text-lg">
                                                {t(
                                                    link.text ?? ''
                                                ).toUpperCase()}
                                            </p>
                                            <p className=" text-lg text-secondary-100 ">
                                                ◆
                                            </p>
                                        </span>
                                    </div>
                                </UiSlider.Slide>
                            )
                    )}
                </UiSlider>
            </div>

            {cart.items.length > 0 ? (
                <div className=" flex h-full flex-col justify-between">
                    <CartItems cart={cart} />
                    <div className="fixed bottom-0 z-[100] h-16 w-full bg-white  xl:bottom-4">
                        <CartActions cart={cart} />
                    </div>
                    {isMobile && (
                        <div className="flex lg:hidden">
                            <UiDivider />
                            <div className="mt-6  flex justify-start">
                                <UiTab.Group>
                                    <div className="grid w-full gap-6 px-2 pb-24">
                                        <div className=" w-full space-x-8 overflow-x-auto overflow-y-hidden bg-white ">
                                            <UiTab.List className=" flex justify-start  ">
                                                {mainCart?.showCampaignProducts && (
                                                    <UiTab
                                                        onClick={() =>
                                                            setActiveTab(
                                                                'campaings'
                                                            )
                                                        }
                                                        className={cls(
                                                            activeTab ===
                                                                'campaings'
                                                                ? '  bg-secondary-100 px-5 py-2 text-white '
                                                                : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                            'cursor-pointer whitespace-nowrap rounded-full px-5 py-2 text-sm font-medium  transition'
                                                        )}
                                                    >
                                                        {t('Campaign Products')}
                                                    </UiTab>
                                                )}

                                                <UiTab
                                                    onClick={() =>
                                                        setActiveTab(
                                                            'lastViewed'
                                                        )
                                                    }
                                                    className={cls(
                                                        activeTab ===
                                                            'lastViewed'
                                                            ? '  bg-secondary-100 px-5 py-2 text-white '
                                                            : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                        'cursor-pointer whitespace-nowrap rounded-full px-5 py-2 text-sm font-medium  transition'
                                                    )}
                                                >
                                                    {t('Last Viewed Products')}
                                                </UiTab>
                                                <UiTab
                                                    onClick={() =>
                                                        setActiveTab(
                                                            'previouslyAdded'
                                                        )
                                                    }
                                                    className={cls(
                                                        activeTab ===
                                                            'previouslyAdded'
                                                            ? '  bg-secondary-100 px-5 py-2 text-white '
                                                            : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                        'cursor-pointer whitespace-nowrap rounded-full px-5 py-2 text-sm font-medium  transition'
                                                    )}
                                                >
                                                    {t('Previously Added')}
                                                </UiTab>
                                                {session?.user && (
                                                    <UiTab
                                                        onClick={() =>
                                                            setActiveTab(
                                                                'myFavorites'
                                                            )
                                                        }
                                                        className={cls(
                                                            activeTab ===
                                                                'myFavorites'
                                                                ? '  bg-secondary-100 px-5 py-2 text-white '
                                                                : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                            'cursor-pointer whitespace-nowrap rounded-full px-5 py-2 text-sm font-medium  transition'
                                                        )}
                                                    >
                                                        {t('My Favorites')}
                                                    </UiTab>
                                                )}
                                            </UiTab.List>
                                        </div>
                                        <UiTab.Panels>
                                            {mainCart?.showCampaignProducts && (
                                                <UiTab.Panel>
                                                    <CampaignProducts
                                                        campaignProducts={
                                                            campaignProducts
                                                        }
                                                    />
                                                </UiTab.Panel>
                                            )}
                                            <UiTab.Panel>
                                                <LastViewed
                                                    lastViewedItems={
                                                        lastViewedItems
                                                    }
                                                    setLastViewedItems={
                                                        setLastViewedItems
                                                    }
                                                />
                                            </UiTab.Panel>
                                            <UiTab.Panel>
                                                <PreviouslyAdded
                                                    previouslyAddedItems={
                                                        previouslyAddedItems
                                                    }
                                                    setPreviouslyAddedItems={
                                                        setPreviouslyAddedItems
                                                    }
                                                />
                                            </UiTab.Panel>

                                            <UiTab.Panel>
                                                <MyFavorites />
                                            </UiTab.Panel>
                                        </UiTab.Panels>
                                    </div>
                                </UiTab.Group>
                            </div>
                        </div>
                    )}
                </div>
            ) : (
                <EmptyCart />
            )}
        </div>
    );
});

if (isDev) {
    MiniCart.displayName = 'MiniCart';
}

export default MiniCart;
