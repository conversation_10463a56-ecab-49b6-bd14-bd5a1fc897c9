import {
    FC,
    Fragment,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {useSession} from 'next-auth/react';
import {Cart, CartItem} from '@core/types';
import {cls, isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useMobile, useStore, useTrans, useUI} from '@core/hooks';
import {UiButton, UiTransition, notification} from '@core/components/ui';
import Price from '../Price';
import {Disclosure} from '@headlessui/react';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/outline';
import {LuPackage} from '@components/icons';
import CouponApplicationForm from '../CouponApplicationForm';

type CartActionsProps = {
    cart: Cart;
};

const CartActions: FC<CartActionsProps> = memo(({cart}) => {
    const t = useTrans();
    const router = useRouter();
    const {isMobile} = useMobile();

    const {closeSideBar} = useUI();
    const [isProceedToCheckOutInProgress, setIsProceedToCheckOutInProgress] =
        useState(false);
    const {currency} = useStore();

    const {data: session} = useSession();

    const isProceedToCheckOutEnabled = useMemo(
        () =>
            cart.items.length > 0 &&
            cart.items.filter(
                item => item.selected && !item.removed && item.quantity > 0
            ).length > 0 &&
            cart.subTotal > 0,
        [cart]
    );

    const onProceedToCheckout = useCallback(async () => {
        for (const item of cart.items) {
            if (item.productStockQuantity < item.quantity) {
                notification({
                    title: t('Error'),
                    description: t(
                        'Please remove out of stock items from your cart!'
                    ),
                    status: 'error'
                });

                return;
            }
        }

        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'begin_checkout',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: cart.items
                    .map((item: CartItem) => item.price * item.quantity)
                    .reduce((a, b) => a + b, 0),
                items: cart.items.map(item => ({
                    item_id: item.productCode,
                    item_name: item.productName,
                    discount: item.discountedPrice
                        ? item.price - item.discountedPrice
                        : 0,
                    price: item.price,
                    item_brand: item.brandName,
                    item_category: item.productCategory,
                    quantity: item.quantity
                }))
            }
        });
        // ----------------------------------------

        setIsProceedToCheckOutInProgress(true);

        if (!!session) {
            await router.push(`/checkout?t=${Date.now()}`);
        } else {
            await router.push('/auth?redirect=checkout');
        }

        closeSideBar();

        setIsProceedToCheckOutInProgress(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router, cart]);

    const onGoToCart = useCallback(() => {
        closeSideBar();
        router.push(`/cart?t=${Date.now()}`);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router]);

    const [campaignAmount, setCampaignAmount] = useState(0);

    useEffect(() => {
        const campaignMissingAmount =
            cart?.subTotal + cart?.taxTotal >
            cart?.minimumPurchaseAmountForFreeShipping
                ? cart?.minimumPurchaseAmountForFreeShipping - cart.grandTotal
                : cart?.minimumPurchaseAmountForFreeShipping -
                  (cart.grandTotal - cart.deliveryTotal);

        setCampaignAmount(campaignMissingAmount);
    }, [cart, cart?.minimumPurchaseAmountForFreeShipping]);

    return (
        <div
            className={cls(
                'absolute  flow-root w-full space-y-3 lg:bottom-0  ',
                {
                    'bottom-3': campaignAmount < 0,
                    'bottom-1': campaignAmount > 0
                }
            )}
        >
            <div className="flex h-10 w-full flex-row items-center justify-between gap-x-2 lg:h-6">
                <div className="relative flex w-full flex-col-reverse bg-white xl:px-6">
                    <Disclosure>
                        {({open, close}) => (
                            <>
                                <div className=" !z-[999] flex w-full items-center justify-center gap-0 border-t bg-white px-4 lg:gap-12 lg:border-t-0 lg:px-0">
                                    <div className="z-[99] h-20  w-48 -space-y-0.5  lg:space-y-2">
                                        <Disclosure.Button className="flex items-center justify-center gap-2 rounded-md pr-3   pt-2   ">
                                            <div className="flex flex-col items-start -space-y-1 text-sm lg:flex-row  lg:items-center lg:space-y-0">
                                                <div className=" text-xs font-semibold text-gray-600 lg:mr-3 lg:text-xl ">
                                                    {t('Total')}:
                                                </div>
                                                <Price
                                                    className="-pt-2 text-lg font-bold text-brand-black  [&>span]:text-base lg:[&>span]:text-xl"
                                                    price={cart.grandTotal}
                                                    decimal={0}
                                                />
                                            </div>
                                            {open ? (
                                                <ChevronDownIcon className=" h-3  w-3 lg:h-4 lg:w-4" />
                                            ) : (
                                                <ChevronUpIcon className="h-3 w-3 lg:h-4 lg:w-4 " />
                                            )}
                                        </Disclosure.Button>
                                        <div>
                                            {campaignAmount > 0 ? null : (
                                                <div className="flex items-center justify-start gap-2 ">
                                                    <LuPackage className="hidden h-3 w-3 stroke-green-500 lg:flex" />
                                                    <span className="text-[10px] font-bold text-green-500 lg:text-xs">
                                                        Kargo Bedava
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    <div className="z-[999] w-full pb-2">
                                        {isMobile ? (
                                            <UiButton
                                                // onClick={onGoToCart}
                                                disabled={
                                                    !isProceedToCheckOutEnabled
                                                }
                                                loading={
                                                    isProceedToCheckOutInProgress
                                                }
                                                onClick={onProceedToCheckout}
                                                className="flex h-10 !w-full space-x-2 rounded-md !border-none bg-secondary-100 text-white transition-all duration-300 ease-in-out hover:bg-secondary-100/90 hover:text-white focus:bg-secondary-100 lg:h-12 xl:w-1/2"
                                            >
                                                <p className="pb-0.5 font-bold md:text-xs xl:text-sm">
                                                    {t('SEPETİ ONAYLA')}
                                                </p>
                                            </UiButton>
                                        ) : (
                                            <UiButton
                                                onClick={onGoToCart}
                                                disabled={
                                                    !isProceedToCheckOutEnabled
                                                }
                                                loading={
                                                    isProceedToCheckOutInProgress
                                                }
                                                // onClick={onProceedToCheckout}
                                                className="flex h-10 !w-full space-x-2 rounded-md !border-none bg-secondary-100 text-white transition-all duration-300 ease-in-out hover:bg-secondary-100/90 hover:text-white focus:bg-secondary-100 lg:h-12 xl:w-1/2"
                                            >
                                                <p className="pb-0.5 font-bold md:text-xs xl:text-sm">
                                                    {t('Sepete Git')}
                                                </p>
                                            </UiButton>
                                        )}
                                    </div>
                                </div>
                                {isMobile && (
                                    <UiTransition.Child
                                        as={Fragment}
                                        enter="transition ease-in-out duration-300"
                                        enterFrom="opacity-0"
                                        enterTo="opacity-100"
                                        leave="transition ease-in-out duration-300"
                                        leaveFrom="opacity-100"
                                        leaveTo="opacity-0"
                                    >
                                        <div
                                            onClick={() => close()}
                                            className="fixed inset-0 bg-gray-900 bg-opacity-40 transition-opacity lg:hidden"
                                        />
                                    </UiTransition.Child>
                                )}
                                <Disclosure.Panel
                                    className={`absolute left-0 right-0  z-[999] transform rounded-md rounded-b-none border bg-white  px-4 py-4 transition-all lg:px-0   lg:pr-2 xl:shadow-[0px_-4px_8px_0px_#edf2f7] ${
                                        open
                                            ? 'translate-y-0'
                                            : 'translate-y-full'
                                    }`}
                                    style={{bottom: '100%'}}
                                >
                                    <dl className="space-y-4  text-sm font-medium xl:space-y-6 xl:px-6">
                                        <div className="flex items-center justify-between  pt-2">
                                            <dt className="text-gray-600">
                                                {t('Total Product')}
                                            </dt>
                                            <dd>
                                                <Price
                                                    className="font-semibold"
                                                    price={
                                                        cart.subTotal +
                                                        cart.taxTotal
                                                    }
                                                    decimal={0}
                                                />
                                            </dd>
                                        </div>

                                        <div className="flex items-center justify-between">
                                            {cart.step !== 'information' ? (
                                                <dt className="text-gray-600">
                                                    {t('Delivery amount')}
                                                </dt>
                                            ) : (
                                                <dt className="text-gray-600">
                                                    {t('Cargo Total')}
                                                </dt>
                                            )}
                                            <dd>
                                                <Price
                                                    className="font-semibold"
                                                    price={cart.deliveryTotal}
                                                    decimal={0}
                                                />
                                            </dd>
                                        </div>

                                        {cart.cashOnDeliveryServiceFee > 0 && (
                                            <div className="flex items-center justify-between">
                                                <dt className="text-gray-600">
                                                    {t(
                                                        'Cash on delivery service fee'
                                                    )}
                                                </dt>

                                                <dd>
                                                    <Price
                                                        price={
                                                            cart.cashOnDeliveryServiceFee
                                                        }
                                                        decimal={0}
                                                    />
                                                </dd>
                                            </div>
                                        )}

                                        {cart.dueDifference > 0 && (
                                            <div className="flex items-center justify-between">
                                                <dt className="text-gray-600">
                                                    {t('Due difference amount')}
                                                </dt>

                                                <dd>
                                                    <Price
                                                        price={
                                                            cart.dueDifference
                                                        }
                                                        decimal={0}
                                                    />
                                                </dd>
                                            </div>
                                        )}

                                        {Array.isArray(cart.discounts) &&
                                            cart.discounts.length > 0 &&
                                            cart.discounts.map(discount => (
                                                <div
                                                    key={discount.id}
                                                    className="flex items-center justify-between gap-4"
                                                >
                                                    <dt className="flex items-center gap-2 rounded-md bg-brand-budget px-3 py-1 text-sm text-secondary-100">
                                                        <svg
                                                            className="h-4 w-4"
                                                            viewBox="0 0 18 18"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <g
                                                                mask="url(#a)"
                                                                className="fill-secondary-100"
                                                            >
                                                                <path
                                                                    d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                                                    fillOpacity=".55"
                                                                />
                                                                <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                                            </g>
                                                        </svg>
                                                        <p className="w-fit">
                                                            {
                                                                discount.description
                                                            }
                                                        </p>
                                                    </dt>

                                                    <dd>
                                                        <Price
                                                            className="font-semibold text-primary-600"
                                                            price={
                                                                -discount.amount
                                                            }
                                                            decimal={0}
                                                        />
                                                    </dd>
                                                </div>
                                            ))}

                                        {typeof cart.discountTotalIncludingProductDiscounts ===
                                            'number' &&
                                            cart.discountTotalIncludingProductDiscounts >
                                                0 && (
                                                <div className="flex items-center justify-between gap-4">
                                                    <dt className="flex items-center gap-2 rounded-md bg-brand-budget px-3 py-1 text-sm text-secondary-100">
                                                        <svg
                                                            className="h-4 w-4"
                                                            viewBox="0 0 18 18"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <g
                                                                mask="url(#a)"
                                                                className="fill-secondary-100"
                                                            >
                                                                <path
                                                                    d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                                                    fillOpacity=".55"
                                                                />
                                                                <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                                            </g>
                                                        </svg>
                                                        <p className="text-secondary-100">
                                                            {t(
                                                                'Total Discount'
                                                            )}
                                                        </p>
                                                    </dt>
                                                    <dd>
                                                        <Price
                                                            className="font-semibold text-primary-600"
                                                            price={
                                                                -cart.discountTotalIncludingProductDiscounts
                                                            }
                                                            decimal={0}
                                                        />
                                                    </dd>
                                                </div>
                                            )}

                                        <div className="my-2 lg:hidden">
                                            <CouponApplicationForm
                                                disabled={
                                                    !isProceedToCheckOutEnabled
                                                }
                                            />
                                        </div>
                                    </dl>
                                </Disclosure.Panel>
                            </>
                        )}
                    </Disclosure>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    CartActions.displayName = 'CartActions';
}

export default CartActions;
