import {FC, memo} from 'react';
import {ProductListItem} from '@core/types';
import {isDev} from '@core/helpers';
import ProductCardConsumer from './ProductCardConsumer';
import {ProductCardProvider} from '@core/components/common/ProductCard/context';

type ProductCardProps = {
    product: ProductListItem;
    preloadImage?: boolean;
    show?: boolean;
    isFavoriteShown?: boolean;
    isUnDiscountedPriceShown?: boolean;
    isFake?: boolean;
    hasColorPicker?: boolean;
    hasQuickLook?: boolean;
    hasSellingOptions?: boolean;
    hasAddToCart?: boolean;
    hasRating?: boolean;
    onRemove?: (product: ProductListItem) => void | Promise<void>;
    className?: string;
};

const ProductCard: FC<ProductCardProps> = memo(
    ({
        product,
        preloadImage = false,
        show = true,
        isUnDiscountedPriceShown = true,
        isFake = false,
        isFavoriteShown = true,
        hasColorPicker = false,
        hasQuickLook = false,
        hasSellingOptions = false,
        hasAddToCart = true,
        hasRating = false,
        onRemove,
        className
    }) => {
        return (
            <ProductCardProvider
                product={product}
                isFake={isFake}
                isFavoriteShown={isFavoriteShown}
                isUnDiscountedPriceShown={isUnDiscountedPriceShown}
                preloadImage={preloadImage}
                hasColorPicker={hasColorPicker}
                hasQuickLook={hasQuickLook}
                hasAddToCart={hasAddToCart}
                hasSellingOptions={hasSellingOptions}
                hasRating={hasRating}
                onRemove={onRemove}
            >
                <ProductCardConsumer className={className} show={show} />
            </ProductCardProvider>
        );
    }
);

if (isDev) {
    ProductCard.displayName = 'ProductCard';
}

export default ProductCard;
