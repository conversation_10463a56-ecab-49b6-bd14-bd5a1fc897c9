import {useMemo} from 'react';
import {UiImage} from '@core/components/ui';
import {useProductCard} from '@core/components/common/ProductCard/context';
import {cls} from '@core/helpers';

const ImagePartial = ({show}: {show?: boolean}) => {
    const {
        isFake,
        product,
        preloadImage,
        isImageLoading,
        setIsImageLoading,
        hasColorPicker,
        selectedVariant
    } = useProductCard();

    const image = useMemo(() => {
        if (product.isAdultProduct) {
            return '/adult-image.png';
        } else if (isFake) {
            return '';
        } else if (
            hasColorPicker &&
            selectedVariant &&
            Array.isArray(selectedVariant.images) &&
            selectedVariant.images.length > 0
        ) {
            return `${selectedVariant.images[0]}?w=480&q=60`;
        } else if (Array.isArray(product.images) && product.images.length > 0) {
            return `${product.images[0]}?w=480&q=60`;
        } else {
            return '/no-image.png';
        }
    }, [product, isFake, selectedVariant, hasColorPicker]);

    return (
        <div className="aspect-h-1 aspect-w-1">
            {(isImageLoading || isFake) && (
                <div className="absolute inset-0 z-[7] h-full w-full rounded-t-xl bg-white">
                    <div className="skeleton-card h-full w-full rounded-none" />
                </div>
            )}

            <div className="overflow-hidden">
                <div className="relative h-full w-full transition">
                    {!!image && (
                        <UiImage
                            className="p-1 xl:p-3"
                            src={image}
                            alt={product.name}
                            fit="cover"
                            position="center"
                            fill
                            priority={preloadImage}
                            onLoadingComplete={() => setIsImageLoading(false)}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default ImagePartial;
