import dynamic from 'next/dynamic';
import {cls, trim} from '@core/helpers';
import ImagePartial from './ImagePartial';
import NamePartial from './NamePartial';
import {useProductCard} from '@core/components/common/ProductCard/context';
import {UiLink} from '@core/components/ui';
import {useElementSize, useTrans} from '@core/hooks';
import PricePartial from './PricePartial';
import AddToCartAction from './AddToCartAction';
import {useRouter} from 'next/router';
import ProductSellingOptions from './ProductSellingOptions';

const FavoriteAction = dynamic(() => import('./FavoriteAction'), {ssr: false});
const QuickLook = dynamic(() => import('../QuickLook'), {ssr: false});

const ProductCardConsumer = ({
    className,
    show
}: {
    className?: string;
    show?: boolean;
}) => {
    const t = useTrans();

    const {product}: any = useProductCard();
    const {ref: containerRef, width: containerWidth} = useElementSize();
    const router = useRouter();

    return (
        <div
            ref={containerRef}
            data-product=""
            data-id={product.code}
            data-name={product.name}
            data-price={product.salesPrice}
            data-brand={product.brandName}
            data-category={product.categoryName}
            className={cls(
                'hover:shadow-custom group relative mb-4 flex	flex-col transition duration-300 hover:z-[8] hover:rounded-t-xl lg:mb-0',
                className
            )}
        >
            <div className="absolute right-1.5 top-1.5 isolate z-[7] flex flex-col gap-1.5 xl:right-4 xl:top-4">
                {!product.isAdultProduct && <FavoriteAction />}
            </div>
            <UiLink
                href={
                    product.link
                        ? product.link
                        : `/${trim(trim(product.slug, '/'))}`
                }
            >
                <ImagePartial show />
            </UiLink>

            <div
                className={cls(
                    'group flex flex-1 flex-col items-center justify-between',
                    {
                        'pb-8 pt-2': !show,
                        'pb-0 pt-0': show
                    }
                )}
            >
                <NamePartial />
                {/* <div className="ml-1 mt-1 flex items-center space-x-2">
                    <div className=" flex cursor-pointer items-center text-xs font-semibold text-gray-500 lg:text-sm">
                        {product.rating === 5 ? '5.0' : product.rating}
                    </div>

                    <div className="hidden items-center lg:flex">
                        <UiRating
                            size="sm"
                            initialRating={product.rating}
                            readonly
                        />
                    </div>
                    <div className="flex items-center lg:hidden">
                        <UiRating
                            size="xs"
                            initialRating={product.rating}
                            readonly
                        />
                    </div>

                    <button className="flex cursor-pointer items-center text-xs text-gray-500 transition duration-100 hover:text-primary-600 hover:underline">
                        {t('({reviewCount})', {
                            reviewCount: product.reviewCount
                        })}
                    </button>
                </div> */}

                {product && product.salesPrice ? (
                    <PricePartial />
                ) : (
                    <div className="h-10"></div>
                )}
                {/* <ProductSellingOptions product={product} /> */}
                {show && (
                    <div
                        className="absolute left-0 right-0 top-full z-[1] hidden h-16 items-center justify-center space-x-1 overflow-hidden !rounded-b-lg bg-white px-4 pb-4 shadow-2xl transition md:group-hover:flex"
                        style={{width: `${containerWidth}px`}}
                    >
                        <div className="flex h-full w-full flex-row items-center justify-center gap-2 pt-2">
                            {router.asPath !==
                                '/account/my-collections/is-notify-customer' && (
                                <QuickLook productSlug={product.slug} />
                            )}

                            {product.quantity > 0 && product ? (
                                <AddToCartAction product={product} />
                            ) : product.quantity < 1 && product ? (
                                <UiLink
                                    className="flex h-12 w-1/2 items-center justify-center space-x-2 rounded-lg !border-none bg-secondary-100 font-normal  text-white transition-all duration-300 ease-in-out hover:bg-black hover:text-white"
                                    href={
                                        product.link
                                            ? product.link
                                            : `/${trim(
                                                  trim(product.slug, '/')
                                              )}`
                                    }
                                >
                                    <p className="pb-0.5 md:text-xs xl:text-xs">
                                        {t('In Stock Soon').toUpperCase()}
                                    </p>
                                </UiLink>
                            ) : (
                                <UiLink
                                    className="flex h-12 w-1/2 items-center justify-center space-x-2 rounded-lg !border-none bg-secondary-100 font-normal  text-white transition-all duration-300 ease-in-out hover:bg-black hover:text-white"
                                    href={
                                        product.link
                                            ? product.link
                                            : `/${trim(
                                                  trim(product.slug, '/')
                                              )}`
                                    }
                                >
                                    <p className="pb-0.5 md:text-xs xl:text-xs">
                                        {t('GO TO PRODUCT')}
                                    </p>
                                </UiLink>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ProductCardConsumer;
