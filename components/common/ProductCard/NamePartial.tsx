import {trim} from '@core/helpers';
import {UiLink} from '@core/components/ui';
import {useProductCard} from '@core/components/common/ProductCard/context';

const NamePartial = () => {
    const {product} = useProductCard();

    return (
        <h3 className=" flex items-start justify-center px-2 text-center font-hurme text-xs text-brand-black lg:px-8 lg:leading-5 lg:tracking-[0.5px] xl:text-[15px]">
            <UiLink
                href={
                    product.link
                        ? product.link
                        : `/${trim(trim(product.slug, '/'))}`
                }
            >
                {product.name}
            </UiLink>
        </h3>
    );
};

export default NamePartial;
