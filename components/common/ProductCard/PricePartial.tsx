import {useStore} from '@core/hooks';
import {cls} from '@core/helpers';
import {useProductCard} from '@core/components/common/ProductCard/context';
import Price from '../Price';

const PricePartial = () => {
    const {currency} = useStore();
    const {product, isUnDiscountedPriceShown} = useProductCard();

    return (
        <div className="my-1.5 xl:mt-2">
            <div className="flex items-center justify-center gap-2 pr-2 text-center font-hurme text-sm font-semibold text-secondary-100 xl:text-xl">
                {product.hasDiscount && product.discount > 0 && (
                    <p className=" block rounded-md border-2 border-discount px-1 py-0.5 text-xs text-discount lg:text-sm">
                        %{Math.round(product.discount)}
                    </p>
                )}
                {!!product.salesPrice ? (
                    isUnDiscountedPriceShown ? (
                        <Price
                            price={
                                product.hasDiscount
                                    ? product.unDiscountedSalesPrice
                                    : product.salesPrice
                            }
                            discountedPrice={
                                product.hasDiscount ? product.salesPrice : null
                            }
                            dontWrapDiscountedPrice={false}
                            className={cls(
                                'text-lg text-secondary-100  lg:text-xl [&>span]:!flex-col [&>span]:!space-x-1.5',
                                {
                                    'xl:[&>span]:flex-col':
                                        product.salesPrice?.toFixed().length > 3
                                }
                            )}
                            decimal={0}
                        />
                    ) : (
                        <Price
                            dontWrapDiscountedPrice={false}
                            price={product.salesPrice}
                            decimal={0}
                        />
                    )
                ) : (
                    `--- ${currency.name}`
                )}
            </div>
        </div>
    );
};

export default PricePartial;
