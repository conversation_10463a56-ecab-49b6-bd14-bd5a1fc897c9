import {useMobile, useTrans} from '@core/hooks';
import {UiImage} from '@core/components/ui';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { useProductCard } from '@core/components/common/ProductCard/context';

const ColorVariantPicker = () => {
    const {product, setColorVariantPicker, colorVariantPicker} =
        useProductCard();

    const t = useTrans();

    const {isMobile} = useMobile();

    return (
        <>
            {isMobile ? <Mobile /> : <Desktop />}

            {colorVariantPicker !== 'desktop' &&
                typeof product.colorVariantCount === 'number' && (
                    <div
                        onMouseEnter={() => {
                            if (isMobile) return;
                            setColorVariantPicker('desktop');
                        }}
                        onClick={() => {
                            if (!isMobile) return;
                            setColorVariantPicker('mobile');
                        }}
                        className="absolute bottom-1 right-1 z-10 inline-flex items-center gap-1.5 rounded-md border bg-white p-1 text-xs text-muted"
                    >
                        <UiImage
                            src={require('@core/assets/images/variant-color.png')}
                            alt=""
                            width={14}
                            height={14}
                        />
                        <div>
                            {t('{count} Colors', {
                                count: product.colorVariantCount
                            })}
                        </div>
                    </div>
                )}
        </>
    );
};

export default ColorVariantPicker;
