import {Play} from '@components/icons';
import {useTrans} from '@core/hooks';

import {Product} from '@core/types';

const ProductSellingOptions = ({product}: {product: Product}) => {
    const t = useTrans();
    const videoRegex = /<iframe.*?src="(.*?)".*?<\/iframe>/;
    const videoMatch = (product.description || '').match(videoRegex);
    const videoUrl = videoMatch ? videoMatch[1] : null;

    return (
        <>
            <div className="absolute left-3 top-4 z-[2] flex select-none  flex-col gap-1 text-[8px] lg:left-6 lg:top-6">
                {videoUrl && (
                    <div className="flex w-16 items-center justify-center gap-1 rounded  border border-primary-600 bg-primary-600 px-1 text-center leading-[12px] text-white">
                        <div className="h-4 w-4 ">
                            <Play className="h-4 w-4 rounded-full border bg-white p-1 " />
                        </div>
                        <span>{t('PRODUCT WITH VIDEO')}</span>
                    </div>
                )}
            </div>
        </>
    );
};

export default ProductSellingOptions;
