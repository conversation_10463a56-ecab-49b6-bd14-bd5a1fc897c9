import {memo, useEffect, useState} from 'react';
import {useRouter} from 'next/router';
import {CartItem, Product, ProductListItem} from '@core/types';
import {
    isDev,
    jsonRequest,
    pushIntoGTMDataLayer,
    slugifyProduct
} from '@core/helpers';
import {useCart, useMobile, useStore, useTrans} from '@core/hooks';
import {UiButton} from '@core/components/ui';
import ProductCartSummary from '../ProductCartSummary';
import {notification} from '@core/components/ui';

type AddToCartActionProps = {
    product: ProductListItem;
};

const AddToCartAction = memo(({product}: AddToCartActionProps) => {
    const [productSlug, setProductSlug] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const {addItem, cart, updateItem} = useCart();
    const {locale, currency} = useStore();
    const router = useRouter();
    const {isMobile} = useMobile();
    const t = useTrans();

    useEffect(() => {
        if (!productSlug) return;

        const {processedSlug} = slugifyProduct(productSlug);

        pushIntoGTMDataLayer({
            event: 'add_to_cart',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: product.salesPrice - product.discount,
                items: [
                    {
                        item_id: product.code,
                        item_name: product.name,
                        discount: product.discount ? product.discount : 0,
                        item_brand: product.brandName,
                        item_category: product.categoryName,
                        price: product.salesPrice,
                        quantity: 1
                    }
                ]
            }
        });

        (async () => {
            setIsLoading(true);
            try {
                const result = await jsonRequest({
                    url: '/api/catalog/product',
                    method: 'POST',
                    data: {
                        slug: processedSlug
                    }
                });

                const product = result.product as Product;

                const cartItem: CartItem = {
                    productId: product.productId,
                    productSlug: product.slug,
                    productImage: (product.images as string[])[0],
                    productName: product.name,
                    brandName: product.brandName,
                    productCategory: product.categoryName,
                    productStockQuantity: product.quantity,
                    productRating: product.rating,
                    productReviewCount: product.reviewCount,
                    productLink: `/${product.slug}`,
                    price: product.salesPrice,
                    unitId: product.unitId,
                    unitName: product.unitName,
                    quantity: 1,
                    weight: product.weight,
                    width: product.width,
                    height: product.height,
                    depth: product.depth,
                    volumetricWeight: 0,
                    deliveryType: 'standard',
                    deliveryOptionIds: product.deliveryOptionIds ?? [],
                    deliveryPrice: 0,
                    estimatedDeliveryDuration:
                        product.estimatedDeliveryDuration,
                    deliveryAtSpecifiedDate: product.deliveryAtSpecifiedDate,
                    deliveryAtSpecifiedTime: product.deliveryAtSpecifiedTime,
                    selected: true,
                    removed: false
                };

                let availableQuantity = product.quantity;
                for (const cartItem of cart.items ?? []) {
                    if (cartItem.productId === product.productId) {
                        availableQuantity -= cartItem.quantity;
                    }
                }

                if (availableQuantity < 1) {
                    notification({
                        title: t('Error'),
                        description: t('Out Of Stock'),
                        status: 'error'
                    });
                    return;
                }

                const inCartProduct = cart.items.find(
                    cartItem => cartItem.productId === product.productId
                );

                let cartResult;
                if (inCartProduct) {
                    cartResult = await updateItem({
                        ...cartItem,
                        quantity: cartItem.quantity + inCartProduct.quantity
                    });
                } else {
                    cartResult = await addItem(cartItem);
                }

                if (cartResult) {
                    notification({
                        title: t('Added to Cart'),
                        description: t('Product has been added to your cart.'),
                        status: 'success',
                        detailRenderer: closeNotification => (
                            <ProductCartSummary
                                locale={locale}
                                currency={currency}
                                item={cartItem}
                                onDetail={() => {
                                    closeNotification();
                                    if (isMobile) {
                                        router.push(
                                            `/mobile/my-cart?t=${Date.now()}`
                                        );
                                    } else {
                                        router.push(`/cart?t=${Date.now()}`);
                                    }
                                }}
                            />
                        )
                    });
                }
            } catch (err) {
                notification({
                    title: t('Error'),
                    description: t(
                        'An error occurred while adding the product to the cart!'
                    ),
                    status: 'error'
                });
            } finally {
                setIsLoading(false);
                setProductSlug('');
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [productSlug]);

    return (
        <UiButton
            onClick={() => setProductSlug(product.slug)}
            className="flex h-12 w-1/2 items-center justify-center space-x-2 rounded-lg !border-none bg-secondary-100 text-xs uppercase  text-white transition-all duration-300 ease-in-out hover:bg-black hover:text-white"
            loading={isLoading}
            disabled={isLoading}
        >
            {t('Add To Cart')}
        </UiButton>
    );
});

if (isDev) {
    AddToCartAction.displayName = 'AddToCartAction';
}

export default AddToCartAction;
