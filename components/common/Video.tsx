import {UiDialog, UiTransition} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useStore} from '@core/hooks';
import {XIcon} from '@core/icons/outline';

import React, {Fragment} from 'react';

const Video = ({
    children,
    isShown,
    setIsShown
}: {
    children: React.ReactNode;
    isShown: boolean;
    setIsShown: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
    const closePopup = () => {
        setIsShown(false);
    };

    return (
        <UiTransition.Root show={isShown} as={Fragment}>
            <UiDialog
                as="div"
                className="fixed inset-0 top-1/4 z-[9999] w-full overflow-y-auto px-8 lg:top-1 lg:px-0"
                onClose={closePopup}
            >
                <div className="flex text-center sm:block sm:px-6 xl:px-8">
                    <UiTransition.Child
                        as={Fragment}
                        enter="transition duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="transition duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <UiDialog.Overlay
                            onClick={closePopup}
                            className="fixed inset-0 bg-gray-900 bg-opacity-20"
                        />
                    </UiTransition.Child>

                    <span
                        className="hidden sm:inline-block sm:h-screen sm:align-middle"
                        aria-hidden="true"
                    >
                        &#8203;
                    </span>

                    <UiTransition.Child
                        as={Fragment}
                        enter="transition duration-300 transform"
                        enterFrom="opacity-0 scale-105"
                        enterTo="opacity-100 scale-100"
                        leave="transition duration-200 transform"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-105"
                    >
                        <div
                            className={cls(
                                'relative flex w-full transform text-left text-base transition sm:my-8 sm:inline-block sm:align-middle lg:w-1/3'
                            )}
                        >
                            <div className="relative flex w-full flex-col items-stretch overflow-hidden bg-white">
                                <button
                                    type="button"
                                    className="absolute right-0 m-3 flex h-8 w-8 items-center justify-center rounded-full bg-white text-gray-400 transition duration-75 ease-out hover:bg-gray-200 hover:text-gray-500"
                                    onClick={closePopup}
                                    aria-label="Close"
                                >
                                    <XIcon className="h-5 w-5" />
                                </button>

                                <div className="w-full cursor-pointer">
                                    {children}
                                </div>
                            </div>
                        </div>
                    </UiTransition.Child>
                </div>
            </UiDialog>
        </UiTransition.Root>
    );
};

export default Video;
