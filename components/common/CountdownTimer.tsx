import {FC, memo, useEffect, useState} from 'react';
import {isDev} from '@core/helpers';
import {ClockIcon} from '@core/icons/outline';
import styles from './CountdownTimer.module.css';

interface CountdownTimerProps {
    targetDate: Date | string;
    title?: string;
    className?: string;
}

const CountdownTimer: FC<CountdownTimerProps> = memo(
    ({targetDate, title, className = ''}) => {
        const [timeLeft, setTimeLeft] = useState<{
            days: number;
            hours: number;
            minutes: number;
            seconds: number;
        }>({
            days: 0,
            hours: 0,
            minutes: 0,
            seconds: 0
        });

        const [isExpired, setIsExpired] = useState(false);

        useEffect(() => {
            const calculateTimeLeft = () => {
                const now = new Date();

                let targetTime;
                if (typeof targetDate === 'string') {
                    const parts = targetDate.split(' ');
                    const dateParts = parts[0].split('-');
                    const timeParts = parts[1].split(':');

                    const day = parseInt(dateParts[0], 10);
                    const month = parseInt(dateParts[1], 10) - 1;
                    const year = parseInt(dateParts[2], 10);
                    const hour = parseInt(timeParts[0], 10);
                    const minute = parseInt(timeParts[1], 10);

                    targetTime = new Date(
                        year,
                        month,
                        day,
                        hour,
                        minute,
                        0
                    ).getTime();
                } else {
                    targetTime = targetDate.getTime();
                }

                const difference = targetTime - now.getTime();

                if (difference <= 0) {
                    setIsExpired(true);
                    return;
                }

                const days = Math.floor(difference / (1000 * 60 * 60 * 24));

                const hours = Math.floor(
                    (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
                );

                const minutes = Math.floor(
                    (difference % (1000 * 60 * 60)) / (1000 * 60)
                );

                const seconds = Math.floor((difference % (1000 * 60)) / 1000);

                setTimeLeft({
                    days,
                    hours,
                    minutes,
                    seconds
                });
            };

            calculateTimeLeft();
            const timer = setInterval(calculateTimeLeft, 1000);

            return () => clearInterval(timer);
        }, [targetDate]);

        if (isExpired) {
            return null;
        }

        return (
            <div
                className={`${styles.countdownContainer} flex flex-col border border-rose-200 bg-gradient-to-r from-red-600 to-rose-500 p-1 text-white shadow-lg sm:p-2 xl:rounded-lg ${className}`}
                style={{
                    boxShadow: '0 4px 15px rgba(219, 39, 60, 0.3)',
                    backgroundImage:
                        'linear-gradient(to right, #DB273C, #E56B73)'
                }}
            >
                <div className="flex w-full items-center justify-center gap-1 sm:gap-2">
                    <ClockIcon className="h-3 w-3 text-rose-100 sm:h-4 sm:w-4 md:h-5 md:w-5" />
                    <div className="xs:text-xs text-2xs font-bold text-rose-100">
                        {title}
                    </div>
                </div>
                <div className="mt-1 flex w-full justify-center gap-1 font-semibold sm:gap-2">
                    <div className="flex flex-col items-center">
                        <div className="flex h-6 w-6 items-center justify-center rounded bg-white bg-opacity-25 backdrop-blur-sm sm:h-8 sm:w-8 md:h-10 md:w-10">
                            <span className="text-sm font-bold sm:text-lg md:text-xl">
                                {timeLeft.days}
                            </span>
                        </div>
                        <span className="xs:text-xs mt-0.5 text-2xs font-medium text-rose-100">
                            Gün
                        </span>
                    </div>
                    <div className="flex flex-col items-center">
                        <div className="flex h-6 w-6 items-center justify-center rounded bg-white bg-opacity-25 backdrop-blur-sm sm:h-8 sm:w-8 md:h-10 md:w-10">
                            <span className="text-sm font-bold sm:text-lg md:text-xl">
                                {String(timeLeft.hours).padStart(2, '0')}
                            </span>
                        </div>
                        <span className="xs:text-xs mt-0.5 text-2xs font-medium text-rose-100">
                            Saat
                        </span>
                    </div>
                    <span className="self-center text-sm font-bold text-rose-100 sm:text-lg md:text-xl">
                        :
                    </span>
                    <div className="flex flex-col items-center">
                        <div className="flex h-6 w-6 items-center justify-center rounded bg-white bg-opacity-25 backdrop-blur-sm sm:h-8 sm:w-8 md:h-10 md:w-10">
                            <span className="text-sm font-bold sm:text-lg md:text-xl">
                                {String(timeLeft.minutes).padStart(2, '0')}
                            </span>
                        </div>
                        <span className="xs:text-xs mt-0.5 text-2xs font-medium text-rose-100">
                            Dk
                        </span>
                    </div>
                    <span className="self-center text-sm font-bold text-rose-100 sm:text-lg md:text-xl">
                        :
                    </span>
                    <div className="flex flex-col items-center">
                        <div className="flex h-6 w-6 items-center justify-center rounded bg-white bg-opacity-25 backdrop-blur-sm sm:h-8 sm:w-8 md:h-10 md:w-10">
                            <span className="text-sm font-bold sm:text-lg md:text-xl">
                                {String(timeLeft.seconds).padStart(2, '0')}
                            </span>
                        </div>
                        <span className="xs:text-xs mt-0.5 text-2xs font-medium text-rose-100">
                            Sn
                        </span>
                    </div>
                </div>
            </div>
        );
    }
);

if (isDev) {
    CountdownTimer.displayName = 'CountdownTimer';
}

export default CountdownTimer;
