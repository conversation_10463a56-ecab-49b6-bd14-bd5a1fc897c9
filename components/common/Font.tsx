import {DM_Serif_Display, Mulish, Spectral} from 'next/font/google';
import localFont from 'next/font/local';

const hurme = localFont({
    src: [
        {
            path: '../../assets/fonts/HurmeGeometricSans2-SemiBold.woff2',
            weight: '600',
            style: 'normal'
        },
        {
            path: '../../assets/fonts/HurmeGeometricSans2-Regular.woff2',
            weight: '400',
            style: 'normal'
        }
    ]
});

const dm = DM_Serif_Display({
    weight: '400',
    subsets: ['latin'],
    display: 'swap'
});

const mulish = Mulish({
    weight: ['300', '400', '500', '600', '700'],
    subsets: ['latin'],
    display: 'swap'
});

const spectral = Spectral({
    weight: ['200', '300', '400', '500', '600', '700'],
    subsets: ['latin'],
    display: 'swap'
});

const Font = () => {
    return (
        <style jsx global>
            {`
                html {
                    --mulish: ${mulish.style.fontFamily};
                    --dm-serif: ${dm.style.fontFamily};
                    --hurme: ${hurme.style.fontFamily};
                    --spectral: ${spectral.style.fontFamily};
                }
            `}
        </style>
    );
};

export default Font;
