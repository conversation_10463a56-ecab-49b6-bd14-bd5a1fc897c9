import {FC, memo, useMemo} from 'react';
import {NavigationItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay, Navigation} from '@core/components/ui/Slider';

interface MainSliderProps {
    forSpecialPage?: boolean;
    items?: NavigationItem[];
}

const MainSlider: FC<MainSliderProps> = memo(
    ({forSpecialPage = false, items}) => {
        const {navigation} = useStore();

        const slides = useMemo(() => {
            if (forSpecialPage) {
                return (items ?? []).map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href.startsWith('/')
                        ? navigationItem.href.substring(1)
                        : navigationItem.href
                }));
            }

            return navigation
                .filter(
                    navigationItem =>
                        navigationItem.type === 'slide' &&
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0 &&
                        navigationItem.depth === 0
                )
                .map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href.startsWith('/')
                        ? navigationItem.href.substring(1)
                        : navigationItem.href
                }));
        }, [forSpecialPage, items, navigation]);

        const collections = useMemo(() => {
            let filteredItems;

            if (forSpecialPage) {
                filteredItems = (items ?? []).filter(
                    navigationItem =>
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0
                );
            } else {
                filteredItems = navigation.filter(
                    navigationItem =>
                        navigationItem.type === 'collection' &&
                        navigationItem.section === 'slider-collection' &&
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0 &&
                        navigationItem.depth === 0
                );
            }

            const collectionItems = filteredItems.map(navigationItem => ({
                title: navigationItem.name,
                src: navigationItem.images as string[],
                link: navigationItem.href.startsWith('/')
                    ? navigationItem.href
                    : navigationItem.href
            }));

            return collectionItems;
        }, [forSpecialPage, items, navigation]);

        return (
            <div
                className={cls('container grid grid-cols-1 py-6 md:gap-4', {
                    'md:grid-cols-1': collections.length === 0,
                    'md:grid-cols-3': collections.length > 0
                })}
            >
                <div className="col-span-2">
                    {slides.length > 0 && (
                        <div className="home-main-slider">
                            <div className="aspect-h-1 aspect-w-2">
                                <div>
                                    <UiSlider
                                        className="h-full"
                                        modules={[Autoplay, Navigation]}
                                        autoplay={{
                                            delay: 5000
                                        }}
                                        navigation
                                    >
                                        {slides.map((slide, index) => (
                                            <UiSlider.Slide key={index}>
                                                <UiLink
                                                    className="relative block h-full w-full"
                                                    href={slide.link ?? ''}
                                                >
                                                    <UiImage
                                                        src={`${slide.src}?w=1280&q=90`}
                                                        alt={slide.title}
                                                        fit="cover"
                                                        position="center"
                                                        priority={index === 0}
                                                        fill
                                                    />
                                                </UiLink>
                                            </UiSlider.Slide>
                                        ))}
                                    </UiSlider>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
                {collections.length > 0 && (
                    <div
                        className={`mt-6 grid gap-6 md:gap-3 xl:gap-2 ${
                            collections.length === 1
                                ? 'grid-rows-1'
                                : 'grid-rows-2'
                        } md:mt-0`}
                    >
                        {collections.map((collection, index) => (
                            <div
                                key={index}
                                className="aspect-h-6 aspect-w-12 relative h-full w-full md:aspect-h-4"
                            >
                                {collection.link ? (
                                    <UiLink href={collection.link}>
                                        <UiImage
                                            fill
                                            className="h-full w-full object-cover"
                                            title={collection.title}
                                            src={collection.src[0]}
                                            alt={collection.title}
                                        />
                                    </UiLink>
                                ) : (
                                    <div>
                                        <UiImage
                                            fill
                                            className="h-full w-full object-cover"
                                            title={collection.title}
                                            src="/no-image.png"
                                            alt={collection.title}
                                        />
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    }
);

if (isDev) {
    MainSlider.displayName = 'MainSlider';
}

export default MainSlider;
