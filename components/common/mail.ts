import nodemailer, {Transporter} from 'nodemailer';

interface MailOptions {
    to: string;
    subject: string;
    text: string;
    html?: string;
}

// Gmail SMTP configuration
const transporter: Transporter = nodemailer.createTransport({
    host: 'smtp.sendgrid.net',
    port: 465,
    secure: true,
    auth: {
        user: 'apikey',
        pass: '*********************************************************************'
    }
});

// Function to send email
export async function sendMail({
    to,
    subject,
    text,
    html
}: MailOptions): Promise<void> {
    try {
        const mailOptions = {
            from: `<EMAIL>`,
            to,
            subject,
            text,
            html
        };

        const info = await transporter.sendMail(mailOptions);
        console.log('Message sent: %s', info.messageId);
    } catch (error) {
        console.error('Error sending email: ', error);
        throw new Error('Failed to send email');
    }
}

export function createEmailTemplate({
    to,
    subject,
    text
}: Pick<MailOptions, 'to' | 'subject' | 'text'>): string {
    return `
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="https://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

    <head>
     <meta charset="UTF-8" />
     <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
     <!--[if !mso]><!-- -->
     <meta http-equiv="X-UA-Compatible" content="IE=edge" />
     <!--<![endif]-->
     <meta name="viewport" content="width=device-width, initial-scale=1.0" />
     <meta name="format-detection" content="telephone=no" />
     <meta name="format-detection" content="date=no" />
     <meta name="format-detection" content="address=no" />
     <meta name="format-detection" content="email=no" />
     <meta name="x-apple-disable-message-reformatting" />
     <link href="https://fonts.googleapis.com/css?family=Poppins:ital,wght@0,400;0,400;0,500;0,600" rel="stylesheet" />
     <title>Untitled</title>
     <!-- Made with Postcards by Designmodo https://designmodo.com/postcards -->
     <style>
     html,
             body {
                 margin: 0 !important;
                 padding: 0 !important;
                 min-height: 100% !important;
                 width: 100% !important;
                 -webkit-font-smoothing: antialiased;
             }

             * {
                 -ms-text-size-adjust: 100%;
             }

             #outlook a {
                 padding: 0;
             }

             .ReadMsgBody,
             .ExternalClass {
                 width: 100%;
             }

             .ExternalClass,
             .ExternalClass p,
             .ExternalClass td,
             .ExternalClass div,
             .ExternalClass span,
             .ExternalClass font {
                 line-height: 100%;
             }

             table,
             td,
             th {
                 mso-table-lspace: 0 !important;
                 mso-table-rspace: 0 !important;
                 border-collapse: collapse;
             }

             u + .body table, u + .body td, u + .body th {
                 will-change: transform;
             }

             body, td, th, p, div, li, a, span {
                 -webkit-text-size-adjust: 100%;
                 -ms-text-size-adjust: 100%;
                 mso-line-height-rule: exactly;
             }

             img {
                 border: 0;
                 outline: 0;
                 line-height: 100%;
                 text-decoration: none;
                 -ms-interpolation-mode: bicubic;
             }

             a[x-apple-data-detectors] {
                 color: inherit !important;
                 text-decoration: none !important;
             }

             .pc-gmail-fix {
                 display: none;
                 display: none !important;
             }

             .body .pc-project-body {
                 background-color: transparent !important;
             }

             @media (min-width: 621px) {
                 .pc-lg-hide {
                     display: none;
                 }

                 .pc-lg-bg-img-hide {
                     background-image: none !important;
                 }
             }
     </style>
     <style>
     @media (max-width: 620px) {
     .pc-project-body {min-width: 0px !important;}
     .pc-project-container {width: 100% !important;}
     .pc-sm-hide {display: none !important;}
     .pc-sm-bg-img-hide {background-image: none !important;}
     .pc-w620-itemsSpacings-0-20 {padding-left: 0px !important;padding-right: 0px !important;padding-top: 10px !important;padding-bottom: 10px !important;}
     table.pc-w620-spacing-0-0-24-0 {margin: 0px 0px 24px 0px !important;}
     td.pc-w620-spacing-0-0-24-0,th.pc-w620-spacing-0-0-24-0{margin: 0 !important;padding: 0px 0px 24px 0px !important;}
     .pc-w620-padding-0-0-0-0 {padding: 0px 0px 0px 0px !important;}
     .pc-w620-valign-middle {vertical-align: middle !important;}
     td.pc-w620-halign-center,th.pc-w620-halign-center {text-align: center !important;}
     table.pc-w620-halign-center {float: none !important;margin-right: auto !important;margin-left: auto !important;}
     img.pc-w620-halign-center {margin-right: auto !important;margin-left: auto !important;}
     div.pc-w620-align-center,th.pc-w620-align-center,a.pc-w620-align-center,td.pc-w620-align-center {text-align: center !important;text-align-last: center !important;}
     table.pc-w620-align-center {float: none !important;margin-right: auto !important;margin-left: auto !important;}
     img.pc-w620-align-center {margin-right: auto !important;margin-left: auto !important;}
     .pc-w620-width-136 {width: 136px !important;}
     .pc-w620-height-auto {height: auto !important;}
     .pc-w620-padding-40-24-40-24 {padding: 40px 24px 40px 24px !important;}
     table.pc-w620-spacing-0-0-0-0 {margin: 0px 0px 0px 0px !important;}
     td.pc-w620-spacing-0-0-0-0,th.pc-w620-spacing-0-0-0-0{margin: 0 !important;padding: 0px 0px 0px 0px !important;}
     .pc-w620-fontSize-32px {font-size: 32px !important;}
     .pc-w620-lineHeight-32 {line-height: 32px !important;}
     .pc-w620-fontSize-14px {font-size: 14px !important;}
     .pc-w620-lineHeight-140pc {line-height: 140% !important;}
     .pc-w620-fontSize-24px {font-size: 24px !important;}
     table.pc-w620-spacing-0-0-20-0 {margin: 0px 0px 20px 0px !important;}
     td.pc-w620-spacing-0-0-20-0,th.pc-w620-spacing-0-0-20-0{margin: 0 !important;padding: 0px 0px 20px 0px !important;}
     .pc-w620-itemsSpacings-0-4 {padding-left: 0px !important;padding-right: 0px !important;padding-top: 2px !important;padding-bottom: 2px !important;}
     .pc-w620-padding-16-24-16-24 {padding: 16px 24px 16px 24px !important;}
     .pc-w620-itemsSpacings-0-16 {padding-left: 0px !important;padding-right: 0px !important;padding-top: 8px !important;padding-bottom: 8px !important;}
     td.pc-w620-halign-left,th.pc-w620-halign-left {text-align: left !important;}
     table.pc-w620-halign-left {float: none !important;margin-right: auto !important;margin-left: 0 !important;}
     img.pc-w620-halign-left {margin-right: auto !important;margin-left: 0 !important;}
     div.pc-w620-textAlign-left,th.pc-w620-textAlign-left,a.pc-w620-textAlign-left,td.pc-w620-textAlign-left {text-align: left !important;text-align-last: left !important;}
     table.pc-w620-textAlign-left{float: none !important;margin-right: auto !important;margin-left: 0 !important;}
     img.pc-w620-textAlign-left{margin-right: auto !important;margin-left: 0 !important;}
     td.pc-w620-halign-right,th.pc-w620-halign-right {text-align: right !important;}
     table.pc-w620-halign-right {float: none !important;margin-right: 0 !important;margin-left: auto !important;}
     img.pc-w620-halign-right {margin-right: 0 !important;margin-left: auto !important;}
     .pc-w620-width-fill {width: 100% !important;}
     div.pc-w620-align-right,th.pc-w620-align-right,a.pc-w620-align-right,td.pc-w620-align-right {text-align: right !important;text-align-last: right !important;}
     table.pc-w620-align-right{float: none !important;margin-left: auto !important;margin-right: 0 !important;}
     img.pc-w620-align-right{margin-right: 0 !important;margin-left: auto !important;}
     .pc-w620-padding-24-0-0-0 {padding: 24px 0px 0px 0px !important;}
     .pc-w620-padding-32-0-4-0 {padding: 32px 0px 4px 0px !important;}
     table.pc-w620-spacing-10-0-0-0 {margin: 10px 0px 0px 0px !important;}
     td.pc-w620-spacing-10-0-0-0,th.pc-w620-spacing-10-0-0-0{margin: 0 !important;padding: 10px 0px 0px 0px !important;}
     .pc-w620-itemsSpacings-6-0 {padding-left: 3px !important;padding-right: 3px !important;padding-top: 0px !important;padding-bottom: 0px !important;}
     .pc-w620-padding-12-12-12-12 {padding: 12px 12px 12px 12px !important;}
     .pc-w620-padding-24-24-24-24 {padding: 24px 24px 24px 24px !important;}
     .pc-w620-itemsSpacings-0-0 {padding-left: 0px !important;padding-right: 0px !important;padding-top: 0px !important;padding-bottom: 0px !important;}
     .pc-w620-radius-10-10-10-10 {border-radius: 10px 10px 10px 10px !important;}

     .pc-w620-gridCollapsed-1 > tbody,.pc-w620-gridCollapsed-1 > tbody > tr,.pc-w620-gridCollapsed-1 > tr {display: inline-block !important;}
     .pc-w620-gridCollapsed-1.pc-width-fill > tbody,.pc-w620-gridCollapsed-1.pc-width-fill > tbody > tr,.pc-w620-gridCollapsed-1.pc-width-fill > tr {width: 100% !important;}
     .pc-w620-gridCollapsed-1.pc-w620-width-fill > tbody,.pc-w620-gridCollapsed-1.pc-w620-width-fill > tbody > tr,.pc-w620-gridCollapsed-1.pc-w620-width-fill > tr {width: 100% !important;}
     .pc-w620-gridCollapsed-1 > tbody > tr > td,.pc-w620-gridCollapsed-1 > tr > td {display: block !important;width: auto !important;padding-left: 0 !important;padding-right: 0 !important;}
     .pc-w620-gridCollapsed-1.pc-width-fill > tbody > tr > td,.pc-w620-gridCollapsed-1.pc-width-fill > tr > td {width: 100% !important;}
     .pc-w620-gridCollapsed-1.pc-w620-width-fill > tbody > tr > td,.pc-w620-gridCollapsed-1.pc-w620-width-fill > tr > td {width: 100% !important;}
     .pc-w620-gridCollapsed-1 > tbody > .pc-grid-tr-first > .pc-grid-td-first,pc-w620-gridCollapsed-1 > .pc-grid-tr-first > .pc-grid-td-first {padding-top: 0 !important;}
     .pc-w620-gridCollapsed-1 > tbody > .pc-grid-tr-last > .pc-grid-td-last,pc-w620-gridCollapsed-1 > .pc-grid-tr-last > .pc-grid-td-last {padding-bottom: 0 !important;}

     .pc-w620-gridCollapsed-0 > tbody > .pc-grid-tr-first > td,.pc-w620-gridCollapsed-0 > .pc-grid-tr-first > td {padding-top: 0 !important;}
     .pc-w620-gridCollapsed-0 > tbody > .pc-grid-tr-last > td,.pc-w620-gridCollapsed-0 > .pc-grid-tr-last > td {padding-bottom: 0 !important;}
     .pc-w620-gridCollapsed-0 > tbody > tr > .pc-grid-td-first,.pc-w620-gridCollapsed-0 > tr > .pc-grid-td-first {padding-left: 0 !important;}
     .pc-w620-gridCollapsed-0 > tbody > tr > .pc-grid-td-last,.pc-w620-gridCollapsed-0 > tr > .pc-grid-td-last {padding-right: 0 !important;}

     .pc-w620-tableCollapsed-1 > tbody,.pc-w620-tableCollapsed-1 > tbody > tr,.pc-w620-tableCollapsed-1 > tr {display: block !important;}
     .pc-w620-tableCollapsed-1.pc-width-fill > tbody,.pc-w620-tableCollapsed-1.pc-width-fill > tbody > tr,.pc-w620-tableCollapsed-1.pc-width-fill > tr {width: 100% !important;}
     .pc-w620-tableCollapsed-1.pc-w620-width-fill > tbody,.pc-w620-tableCollapsed-1.pc-w620-width-fill > tbody > tr,.pc-w620-tableCollapsed-1.pc-w620-width-fill > tr {width: 100% !important;}
     .pc-w620-tableCollapsed-1 > tbody > tr > td,.pc-w620-tableCollapsed-1 > tr > td {display: block !important;width: auto !important;}
     .pc-w620-tableCollapsed-1.pc-width-fill > tbody > tr > td,.pc-w620-tableCollapsed-1.pc-width-fill > tr > td {width: 100% !important;box-sizing: border-box !important;}
     .pc-w620-tableCollapsed-1.pc-w620-width-fill > tbody > tr > td,.pc-w620-tableCollapsed-1.pc-w620-width-fill > tr > td {width: 100% !important;box-sizing: border-box !important;}
     }
     </style>
     <!--[if !mso]><!-- -->
     <style>
     @media all { @font-face { font-family: 'Poppins'; font-style: normal; font-weight: 600; src: url('https://fonts.gstatic.com/s/poppins/v21/pxiByp8kv8JHgFVrLEj6Z1JlEw.woff') format('woff'), url('https://fonts.gstatic.com/s/poppins/v21/pxiByp8kv8JHgFVrLEj6Z1JlFQ.woff2') format('woff2'); } @font-face { font-family: 'Poppins'; font-style: normal; font-weight: 400; src: url('https://fonts.gstatic.com/s/poppins/v21/pxiEyp8kv8JHgFVrJJnedA.woff') format('woff'), url('https://fonts.gstatic.com/s/poppins/v21/pxiEyp8kv8JHgFVrJJnecg.woff2') format('woff2'); } @font-face { font-family: 'Poppins'; font-style: normal; font-weight: 500; src: url('https://fonts.gstatic.com/s/poppins/v21/pxiByp8kv8JHgFVrLGT9Z1JlEw.woff') format('woff'), url('https://fonts.gstatic.com/s/poppins/v21/pxiByp8kv8JHgFVrLGT9Z1JlFQ.woff2') format('woff2'); } }
     </style>
     <!--<![endif]-->
     <!--[if mso]>
        <style type="text/css">
            .pc-font-alt {
                font-family: Arial, Helvetica, sans-serif !important;
            }
        </style>
        <![endif]-->
     <!--[if gte mso 9]>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
        <![endif]-->
    </head>

    <body class="body pc-font-alt" style="width: 100% !important; min-height: 100% !important; margin: 0 !important; padding: 0 !important; line-height: 1.5; color: #2D3A41; mso-line-height-rule: exactly; -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; font-variant-ligatures: normal; text-rendering: optimizeLegibility; -moz-osx-font-smoothing: grayscale; background-color: #ffffff;" bgcolor="#ffffff">
     <table class="pc-project-body" style="table-layout: fixed; min-width: 600px; background-color: #ffffff;" bgcolor="#ffffff" width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
      <tr>
       <td align="center" valign="top">
        <table class="pc-project-container" align="center" width="600" style="width: 600px; max-width: 600px;" border="0" cellpadding="0" cellspacing="0" role="presentation">
         <tr>
          <td style="padding: 20px 0px 20px 0px;" align="left" valign="top">
           <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="width: 100%;">
            <tr>
             <td valign="top">
              <!-- BEGIN MODULE: Hero -->
              <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
               <tr>
                <td class="pc-w620-spacing-0-0-0-0" style="padding: 0px 0px 0px 0px;">
                 <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
                  <tr>
                   <td valign="top" class="pc-w620-padding-24-0-0-0" style="padding: 0px 0px 0px 0px; border-radius: 0px; background-color: #ffffff;" bgcolor="#ffffff">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                     <tr>
                      <td class="pc-w620-spacing-0-0-24-0" style="padding: 0px 0px 24px 0px;">
                       <table class="pc-width-fill pc-w620-gridCollapsed-1" width="100%" height="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                        <tr class="pc-grid-tr-first pc-grid-tr-last">
                         <td class="pc-grid-td-first pc-grid-td-last pc-w620-itemsSpacings-0-20" align="left" valign="top" style="width: 50%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                          <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0; width: 100%; height: 100%;">
                           <tr>
                            <td class="pc-w620-halign-center pc-w620-valign-middle" align="left" valign="middle">
                             <table class="pc-w620-halign-center" align="left" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                              <tr>
                               <td class="pc-w620-halign-center" align="left" valign="top">
                                <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                 <tr>
                                  <td class="pc-w620-halign-center" align="left" valign="top" style="padding: 0px 0px 0px 0px;">
                                   <img src="https://cloudfilesdm.com/postcards/image-1724330194061.png" class="pc-w620-width-136 pc-w620-height-auto pc-w620-align-center" width="134" height="45" alt="" style="display: block; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:134px; height: auto; max-width: 100%; border: 0;" />
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                             </table>
                            </td>
                           </tr>
                          </table>
                         </td>
                        </tr>
                       </table>
                      </td>
                     </tr>
                    </table>
                    <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                     <tr>
                      <td>
                       <table class="pc-width-fill pc-w620-gridCollapsed-0" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                        <tr class="pc-grid-tr-first pc-grid-tr-last">
                         <td class="pc-grid-td-first pc-grid-td-last" align="center" valign="top" style="padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                          <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0; width: 100%;">
                           <tr>
                            <td class="pc-w620-padding-40-24-40-24" align="center" valign="bottom" style="padding: 44px 44px 44px 44px; background-color: #f9ecee; border-radius: 10px 10px 0px 0px;">
                             <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                              <tr>
                               <td align="center" valign="top">
                               </td>
                              </tr>
                              <tr>
                               <td align="center" valign="top">
                                <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                 <tr>
                                  <td valign="top" style="padding: 0px 0px 20px 0px;">
                                   <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: separate; border-spacing: 0;">
                                    <tr>
                                     <td valign="top" align="center">
                                      <div class="pc-font-alt pc-w620-fontSize-32px pc-w620-lineHeight-32" style="line-height: 100%; letter-spacing: -0.03em; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-size: 40px; font-weight: 600; font-variant-ligatures: normal; color: #001942; text-align: center; text-align-last: center;">
                                       <div><span>Mesajınız İçin Teşekkürler</span>
                                       </div>
                                      </div>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                              <tr>
                               <td align="center" valign="top">
                                <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                 <tr>
                                  <td valign="top" style="padding: 0px 0px 20px 0px;">
                                   <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: separate; border-spacing: 0;">
                                    <tr>
                                     <td valign="top" align="center">
                                      <div class="pc-font-alt pc-w620-fontSize-14px pc-w620-lineHeight-140pc" style="line-height: 140%; letter-spacing: 0px; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-size: 14px; font-weight: normal; font-variant-ligatures: normal; color: #001942; text-align: center; text-align-last: center;">
                                       <div><span>Değerli mesajınız için teşekkürler. Mesajınızı en kısa sürede inceleyip size geri dönüş sağlayacağız.</span>
                                       </div>
                                      </div>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                              <tr>
                               <td align="center" valign="top">
                                <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                 <tr>
                                  <th valign="top" align="center" style="text-align: center; font-weight: normal; line-height: 1;">
                                   <!--[if mso]>
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" align="center" style="border-collapse: separate; border-spacing: 0; margin-right: auto; margin-left: auto;">
                <tr>
                    <td valign="middle" align="center" style="border-radius: 500px 500px 500px 500px; background-color: #9f2842; text-align:center; color: #ffffff; padding: 14px 28px 14px 28px; mso-padding-left-alt: 0; margin-left:28px;" bgcolor="#9f2842">
                                        <a class="pc-font-alt" style="display: inline-block; text-decoration: none; font-variant-ligatures: normal; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-weight: 500; font-size: 17px; line-height: 24px; text-align: center; color: #ffffff;" href="https://www.nehir.com.tr/" target="_blank"><span style="display: block;"><span>Nehir&#39;e Git</span></span></a>
                                    </td>
                </tr>
            </table>
            <![endif]-->
                                   <!--[if !mso]><!-- -->
                                   <a style="display: inline-block; box-sizing: border-box; border-radius: 500px 500px 500px 500px; background-color: #9f2842; padding: 14px 28px 14px 28px; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-weight: 500; font-size: 17px; line-height: 24px; color: #ffffff; vertical-align: top; text-align: center; text-align-last: center; text-decoration: none; -webkit-text-size-adjust: none;" href="https://www.nehir.com.tr/" target="_blank"><span style="display: block;"><span>Nehir&#39;e Git</span></span></a>
                                   <!--<![endif]-->
                                  </th>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                              <tr>
                               <td align="center" valign="top">
                                <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                 <tr>
                                  <td class="pc-w620-spacing-0-0-20-0" valign="top" style="padding: 0px 0px 32px 0px;">
                                   <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: separate; border-spacing: 0;">
                                    <tr>
                                     <td valign="top" class="pc-w620-padding-0-0-0-0" align="center" style="padding: 24px 0px 0px 0px;">
                                      <div class="pc-font-alt pc-w620-fontSize-24px pc-w620-lineHeight-32" style="line-height: 140%; letter-spacing: -0.03em; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-size: 24px; font-weight: 600; font-variant-ligatures: normal; color: #001942; text-align: center; text-align-last: center;">
                                       <div><span>Mesajınızla ilgili sorun mu var?</span>
                                       </div>
                                      </div>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                              <tr>
                               <td align="center" valign="top">
                                <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                 <tr>
                                  <td>
                                   <table class="pc-width-fill pc-w620-gridCollapsed-1" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                    <tr class="pc-grid-tr-first pc-grid-tr-last">
                                     <td class="pc-grid-td-first pc-w620-itemsSpacings-0-4" align="left" valign="top" style="width: 50%; padding-top: 0px; padding-right: 2px; padding-bottom: 0px; padding-left: 0px;">
                                      <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0; width: 100%;">
                                       <tr>
                                        <td class="pc-w620-padding-16-24-16-24 pc-w620-halign-center pc-w620-valign-middle" align="left" valign="middle" style="padding: 16px 16px 16px 16px; background-color: #9f2842; border-radius: 10px 10px 10px 10px;">
                                         <table class="pc-w620-halign-center" align="left" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                          <tr>
                                           <td class="pc-w620-halign-center" align="left" valign="top">
                                            <table class="pc-w620-halign-center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                             <tr>
                                              <td class="pc-w620-valign-middle pc-w620-halign-left">
                                               <table class="pc-width-fill pc-w620-gridCollapsed-0 pc-w620-halign-left" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                                <tr class="pc-grid-tr-first pc-grid-tr-last">
                                                 <td class="pc-grid-td-first pc-w620-itemsSpacings-0-16" align="left" valign="middle" style="padding-top: 0px; padding-right: 8px; padding-bottom: 0px; padding-left: 0px;">
                                                  <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0; width: 100%;">
                                                   <tr>
                                                    <td class="pc-w620-halign-left pc-w620-valign-middle" align="left" valign="middle">
                                                     <table class="pc-w620-halign-left" align="left" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                                      <tr>
                                                       <td class="pc-w620-halign-left" align="left" valign="top">
                                                        <table class="pc-w620-halign-left" align="left" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                                         <tr>
                                                          <td valign="top" style="padding: 0px 0px 5px 0px;">
                                                           <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: separate; border-spacing: 0;">
                                                            <tr>
                                                             <td valign="top" class="pc-w620-textAlign-left" align="left">
                                                              <div class="pc-font-alt pc-w620-textAlign-left" style="line-height: 140%; letter-spacing: 0.04em; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-size: 12px; font-weight: normal; font-variant-ligatures: normal; color: #ffffff; text-transform: uppercase; text-align: left; text-align-last: left;">
                                                               <div><span style="text-transform: uppercase;color: #ffffff;">EMAİL</span>
                                                               </div>
                                                              </div>
                                                             </td>
                                                            </tr>
                                                           </table>
                                                          </td>
                                                         </tr>
                                                        </table>
                                                       </td>
                                                      </tr>
                                                      <tr>
                                                       <td class="pc-w620-halign-left" align="left" valign="top">
                                                        <table class="pc-w620-halign-left" align="left" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                                         <tr>
                                                          <td class="pc-w620-spacing-0-0-0-0" valign="top" style="padding: 0px 0px 0px 0px;">
                                                           <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: separate; border-spacing: 0;">
                                                            <tr>
                                                             <td valign="top" class="pc-w620-padding-0-0-0-0 pc-w620-textAlign-left" align="left">
                                                              <div class="pc-font-alt pc-w620-textAlign-left" style="line-height: 140%; letter-spacing: -0.03em; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-size: 17px; font-weight: 600; font-variant-ligatures: normal; color: #ffffff; text-align: left; text-align-last: left;">
                                                               <div><span style="color: #ffffff !important;letter-spacing: -0.03em;" data-letter-spacing-original="-0.03em"><EMAIL>@nehir.com.tr</span>
                                                               </div>
                                                              </div>
                                                             </td>
                                                            </tr>
                                                           </table>
                                                          </td>
                                                         </tr>
                                                        </table>
                                                       </td>
                                                      </tr>
                                                     </table>
                                                    </td>
                                                   </tr>
                                                  </table>
                                                 </td>
                                                 <td class="pc-grid-td-last pc-w620-itemsSpacings-0-16" align="left" valign="middle" style="padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 8px;">
                                                  <table class="pc-w620-width-fill" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0; width: 100%;">
                                                   <tr>
                                                    <td class="pc-w620-halign-right pc-w620-valign-middle" align="right" valign="middle">
                                                     <table class="pc-w620-halign-right" align="right" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                                      <tr>
                                                       <td class="pc-w620-halign-right" align="right" valign="top">
                                                        <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                                         <tr>
                                                          <td class="pc-w620-halign-right" align="right" valign="top">
                                                           <img src="https://cloudfilesdm.com/postcards/image-1724333791947.png" class="pc-w620-align-right" width="32" height="32" alt="" style="display: block; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:32px; height: auto; max-width: 100%; border: 0;" />
                                                          </td>
                                                         </tr>
                                                        </table>
                                                       </td>
                                                      </tr>
                                                     </table>
                                                    </td>
                                                   </tr>
                                                  </table>
                                                 </td>
                                                </tr>
                                               </table>
                                              </td>
                                             </tr>
                                            </table>
                                           </td>
                                          </tr>
                                         </table>
                                        </td>
                                       </tr>
                                      </table>
                                     </td>
                                     <td class="pc-grid-td-last pc-w620-itemsSpacings-0-4" align="left" valign="top" style="width: 50%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 2px;">
                                      <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0; width: 100%;">
                                       <tr>
                                        <td class="pc-w620-padding-16-24-16-24 pc-w620-halign-center pc-w620-valign-middle" align="left" valign="middle" style="padding: 16px 16px 16px 16px; background-color: #9f2842; border-radius: 10px 10px 10px 10px;">
                                         <table class="pc-w620-halign-center" align="left" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                          <tr>
                                           <td class="pc-w620-halign-center" align="left" valign="top">
                                            <table class="pc-w620-halign-center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                             <tr>
                                              <td class="pc-w620-valign-middle pc-w620-halign-left">
                                               <table class="pc-width-fill pc-w620-gridCollapsed-0 pc-w620-halign-left" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                                <tr class="pc-grid-tr-first pc-grid-tr-last">
                                                 <td class="pc-grid-td-first pc-w620-itemsSpacings-0-16" align="left" valign="middle" style="padding-top: 0px; padding-right: 8px; padding-bottom: 0px; padding-left: 0px;">
                                                  <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0; width: 100%;">
                                                   <tr>
                                                    <td class="pc-w620-halign-left pc-w620-valign-middle" align="left" valign="middle">
                                                     <table class="pc-w620-halign-left" align="left" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                                      <tr>
                                                       <td class="pc-w620-halign-left" align="left" valign="top">
                                                        <table class="pc-w620-halign-left" align="left" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                                         <tr>
                                                          <td valign="top" style="padding: 0px 0px 5px 0px;">
                                                           <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: separate; border-spacing: 0;">
                                                            <tr>
                                                             <td valign="top" class="pc-w620-textAlign-left" align="left">
                                                              <div class="pc-font-alt pc-w620-textAlign-left" style="line-height: 140%; letter-spacing: 0.04em; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-size: 12px; font-weight: normal; font-variant-ligatures: normal; color: #ffffff; text-transform: uppercase; text-align: left; text-align-last: left;">
                                                               <div><span style="text-transform: uppercase;color: #ffffff;">TELEFON</span>
                                                               </div>
                                                              </div>
                                                             </td>
                                                            </tr>
                                                           </table>
                                                          </td>
                                                         </tr>
                                                        </table>
                                                       </td>
                                                      </tr>
                                                      <tr>
                                                       <td class="pc-w620-halign-left" align="left" valign="top">
                                                        <table class="pc-w620-halign-left" align="left" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                                         <tr>
                                                          <td class="pc-w620-spacing-0-0-0-0" valign="top" style="padding: 0px 0px 0px 0px;">
                                                           <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: separate; border-spacing: 0;">
                                                            <tr>
                                                             <td valign="top" class="pc-w620-padding-0-0-0-0 pc-w620-textAlign-left" align="left">
                                                              <div class="pc-font-alt pc-w620-textAlign-left" style="line-height: 140%; letter-spacing: -0.03em; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-size: 17px; font-weight: 600; font-variant-ligatures: normal; color: #ffffff; text-align: left; text-align-last: left;">
                                                               <div style="text-align: start; text-align-last: start;"><span style="font-weight: 500;font-style: normal;color: #ffffff;">0 212 656 65 50</span>
                                                               </div>
                                                              </div>
                                                             </td>
                                                            </tr>
                                                           </table>
                                                          </td>
                                                         </tr>
                                                        </table>
                                                       </td>
                                                      </tr>
                                                     </table>
                                                    </td>
                                                   </tr>
                                                  </table>
                                                 </td>
                                                 <td class="pc-grid-td-last pc-w620-itemsSpacings-0-16" align="left" valign="middle" style="padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 8px;">
                                                  <table class="pc-w620-width-fill" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0; width: 100%;">
                                                   <tr>
                                                    <td class="pc-w620-halign-right pc-w620-valign-middle" align="right" valign="middle">
                                                     <table class="pc-w620-halign-right" align="right" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                                      <tr>
                                                       <td class="pc-w620-halign-right" align="right" valign="top">
                                                        <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                                         <tr>
                                                          <td class="pc-w620-halign-right" align="right" valign="top">
                                                           <img src="https://cloudfilesdm.com/postcards/image-1724333803693.png" class="pc-w620-align-right" width="32" height="32" alt="" style="display: block; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:32px; height: auto; max-width: 100%; border: 0;" />
                                                          </td>
                                                         </tr>
                                                        </table>
                                                       </td>
                                                      </tr>
                                                     </table>
                                                    </td>
                                                   </tr>
                                                  </table>
                                                 </td>
                                                </tr>
                                               </table>
                                              </td>
                                             </tr>
                                            </table>
                                           </td>
                                          </tr>
                                         </table>
                                        </td>
                                       </tr>
                                      </table>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                             </table>
                            </td>
                           </tr>
                          </table>
                         </td>
                        </tr>
                       </table>
                      </td>
                     </tr>
                    </table>
                   </td>
                  </tr>
                 </table>
                </td>
               </tr>
              </table>
              <!-- END MODULE: Hero -->
             </td>
            </tr>
            <tr>
             <td valign="top">
              <!-- BEGIN MODULE: Footer -->
              <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
               <tr>
                <td class="pc-w620-spacing-0-0-0-0" style="padding: 0px 0px 0px 0px;">
                 <table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
                  <tr>
                   <td valign="top" class="pc-w620-radius-10-10-10-10 pc-w620-padding-24-24-24-24" style="padding: 24px 24px 24px 24px; border-radius: 0px 0px 10px 10px; background-color: #9f2842;" bgcolor="#9f2842">
                    <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                     <tr>
                      <td align="center" valign="top" style="padding: 0px 0px 12px 0px;">
                       <img src="https://cloudfilesdm.com/postcards/image-1724330417964.png" class="" width="135" height="45" alt="" style="display: block; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:135px; height: auto; max-width: 100%; border: 0;" />
                      </td>
                     </tr>
                    </table>
                    <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                     <tr>
                      <td class="pc-w620-spacing-10-0-0-0" align="center" valign="top" style="padding: 0px 0px 0px 0px;">
                       <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="border-collapse: separate; border-spacing: 0; margin-right: auto; margin-left: auto;">
                        <tr>
                         <td valign="top" class="pc-w620-padding-0-0-0-0" align="center">
                          <div class="pc-font-alt" style="line-height: 143%; letter-spacing: -0.2px; font-family: 'Poppins', Arial, Helvetica, sans-serif; font-size: 14px; font-weight: normal; font-variant-ligatures: normal; color: #ffffff; text-align: center; text-align-last: center;">
                           <div><span>2417 Sok. Z Blok No.83 İstoç İkitelli / İstanbul</span>
                           </div>
                          </div>
                         </td>
                        </tr>
                       </table>
                      </td>
                     </tr>
                    </table>
                    <table width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                     <tr>
                      <td align="center" style="padding: 24px 0px 0px 0px;">
                       <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                        <tr>
                         <td valign="top">
                          <table class="pc-width-hug pc-w620-gridCollapsed-0" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                           <tr class="pc-grid-tr-first">
                            <td class="pc-grid-td-first pc-w620-itemsSpacings-6-0" valign="middle" style="width: 20%; padding-top: 0px; padding-right: 12px; padding-bottom: 0px; padding-left: 0px;">
                             <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0;">
                              <tr>
                               <td class="pc-w620-padding-12-12-12-12" align="center" valign="middle" style="padding: 0px 0px 0px 0px; border-radius: 500px 500px 500px 500px;">
                                <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                 <tr>
                                  <td align="center" valign="top">
                                   <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                    <tr>
                                     <td valign="top">
                                      <a class="pc-font-alt" href="https://designmodo.com/postcards" target="_blank" style="text-decoration: none;">
                                       <img src="https://cloudfilesdm.com/postcards/649c68b0a6dc8f618df90ec1f44e0082.png" class="" width="26" height="26" style="display: block; border: 0; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:26px; height:26px;" alt="" />
                                      </a>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                             </table>
                            </td>
                            <td class="pc-w620-itemsSpacings-6-0" valign="middle" style="width: 20%; padding-top: 0px; padding-right: 12px; padding-bottom: 0px; padding-left: 12px;">
                             <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0;">
                              <tr>
                               <td class="pc-w620-padding-12-12-12-12" align="center" valign="middle" style="padding: 0px 0px 0px 0px; border-radius: 500px 500px 500px 500px;">
                                <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                 <tr>
                                  <td align="center" valign="top">
                                   <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                    <tr>
                                     <td valign="top">
                                      <a class="pc-font-alt" href="https://designmodo.com/postcards" target="_blank" style="text-decoration: none;">
                                       <img src="https://cloudfilesdm.com/postcards/e931e54b1bf5c1e0cac743c437478e90.png" class="" width="26" height="26" style="display: block; border: 0; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:26px; height:26px;" alt="" />
                                      </a>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                             </table>
                            </td>
                            <td class="pc-w620-itemsSpacings-6-0" valign="middle" style="width: 20%; padding-top: 0px; padding-right: 12px; padding-bottom: 0px; padding-left: 12px;">
                             <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0;">
                              <tr>
                               <td class="pc-w620-padding-12-12-12-12" align="center" valign="middle" style="padding: 0px 0px 0px 0px; border-radius: 500px 500px 500px 500px;">
                                <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                 <tr>
                                  <td align="center" valign="top">
                                   <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                    <tr>
                                     <td valign="top">
                                      <a class="pc-font-alt" href="https://designmodo.com/postcards" target="_blank" style="text-decoration: none;">
                                       <img src="https://cloudfilesdm.com/postcards/d39505db407e6ca83fd432b2866ccda0.png" class="" width="26" height="26" style="display: block; border: 0; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:26px; height:26px;" alt="" />
                                      </a>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                             </table>
                            </td>
                            <td class="pc-w620-itemsSpacings-6-0" valign="middle" style="width: 20%; padding-top: 0px; padding-right: 12px; padding-bottom: 0px; padding-left: 12px;">
                             <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0;">
                              <tr>
                               <td class="pc-w620-padding-12-12-12-12" align="center" valign="middle" style="padding: 0px 0px 0px 0px; border-radius: 500px 500px 500px 500px;">
                                <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                 <tr>
                                  <td align="center" valign="top">
                                   <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                    <tr>
                                     <td valign="top">
                                      <a class="pc-font-alt" href="https://designmodo.com/postcards" target="_blank" style="text-decoration: none;">
                                       <img src="https://cloudfilesdm.com/postcards/502b0595d6ddd6fc8df1fe4bb7f886cd.png" class="" width="26" height="26" style="display: block; border: 0; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:26px; height:26px;" alt="" />
                                      </a>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                             </table>
                            </td>
                            <td class="pc-grid-td-last pc-w620-itemsSpacings-6-0" valign="middle" style="width: 20%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 12px;">
                             <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0;">
                              <tr>
                               <td class="pc-w620-padding-12-12-12-12" align="center" valign="middle" style="padding: 0px 0px 0px 0px; border-radius: 500px 500px 500px 500px;">
                                <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                 <tr>
                                  <td align="center" valign="top">
                                   <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
                                    <tr>
                                     <td valign="top">
                                      <a class="pc-font-alt" href="https://designmodo.com/postcards" target="_blank" style="text-decoration: none;">
                                       <img src="https://cloudfilesdm.com/postcards/3b897c472dab53429b14734d1fa9a8bd.png" class="" width="26" height="26" style="display: block; border: 0; outline: 0; line-height: 100%; -ms-interpolation-mode: bicubic; width:26px; height:26px;" alt="" />
                                      </a>
                                     </td>
                                    </tr>
                                   </table>
                                  </td>
                                 </tr>
                                </table>
                               </td>
                              </tr>
                             </table>
                            </td>
                           </tr>
                           <tr class="pc-grid-tr-last">
                            <td class="pc-grid-td-first pc-grid-td-last pc-w620-itemsSpacings-6-0" valign="middle" style="width: 20%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                             <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: separate; border-spacing: 0;">
                              <tr>
                               <td align="left" valign="top">
                                <table align="left" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
                                </table>
                               </td>
                              </tr>
                             </table>
                            </td>
                           </tr>
                          </table>
                         </td>
                        </tr>
                       </table>
                      </td>
                     </tr>
                    </table>
                   </td>
                  </tr>
                 </table>
                </td>
               </tr>
              </table>
              <!-- END MODULE: Footer -->
             </td>
            </tr>

           </table>
          </td>
         </tr>
        </table>
       </td>
      </tr>
     </table>
     <!-- Fix for Gmail on iOS -->
     <div class="pc-gmail-fix" style="white-space: nowrap; font: 15px courier; line-height: 0;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
     </div>
    </body>

    </html>


    `;
}
