import {FC, memo, useMemo} from 'react';
import {NavigationItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';

interface StorySliderProps {
    forSpecialPage?: boolean;
    items?: NavigationItem[];
}

const StorySlider: FC<StorySliderProps> = memo(
    ({forSpecialPage = false, items}) => {
        const {navigation} = useStore();

        const storyItems = useMemo(() => {
            if (forSpecialPage) {
                return (items ?? []).map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href
                }));
            }

            return navigation
                .filter(
                    navigationItem =>
                        navigationItem.type === 'story' &&
                        navigationItem.section === '' &&
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0 &&
                        navigationItem.depth === 0
                )
                .map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href
                }));
        }, [forSpecialPage, items, navigation]);

        return storyItems.length > 0 ? (
            <div className="flex items-center justify-center py-6 xl:container">
                <div className="swiper-story-padding w-full rounded-lg bg-white px-4 xl:px-[8%]">
                    <div>
                        <UiSlider
                            className="h-full w-full"
                            modules={[]}
                            slidesPerView={3}
                            spaceBetween={10}
                            breakpoints={{
                                320: {
                                    slidesPerView: 3,
                                    spaceBetween: 15
                                },
                                480: {
                                    slidesPerView: 3,
                                    spaceBetween: 20
                                },
                                768: {
                                    slidesPerView: 5,
                                    spaceBetween: 20
                                },
                                1200: {
                                    slidesPerView: 8,
                                    spaceBetween: 20
                                }
                            }}
                        >
                            {storyItems?.map(story => {
                                return (
                                    <UiSlider.Slide
                                        className="xl:!mr-4.5 group"
                                        key={story.src}
                                    >
                                        <UiLink
                                            className="relative block h-full max-w-[148px] md:max-w-[113px] lg:max-w-[160px] xl:max-w-[110px]"
                                            href={story.link ?? '#'}
                                        >
                                            <div className="flex flex-col">
                                                <div className="rounded-full transition group-hover:text-primary-600 lg:aspect-2 xl:aspect-1">
                                                    <UiImage
                                                        className="rounded-full"
                                                        src={`${story.src}?w=180&q=90`}
                                                        alt={
                                                            story.title +
                                                            story.link
                                                        }
                                                        aspectH={1}
                                                        aspectW={1}
                                                        priority
                                                    />
                                                </div>
                                                <div className="cursor-pointer pt-2 text-center text-3xs !font-bold uppercase text-brand-black transition group-hover:text-primary-600 md:pt-2 lg:text-xs">
                                                    {story.title}
                                                </div>
                                            </div>
                                        </UiLink>
                                    </UiSlider.Slide>
                                );
                            })}
                        </UiSlider>
                    </div>
                </div>
            </div>
        ) : null;
    }
);

if (isDev) {
    StorySlider.displayName = 'StorySlider';
}

export default StorySlider;
