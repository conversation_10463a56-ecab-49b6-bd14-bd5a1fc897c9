import {FC, memo, useMemo} from 'react';
import {NavigationItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useMobile, useStore, useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';

type CollectionProps = {
    title: string;
    src: string[];
    link?: string;
};

const Collection: FC<CollectionProps> = ({src, title, link}) => {
    const t = useTrans();

    const Component = link ? UiLink : 'div';

    return (
        <Component
            href={link!}
            className="card group grid !h-24 w-full grid-cols-2 overflow-hidden rounded-none bg-secondary-200 sm:h-20 md:!h-52"
        >
            <div className="flex items-center pl-5 md:items-start md:pt-5">
                <div className="text-sm font-bold uppercase leading-5 text-brand-black group-hover:text-secondary-100">
                    {title}
                </div>
            </div>

            <div className="lg:w-150 lg:h-90 relative">
                <div className="absolute inset-0 flex items-center justify-end">
                    <UiImage
                        src={`${src[0]}?w=400&q=75`}
                        className="h-20 w-20 self-end object-cover md:h-full md:w-full md:object-contain"
                        alt={title + link}
                        width={150}
                        height={90}
                    />
                </div>
            </div>
        </Component>
    );
};

interface CollectionsProps {
    forSpecialPage?: boolean;
    items?: NavigationItem[];
}

const Collections: FC<CollectionsProps> = memo(
    ({forSpecialPage = false, items}) => {
        const {navigation} = useStore();
        const t = useTrans();
        const {isMobile} = useMobile();

        const collections = useMemo(() => {
            if (forSpecialPage) {
                return (items ?? [])
                    .filter(
                        navigationItem =>
                            Array.isArray(navigationItem.images) &&
                            navigationItem.images.length > 0
                    )
                    .map(navigationItem => ({
                        title: navigationItem.name,
                        src: navigationItem.images as string[],
                        link: navigationItem.href
                    }));
            }

            return navigation
                .filter(
                    navigationItem =>
                        navigationItem.type === 'collection' &&
                        navigationItem.section !== 'slider-collection' &&
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0 &&
                        navigationItem.depth === 0
                )
                .map(navigationItem => ({
                    title: navigationItem.name,
                    src: navigationItem.images as string[],
                    link: navigationItem.href
                }));
        }, [forSpecialPage, items, navigation]);

        return collections.length > 0 ? (
            <>
                <div className="flex items-center justify-center sm:pt-32 md:pt-5">
                    <h2
                        className={`pb-5 text-center font-dm-serif sm:pb-10 ${
                            isMobile ? 'text-3xl' : 'text-3xl'
                        } font-normal text-brand-clr sm:text-[43px]`}
                    >
                        {t('Check out what else we have!')}
                    </h2>
                </div>

                <div className="grid gap-1.5 px-6 xl:container sm:px-16 md:grid-cols-3 md:gap-4 md:px-6 lg:px-20 xl:px-32">
                    {collections.map((collection, index) => (
                        <div key={index} className="h-full w-full lg:py-2">
                            <Collection
                                title={collection.title}
                                src={collection.src}
                                link={collection.link}
                            />
                        </div>
                    ))}
                </div>
            </>
        ) : null;
    }
);

if (isDev) {
    Collections.displayName = 'Collections';
}

export default Collections;
