import {FC, memo, useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import {isDev} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {UiButton, UiForm, UiInput} from '@core/components/ui';

type CouponApplicationFormProps = {
    disabled?: boolean;
};

const CouponApplicationForm: FC<CouponApplicationFormProps> = memo(
    ({disabled}) => {
        const t = useTrans();
        const {applyCouponCode} = useCart();
        const {
            register,
            handleSubmit,
            formState: {isSubmitting},
            watch,
            reset
        } = useForm();
        const [errorMessage, setErrorMessage] = useState('');
        const couponCode = watch('couponCode');

        useEffect(() => {
            setErrorMessage('');
        }, [couponCode]);

        const onSubmit = async (data: Record<string, any>) => {
            try {
                await applyCouponCode(data.couponCode);

                reset();
            } catch (error: any) {
                setErrorMessage(t(error.message));
            }
        };

        return (
            <>
                <UiForm
                    onSubmit={handleSubmit(onSubmit)}
                    className="mt-3 flex items-center justify-between"
                >
                    <UiInput
                        className="h-10 rounded-br-none rounded-tr-none border-r-0 !ring-0 hover:border-gray-300 focus:!border-gray-300 lg:h-11 lg:w-60"
                        placeholder={t('İndirim Kodu Gir')}
                        {...register('couponCode', {required: true})}
                    />
                    <UiButton
                        className="h-10 rounded-bl-none rounded-br-md rounded-tl-none rounded-tr-md border border-gray-300 bg-brand-buttongray text-base hover:border-gray-300 hover:bg-brand-buttongray lg:h-11"
                        variant="outline"
                        loading={isSubmitting}
                        disabled={isSubmitting || disabled}
                    >
                        {t('USE')}
                    </UiButton>
                </UiForm>

                {errorMessage && (
                    <div className="mb-2 mt-1 text-xs text-red-600 xl:text-sm">
                        {errorMessage}
                    </div>
                )}
            </>
        );
    }
);

if (isDev) {
    CouponApplicationForm.displayName = 'CouponApplicationForm';
}

export default CouponApplicationForm;
