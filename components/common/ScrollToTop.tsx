import {useCallback, useEffect, useState} from 'react';
import {useTrans, useViewportSize} from '@core/hooks';
import ArrowUp from '@core/icons/outline/ArrowUp';

const ScrollToTop = () => {
    const [isVisible, setIsVisible] = useState(false);
    const t = useTrans();

    const {height} = useViewportSize();

    const scrollToTop = useCallback(() => {
        const container = document.querySelector('.content-wrapper');
        if (container === null || container === undefined) return;

        container.scrollTo({top: 0, behavior: 'smooth'});
    }, []);

    useEffect(() => {
        const container = document.querySelector('.content-wrapper');
        if (container === null || container === undefined) return;

        const toggleVisibility = () => {
            if (container?.scrollTop > height) {
                setIsVisible(true);
            } else {
                setIsVisible(false);
            }
        };

        container.addEventListener('scroll', toggleVisibility);

        return () => container.removeEventListener('scroll', toggleVisibility);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return isVisible ? (
        <button
            onClick={scrollToTop}
            className="fixed bottom-5 right-10 z-40 hidden items-center justify-center rounded-full border-2 bg-secondary-100 p-4 text-sm transition-shadow hover:shadow-md xl:inline-flex"
        >
            <ArrowUp
                aria-label={t('Scroll to top')}
                className="h-5 w-5 text-white"
            />
        </button>
    ) : null;
};

export default ScrollToTop;
