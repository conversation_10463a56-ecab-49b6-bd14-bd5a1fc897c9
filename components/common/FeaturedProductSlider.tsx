import {FC, memo, useEffect, useRef, useState} from 'react';
import {ProductListItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiLink, UiSlider} from '@core/components/ui';
import {SliderInterface} from '@core/components/ui/Slider';
import ProductCard from './ProductCard';

interface SliderProps {
    forSpecialPage?: boolean;
    products: ProductListItem[];
    detailPageLink: string;
    productSliderName?: string;
}

const FeaturedProductSlider: FC<SliderProps> = memo(
    ({products, detailPageLink, productSliderName}) => {
        const t = useTrans();

        const swiperRef = useRef<SliderInterface>();

        let link = '';

        if (detailPageLink.startsWith('/')) {
            link = detailPageLink;
        } else {
            link = `/${detailPageLink}`;
        }

        const [isSwiperInitialized, setIsSwiperInitialized] = useState(false);

        useEffect(() => {
            setIsSwiperInitialized(true);
        }, []);

        return products.length > 0 ? (
            <section className="container pt-6">
                <div className="flex items-center justify-between gap-2 pb-2 lg:justify-start">
                    <p className="font-dm-serif text-lg text-brand-black lg:text-3xl">
                        {t(productSliderName ?? 'Featured Products')}
                    </p>
                    <UiLink
                        className="text-3xs uppercase text-primary-800"
                        href={link}
                    >
                        {t('All Products')}
                        <span className="ml-1">({products.length})</span>
                    </UiLink>
                </div>

                <div className="w-full select-none rounded-lg bg-white">
                    <div className="aspect-h-7 aspect-w-8 flex items-center justify-center sm:aspect-h-4 xl:aspect-h-3 xl:aspect-w-8">
                        {isSwiperInitialized && (
                            <div>
                                <UiSlider
                                    className="h-full w-full"
                                    modules={[]}
                                    spaceBetween={12}
                                    onSwiper={swiper => {
                                        swiperRef.current = swiper;
                                    }}
                                    threshold={2}
                                    breakpoints={{
                                        300: {
                                            slidesPerView: 2
                                        },
                                        768: {
                                            slidesPerView: 3
                                        },
                                        1280: {
                                            slidesPerView: 4
                                        }
                                    }}
                                >
                                    {products.map(product => (
                                        <UiSlider.Slide
                                            className="group"
                                            key={product.productId}
                                        >
                                            <div className="">
                                                <ProductCard
                                                    className="h-full "
                                                    show={false}
                                                    product={product}
                                                    // @ts-ignore
                                                    isFake={product.isFake}
                                                />
                                            </div>
                                        </UiSlider.Slide>
                                    ))}
                                </UiSlider>
                            </div>
                        )}
                        <ul className="sr-only">
                            {products.map(product => (
                                <li key={product.productId}>{product.name}</li>
                            ))}
                        </ul>
                    </div>
                </div>
            </section>
        ) : null;
    }
);

if (isDev) {
    FeaturedProductSlider.displayName = 'FeaturedProductSlider';
}

export default FeaturedProductSlider;
