import {
    ChangeEvent<PERSON><PERSON><PERSON>,
    FC,
    FocusEventHandler,
    memo,
    useCallback,
    useEffect,
    useState
} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiInput} from '@core/components/ui';
import {MinusIcon, PlusIcon, TrashIcon} from '@core/icons/outline';
import {CartItem} from '@core/types';
import {useCart} from '@core/hooks';

export type QuantityProps = {
    className?: string;
    size?: 'sm' | 'md';
    quantity: number;
    availableQuantity: number;
    onChange: (quantity: number) => void;
    item: CartItem;
    handleRemoveItem: (item: CartItem) => void;
};

const MobileQuantity: FC<QuantityProps> = memo(props => {
    const {
        size = 'md',
        quantity = 1,
        availableQuantity = 1,
        className,
        onChange,
        item,
        handleRemoveItem
    } = props;
    const [qty, setQty] = useState(quantity.toString());
    const {removeItem} = useCart();

    const onRemoveItem = useCallback(
        (item: CartItem) => removeItem({...item}),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    useEffect(() => {
        if (qty !== quantity.toString()) {
            setQty(quantity.toString());
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [quantity]);

    const increaseQty = useCallback(() => {
        const parsed = parseFloat(qty);

        if (isNaN(parsed)) {
            return qty;
        }

        let newQty = Math.min(parsed + 1, availableQuantity);

        if (newQty < 1) {
            newQty = 1;
        }

        onChange(newQty);
        setQty(newQty.toString());
    }, [availableQuantity, qty, onChange]);
    const decreaseQty = useCallback(() => {
        const parsed = parseFloat(qty);

        if (isNaN(parsed)) {
            return qty;
        }

        const newQty = Math.max(1, parsed - 1);

        onChange(newQty);
        setQty(newQty.toString());
    }, [qty, onChange]);
    const onInputChange: ChangeEventHandler<HTMLInputElement> = useCallback(
        e => setQty(e.target.value),
        []
    );
    const onInputBlur: FocusEventHandler<HTMLInputElement> = useCallback(() => {
        let newQty = parseFloat(qty);

        if (isNaN(newQty)) {
            setQty(quantity.toString());

            return;
        }

        newQty = Math.max(1, Math.min(newQty, availableQuantity));

        onChange(newQty);
        setQty(newQty.toString());
    }, [availableQuantity, qty, quantity, onChange]);

    return (
        <UiInput.Group className={className} size={size === 'md' ? 'xl' : 'md'}>
            <UiInput.LeftAddon
                className={cls(
                    'flex cursor-pointer items-center rounded-md border-gray-200 bg-white px-2.5 text-primary-600 xl:px-4'
                )}
                onClick={() =>
                    quantity === 1 ? handleRemoveItem(item) : decreaseQty()
                }
            >
                {quantity === 1 ? (
                    <TrashIcon
                        className={cls('h-3 w-3 xl:h-4 xl:w-4', {
                            'h-3 w-3': size === 'sm'
                        })}
                    />
                ) : (
                    <MinusIcon
                        className={cls('h-3 w-3 xl:h-4 xl:w-4', {
                            'h-3 w-3': size === 'sm'
                        })}
                    />
                )}
            </UiInput.LeftAddon>

            <UiInput
                className={cls(
                    'h-9 w-14 border-gray-200  border-l-transparent border-r-transparent text-center text-primary-600 xl:h-10',
                    {
                        'w-11': size === 'sm',
                        'bg-primary-50 ': availableQuantity > 0,
                        'bg-red-100 text-red-700': availableQuantity < 1
                    }
                )}
                value={qty}
                onChange={onInputChange}
                onBlur={onInputBlur}
            />

            <UiInput.RightAddon
                className={cls(
                    'flex items-center rounded-md border-gray-200 bg-white px-2.5 text-primary-600 !shadow-none xl:px-4',
                    quantity >= availableQuantity
                        ? 'cursor-not-allowed text-gray-300'
                        : 'cursor-pointer'
                )}
                onClick={increaseQty}
            >
                <PlusIcon
                    className={cls('h-3 w-3 xl:h-4 xl:w-4', {
                        'h-3 w-3': size === 'sm'
                    })}
                />
            </UiInput.RightAddon>
        </UiInput.Group>
    );
});

if (isDev) {
    MobileQuantity.displayName = 'MobileQuantity';
}

export default MobileQuantity;
