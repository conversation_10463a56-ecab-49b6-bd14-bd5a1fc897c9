import {Fragment, useEffect, useRef, useState} from 'react';
import siteLogo from '@assets/images/common/site-logo.png';
import CookieConsent from 'react-cookie-consent';
import storeConfig from '~/store.config';
import {useTrans} from '@core/hooks';
import {MinusIcon, PlusIcon, XIcon} from '@core/icons/outline';
import {
    UiButton,
    UiDialog,
    UiDisclosure,
    UiImage,
    UiLink,
    UiSwitch,
    UiTransition
} from '@core/components/ui';

interface CookieItem {
    id: number;
    title: string;
    description: string;
    defaultChecked: boolean;
    disabled: boolean;
    checked?: boolean;
    gtmCategory: string;
}

const cookieAccordionItems: CookieItem[] = [
    {
        id: 1,
        title: 'Mandatory Identification Information',
        description:
            'These cookies are necessary for the website to function and cannot be turned off in our systems. They are usually only set to perform your transactions. These actions include your requests for services, such as setting your privacy preferences, logging in or filling out forms. You can set your browser to block or alert you about these cookies, but then some parts of the site may not work.',
        defaultChecked: true,
        disabled: true,
        checked: true,
        gtmCategory: 'ad_user_data'
    },
    {
        id: 2,
        title: 'Performance Identification Information',
        description:
            'These cookies allow us to count the number of visits to the site and the sources of traffic so that we can measure and improve the performance of our site. They help us learn which pages are most and least visited and how visitors navigate the site. All information these cookies collect is aggregated and therefore anonymous. If you do not allow these cookies, we will not know when you visit our site.',
        defaultChecked: true,
        disabled: false,
        checked: true,
        gtmCategory: 'ad_storage'
    },
    {
        id: 3,
        title: 'Cookies for Targeting Purposes',
        description:
            'These cookies are placed on our site by our advertising partners. They can be used by the companies concerned to build your interest profile and show relevant ads on other sites. They work by uniquely identifying your browser and device. If you do not allow these cookies, we cannot provide you with a tailored advertising experience across different sites.',
        defaultChecked: true,
        disabled: false,
        checked: true,
        gtmCategory: 'ad_personalization'
    },
    {
        id: 4,
        title: 'Analytics',
        description: 'These Cookies are used for Analytics purposes.',
        defaultChecked: true,
        disabled: false,
        checked: true,
        gtmCategory: 'analytics_storage'
    }
];

declare global {
    interface Window {
        dataLayer: Array<Record<string, any>>;
    }
}

const Cookie = () => {
    const [showDrawer, setShowDrawer] = useState(false);
    const [cookieItems, setCookieItems] =
        useState<CookieItem[]>(cookieAccordionItems);
    const cookieConsentRef = useRef<CookieConsent>(null);
    const t = useTrans();

    const handleSwitchChange = (id: number, value: boolean) => {
        setCookieItems(prevItems =>
            prevItems.map(item =>
                item.id === id ? {...item, checked: value} : item
            )
        );
    };

    const handleConfirmChoices = (accepted: boolean) => {
        setShowDrawer(false);

        const consent: {[key: string]: string} = {};

        cookieItems.forEach(cookieItem => {
            if (cookieItem.gtmCategory === 'ad_user_data') {
                consent[cookieItem.gtmCategory] = 'granted';
            } else {
                const cookieValue =
                    accepted && cookieItem.checked ? 'granted' : 'denied';
                consent[cookieItem.gtmCategory] = cookieValue;
            }
        });

        const cookieDate = new Date();
        cookieDate.setTime(cookieDate.getTime() + 365 * 24 * 60 * 60 * 1000);

        document.cookie = `consent=${JSON.stringify(
            consent
        )};path=/;SameSite=Lax;expires=${cookieDate.toUTCString()};`;

        if (window.dataLayer && Array.isArray(window.dataLayer)) {
            window.dataLayer.push({consent: null});
            window.dataLayer.push({
                event: 'consentUpdate',
                consent: consent
            });
        }
    };

    return (
        <>
            <CookieConsent
                ref={cookieConsentRef}
                enableDeclineButton
                disableStyles
                location="bottom"
                declineButtonText={t('I decline')}
                buttonText={t('Accept')}
                cookieName="nehir-cookie-consent"
                buttonClasses="cookie-btn-accept"
                declineButtonClasses="cookie-btn-decline"
                buttonWrapperClasses="cookie-btn-wrapper"
                containerClasses="cookie-container-custom"
                onAccept={() => handleConfirmChoices(true)}
                onDecline={() => handleConfirmChoices(false)}
            >
                <div className="flex flex-col items-start gap-4 max-lg:mt-3 lg:flex-row lg:gap-10">
                    <div>
                        <p className="mb-1.5 text-lg font-bold">
                            {t('This website uses cookies.')}
                        </p>
                        <p className="text-xs">
                            {t(
                                'Cookies are used on our website to provide you with a better service.'
                            )}
                            <span className="no-underline hover:text-primary-100 hover:underline">
                                <UiLink href="cozum-merkezi/cerez-politikasi">
                                    {t('Click for detailed information.')}
                                </UiLink>
                            </span>
                        </p>
                        <div className="flex items-center space-x-2 py-1.5">
                            <button
                                className="whitespace-nowrap text-xs font-semibold uppercase opacity-80 transition-opacity hover:opacity-100"
                                onClick={() => setShowDrawer(true)}
                            >
                                {t('Show details')}
                            </button>
                        </div>
                    </div>
                </div>
            </CookieConsent>

            <UiTransition.Root show={showDrawer} as={Fragment}>
                <UiDialog
                    as="div"
                    className="relative z-modal"
                    onClose={() => setShowDrawer(false)}
                >
                    <UiTransition.Child
                        as={Fragment}
                        enter="transition duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="transition duration-300"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-gray-900 bg-opacity-20 transition-opacity" />
                    </UiTransition.Child>

                    <div className="fixed inset-0 overflow-hidden">
                        <div className="absolute inset-0 overflow-hidden">
                            <div className="pointer-events-none fixed inset-y-0 left-0 flex max-w-full">
                                <UiTransition.Child
                                    as={Fragment}
                                    enter="transform transition duration-500"
                                    enterFrom="translate-y-full translate-x-0 xl:-translate-x-full xl:translate-y-0"
                                    enterTo="translate-y-0 xl:-translate-x-0"
                                    leave="transform transition duration-500"
                                    leaveFrom="translate-y-0 xl:-translate-x-0"
                                    leaveTo="translate-y-full translate-x-0 xl:-translate-x-full xl:translate-y-0"
                                >
                                    <UiDialog.Panel className="pointer-events-auto w-screen xl:max-w-md">
                                        <div className="flex h-full flex-col overflow-y-auto bg-white shadow-xl">
                                            <div className="hidden border-b p-6 xl:block">
                                                <div className="flex items-center justify-between">
                                                    <UiDialog.Title className="flex select-none items-center text-lg font-medium">
                                                        <UiImage
                                                            src={siteLogo}
                                                            alt="Logo"
                                                            width={parseFloat(
                                                                storeConfig.theme.logoWidth.replace(
                                                                    'px',
                                                                    ''
                                                                )
                                                            )}
                                                            height={parseFloat(
                                                                storeConfig.theme.logoHeight.replace(
                                                                    'px',
                                                                    ''
                                                                )
                                                            )}
                                                        />
                                                    </UiDialog.Title>
                                                    <div className="ml-3 flex h-8 items-center">
                                                        <button
                                                            type="button"
                                                            className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition hover:bg-gray-200 hover:text-gray-500"
                                                            onClick={() =>
                                                                setShowDrawer(
                                                                    false
                                                                )
                                                            }
                                                        >
                                                            <XIcon
                                                                className="h-5 w-5"
                                                                aria-hidden="true"
                                                            />
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex h-mobile-header w-full select-none border-b border-gray-200 bg-white xl:hidden">
                                                <div className="container">
                                                    <div className="flex h-full items-center">
                                                        <div className="flex items-center">
                                                            <button
                                                                className="font-semibold text-primary-600 transition active:opacity-30"
                                                                onClick={() =>
                                                                    setShowDrawer(
                                                                        false
                                                                    )
                                                                }
                                                            >
                                                                {t('Close')}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex-1 overflow-y-auto overflow-x-hidden">
                                                <div className="px-6 pt-4">
                                                    <p className="text-lg font-bold">
                                                        {t(
                                                            'How Our Website Uses Cookies'
                                                        )}
                                                    </p>
                                                    <p className="mt-2 text-sm">
                                                        {t(
                                                            'Our Website may request cookies to be set on your device. We use cookies to let us know when you visit our Web Site, to understand how you interact with us, to enrich and personalize your user experience, to enable social media functionality and to customize your relationship with us, including providing you with more relevant advertising. Click on the different category headings to find out more. Note that blocking some types of cookies may impact your experience on our website and the services we are able to offer.'
                                                        )}
                                                    </p>

                                                    <p className="mt-8 text-lg font-bold">
                                                        {t(
                                                            'Manage Consent Preferences'
                                                        )}
                                                    </p>
                                                </div>
                                                <div className="mt-4 flex flex-col border-b px-6 pb-2.5">
                                                    {cookieItems.map(
                                                        cookieItem => (
                                                            <UiDisclosure
                                                                key={
                                                                    cookieItem.id
                                                                }
                                                            >
                                                                {({open}) => (
                                                                    <>
                                                                        <UiDisclosure.Button className="py-2.5">
                                                                            <div className="flex items-center justify-between">
                                                                                <div className="flex items-center gap-4">
                                                                                    {!open && (
                                                                                        <PlusIcon className="h-7 w-7 rounded-full bg-gray-100 p-1.5" />
                                                                                    )}
                                                                                    {open && (
                                                                                        <MinusIcon className="h-7 w-7 rounded-full bg-gray-100 p-1.5" />
                                                                                    )}
                                                                                    <p className="text-left text-sm font-semibold">
                                                                                        {t(
                                                                                            cookieItem.title
                                                                                        )}
                                                                                    </p>
                                                                                </div>

                                                                                <UiSwitch
                                                                                    checked={
                                                                                        cookieItem.checked ||
                                                                                        false
                                                                                    }
                                                                                    onChange={(
                                                                                        e: any
                                                                                    ) =>
                                                                                        handleSwitchChange(
                                                                                            cookieItem.id,
                                                                                            e
                                                                                                .target
                                                                                                .checked
                                                                                        )
                                                                                    }
                                                                                    disabled={
                                                                                        cookieItem.disabled
                                                                                    }
                                                                                    size="xl"
                                                                                />

                                                                                <span className="sr-only">
                                                                                    {t(
                                                                                        cookieItem.title
                                                                                    )}
                                                                                </span>
                                                                            </div>
                                                                        </UiDisclosure.Button>
                                                                        <UiDisclosure.Panel className="text-sm text-gray-500">
                                                                            <p className="text-sm text-gray-600">
                                                                                {t(
                                                                                    cookieItem.description
                                                                                )}
                                                                            </p>
                                                                        </UiDisclosure.Panel>
                                                                    </>
                                                                )}
                                                            </UiDisclosure>
                                                        )
                                                    )}
                                                </div>
                                                <div className="px-6 pb-6">
                                                    <UiButton
                                                        className="text-brand-600 mt-4 focus:text-white"
                                                        onClick={() => (
                                                            handleConfirmChoices(
                                                                true
                                                            ),
                                                            cookieConsentRef.current?.accept()
                                                        )}
                                                        variant="solid"
                                                        size="lg"
                                                        color="primary"
                                                    >
                                                        {t(
                                                            'Confirm My Choices'
                                                        )}
                                                    </UiButton>
                                                </div>
                                            </div>
                                        </div>
                                    </UiDialog.Panel>
                                </UiTransition.Child>
                            </div>
                        </div>
                    </div>
                </UiDialog>
            </UiTransition.Root>
        </>
    );
};

export default Cookie;
