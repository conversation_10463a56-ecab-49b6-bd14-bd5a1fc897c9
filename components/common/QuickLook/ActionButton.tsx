import {useTrans} from '@core/hooks';
import {UiButton, UiSpinner} from '@core/components/ui';
import {EyeIcon} from '@core/icons/outline';
import {useQuickLook} from './context';

type ActionButtonProps = {
    variant?: 'icon' | 'button';
    icon?: JSX.Element;
};

const ActionButton = ({
    variant = 'icon',
    icon = <EyeIcon className="h-4 w-4" />
}: ActionButtonProps) => {
    const {status, setIsModalActive} = useQuickLook();
    const t = useTrans();

    const isLoading = status === 'pending';

    if (variant === 'icon') {
        return (
            <UiButton
                className="flex h-12 w-1/2 justify-center space-x-2 rounded-lg border-secondary-100 bg-white text-secondary-100  transition-all duration-300 ease-in-out hover:border-black  hover:bg-white hover:text-black"
                onClick={() => setIsModalActive(true)}
                disabled={isLoading}
            >
                {!isLoading ? (
                    <p className="pb-0.5 md:text-xs xl:text-xs">
                        {t('QUICK REVIEW')}
                    </p>
                ) : (
                    <div className="inline-flex h-4 w-4 items-center justify-center">
                        <UiSpinner className="transition group-hover/inner:text-white" />
                    </div>
                )}
            </UiButton>
        );
    } else if (variant === 'button') {
        return (
            <div
                className="relative isolate z-10 xl:opacity-0 xl:transition xl:group-hover:opacity-100"
                onClick={() => setIsModalActive(true)}
            >
                <UiButton
                    className="inline-flex w-full gap-3 focus:!bg-secondary-500 focus-visible:ring-2 focus-visible:ring-offset-2"
                    variant="solid"
                    color="primary"
                    size="xs"
                    leftIcon={
                        <div className="hidden sm:inline-block">{icon}</div>
                    }
                    loading={isLoading}
                    disabled={isLoading}
                >
                    {t('SEE OPTIONS')}
                </UiButton>
            </div>
        );
    } else {
        return null;
    }
};

export default ActionButton;
