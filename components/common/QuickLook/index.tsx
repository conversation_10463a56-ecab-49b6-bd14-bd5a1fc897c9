import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import ActionButton from './ActionButton';
import {QuickLookProvider} from './context';
import ProductPartial from './ProductPartial';

type QuickLookProps = {
    productSlug: string;
    variant?: 'icon' | 'button';
    icon?: JSX.Element;
};

const QuickLook: FC<QuickLookProps> = memo(({productSlug, icon, variant}) => {
    return (
        <QuickLookProvider productSlug={productSlug}>
            <ActionButton icon={icon} variant={variant} />
            <ProductPartial />
        </QuickLookProvider>
    );
});

if (isDev) {
    QuickLook.displayName = 'QuickLook';
}

export default QuickLook;
