import {Dispatch, SetStateAction} from 'react';
import {
    Campaign,
    CustomerProductPrams,
    Product,
    ProductOption,
    SelectedProduct
} from '@core/types';

export type Status = 'pending' | 'resolved' | 'rejected';

export type QuickLookContext = {
    product: Product | undefined;
    setProduct: Dispatch<SetStateAction<Product | undefined>>;
    isModalActive: boolean;
    setIsModalActive: Dispatch<SetStateAction<boolean>>;
    status: Status;
    setStatus: Dispatch<SetStateAction<Status>>;
    selectedProduct: SelectedProduct;
    isAddToCartInProgress: boolean;
    availableQuantity: number;
    inStock: boolean;
    addToCart: () => void;
    setQuantity: (quantity: number) => void;
    setAttribute: (code: string, value: string) => void;
    productOptions: ProductOption[] | undefined;
    campaigns: Campaign[];
    setCustomerProductParams: Dispatch<SetStateAction<CustomerProductPrams>>;
    customerProductParams: CustomerProductPrams;
};
