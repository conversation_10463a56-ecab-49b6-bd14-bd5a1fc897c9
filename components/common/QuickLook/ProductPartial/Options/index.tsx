import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useQuickLook} from '../../context';
import ColorOptions from './ColorOptions';
import ImageOptions from './ImageOptions';
import OtherOptions from './OtherOptions';
import SizeOptions from './SizeOptions';

const Options: FC = memo(() => {
    const {productOptions} = useQuickLook();

    return (
        <div className="mb-4 space-y-4 border-b pb-4">
            {productOptions?.map(option => (
                <div key={option.code}>
                    {option.type === 'color' && option.showVariantImage && (
                        <ImageOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'color' && !option.showVariantImage && (
                        <ColorOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'size' && (
                        <SizeOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'other' && (
                        <OtherOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                </div>
            ))}
        </div>
    );
});

if (isDev) {
    Options.displayName = 'Options';
}

export default Options;
