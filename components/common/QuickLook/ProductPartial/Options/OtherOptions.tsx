import {FC, Fragment, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiListBox, UiTransition} from '@core/components/ui';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/outline';
import {useQuickLook} from '../../context';

type OtherOptionsProps = {
    code: string;
    label: string;
    selections: {
        value: string;
        color?: string;
        inStock?: boolean;
    }[];
};

const OtherOptions: FC<OtherOptionsProps> = memo(props => {
    const {code, label, selections} = props;
    const t = useTrans();
    const {selectedProduct, setAttribute} = useQuickLook();
    const value = useMemo(
        () => (selectedProduct.attributes ?? {})[code],
        [selectedProduct, code]
    );
    const options = useMemo(
        () =>
            selections.map(selection => ({
                ...selection,
                value: selection.value,
                label: selection.value
            })),
        [selections]
    );
    const selectedLabel = useMemo(() => {
        const option = options.find(option => option.value === value);

        if (!!option) {
            return option.label;
        }

        return '';
    }, [options, value]);
    function capitalizeFirstLetter(text: string) {
        return text
            .split(' ')
            .map(
                word =>
                    word.charAt(0).toLocaleUpperCase('tr') +
                    word.slice(1).toLocaleLowerCase('tr')
            )
            .join(' ');
    }
    return (
        <>
            <h3 className="text-sm font-medium text-default">{label}</h3>

            <div className="mt-4 flex items-center space-x-3">
                <UiListBox
                    value={value}
                    onChange={(value: string) => setAttribute(code, value)}
                    as="div"
                    className="relative space-y-1"
                    style={{minWidth: '180px'}}
                >
                    {({open}) => (
                        <>
                            <UiListBox.Button
                                className={cls(
                                    'relative inline-flex h-8 w-32 cursor-pointer appearance-none items-center  py-0 pr-6 text-sm text-default transition focus:outline-none',
                                    {
                                        ' !bg-white ring-0 ': open
                                    }
                                )}
                            >
                                {!value && (
                                    <span className="truncate text-sm   text-muted">
                                        {t('Choose a sort criteria.')}
                                    </span>
                                )}
                                {!!value && (
                                    <span className="truncate font-dm-serif text-sm text-brand-black">
                                        {capitalizeFirstLetter(selectedLabel)}
                                    </span>
                                )}
                                <span className="pointer-events-none absolute right-2 ml-5 flex items-center  ">
                                    {open ? (
                                        <ChevronUpIcon
                                            className="h-3 w-3 stroke-current   text-secondary-600"
                                            aria-hidden="true"
                                            style={{strokeWidth: 60}}
                                        />
                                    ) : (
                                        <ChevronDownIcon
                                            className="h-3 w-3 stroke-current   text-secondary-600"
                                            aria-hidden="true"
                                            style={{strokeWidth: 60}}
                                        />
                                    )}
                                </span>
                            </UiListBox.Button>

                            <UiTransition
                                show={open}
                                as={Fragment}
                                enter="transition"
                                enterFrom="transform scale-95 opacity-0"
                                enterTo="transform scale-100 opacity-100"
                                leave="transition"
                                leaveFrom="transform scale-100 opacity-100"
                                leaveTo="transform scale-95 opacity-0"
                            >
                                <UiListBox.Options
                                    static
                                    className="absolute left-0 z-[2]  mt-2 max-h-64 w-32 origin-top-left overflow-auto border border-gray-200 bg-white   shadow-sm outline-none"
                                >
                                    {options.map((option, index) => (
                                        <UiListBox.Option
                                            className="relative"
                                            key={option.value}
                                            value={option.value}
                                        >
                                            {({active, selected, disabled}) => (
                                                <button
                                                    disabled={disabled}
                                                    aria-disabled={disabled}
                                                    className={cls(
                                                        'flex h-8 w-full flex-shrink-0 cursor-pointer items-center border-0 px-1 text-left text-sm   focus:outline-none ',
                                                        active && 'bg-white',
                                                        selected &&
                                                            '!bg-secondary-50',
                                                        index % 2 !== 0
                                                            ? ''
                                                            : 'bg-gray-100',
                                                        index == 0
                                                            ? '!bg-gray-100'
                                                            : ''
                                                    )}
                                                >
                                                    <span
                                                        className={cls(
                                                            'block flex-1 truncate font-dm-serif   text-brand-black',
                                                            selected
                                                                ? 'font-medium '
                                                                : 'font-normal',
                                                            index == 0
                                                                ? '!text-muted'
                                                                : ''
                                                        )}
                                                    >
                                                        {capitalizeFirstLetter(
                                                            t(option.label)
                                                        )}
                                                    </span>
                                                    {selected && (
                                                        <span
                                                            className="absolute -left-1 h-6 rounded-full bg-gray-100  "
                                                            style={{
                                                                width: 2
                                                            }}
                                                        ></span>
                                                    )}
                                                </button>
                                            )}
                                        </UiListBox.Option>
                                    ))}
                                </UiListBox.Options>
                            </UiTransition>
                        </>
                    )}
                </UiListBox>
            </div>
        </>
    );
});

if (isDev) {
    OtherOptions.displayName = 'OtherOptions';
}

export default OtherOptions;
