import {FC, memo, useCallback, useState} from 'react';
import {isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiButton, UiSpinner} from '@core/components/ui';
import {HeartIcon as HeartSolidIcon} from '@core/icons/solid';
import {useQuickLook} from '../context';
import Quantity from '@components/pages/store/Product/Quantity';
import {HeartIcon} from '@components/icons';

const Actions: FC = memo(() => {
    const t = useTrans();
    const {
        currency,
        addToFavorites,
        removeFromFavorites,
        addToCollection,
        removeFromCollection
    } = useStore();

    const {
        selectedProduct,
        setQuantity,
        isAddToCartInProgress,
        addToCart,
        availableQuantity,
        inStock,
        customerProductParams,
        setCustomerProductParams
    } = useQuickLook();

    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);

    const onAddToFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await addToFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: true
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        addToFavorites,
        selectedProduct
    ]);
    const onRemoveFromFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await removeFromFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: false
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        removeFromFavorites,
        selectedProduct
    ]);

    const [
        isAlarmCollectionUpdateInProgress,
        setIsAlarmCollectionUpdateInProgress
    ] = useState(false);

    const onAddToAlarmCollection = useCallback(async () => {
        if (isAlarmCollectionUpdateInProgress) {
            return;
        }

        setIsAlarmCollectionUpdateInProgress(true);

        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'add_to_wishlist',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: selectedProduct.salesPrice - selectedProduct.discount,
                items: [
                    {
                        item_id: selectedProduct.code,
                        item_name: selectedProduct.definition,
                        discount: selectedProduct.discount,
                        item_brand: selectedProduct.brandName,
                        item_category: selectedProduct.categoryName,
                        price: selectedProduct.salesPrice
                    }
                ]
            }
        });
        // ----------------------------------------

        const result = await addToCollection(
            {
                id: 'is-alarm',
                isAlarm: true
            },
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isInAlarmCollection: true
            });
        }
        setIsAlarmCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isAlarmCollectionUpdateInProgress,
        addToCollection,
        selectedProduct,
        currency.name
    ]);
    const onRemoveFromAlarmCollection = useCallback(async () => {
        if (isAlarmCollectionUpdateInProgress) {
            return;
        }

        setIsAlarmCollectionUpdateInProgress(true);

        const result = await removeFromCollection(
            {
                id: 'is-alarm',
                isAlarm: true
            },
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isInAlarmCollection: false
            });
        }
        setIsAlarmCollectionUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isAlarmCollectionUpdateInProgress,
        removeFromCollection,
        selectedProduct
    ]);

    return (
        <div className=" pb-3">
            {inStock ? (
                <div className="flex select-none items-center gap-4">
                    <Quantity
                        quantity={selectedProduct.quantity}
                        availableQuantity={availableQuantity}
                        onChange={quantity => setQuantity(quantity)}
                    />

                    <UiButton
                        className="h-12 w-56 flex-1 rounded-none border-secondary-100 bg-secondary-100 text-sm font-bold text-white transition-all duration-300 ease-in-out hover:bg-black hover:text-white focus:border-black focus:bg-black focus:ring-black"
                        loading={isAddToCartInProgress}
                        disabled={!inStock}
                        onClick={addToCart}
                    >
                        {t('ADD TO CART')}
                    </UiButton>

                    {customerProductParams.isFavorite ? (
                        !isFavoriteUpdateInProgress ? (
                            <UiButton
                                className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white "
                                onClick={onRemoveFromFavorites}
                            >
                                <HeartSolidIcon className="mr-1 h-4 w-4 stroke-current stroke-[20px] text-secondary-600" />
                                <div className="truncate text-xs font-medium   ">
                                    {t('Remove From Favorites')}
                                </div>
                            </UiButton>
                        ) : (
                            <UiButton className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white ">
                                <div className="mr-1 w-4">
                                    <UiSpinner size="sm" />
                                </div>
                                <div className="truncate text-xs font-medium">
                                    {t('Remove From Favorites')}
                                </div>
                            </UiButton>
                        )
                    ) : !isFavoriteUpdateInProgress ? (
                        <UiButton
                            className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white "
                            onClick={onAddToFavorites}
                        >
                            <HeartIcon className="mr-1 h-4 w-4  stroke-secondary-100 " />
                            <div className="truncate text-xs font-medium">
                                {t('Add To Favorites')}
                            </div>
                        </UiButton>
                    ) : (
                        <UiButton className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white ">
                            <div className="mr-1 w-4">
                                <UiSpinner size="sm" />
                            </div>
                            <div className="truncate text-xs font-medium ">
                                {t('Add To Favorites')}
                            </div>
                        </UiButton>
                    )}
                </div>
            ) : (
                <div className="flex select-none items-center gap-4">
                    <Quantity
                        quantity={selectedProduct.quantity}
                        availableQuantity={availableQuantity}
                        onChange={quantity => setQuantity(quantity)}
                    />

                    <UiButton
                        className="h-12 w-56 flex-1 rounded-none border-secondary-100 bg-secondary-100 text-sm font-bold text-white transition-all duration-300 ease-in-out hover:bg-black hover:text-white focus:border-black focus:bg-black focus:ring-black active:bg-secondary-100"
                        loading={isAddToCartInProgress}
                        disabled={!inStock}
                        onClick={addToCart}
                    >
                        {t('Out Of Stock')}
                    </UiButton>

                    {customerProductParams.isFavorite ? (
                        !isFavoriteUpdateInProgress ? (
                            <UiButton
                                className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white "
                                onClick={onRemoveFromFavorites}
                            >
                                <HeartSolidIcon className="mr-1 h-4 w-4 stroke-current stroke-[20px] text-secondary-600" />
                                <div className="truncate text-xs font-medium   ">
                                    {t('Remove From Favorites')}
                                </div>
                            </UiButton>
                        ) : (
                            <UiButton className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white ">
                                <div className="mr-1 w-4">
                                    <UiSpinner size="sm" />
                                </div>
                                <div className="truncate text-xs font-medium">
                                    {t('Remove From Favorites')}
                                </div>
                            </UiButton>
                        )
                    ) : !isFavoriteUpdateInProgress ? (
                        <UiButton
                            className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white "
                            onClick={onAddToFavorites}
                        >
                            <HeartIcon className="mr-1 h-4 w-4  stroke-secondary-100 " />
                            <div className="truncate text-xs font-medium">
                                {t('Add To Favorites')}
                            </div>
                        </UiButton>
                    ) : (
                        <UiButton className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white ">
                            <div className="mr-1 w-4">
                                <UiSpinner size="sm" />
                            </div>
                            <div className="truncate text-xs font-medium ">
                                {t('Add To Favorites')}
                            </div>
                        </UiButton>
                    )}
                </div>
            )}
        </div>
    );
});

if (isDev) {
    Actions.displayName = 'Actions';
}

export default Actions;
