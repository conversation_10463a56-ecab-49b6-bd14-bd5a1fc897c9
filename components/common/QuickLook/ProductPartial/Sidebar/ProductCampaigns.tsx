import {memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useProduct from '@core/pages/store/Product/useProduct';
import {LuPackage} from '@components/icons';

const ProductCampaigns = memo(() => {
    const t = useTrans();
    const {campaigns} = useProduct();

    return (
        <div>
            {Array.isArray(campaigns) && campaigns.length > 0 && (
                <div className="px-4 py-1 text-xs xl:px-2">
                    {campaigns.map(campaign => (
                        <div
                            key={campaign.id}
                            className="flex items-center gap-2"
                        >
                            <LuPackage />
                            <p className="text-xs font-semibold text-brand-clr">
                                {campaign.description}
                            </p>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
});

if (isDev) {
    ProductCampaigns.displayName = 'ProductCampaigns';
}

export default ProductCampaigns;
