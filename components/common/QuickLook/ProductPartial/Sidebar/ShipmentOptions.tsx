import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {TruckFastIcon} from '@core/icons/solid';
import {useQuickLook} from '../../context';

const ShipmentOptions: FC = memo(() => {
    const t = useTrans();
    const {locale} = useStore();
    const {product} = useQuickLook();

    const getEstimatedShippingDate = useMemo(() => {
        const currentDate = new Date();
        currentDate.setDate(
            currentDate.getDate() + (product?.estimatedDeliveryDuration ?? 2)
        );

        const formatter = new Intl.DateTimeFormat(locale, {
            day: 'numeric',
            month: 'long'
        });
        return formatter.format(currentDate);
    }, [locale, product?.estimatedDeliveryDuration]);

    return typeof product?.estimatedDeliveryDuration === 'number' ? (
        <div>
            <div className="pt-1 text-xs">
                {product?.estimatedDeliveryDuration === 1 && (
                    <div className="flex items-center gap-3 rounded bg-green-50 p-2 max-xl:border max-xl:border-green-600">
                        <TruckFastIcon className="h-4 w-4 text-green-600" />
                        <p className="w-full text-green-600">
                            {t('If you order now, we will ship it tomorrow!')}
                        </p>
                    </div>
                )}
                {product?.estimatedDeliveryDuration >= 2 && (
                    <div className="flex items-center gap-3">
                        <TruckFastIcon className="h-4 w-4 text-brand-clr" />
                        <p
                            dangerouslySetInnerHTML={{
                                __html: t(
                                    'We will ship it on {getEstimatedShippingDate}!',
                                    {getEstimatedShippingDate}
                                )
                            }}
                        />
                    </div>
                )}
            </div>
        </div>
    ) : null;
});

if (isDev) {
    ShipmentOptions.displayName = 'ShipmentOptions';
}

export default ShipmentOptions;
