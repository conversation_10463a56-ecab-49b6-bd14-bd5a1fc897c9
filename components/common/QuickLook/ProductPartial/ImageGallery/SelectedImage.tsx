import {
    FC,
    Fragment,
    memo,
    MouseEventHandler,
    MutableRefObject,
    useCallback,
    useEffect,
    useRef,
    useState
} from 'react';
import dynamic from 'next/dynamic';
import {cls, isDev} from '@core/helpers';
import {useScrollLock} from '@core/hooks';
import {UiImage, UiPortal, UiTransition} from '@core/components/ui';
import {getLensPosition} from './utils';

const Lightbox = dynamic(() => import('@core/components/ui/Lightbox'));

type SelectedImageProps = {
    src: string;
    alt: string;
    preload: boolean;
    containerRef: MutableRefObject<HTMLDivElement>;
    containerWidth: number;
    containerHeight: number;
    isSliding: boolean;
    images: string[];
};

const SelectedImage: FC<SelectedImageProps> = memo(props => {
    const {
        src,
        alt,
        preload,
        containerRef,
        containerWidth,
        containerHeight,
        isSliding,
        images
    } = props;
    const [isImageLoaded, setIsImageLoaded] = useState(false);
    const lens = useRef<HTMLDivElement>(null);
    const [[lensWidth, lensHeight], setLensSize] = useState([0, 0]);
    const [[lensX, lensY], setLensPos] = useState([0, 0]);
    const [isZoomShown, setIsZoomShown] = useState(false);
    const [[zoomX, zoomY], setZoomPos] = useState([0, 0]);
    const zoomLevel = 3;

    const [, setScrollLocked] = useScrollLock();
    const [isLightboxOpened, setIsLightboxOpened] = useState(false);
    const [lightboxImageIndex, setLightboxImageIndex] = useState(0);
    useEffect(() => {
        setLightboxImageIndex(images.findIndex(image => image === src));
    }, [src, images]);

    // Set lens size and zoom positions.
    useEffect(() => {
        setLensSize([containerWidth / zoomLevel, containerHeight / zoomLevel]);

        const containerRect = containerRef.current.getBoundingClientRect();
        setZoomPos([
            containerRect.left + containerWidth + 32,
            containerRect.top
        ]);
    }, [containerRef, containerWidth, containerHeight, zoomLevel]);
    useEffect(() => {
        const handleScroll = () => {
            const containerRect = containerRef.current?.getBoundingClientRect();

            if (!!containerRect) {
                setZoomPos([
                    containerRect.left + containerWidth + 32,
                    containerRect.top
                ]);
            }
        };

        const contentWrapper = document.querySelector('.content-wrapper');

        if (contentWrapper !== null) {
            contentWrapper.addEventListener('scroll', handleScroll);
        }

        return () => {
            if (contentWrapper !== null) {
                contentWrapper.removeEventListener('scroll', handleScroll);
            }
        };
    }, [containerRef, containerWidth, containerHeight]);

    const timerRef = useRef<any>(null);
    // Events.
    const onMouseEnter: MouseEventHandler<HTMLDivElement> = useCallback(
        e => {
            if (!isImageLoaded) {
                return;
            }

            clearTimeout(timerRef.current);

            timerRef.current = setTimeout(() => {
                setIsZoomShown(true);
            }, 200);
        },
        [isImageLoaded]
    );

    const onMouseLeave: MouseEventHandler<HTMLDivElement> = useCallback(e => {
        clearTimeout(timerRef.current);
        setIsZoomShown(false);
    }, []);

    const onMouseMove: MouseEventHandler<HTMLDivElement> = useCallback(
        e => {
            if (!containerRef.current || !lens.current || !isImageLoaded) {
                return;
            }

            clearTimeout(timerRef.current);

            if (!isZoomShown) {
                timerRef.current = setTimeout(() => {
                    setIsZoomShown(true);
                }, 200);
            }

            const {x, y} = getLensPosition(
                containerRef.current,
                lens.current,
                e
            );
            setLensPos([x, y]);
        },
        [containerRef, isImageLoaded, isZoomShown]
    );

    return (
        <>
            <div
                onMouseEnter={onMouseEnter}
                onMouseLeave={onMouseLeave}
                onMouseMove={onMouseMove}
                className="rounded-md"
            >
                <UiImage
                    className="cursor-zoom-in rounded-md"
                    src={`${src}?w=540&q=95`}
                    alt={alt}
                    priority={preload}
                    fill
                    fit="cover"
                    position="center"
                    onLoadingComplete={() => setIsImageLoaded(true)}
                    onClick={() => {
                        setScrollLocked(true);
                        setIsLightboxOpened(true);
                    }}
                />

                <div
                    ref={lens}
                    className={cls(
                        'image-zoom-lens pointer-events-none absolute rounded-md bg-white bg-opacity-0 shadow-none transition',
                        {
                            '!bg-opacity-40 !shadow': isZoomShown && !isSliding
                        }
                    )}
                    style={{
                        top: `${lensY}px`,
                        left: `${lensX}px`,
                        width: `${lensWidth}px`,
                        height: `${lensHeight}px`
                    }}
                />
            </div>

            <UiPortal>
                <UiTransition
                    show={isZoomShown && !isSliding}
                    as={Fragment}
                    enter="ease-in-out duration-150"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-150"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div
                        className="fixed z-10 overflow-hidden rounded-md shadow"
                        style={{
                            width: `${containerWidth + 1}px`,
                            height: `${containerHeight}px`,
                            left: `${zoomX - 1}px`,
                            top: `${zoomY}px`
                        }}
                    >
                        <div
                            className="absolute right-auto rounded-md"
                            style={{
                                top: -(lensY * zoomLevel),
                                left: -(lensX * zoomLevel),
                                width: containerWidth * zoomLevel,
                                height: containerHeight * zoomLevel
                            }}
                        >
                            <div className="relative h-full w-full overflow-hidden">
                                <UiImage
                                    src={`${src}?w=640&q=100`}
                                    alt={alt}
                                    fill
                                    fit="cover"
                                    position="center"
                                    className="rounded-md"
                                    priority
                                />
                            </div>
                        </div>
                    </div>
                </UiTransition>
            </UiPortal>

            {isLightboxOpened && (
                <Lightbox
                    reactModalStyle={{overlay: {zIndex: 1700}}}
                    mainSrc={images[lightboxImageIndex]}
                    nextSrc={images[(lightboxImageIndex + 1) % images.length]}
                    prevSrc={
                        images[
                            (lightboxImageIndex + images.length - 1) %
                                images.length
                        ]
                    }
                    imageTitle={alt}
                    imagePadding={96}
                    onCloseRequest={() => {
                        setIsLightboxOpened(false);
                        setScrollLocked(false);
                        setLightboxImageIndex(0);
                    }}
                    onMovePrevRequest={() =>
                        setLightboxImageIndex(
                            (lightboxImageIndex + images.length - 1) %
                                images.length
                        )
                    }
                    onMoveNextRequest={() =>
                        setLightboxImageIndex(
                            (lightboxImageIndex + 1) % images.length
                        )
                    }
                />
            )}
        </>
    );
});

if (isDev) {
    SelectedImage.displayName = 'SelectedImage';
}

export default SelectedImage;
