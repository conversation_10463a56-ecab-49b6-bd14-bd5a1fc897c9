import {MouseEvent} from 'react';

export function getLensPosition(
    container: HTMLDivElement,
    lens: HTMLDivElement,
    e: MouseEvent
) {
    const rect = container.getBoundingClientRect();
    let x = 0;
    let y = 0;

    x = e.pageX - rect.left;
    y = e.pageY - rect.top;
    x = x - window.scrollX;
    y = y - window.scrollY;
    x = x - lens.offsetWidth / 2;
    y = y - lens.offsetHeight / 2;
    if (x > rect.width - lens.offsetWidth) {
        x = rect.width - lens.offsetWidth;
    }
    if (x < 0) {
        x = 0;
    }
    if (y > rect.height - lens.offsetHeight) {
        y = rect.height - lens.offsetHeight;
    }
    if (y < 0) {
        y = 0;
    }

    return {x, y};
}
