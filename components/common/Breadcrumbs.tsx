import {FC, memo} from 'react';
import {Breadcrumb} from '@core/types';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiLink} from '@core/components/ui';
import {ChevronRight} from '@components/icons';

export type BreadcrumbsProps = {
    breadcrumbs: Breadcrumb[];
    border?: boolean;
};

const Breadcrumbs: FC<BreadcrumbsProps> = memo(({breadcrumbs}) => {
    const t = useTrans();

    return (
        <nav
            className="breadcrumbs w-full overflow-x-auto py-3 xl:container md:py-10"
            aria-label="Breadcrumb"
        >
            <ol
                className="breadcrumbs-list flex items-center space-x-2"
                role="list"
            >
                {breadcrumbs.map(breadcrumb =>
                    !!breadcrumb.href ? (
                        <li
                            className="breadcrumbs-item flex-shrink-0"
                            key={breadcrumb.slug}
                        >
                            <div className="flex items-center">
                                <UiLink
                                    href={breadcrumb.href}
                                    className="breadcrumbs-link mr-2 text-2xs font-bold uppercase text-brand-clr transition-colors duration-100 hover:text-current md:text-xs"
                                >
                                    {breadcrumb.name === 'Home'
                                        ? t('Home')
                                        : breadcrumb.name}
                                </UiLink>

                                <ChevronRight
                                    className="h-3 w-3 stroke-current !text-brand-clr"
                                    style={{strokeWidth: 10}}
                                />
                            </div>
                        </li>
                    ) : (
                        <li
                            className="breadcrumbs-item flex-shrink-0"
                            key={breadcrumb.slug}
                        >
                            <div
                                aria-current="page"
                                className="breadcrumbs-current text-2xs font-bold uppercase text-secondary-900 md:text-xs"
                            >
                                {breadcrumb.name}
                            </div>
                        </li>
                    )
                )}
            </ol>
        </nav>
    );
});

if (isDev) {
    Breadcrumbs.displayName = 'Breadcrumbs';
}

export default Breadcrumbs;
