import {FC, memo, useState, useCallback} from 'react';
import {isDev} from '@core/helpers';
import {useMobile, useTrans} from '@core/hooks';

import {UiTab} from '@core/components/ui';
import {ArrowLeftIcon, ChevronRightIcon} from '@core/icons/outline';
import {useRouter} from 'next/router';
import Seo from '@components/common/Seo';
import SignIn from '@components/auth/SignIn';
import SignUp from '@components/auth/SignUp';
import ForgotPassword from '@components/auth/ForgotPassword';

type AuthProps = {
    termsOfMembershipText?: string;
    clarificationText?: string;
    redirect?: string;
};

const Auth: FC<AuthProps> = memo(props => {
    const {isMobile} = useMobile();
    const {termsOfMembershipText, clarificationText, redirect} = props;

    const [activeTab, setActiveTab] = useState('signIn');
    const [isForgotPasswordShown, setIsForgotPasswordShown] = useState(false);
    const t = useTrans();
    const router = useRouter();
    const isGuestCheckoutEnabled = router.asPath.includes('checkout');
    const redirectToGuestCheckout = useCallback(() => {
        router.push(`/checkout?t=${Date.now()}`);
    }, [router]);

    const handleForgotPasswordClick = () => {
        setIsForgotPasswordShown(!isForgotPasswordShown);
    };
    return (
        <>
            <Seo title={activeTab === 'signIn' ? t('Sign In') : t('Sign Up')} />
            <div
                className={`flex ${
                    isMobile ? 'h-[100vh]' : 'h-[91vh]'
                } w-full items-start justify-center overflow-y-scroll bg-white pb-10 md:h-[80vh] lg:h-[58vh] xl:!h-full`}
            >
                <div
                    className={`relative flex w-full flex-col justify-start p-8 ${
                        isForgotPasswordShown ? 'lg:w-5/12' : 'lg:w-[36rem]'
                    } xl:p-16`}
                >
                    {!isForgotPasswordShown ? (
                        <h2 className="mb-4 pb-3 text-center font-dm-serif text-4xl font-bold text-brand-black">
                            {t('Welcome!')}
                        </h2>
                    ) : (
                        <>
                            <h2 className="mb-4 pb-3 text-center font-dm-serif text-4xl font-bold text-brand-black">
                                {t('Forgot Password')}
                            </h2>
                        </>
                    )}
                    {!isForgotPasswordShown ? (
                        <>
                            {activeTab === 'signIn' ? (
                                <>
                                    <p className="mb-4 text-center text-sm text-gray-800">
                                        {t(
                                            'Please sign in using your e-mail address and password.'
                                        )}
                                    </p>
                                    {isGuestCheckoutEnabled && (
                                        <button
                                            className="mb-4 inline-flex items-center justify-center gap-2.5 text-sm"
                                            onClick={redirectToGuestCheckout}
                                        >
                                            <span>
                                                {t('Shop without membership')}
                                            </span>
                                            <ChevronRightIcon className="h-3 w-3" />
                                        </button>
                                    )}
                                </>
                            ) : (
                                <>
                                    <p className="mb-4 text-center text-sm text-gray-800">
                                        {t(
                                            'Please make sure your e-mail address and password are correct.'
                                        )}
                                    </p>
                                    {isGuestCheckoutEnabled && (
                                        <button
                                            className="mb-4 inline-flex items-center justify-center gap-2.5 text-sm"
                                            onClick={redirectToGuestCheckout}
                                        >
                                            <span>
                                                {t('Shop without membership')}
                                            </span>
                                            <ChevronRightIcon className="h-3 w-3" />
                                        </button>
                                    )}
                                </>
                            )}

                            <UiTab.Group
                                as="div"
                                className="flex flex-col "
                                defaultIndex={0}
                                // @ts-ignore
                                onChange={tabIndex =>
                                    setActiveTab(
                                        tabIndex === 0 ? 'signIn' : 'signUp'
                                    )
                                }
                            >
                                <div
                                    className={`px-6 ${
                                        isMobile ? 'pb-6' : 'pb-6'
                                    }`}
                                >
                                    <UiTab.List
                                        aria-label="tabs"
                                        className="flex flex-row rounded border-2 border-gray-100 bg-brand-search p-0.5 "
                                    >
                                        <UiTab
                                            className="
                                    flex flex-1 cursor-pointer items-center justify-center whitespace-nowrap rounded
                                    bg-transparent py-1 text-sm
                                    font-bold text-brand-clr hover:bg-white
                                    focus:outline-none selected:bg-white
                                    "
                                        >
                                            {t('Sign In')}
                                        </UiTab>
                                        <UiTab
                                            className="
                                    flex flex-1 cursor-pointer items-center justify-center whitespace-nowrap rounded
                                    bg-transparent py-2.5 text-sm
                                    font-bold text-brand-clr hover:bg-white
                                    focus:outline-none selected:bg-white
                                    "
                                        >
                                            {t('Sign Up')}
                                        </UiTab>
                                    </UiTab.List>
                                </div>

                                <UiTab.Panels className="mt-6">
                                    <UiTab.Panel>
                                        <SignIn
                                            redirect={redirect}
                                            onForgotPasswordClick={
                                                handleForgotPasswordClick
                                            }
                                        />
                                    </UiTab.Panel>

                                    <UiTab.Panel>
                                        <SignUp
                                            termsOfMembershipText={
                                                termsOfMembershipText
                                            }
                                            clarificationText={
                                                clarificationText
                                            }
                                            redirect={redirect}
                                        />
                                    </UiTab.Panel>
                                </UiTab.Panels>
                            </UiTab.Group>
                        </>
                    ) : (
                        <>
                            <div
                                className="absolute left-6 top-6 hidden h-9 w-9 cursor-pointer items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 xl:flex"
                                onClick={() => setIsForgotPasswordShown(false)}
                            >
                                <ArrowLeftIcon className="h-4 w-4 text-muted" />
                            </div>

                            <div className="text-xs font-bold text-brand-black">
                                {t(
                                    'A security link will be sent to your e-mail address to reset your password.'
                                )}
                            </div>

                            <div className=" pt-10">
                                <ForgotPassword redirect={redirect} />
                            </div>
                        </>
                    )}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    Auth.displayName = 'Auth';
}

export default Auth;
