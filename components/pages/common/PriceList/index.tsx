import Breadcrumbs from '@components/common/Breadcrumbs';
import {onSubmitHandler} from '@components/helpers/onSubmitHandler';
import {RequestData} from '@components/helpers/requestData';
import {UiButton, UiForm} from '@core/components/ui';

import {regexp} from '@core/helpers';
import {useTrans} from '@core/hooks';

import React, {useCallback, useMemo, useState} from 'react';
import {useForm} from 'react-hook-form';

type Props = {
    countries: Record<string, any>[];
};

const PriceListPage = (props: Props) => {
    const {countries} = props;
    const t = useTrans();
    const defaultCountry = useMemo(
        () =>
            countries.find(country => !!country.isDefault) as Record<
                string,
                any
            >,
        [countries]
    );

    const {
        register,
        formState: {errors, isValid},
        handleSubmit,
        reset
    } = useForm({
        mode: 'onChange',
        defaultValues: {
            name: '',
            lastName: '',
            email: '',
            companyName: '',
            phone: '',
            message: '',
            countryId: ''
        }
    });

    const [isTrue, setIsTrue] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const onSubmit = async (data: any) => {
        if (isSubmitting) return;
        setIsSubmitting(true);

        let nameAndSurname = `${data.name} ${data.lastName}`;
        const requestData = await RequestData(
            nameAndSurname,
            data.email,
            data.phone || '-',
            data.message || 'Fiyat Listesi Kataloğu'
        );
        const extra = [
            {
                label: 'Firma Adı',
                value: data.companyName
            },
            {
                label: 'Ülke',
                value: data.countryId
            }
        ];

        try {
            await onSubmitHandler(requestData, extra);

            reset({
                name: '',
                lastName: '',
                email: '',
                companyName: '',
                phone: '',
                message: '',
                countryId: ''
            });
            setIsTrue(true);
            const isMobile = /iPhone|iPad|iPod|Android/i.test(
                navigator.userAgent
            );

            const pdfUrl = '/files/Nehir-Export-Fiyat-Listesi-19-09-24.pdf';

            if (isMobile) {
                const link = document.createElement('a');
                link.href = pdfUrl;
                link.setAttribute(
                    'download',
                    'Nehir-Export-Fiyat-Listesi-19-09-24.pdf'
                );
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                window.open(pdfUrl, '_blank');
            }
        } catch (error) {
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="container">
            <Breadcrumbs
                breadcrumbs={[
                    {name: 'Nehir', href: '/', slug: '/'},
                    {name: 'Price Catalog', slug: '/price-list-2024-export'}
                ]}
            />

            <div>
                <h1 className="text-center font-dm-serif text-[33px]">
                    Price List Catalog
                </h1>

                <div className=" my-12 grid place-items-center">
                    {isTrue && (
                        <div className="max-w-lg px-4">
                            <div className="  rounded bg-green-500 px-2 py-3 text-center text-sm text-green-100 shadow">
                                * You have successfully viewed the Price
                                catalog. Please fill in the form to view it
                                again.
                            </div>
                        </div>
                    )}
                    <div className="flex  max-w-2xl place-items-center  items-center gap-12 ">
                        <div className="grid gap-4 rounded  px-6 py-10 lg:p-16">
                            <div>
                                <p className="text-xs">
                                    Please fill in the required fields to
                                    download the price list catalog.
                                </p>
                            </div>
                            <UiForm onSubmit={handleSubmit(onSubmit)}>
                                <div className="space-y-5">
                                    <div className="flex flex-col gap-5 lg:flex-row">
                                        <div className="custom-form-input-2 h-14 lg:w-1/2">
                                            <UiForm.Field
                                                rightElement={
                                                    <p className="mb-4 ml-5 text-secondary-100">
                                                        *
                                                    </p>
                                                }
                                                label={'Name'}
                                                size="sm"
                                                error={
                                                    errors.name &&
                                                    errors.name.type ===
                                                        'required'
                                                        ? t(
                                                              'First name is required'
                                                          )
                                                        : undefined
                                                }
                                                {...register('name', {
                                                    required: true
                                                })}
                                            />
                                        </div>
                                        <div className="custom-form-input-2 custom-form h-14 lg:w-1/2">
                                            <UiForm.Field
                                                label={t('Surname')}
                                                rightElement={
                                                    <p className="mb-4 ml-5 text-secondary-100">
                                                        *
                                                    </p>
                                                }
                                                error={
                                                    errors.lastName &&
                                                    errors.lastName.type ===
                                                        'required'
                                                        ? t(
                                                              'Last name is required'
                                                          )
                                                        : undefined
                                                }
                                                {...register('lastName', {
                                                    required: true
                                                })}
                                            />
                                        </div>
                                    </div>
                                    <div className="custom-form-input-2 h-14">
                                        <UiForm.Field
                                            className="mt-3"
                                            label={'Email address'}
                                            rightElement={
                                                <p className="mb-4 ml-5 text-secondary-100">
                                                    *
                                                </p>
                                            }
                                            error={
                                                errors.email &&
                                                errors.email.type === 'required'
                                                    ? t(
                                                          'Email address is required'
                                                      )
                                                    : errors.email &&
                                                      errors.email.type ===
                                                          'pattern'
                                                    ? t(
                                                          'Email address is invalid'
                                                      )
                                                    : undefined
                                            }
                                            {...register('email', {
                                                required: true,
                                                pattern: regexp.email
                                            })}
                                        />
                                    </div>

                                    <div className="custom-form-input-2 h-14">
                                        <UiForm.Field
                                            label={'Country'}
                                            {...register('countryId', {
                                                required: true
                                            })}
                                            selection
                                            lang="en"
                                        >
                                            {countries.map((country: any) => (
                                                <option
                                                    key={country.name}
                                                    value={country.name}
                                                >
                                                    {country.name}
                                                </option>
                                            ))}
                                        </UiForm.Field>
                                    </div>

                                    <div className="custom-form-input-2 h-14">
                                        <UiForm.Field
                                            className="mt-3"
                                            label={t('Company Name')}
                                            rightElement={
                                                <p className="mb-4 ml-5 text-secondary-100">
                                                    *
                                                </p>
                                            }
                                            error={
                                                errors.companyName &&
                                                errors.companyName.type ===
                                                    'required'
                                                    ? t(
                                                          'Company name is required'
                                                      )
                                                    : undefined
                                            }
                                            {...register('companyName', {
                                                required: true
                                            })}
                                        />
                                    </div>
                                </div>

                                <UiButton
                                    className="mt-4 h-14 w-full rounded bg-secondary-800 font-hurme font-semibold  text-base text-white hover:bg-secondary-800 hover:text-white focus:border-0 focus:bg-secondary-800 focus:text-white focus:ring-0"
                                    type="submit"
                                    disabled={!isValid || isSubmitting}
                                    size="xl"
                                >
                                    {t('Download Price List')}
                                </UiButton>
                            </UiForm>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PriceListPage;
