import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {UiSlider} from '@core/components/ui';
import {StarIcon} from '@core/icons/solid';
import {useStore} from '@core/hooks';

const Comments: FC = memo(() => {
    const {navigation} = useStore();
    const commentItems = useMemo(() => {
        return navigation
            .filter(
                navigationItem =>
                    navigationItem.type === 'story' &&
                    navigationItem.section === 'home-page-reviews'
            )
            .map(navigationItem => ({
                content: navigationItem.description,
                person: navigationItem.name,
                link: navigationItem.href
            }));
    }, [navigation]);

    return (
        commentItems.length > 0 && (
            <div className="container mt-8 py-16 lg:mt-16">
                <UiSlider loop spaceBetween={10}>
                    {commentItems.map((comment, index) => (
                        <UiSlider.Slide key={index}>
                            <div className="flex flex-col items-center gap-2">
                                <div className="flex space-x-1">
                                    <StarIcon className="h-4 w-4 text-secondary-100" />
                                    <StarIcon className="h-4 w-4 text-secondary-100" />
                                    <StarIcon className="h-4 w-4 text-secondary-100" />
                                    <StarIcon className="h-4 w-4 text-secondary-100" />
                                    <StarIcon className="h-4 w-4 text-secondary-100" />
                                </div>
                                <p className="max-w-xl text-center font-dm-serif text-lg text-brand-black md:text-2xl">
                                    {comment.content}
                                </p>
                                <p className="font-bold text-secondary-100">
                                    {comment.person}
                                </p>
                            </div>
                        </UiSlider.Slide>
                    ))}
                </UiSlider>
            </div>
        )
    );
});

if (isDev) {
    Comments.displayName = 'Comments';
}

export default Comments;
