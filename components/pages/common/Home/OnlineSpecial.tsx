import {FC, memo, useEffect, useMemo, useState} from 'react';
import {isDev} from '@core/helpers';
import {UiButton, UiImage, UiLink} from '@core/components/ui';
import leftImage from '@assets/images/common/online-special/left.webp';
import rightImage from '@assets/images/common/online-special/right.webp';
import iconN from '@assets/images/common/online-special/icon-n.svg';
import ring from '@assets/images/common/online-special/ring.svg';
import BlackBackground from '@assets/images/common/online-special/black.png';
import {useStore, useTrans} from '@core/hooks';

const OnlineSpecial: FC = memo(() => {
    const [rotation, setRotation] = useState(0);
    const {navigation} = useStore();
    const t = useTrans();

    useEffect(() => {
        const intervalId = setInterval(() => {
            setRotation(rotation => rotation + 1);
        }, 50);

        return () => clearInterval(intervalId);
    }, [rotation]);

    const startShoppingInfo = useMemo(() => {
        const startShoppingLinkNavigation = navigation.find(
            navigationItem => navigationItem.type === 'link'
        );

        if (startShoppingLinkNavigation) {
            const {href, name} = startShoppingLinkNavigation;
            return {href, name};
        }

        return null;
    }, [navigation]);

    return (
        <div className="container relative mt-6 flex pb-96 sm:pb-44 lg:pb-16">
            <div className="w-1/2 lg:w-1/3">
                <UiImage src={leftImage} alt="" />
            </div>
            <div className="max-lg:brand-gradient relative flex flex-col items-center justify-center gap-4 max-lg:absolute max-lg:left-10 max-lg:right-10 max-lg:top-28 max-lg:py-16 lg:w-1/3">
                <div className="relative ">
                    <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
                        <UiImage
                            src={iconN}
                            alt=""
                            className="m-auto h-7 w-7"
                        />
                    </div>
                    <div
                        className="h-20 w-20"
                        style={{
                            transform: `rotate(${rotation}deg)`
                        }}
                    >
                        <UiImage src={ring} alt="" />
                    </div>
                </div>
                <UiImage
                    src={BlackBackground}
                    alt="nehir online özel"
                    className="absolute   z-[-1]  "
                    fill
                />

                <p className="text-xs font-semibold tracking-widest lg:text-sm lg:text-white">
                    {t('*ONLY FOR THE ONLINE STORE')}
                </p>

                <h3 className="max-w-xs text-center font-dm-serif  text-2xl font-medium lg:mx-4 lg:px-0 lg:text-3xl lg:text-white">
                    {t(
                        'Get off to a fresh start in your kitchen with our dowry set'
                    )}
                </h3>
                <UiLink href={'/ceyiz-setleri'}>
                    <UiButton className="mt-2 rounded bg-white px-8 py-5 text-sm shadow-sm focus:border-0 focus:bg-white focus:ring-0 active:bg-white ">
                        Alışverişe Başla
                    </UiButton>
                </UiLink>
            </div>
            <div className="w-1/2 lg:w-1/3">
                <UiImage src={rightImage} alt="" />
            </div>
        </div>
    );
});

if (isDev) {
    OnlineSpecial.displayName = 'OnlineSpecial';
}

export default OnlineSpecial;
