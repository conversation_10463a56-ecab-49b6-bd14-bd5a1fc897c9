import {memo, useMemo} from 'react';
import {OrderedGroupedItems, Page, SpecialPageProducts} from '@core/types';
import {isDev, trim} from '@core/helpers';
import {useStore} from '@core/hooks';
import FeaturedProductSlider from '@components/common/FeaturedProductSlider';
import MainSlider from '@components/common/MainSlider';
import Seo from '@components/common/Seo';
import StorySlider from '@components/common/StorySlider';
import CompanyStory from './CompanyStory';
import Comments from './Comments';
import OnlineSpecial from './OnlineSpecial';
import Collection from '@components/common/Collections';
import {UiImage, UiLink} from '@core/components/ui';
import EnCokSatanImage from '@assets/images/common/main-story/coksatan.webp';
import EnYeniImage from '@assets/images/common/main-story/enyeni.webp';
import IndirimImage from '@assets/images/common/main-story/indirim.webp';
import {ChevronRightIcon} from '@core/icons/outline';
type HomePageProps = {
    productCatalogMap: SpecialPageProducts;
};

const HomePage: Page<HomePageProps> = memo(({productCatalogMap}) => {
    const {navigation} = useStore();

    const orderedGroupedItems = useMemo(() => {
        const subItems = navigation.filter(
            item => item.depth === 0 && !item.showInMainMenu
        );

        const items: OrderedGroupedItems[] = [];

        let index = 0;

        for (const subItem of subItems) {
            if (subItem.type !== 'product-catalog') {
                const lastItem =
                    items.length > 0 ? items[items.length - 1] : undefined;

                if (typeof lastItem !== 'undefined') {
                    if (lastItem.type === subItem.type) {
                        lastItem.items.push(subItem);
                    } else {
                        items.push({
                            id: `${subItem.type}-${index}`,
                            type: subItem.type,
                            items: [subItem]
                        });
                    }
                } else {
                    items.push({
                        id: `${subItem.type}-${index}`,
                        type: subItem.type,
                        items: [subItem]
                    });
                }
            } else {
                items.push({
                    id: `${subItem.type}-${index}`,
                    type: subItem.type,
                    items: [subItem],
                    products: productCatalogMap[subItem.id]?.products,
                    detailPageLink:
                        productCatalogMap[subItem.id]?.detailPageLink,
                    catalogName: productCatalogMap[subItem.id]?.catalogName
                });
            }

            index++;
        }

        return items;
    }, [navigation, productCatalogMap]);

    const topStory = useMemo(() => {
        return navigation?.filter(
            navigationItem =>
                navigationItem.type === 'story' &&
                navigationItem.section === 'top-story'
        );
    }, [navigation]);

    return (
        <>
            <Seo
                canonical={`${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/`}
            />

            <div className="grid grid-cols-3  place-items-center lg:hidden ">
                {topStory?.map(story => {
                    return (
                        <UiLink
                            key={story.id}
                            className="relative block h-full w-[98px] "
                            href={story.href ?? '#'}
                        >
                            <div className="grid place-items-center pt-4">
                                <div className="rounded-full transition group-hover:text-primary-600 ">
                                    <UiImage
                                        className="rounded-full"
                                        src={`${story.images?.[0]}?w=180&q=90`}
                                        alt={story.name}
                                        priority
                                        width={180}
                                        height={180}
                                    />
                                </div>
                                <div className="flex cursor-pointer items-center gap-0.5 pt-2 text-center text-[8px] !font-bold uppercase text-brand-black transition group-hover:text-primary-600 md:pt-2 lg:text-xs">
                                    <span className="font-bold">
                                        {' '}
                                        {story.name}
                                    </span>
                                    <ChevronRightIcon className="h-2 w-2 stroke-current stroke-[30px]" />
                                </div>
                            </div>
                        </UiLink>
                    );
                })}
            </div>

            <MainSlider />
            <StorySlider />

            {orderedGroupedItems.map(item => {
                if (
                    item.type === 'product-catalog' &&
                    item.items[0].section === 'nehir-slider'
                ) {
                    return (
                        <FeaturedProductSlider
                            key={item.id}
                            forSpecialPage
                            products={item.products ?? []}
                            detailPageLink={item.detailPageLink ?? ''}
                            productSliderName={item.catalogName ?? ''}
                        />
                    );
                }
            })}

            <OnlineSpecial />
            <Collection />
            <Comments />
            <CompanyStory />
        </>
    );
});

if (isDev) {
    HomePage.displayName = 'HomePage';
}

HomePage.initPageProps = async props => {
    return props;
};

export default HomePage;
