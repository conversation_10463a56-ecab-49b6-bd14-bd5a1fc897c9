import {FC, memo, useState} from 'react';
import {isDev} from '@core/helpers';
import {UiButton, UiImage, UiLink} from '@core/components/ui';
import nehirStory from '@assets/images/common/company-story/banner-history.webp';
import siteLogo from '@assets/images/common/site-logo.svg';
import {useClickOutside, useMobile, useTrans} from '@core/hooks';
import {Play, PlayCircle} from '@components/icons';
import {XIcon} from '@core/icons/outline';

const CompanyStory: FC = memo(() => {
    const t = useTrans();
    const [modalOpen, setModalOpen] = useState(false);
    const {isMobile} = useMobile();

    const openModal = () => {
        setModalOpen(true);
    };

    const closeModal = () => {
        setModalOpen(false);
    };
    const clickOutsideRef = useClickOutside(() => setModalOpen(false));

    return (
        <div className="flex items-center justify-center bg-secondary-200 py-6 pt-20 xl:h-5/6 xl:pb-7">
            <div>
                <div className="pb-6 text-center font-dm-serif text-4xl font-normal text-brand-clr lg:text-[43px]">
                    <h1>{t('The Story of Nehir')}</h1>
                </div>
                <div className="relative">
                    <div className="flex flex-grow items-center justify-center">
                        <UiImage
                            src={nehirStory}
                            alt={'nehir history'}
                            className="h-full w-11/12 lg:h-[400px] lg:w-[1167px]"
                        />
                    </div>

                    <div className="absolute inset-0 flex flex-col items-center justify-center space-y-3 md:mt-20">
                        <div
                            className="hidden cursor-pointer md:flex"
                            onClick={openModal}
                        >
                            <PlayCircle className="!h-24 !w-24" />
                        </div>

                        <UiImage
                            className="w-16 lg:w-32"
                            src={siteLogo}
                            alt="site logo"
                            priority
                            style={{filter: 'brightness(0) invert(1)'}}
                        />

                        <div className="hidden pb-10 md:flex md:pt-2 lg:pt-2">
                            <p className="text-2xs font-bold text-white md:text-sm lg:text-sm">
                                {t(
                                    'WATCH THE STORY OF MASTERSHİP AND CRAFTSMANSHİP'
                                )}
                            </p>
                        </div>
                    </div>
                </div>

                <div className="flex items-center justify-center pt-5 md:hidden">
                    <UiButton
                        onClick={openModal}
                        className=" flex h-10 w-60 items-center justify-center space-x-2 !rounded-full !border-gray-500 bg-transparent text-black transition-all duration-300 ease-in-out"
                    >
                        <Play
                            className="h-10 w-10 pl-1"
                            style={{color: 'black'}}
                        />
                        <p className="pb-0.5 text-2xs">
                            {t(
                                'WATCH THE STORY OF MASTERSHİP AND CRAFTSMANSHİP'
                            )}
                        </p>
                    </UiButton>
                </div>

                <div className="flex items-center justify-center pb-4 pt-4">
                    <div className="flex items-center justify-center px-5 sm:max-w-lg xl:max-w-lg xl:px-0">
                        <p className="text-center font-dm-serif text-brand-clr">
                            {t(
                                'Since 1981, we have been providing hand-crafted products in Turkey. We combined our dreams and hopes with experience and honest labor and produced wonderful steelwork.'
                            )}
                        </p>
                    </div>
                </div>
                <div className="flex items-center justify-center pb-6 ">
                    <UiLink href={'/kurumsal/hakkimizda'}>
                        <UiButton className="flex h-11 w-44 flex-row justify-center space-x-2 !rounded-sm !border-gray-300 bg-black text-white transition-all duration-300 ease-in-out hover:bg-transparent hover:text-black focus:border-black focus:ring-black  xl:bg-transparent  xl:text-brand-clr  xl:transition-all  xl:duration-300 xl:ease-in-out xl:hover:bg-black xl:hover:text-white">
                            <p className="pb-0.5 ">{t('READ THE STORY')}</p>
                        </UiButton>
                    </UiLink>
                </div>
            </div>

            {modalOpen && (
                <div className=" fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50">
                    <span
                        className="absolute left-4 top-24 cursor-pointer text-[35px] text-secondary-100 md:left-12 md:top-24 lg:left-36  lg:top-24 xl:left-52 xl:top-24"
                        onClick={closeModal}
                    >
                        <XIcon className="h-6 w-6 stroke-current stroke-[60] text-secondary-100"></XIcon>
                    </span>

                    <iframe
                        ref={clickOutsideRef}
                        width="80%"
                        height={isMobile ? '610' : '950'}
                        src="https://www.youtube.com/embed/wgiFd4hYT6s"
                        allowFullScreen
                        title="YouTube Video"
                    ></iframe>
                </div>
            )}
        </div>
    );
});

if (isDev) {
    CompanyStory.displayName = 'CompanyStory';
}

export default CompanyStory;
