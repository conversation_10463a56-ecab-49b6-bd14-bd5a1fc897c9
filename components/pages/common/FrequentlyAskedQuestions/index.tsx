import Breadcrumbs from '@components/common/Breadcrumbs';
import {UiLink} from '@core/components/ui';

import {useTrans} from '@core/hooks';
import {ChevronDownIcon, ChevronRightIcon} from '@core/icons/outline';
import {Disclosure, Transition} from '@headlessui/react';

type Props = {};

const FrequentlyAskedQuestionsPage = (props: Props) => {
    const t = useTrans();

    const questions = [
        {
            question: t(
                'Şifremi unuttum/bilmiyorum, siteye nasıl giriş yapacağım?'
            ),
            answer: t(
                'Şifrenizi unuttuğunuz ya da bilmediğiniz durumlarda, giriş sayfasında bulunan "Şifremi Unuttum" butonuna tıklamanız ve sistemimize üye olduğunuz e-posta adresinizi açılan sayfadaki kutuya yazmanız yeterlidir.'
            )
        },
        {
            question: t('Aldığım ürünlerin fiyatlarına KDV dahil midir?'),
            answer: t(
                'nehir.com.tr  sitesi üzerinden satılan her ürünün fiyatına KDV dahildir.'
            )
        },
        {
            question: t('Siparişlerimi nereden görebilirim?'),
            answer: t(
                'Siparişlerinizi, "Hesabım" sayfasında bulunan "Siparişlerim" bölümünden görebilirsiniz.'
            )
        },
        {
            question: t(
                'Sipariş adresimi değiştirmek istiyorum, nasıl yapabilirim?'
            ),
            answer: t(
                'Siparişiniz sırasında Fatura Adresi/Sevk adresi kısmından adres değişikliğinizi yapabilirsiniz.Ancak girdiğiniz bu adres; adresi güncelledikten sonra vereceğiniz siparişler için geçerli olacaktır.'
            )
        },
        {
            question: t(
                'Siparişimin elime ulaşamadığı durumlarda ne yapabilirim?'
            ),
            answer: t(
                'Kargo firması belirtmiş olduğunuz adrese siparişinizi teslim etmektedir. Adresinizde bulunamamanız durumunda kargonuzun size teslimat sağlayacak şubeden teslim alabilirsiniz. Kargonuzun 3 gün boyunca şubeden alınmaması durumunda siparişiniz tarafımıza iade edilir. Bu aşamada Müşteri Hizmetlerimizi arayarak adres değişikliği talebinizi iletebilirsiniz.'
            )
        },
        {
            question: t(
                'Teslimat adresimden bana ulaşılamazsa başkasına teslimat yapılabilir mi?'
            ),
            answer: t(
                'Belirtmiş olduğunuz teslimat adresinizde kargonuzu teslim alacak kişi var ise, bu bilgiyi siparişinizi oluştururken "Sipariş Notu" kısmında belirtmeniz gerekmektedir.'
            )
        },
        {
            question: t(
                'Satın aldığım ürünü yurt dışına gönderebilir misiniz?'
            ),
            answer: t(
                'nehir.com.tr’den  verilen siparişlerde yalnızca Türkiye sınırları içerisinde teslimat yapılabilir. KKTC sevkiyat sınırlarımız içerisinde yer almamaktadır. Yurt dışına gönderimimiz şimdilik bulunmamaktadır.'
            )
        },
        {
            question: t('Siparişlerde alt limit var mı?'),
            answer: t(
                'nehir.com.tr’den yapacağınız alışverişlerde alt limit bulunmamaktadır. İstediğiniz ürünü sepetinize ekleyip, satın alma işlemi yapabilirsiniz.'
            )
        }
    ];

    return (
        <div className="container">
            <Breadcrumbs
                breadcrumbs={[
                    {name: 'Nehir', href: '/', slug: '/'},
                    {
                        name: 'Sıkça Sorulan Sorular',
                        slug: '/frequently-asked-questions'
                    }
                ]}
            />
            <div className="mb-10 mt-8 grid gap-0 px-4 lg:mt-0 lg:gap-4 lg:px-24 ">
                <div className="w-full justify-self-center border-b  border-gray-300 xl:w-7/12 ">
                    <h2 className="pb-4 text-center font-dm-serif text-[33px] text-brand-black  lg:pb-7 lg:text-3xl">
                        {t('Sıkça Sorulan Sorular')}
                    </h2>
                </div>

                <div className="  grid w-full gap-2 justify-self-center lg:w-7/12 lg:gap-4 ">
                    {questions.map((item, index) => (
                        <Disclosure key={index}>
                            {({open}) => (
                                <>
                                    <div className="grid py-4">
                                        <Disclosure.Button className="flex w-full items-center justify-between pr-4 text-base text-brand-black">
                                            <div className="flex items-start justify-start text-left">
                                                {item.question}
                                            </div>
                                            <div className="flex items-center">
                                                {open ? (
                                                    <ChevronDownIcon className="ml-2 h-2.5 w-2.5 stroke-current stroke-[60px] text-brand-black" />
                                                ) : (
                                                    <ChevronRightIcon className="ml-2 h-2.5 w-2.5 stroke-current stroke-[60px] text-brand-black" />
                                                )}
                                            </div>
                                        </Disclosure.Button>
                                    </div>

                                    <Transition
                                        show={open}
                                        enter="transition duration-500 ease-in"
                                        enterFrom="transform scale-95 opacity-0"
                                        enterTo="transform scale-100 opacity-100"
                                        leave="transition duration-300 ease-out"
                                        leaveFrom="transform scale-100 opacity-100"
                                        leaveTo="transform scale-95 opacity-0"
                                    >
                                        <Disclosure.Panel>
                                            <p className=" mx-6 bg-pink-50/30 p-2 text-sm text-brand-black">
                                                {item.answer}
                                            </p>
                                        </Disclosure.Panel>
                                    </Transition>
                                </>
                            )}
                        </Disclosure>
                    ))}
                </div>
            </div>
            <div className="mt-16 grid place-items-center gap-4">
                <h3 className="font-dm-serif text-2xl font-normal text-brand-black">
                    Aklınıza takılan bir soru mu var ?
                </h3>
                <UiLink
                    href="/contact"
                    className="flex items-center gap-1 text-secondary-100"
                >
                    <span>{t('Bizimle iletişime geçin')}</span>
                    <ChevronRightIcon className="ml-2 mt-0.5 h-2 w-2 stroke-current stroke-[60px] text-secondary-100" />
                </UiLink>
            </div>
        </div>
    );
};

export default FrequentlyAskedQuestionsPage;
