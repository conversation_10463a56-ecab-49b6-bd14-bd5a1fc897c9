import Breadcrumbs from '@components/common/Breadcrumbs';
import {
    Refund1Icon,
    Refund2Icon,
    Refund3Icon,
    Refund4Icon
} from '@components/icons';
import {UiSlider} from '@core/components/ui';

import {useTrans} from '@core/hooks';
import {Pagination} from 'swiper/modules';

import 'swiper/css';
import 'swiper/css/pagination';
import Seo from '@components/common/Seo';
type Props = {};

const RefundPage = (props: Props) => {
    const t = useTrans();
    <Seo
        title="<PERSON>ade, Değişim ve Garanti Şartları"
        description="<PERSON>ade, Değişim ve Garanti Şartları"
    />;
    return (
        <div>
            <div className="container">
                <Breadcrumbs
                    breadcrumbs={[
                        {name: 'Nehir', href: '/', slug: '/'},
                        {
                            name: '<PERSON><PERSON><PERSON><PERSON> Sorulan Sorular',
                            slug: '/frequently-asked-questions'
                        }
                    ]}
                />
            </div>
            <div className="container mb-8 text-center font-dm-serif text-[33px]">
                <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ve Garanti
            </div>
            <div className=" flex items-center justify-center bg-[#F9F9F9]">
                <div className="flex flex-col items-center   gap-x-24 gap-y-12 xl:max-w-[1532px] xl:flex-row 2xl:gap-20">
                    <UiSlider
                        className="production-swiper-pagination h-72 w-[360px] select-none overflow-hidden lg:w-[660px]"
                        slidesPerView={1}
                        loop
                        pagination={{
                            dynamicBullets: true
                        }}
                        modules={[Pagination]}
                    >
                        <UiSlider.Slide className="flex  flex-col items-start justify-start ">
                            <div className="flex flex-col items-center justify-center gap-1">
                                <Refund3Icon className="h-44 w-44" />

                                <div className="mt-2 flex-col px-4 text-center text-brand-clr">
                                    <p className="w-60 text-sm ">
                                        <strong>
                                            Hesabım siparişler sayfasında
                                        </strong>{' '}
                                        iadeler sekmesine tıklayın.
                                    </p>
                                </div>
                            </div>
                        </UiSlider.Slide>

                        <UiSlider.Slide className="flex  flex-col items-start justify-start ">
                            <div className="flex flex-col items-center justify-center gap-1">
                                <Refund1Icon className="h-44 w-44" />

                                <div className="mt-2 flex-col px-4 text-center text-brand-clr">
                                    <p className="w-60 text-sm ">
                                        <strong>İade etmek istediğiniz</strong>{' '}
                                        ürünün adetini girip neden iade
                                        ediyorsunuz iade formunu doldurun.
                                    </p>
                                </div>
                            </div>
                        </UiSlider.Slide>
                        <UiSlider.Slide className="flex  flex-col items-start justify-start ">
                            <div className="flex flex-col items-center justify-center gap-1">
                                <Refund2Icon className="h-44 w-44" />

                                <div className="mt-2 flex-col px-4 text-center text-brand-clr">
                                    <p className="w-60 text-sm ">
                                        <strong>Ürünleri faturası ile</strong>{' '}
                                        beraber paketine koyun.
                                    </p>
                                </div>
                            </div>
                        </UiSlider.Slide>
                        <UiSlider.Slide className="flex  flex-col items-start justify-start ">
                            <div className="flex flex-col items-center justify-center gap-1">
                                <Refund4Icon className="h-44 w-44" />

                                <div className="mt-2 flex-col px-4 text-center text-brand-clr">
                                    <p className="w-60 text-sm ">
                                        <strong>
                                            İade kodu ile paketi kargoya verin
                                        </strong>{' '}
                                        ve ücretsiz iadenizi tamamlayın.
                                    </p>
                                </div>
                            </div>
                        </UiSlider.Slide>
                    </UiSlider>
                </div>
            </div>
            <div className="container grid gap-8 px-8 py-12 lg:px-36">
                <div className="grid gap-4">
                    <h3 className="font-hurme font-bold tracking-wide text-secondary-100">
                        İADE VE DEĞİŞİM
                    </h3>
                    <p className="font-hurme text-[15px] leading-6 tracking-wider">
                        Satın almış olduğunuz ürünü teslimat tarihinden itibaren
                        14 gün içerisinde sözleşme koşulları kapsamında
                        www.nehir.com.tr &apos;nin düzenlemiş olduğu faturanız
                        ve iade sebebinizi içeren bir not eki ile iade edip,
                        ödediğiniz tutarı geri alabilirsiniz.
                    </p>
                    <p className="font-hurme text-[15px] leading-6 tracking-wider">
                        Genel iade koşulları aşağıdaki gibidir;
                    </p>
                    <ol className="grid list-disc gap-3 pl-4">
                        <li className="text-[15px] leading-6 tracking-wider">
                            İadeler orijinal kutu veya ambalajı ile birlikte
                            yapılmalıdır.
                        </li>
                        <li className="text-[15px] leading-6 tracking-wider">
                            Orijinal kutusu/ambalajı bozulmuş (örnek: orijinal
                            kutu üzerine kargo etiketi yapıştırılmış ve kargo
                            koli bandı ile bantlanmış ürünler kabul edilemez)
                            tekrar satılabilirlik özelliğini kaybetmiş, başka
                            bir müşteri tarafından satın alınamayacak durumda
                            olan ürünlerin iadesi kabul edilemez.
                        </li>
                        <li className="text-[15px] leading-6 tracking-wider">
                            İade etmek istediğiniz ürünün faturası kurumsal ise,
                            geri iade ederken kurumun düzenlemiş olduğu iade
                            faturası ile birlikte göndermeniz gerekir. İade
                            faturası, kargo payı dahil edilmeden (ürün birim
                            fiyatı + KDV şeklinde ) kesilmelidir.
                        </li>
                        <li className="text-[15px] leading-6 tracking-wider">
                            Faturası kurumlar adına düzenlenen sipariş iadeleri
                            iade faturası kesilmediği takdirde tamamlanamaz.
                        </li>
                    </ol>
                </div>
                <div className="grid gap-4">
                    <h3 className="font-hurme font-bold tracking-wide text-secondary-100">
                        Ambajından kırılmış, çizilmiş ya da parçası eksik çıkmış
                        ürünlerin değişim ve iade işlemleri için;
                    </h3>

                    <ol className="grid list-disc  gap-3 pl-4">
                        <li className="text-[15px] leading-6 tracking-wider">
                            Satın almış olduğunuz ürün veya ürünleri orijinal
                            ambalajında ve bütün aksesuarları ile birlikte
                            doğrudan www.nehir.com.tr’ye geri göndermeniz
                            gerekir. Aksi takdirde değişim veya iade yapılamaz.
                        </li>
                        <li className="text-[15px] leading-6 tracking-wider">
                            www.nehir.com.tr &apos;ye göndereceğiniz iade ve
                            değişim işlemleri için yukarıda yer alan işlemleri
                            tamamlamanız gerekir. Gönderim işleminizin kabul
                            edilmesi durumunda size kargo gönderi numarası
                            tarafımızdan iletilecektir. Bu numara ile ürünü
                            tarafımıza gönderebilirsiniz. Ambalajından kusurlu
                            çıkan yeni ürünleriniz için 3 alternatif söz
                            konusudur; onarım, değişim veya iade. Dolayısıyla
                            mutlaka isteğinizi ifade eden bir not ile birlikte
                            ürün veya ürünlerinizi göndermeniz gerekir.
                        </li>
                        <li className="text-[15px] leading-6 tracking-wider">
                            Tarafımıza ulaşan ürünlerin değişim işlem süresi
                            ürün stoklarına bağlı olarak, iade işlem süresi
                            Nehir yetkili ekibinin incelemesine bağlı olarak,
                            onarım işlem süresi ise ürünlerinizin onarım
                            süresine bağlı olarak değişir. (Sizleri mağdur
                            etmemek için işleminizi en kısa sürede
                            sonuçlandıracağız.)
                        </li>
                    </ol>
                </div>
                <div className="grid gap-4">
                    <h3 className="font-hurme font-bold tracking-wide text-secondary-100">
                        GARANTİ KOŞULLARI
                    </h3>
                    <p className="text-[15px] leading-6 tracking-wider">
                        <strong>www.nehir.com.tr &apos;</strong> deki tüm
                        ürünler Nehir garantisi altındadır. Satın almış
                        olduğunuz ürünün faturasını sakladığınız süre boyunca,
                        garanti kapsamındaki tüm konular için bizimle iletişime
                        geçebilirsiniz.
                    </p>
                </div>
            </div>
        </div>
    );
};

export default RefundPage;
