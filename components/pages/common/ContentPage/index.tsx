import {memo, useMemo, useState} from 'react';
import {
    Breadcrumb,
    ContentPage as ContentPageType,
    NavigationItem,
    Page
} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import {
    UiButton,
    UiDivider,
    UiImage,
    UiLink,
    UiStickyBox
} from '@core/components/ui';
import Breadcrumbs from '@components/common/Breadcrumbs';
import {ChevronRight} from '@components/icons';
import BottomVideoImage from '@assets/images/common/bottom-video.webp';
import BottomVideoImageLogo from '@assets/images/common/nehir-video-logo.webp';
import WatchIcon from '@assets/images/common/triangle.webp';
import {NextSeo} from 'next-seo';

type ContentPageProps = {
    breadcrumbs: Breadcrumb[];
    page: ContentPageType;
    sideNavigation: (NavigationItem & {isActive: boolean})[];
};

const ContentPage: Page<ContentPageProps> = memo(props => {
    const {breadcrumbs, page, sideNavigation} = props;
    const {navigation, navigationItem} = useStore();
    const [isOpen, setIsOpen] = useState(false);

    const handleClick = () => {
        setIsOpen(true);
    };

    const handleClose = () => {
        setIsOpen(false);
    };

    const handleOutsideClick = (e: any) => {
        if (e.target.id === 'modal-wrapper') {
            setIsOpen(false);
        }
    };

    const parentItem = useMemo(() => {
        if (typeof navigationItem === 'undefined') {
            return null;
        }

        const slugParts = navigationItem.slug.split('/');
        if (slugParts.length < 2) {
            return null;
        }

        const parentSlug = slugParts.slice(0, slugParts.length - 1).join('/');

        return navigation.find(item => item.slug === parentSlug);
    }, [navigation, navigationItem]);

    return (
        <>
            <NextSeo
                title={navigationItem?.seoTitle}
                description={navigationItem?.seoDescription ?? ''}
            />

            <div className="flex-col px-6 xl:container">
                {navigationItem?.name !== 'Hakkımızda' && (
                    <div className="py-3 xl:py-0">
                        <Breadcrumbs breadcrumbs={breadcrumbs} />
                    </div>
                )}

                {navigationItem?.name === 'Hakkımızda' ? (
                    <div className="mt-4  grid place-items-center">
                        <div
                            className={
                                'hprose grid place-items-center text-left font-dm-serif'
                            }
                            dangerouslySetInnerHTML={{
                                __html: page.content
                                    ? page.content
                                          .replaceAll('&gt;', '>')
                                          .replaceAll('&lt;', '<')
                                          .replaceAll('&nbsp;', '')
                                          .replaceAll('</p><p>', ' ')
                                          .replaceAll('<p>', '')
                                          .replaceAll('</p>', '') ?? ''
                                    : page.content
                            }}
                        />
                        <div className="relative">
                            <UiImage
                                src={BottomVideoImage}
                                alt="Nehir Hakkımızda"
                                width={1150}
                                height={500}
                            />
                            <div className="absolute left-1/2 top-1/2 grid -translate-x-1/2 -translate-y-1/2 place-items-center gap-2 lg:gap-4">
                                <UiImage
                                    src={BottomVideoImageLogo}
                                    alt="Nehir Hakkımızda Logo"
                                    width={186}
                                    height={56}
                                />
                                <p className=" text-center text-sm font-medium uppercase leading-6 tracking-wide !text-white lg:leading-7 lg:tracking-widest lg:text-base">
                                    Ustalık ve İşçilik Hikayesini İzleyin
                                </p>
                                <UiButton
                                    onClick={handleClick}
                                    className="flex h-7 w-20 items-center justify-between rounded-full selection:ring-0 hover:bg-transparent hover:opacity-90 focus:border-white focus:bg-transparent focus:ring-0 focus:!ring-white active:bg-transparent"
                                >
                                    <span className="tracking-wider text-white">
                                        İzle
                                    </span>
                                    <UiImage
                                        src={WatchIcon}
                                        alt="Nehir Hakkımızda"
                                        width={7}
                                        height={50}
                                    />
                                </UiButton>
                            </div>
                        </div>
                        {isOpen && (
                            <div
                                id="modal-wrapper"
                                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
                                onClick={handleOutsideClick}
                            >
                                <div
                                    className="relative w-full max-w-4xl"
                                    onClick={e => e.stopPropagation()}
                                >
                                    <button
                                        className="absolute right-2 top-2 text-3xl text-white"
                                        onClick={handleClose}
                                    >
                                        &times;
                                    </button>
                                    <iframe
                                        title="Nehir Hakkımızda"
                                        className="h-[500px] w-full"
                                        src="https://www.youtube.com/embed/wgiFd4hYT6s?&autoplay=1&loop=1&title=0&byline=0&portrait=0&muted=1"
                                        frameBorder="0"
                                        allow="autoplay; fullscreen"
                                        allowFullScreen
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                ) : sideNavigation.length > 1 ? (
                    <div className="grid grid-cols-12 lg:gap-x-12">
                        <div className="col-span-12 select-none lg:col-span-3">
                            <div className="sticky top-36">
                                <>
                                    {parentItem && (
                                        <>
                                            <h3 className="pb-5 text-lg font-semibold leading-5 text-default">
                                                {parentItem.name}
                                            </h3>
                                        </>
                                    )}

                                    <div className="-mx-3 ">
                                        {sideNavigation.map(item => (
                                            <UiLink
                                                key={item.slug}
                                                href={item.href}
                                                className={cls(
                                                    'text[#333333] flex cursor-pointer items-center justify-between border-t px-3 py-4 pl-7 font-hurme text-[13px] font-normal transition focus:outline-none lg:max-w-xs',
                                                    {
                                                        'bg-brand-buttongray text-secondary-100':
                                                            item.isActive
                                                    }
                                                )}
                                            >
                                                <span> {item.name}</span>
                                                <ChevronRight className="h-2.5 w-2.5 stroke-current stroke-[50px] !text-secondary-100" />
                                            </UiLink>
                                        ))}
                                        <a
                                            download
                                            href={
                                                '/files/NEHIR_2025_KATALOG.pdf'
                                            }
                                            className={cls(
                                                'text[#333333] flex cursor-pointer items-center justify-between border-t px-3 py-4 pl-7 font-hurme text-[13px] font-normal transition focus:outline-none'
                                            )}
                                        >
                                            <span>
                                                {' '}
                                                İngilizce Online Katalog
                                            </span>
                                            <ChevronRight className="h-2.5 w-2.5 stroke-current stroke-[50px] !text-secondary-100" />
                                        </a>
                                    </div>
                                </>
                            </div>
                        </div>

                        <div className="col-span-12 mt-12 lg:col-span-9 lg:mt-0">
                            <h1 className=" text-2xl font-semibold leading-6 text-default lg:flex">
                                {navigationItem?.name}
                            </h1>

                            <UiDivider
                                orientation="horizontal"
                                className="mb-6 mt-4  border-gray-200 lg:flex"
                            />

                            <div
                                className="prose max-w-none"
                                dangerouslySetInnerHTML={{
                                    __html: page.content ?? ''
                                }}
                            />
                        </div>
                    </div>
                ) : (
                    <div
                        className="prose max-w-none py-12"
                        dangerouslySetInnerHTML={{
                            __html: page.content ?? ''
                        }}
                    />
                )}
            </div>
        </>
    );
});

ContentPage.initPageProps = async props => {
    return props;
};

if (isDev) {
    ContentPage.displayName = 'ContentPage';
}

export default ContentPage;
