import Breadcrumbs from '@components/common/Breadcrumbs';
import Seo from '@components/common/Seo';
import {onSubmitHandler} from '@components/helpers/onSubmitHandler';
import {RequestData} from '@components/helpers/requestData';
import CatalogSearchBarPartial from '@components/pages/store/Catalog/CatalogSearch';
import UiTextareaField from '@components/ui/Form/Field/TextareaField';
import {
    UiButton,
    UiCheckbox,
    UiForm,
    UiLink,
    UiRadio,
    UiRadioGroup,
    notification
} from '@core/components/ui';
import {regexp} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {ChevronRightIcon} from '@core/icons/outline';
import React, {useCallback} from 'react';
import {useForm} from 'react-hook-form';

type Props = {};

const ContactPage = (props: Props) => {
    const t = useTrans();
    const {
        register,
        formState: {errors},
        handleSubmit,
        reset,
        getValues
    } = useForm({
        defaultValues: {
            name: '',
            lastName: '',
            email: '',
            phone: '',
            subject: '',
            message: ''
        }
    });

    const handleSendCustomerEmail = async () => {
        try {
            const response = await fetch('/api/send-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    to: getValues().email,
                    subject: 'Nehir E-Posta',
                    text: 'Nehir E-Posta'
                })
            });

            if (!response.ok) {
                throw new Error('Failed to send email');
            }

            const data = await response.json();
        } catch (error) {
            console.error('Error sending email: ', error);
        }
    };

    const onSubmit = async (data: any) => {
        let nameAndSurname = `${data.name} ${data.lastName}`;
        const requestData = await RequestData(
            nameAndSurname,
            data.email,
            data.phone,
            data.message
        );
        const extra = [
            {
                label: 'Öneri Veya Şikayet',
                value: data.subject || 'Nehir E-Posta'
            }
        ];

        await handleSendCustomerEmail();

        try {
            await onSubmitHandler(requestData, extra);

            reset({
                name: '',
                lastName: '',
                email: '',
                phone: '',
                message: ''
            });

            notification({
                title: t('Mesajınız Başarıyla Gönderilmiştir'),
                description: t(
                    'Mesajınız başarıyla gönderilmiştir. En kısa süre içerisinde geri dönüş sağlayacağız '
                ),
                status: 'success'
            });
        } catch (error) {
            notification({
                title: t('Mesaj Gönderilirken Bir Hata Oluştu!'),
                description: t(
                    'Mesaj gönderilirken bir hata oluştu! Lütfen daha sonra tekrar deneyin.'
                ),
                status: 'error'
            });
        }
    };

    return (
        <div className="container">
            <Seo
                title="Nehir İletişim: Bize Nasıl Ulaşabilirsiniz?"
                description="Nehir iletişim sayfası ile bize ulaşmanız çok kolay! İletişim numarası ve adres gibi bilgileri öğrenmek için hemen tıklayın."
            />
            <Breadcrumbs
                breadcrumbs={[
                    {name: 'Nehir', href: '/', slug: '/'},
                    {name: 'İletişim', slug: '/contact'}
                ]}
            />

            <div>
                <h1 className="text-center font-dm-serif text-[33px]">
                    Bizimle İletişime Geçin
                </h1>
                <div className=" my-12 grid place-items-center">
                    <div className="grid  max-w-6xl   gap-12 lg:grid-cols-2 ">
                        <div className="grid gap-8 lg:px-12 lg:py-16">
                            <div className="grid gap-4">
                                <h3 className="font-dm-serif text-2xl">
                                    Size Nasıl Yardımcı Olabiliriz?
                                </h3>
                                <div className="font-sans">
                                    <p className="text-sm">
                                        Müşteri Bilgi Hattı
                                    </p>
                                    <a
                                        href="tel:02126566550"
                                        className="font-semibold  tracking-tight text-base"
                                    >
                                        0 212 656 65 50
                                    </a>
                                </div>
                                <div className="font-sans">
                                    <p className="text-sm">
                                        Bir e-posta gönder
                                    </p>
                                    <a
                                        href="mailto:<EMAIL>"
                                        className="font-semibold tracking-tight text-base"
                                    >
                                        <EMAIL>
                                    </a>
                                </div>
                                <UiLink href={'/frequently-asked-questions'}>
                                    <UiButton className="flex w-60 items-center gap-2 border-secondary-100 py-6 text-secondary-100 hover:border-secondary-100 hover:bg-transparent focus:border-secondary-100 focus:ring-secondary-100 active:bg-transparent">
                                        <span>
                                            {' '}
                                            Sık Sorulan Sorulara Göz At
                                        </span>
                                        <ChevronRightIcon className="h-4 w-4" />
                                    </UiButton>
                                </UiLink>
                            </div>
                            <div className="grid gap-4">
                                <h3 className="font-dm-serif text-2xl">
                                    Bize Ulaşın
                                </h3>
                                <div className="grid gap-4">
                                    <h4 className="font-bold">Mağazamız</h4>
                                    <p className="text-sm">
                                        2417 Sok. Z Blok No.83 İstoç - İkitelli
                                        / İstanbul
                                    </p>
                                    <p className="text-xs text-muted opacity-50">
                                        Güneşli V.D. O6301000481
                                    </p>
                                    <UiLink
                                        className="flex items-center gap-1 text-xs font-bold text-secondary-100"
                                        href="https://www.google.com/maps/place/Nehir+Mutfak+-+%C4%B0sto%C3%A7/@41.0674078,28.8295873,19z/data=!4m6!3m5!1s0x14caa5bdebd12cf9:0xfec42e2b9dddf039!8m2!3d41.0676837!4d28.8298538!15sCg1uZWhpciB0ZW5jZXJlWg8iDW5laGlyIHRlbmNlcmWSARRraXRjaGVuX3N1cHBseV9zdG9yZQ?shorturl=1"
                                    >
                                        <span>Yol Tarifi Al</span>
                                        <ChevronRightIcon className="h-3 w-3" />
                                    </UiLink>
                                </div>
                                <div className="grid gap-4">
                                    <div>
                                        <h4 className="font-bold">
                                            Fabrika Adresi
                                        </h4>
                                        <p className="text-xs font-light text-muted">
                                            Nehir Mutfak Eşyaları San. Ve Tic.
                                            A.Ş.
                                        </p>
                                    </div>
                                    <p className="text-sm">
                                        15 Temmuz Mh. 1498. Sokak No.35 Güneşli
                                        / İstanbul
                                    </p>
                                    <p className="text-xs">
                                        Fabrika adresimizde perakende satışımız
                                        bulunmamaktadır.
                                    </p>
                                    <UiLink
                                        className="flex items-center gap-1 text-xs font-bold text-secondary-100"
                                        href="https://www.google.com/maps/place/Nehir+Mutfak+-+Fabrika/@41.031621,28.809401,17z/data=!3m1!4b1!4m5!3m4!1s0x14caa457935f91bf:0x43ce3251fd25cd01!8m2!3d41.0316296!4d28.8115936?shorturl=1"
                                    >
                                        <span>Yol Tarifi Al</span>
                                        <ChevronRightIcon className="h-3 w-3" />
                                    </UiLink>
                                </div>
                                <div className="mt-2">
                                    <a
                                        href="tel:02126566550"
                                        className="text-sm"
                                    >
                                        T. 0 212 656 65 50
                                    </a>
                                    <p className="text-sm">
                                        F. 0 212 651 75 71
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="grid gap-4 rounded border border-gray-100 px-6 py-10 lg:p-16">
                            <h3 className="font-dm-serif text-[22px]">
                                Aklınıza takılan bir soru mu var ?
                            </h3>
                            <div>
                                <p className="text-xs">
                                    İletişime geçmek için bilgilerinizi aşağıya
                                    girin.
                                </p>
                                <p className="text-xs">
                                    Ekibimiz sorduğunuz soruyu yanıtlamak için
                                    burada.
                                </p>
                            </div>
                            <UiForm onSubmit={handleSubmit(onSubmit)}>
                                <div className="space-y-5">
                                    <div className="flex flex-col gap-5 lg:flex-row">
                                        <div className="custom-form-input-2 h-14 lg:w-1/2">
                                            <UiForm.Field
                                                rightElement={
                                                    <p className="mb-4 ml-5 text-secondary-100">
                                                        *
                                                    </p>
                                                }
                                                label={t('Adınız')}
                                                size="sm"
                                                error={
                                                    errors.name &&
                                                    errors.name.type ===
                                                        'required'
                                                        ? t(
                                                              'First name is required'
                                                          )
                                                        : undefined
                                                }
                                                {...register('name', {
                                                    required: true
                                                })}
                                            />
                                        </div>
                                        <div className="custom-form-input-2 custom-form h-14 lg:w-1/2">
                                            <UiForm.Field
                                                label={t('Soyadınız')}
                                                rightElement={
                                                    <p className="mb-4 ml-5 text-secondary-100">
                                                        *
                                                    </p>
                                                }
                                                error={
                                                    errors.lastName &&
                                                    errors.lastName.type ===
                                                        'required'
                                                        ? t(
                                                              'Last name is required'
                                                          )
                                                        : undefined
                                                }
                                                {...register('lastName', {
                                                    required: true
                                                })}
                                            />
                                        </div>
                                    </div>
                                    <div className="custom-form-input-2 h-14">
                                        <UiForm.Field
                                            className="mt-3"
                                            label={t('Email address')}
                                            rightElement={
                                                <p className="mb-4 ml-5 text-secondary-100">
                                                    *
                                                </p>
                                            }
                                            error={
                                                errors.email &&
                                                errors.email.type === 'required'
                                                    ? t(
                                                          'Email address is required'
                                                      )
                                                    : errors.email &&
                                                      errors.email.type ===
                                                          'pattern'
                                                    ? t(
                                                          'Email address is invalid'
                                                      )
                                                    : undefined
                                            }
                                            {...register('email', {
                                                required: true,
                                                pattern: regexp.email
                                            })}
                                        />
                                    </div>

                                    <div className="custom-form-input-2 h-14">
                                        <UiForm.Field
                                            className="mt-3"
                                            label={t('Telefon')}
                                            rightElement={
                                                <p className="mb-4 ml-5 text-secondary-100">
                                                    *
                                                </p>
                                            }
                                            error={
                                                errors.phone &&
                                                errors.phone.type === 'required'
                                                    ? t('phone is required')
                                                    : undefined
                                            }
                                            {...register('phone', {})}
                                        />
                                    </div>
                                </div>
                                <div className="mt-3">
                                    <h6 className="font-dm-serif font-normal">
                                        Size uygun seçeneği seçiniz:
                                    </h6>
                                    <UiForm.Control className="mt-3">
                                        <UiRadioGroup
                                            className={`flex items-center space-x-4`}
                                            {...register('subject', {})}
                                        >
                                            <UiRadio
                                                name="oneri"
                                                value={'1'}
                                                className={`mr-0.5 h-5 w-5 rounded-full text-xs checked:hover:bg-secondary-100 focus:ring-0`}
                                            >
                                                Önerim Var
                                            </UiRadio>

                                            <UiRadio
                                                name="oneri"
                                                value={'2'}
                                                className={`mr-0.5 h-5 w-5 rounded-full text-xs checked:hover:bg-secondary-100 focus:ring-0`}
                                            >
                                                Şikayetim Var
                                            </UiRadio>
                                        </UiRadioGroup>
                                    </UiForm.Control>
                                </div>
                                <div className="mt-3">
                                    <h6 className="font-dm-serif font-normal">
                                        Öneriniz:
                                    </h6>
                                    <UiTextareaField
                                        className="mt-3"
                                        label={t('Mesajınız')}
                                        rows={50}
                                        cols={200}
                                        error={
                                            errors.message &&
                                            errors.message.type === 'required'
                                                ? t(
                                                      'message is the required field.'
                                                  )
                                                : undefined
                                        }
                                        {...register('message', {
                                            required: true
                                        })}
                                    />
                                </div>

                                <UiForm.Control className="mt-3">
                                    <UiCheckbox
                                        className={`mr-0.5 h-4 w-4 rounded-none checked:hover:bg-secondary-100 focus:ring-0`}
                                    >
                                        <div
                                            className="mt-1 text-xs"
                                            dangerouslySetInnerHTML={{
                                                __html: t(
                                                    'I have read and understood the <span id="clarificationText" class="text-primary-600">clarification text</span> for the processing of my personal data.'
                                                )
                                            }}
                                        ></div>
                                    </UiCheckbox>
                                </UiForm.Control>

                                <UiButton
                                    className="mt-4 h-14 w-full rounded bg-secondary-800 font-hurme font-semibold  text-base text-white hover:bg-secondary-800 hover:text-white focus:border-0 focus:bg-secondary-800 focus:text-white focus:ring-0"
                                    type="submit"
                                    size="xl"
                                >
                                    {t('MESAJINIZI GÖNDERİN')}
                                </UiButton>
                            </UiForm>
                        </div>
                    </div>
                </div>
                <div className="mb-24 h-96 w-full">
                    <iframe
                        title="Google Maps Nehir"
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3008.063018355118!2d28.827616915055128!3d41.06761512372083!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDHCsDA0JzAzLjQiTiAyOMKwNDknNDcuMyJF!5e0!3m2!1str!2str!4v1583850190893!5m2!1str!2str"
                        frameBorder="0"
                        allow="fullscreen"
                        data-ready="true"
                        className="h-full w-full"
                    ></iframe>
                </div>
                <CatalogSearchBarPartial />
            </div>
        </div>
    );
};

export default ContactPage;
