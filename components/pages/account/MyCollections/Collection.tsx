import {FC, memo, useCallback, useEffect, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {Collection as CollectionType, ProductListItem} from '@core/types';
import {useIntersection, useStore, useTrans} from '@core/hooks';
import {jsonRequest, randomId} from '@core/helpers';
import {UiDivider, UiSelect} from '@core/components/ui';
import {BookmarkIcon} from '@core/icons/outline';
import ProductCard from '@components/common/ProductCard';
import Seo from '@components/common/Seo';

type CollectionProps = {
    collections: CollectionType[];
    collection: CollectionType;
    products: ProductListItem[];
    total: number;
    hasNextPage: boolean;
};

const Collection: FC<CollectionProps> = memo(props => {
    const t = useTrans();
    const {
        collections,
        collection,
        products: initialProducts,
        total: initialTotal,
        hasNextPage: initialHasNextPage
    } = props;
    const router = useRouter();
    const [ref, observer] = useIntersection();
    const {removeFromCollection} = useStore();
    const [products, setProducts] = useState<ProductListItem[]>(
        () => initialProducts
    );
    const [hasNextPage, setHasNextPage] = useState(() => initialHasNextPage);
    const [total, setTotal] = useState(() => initialTotal);
    const [isLoading, setIsLoading] = useState(false);
    const limit = useRef(32);
    const skip = useRef(0);
    const inProgress = useRef(false);
    const loadMoreRequested = useRef(false);

    // Load more.
    useEffect(() => {
        if (observer?.isIntersecting) {
            if (inProgress.current) {
                loadMoreRequested.current = true;

                return;
            }

            inProgress.current = true;
            setIsLoading(true);
            skip.current += limit.current;

            setProducts(currentProducts =>
                currentProducts.concat(
                    [...Array(limit.current)].map(
                        () =>
                            ({
                                productId: randomId(16),
                                isFake: true
                            } as any)
                    )
                )
            );

            const load = async () => {
                const result = await jsonRequest({
                    url: '/api/customers/collection-products',
                    method: 'POST',
                    data: {
                        collectionId: collection.id,
                        skip: skip.current,
                        limit: limit.current
                    }
                });

                setProducts(currentProducts => {
                    currentProducts = currentProducts.filter(
                        // @ts-ignore
                        currentProduct => !currentProduct.isFake
                    );

                    return currentProducts.concat(result.data);
                });
                setTotal(result.total);
                setHasNextPage(result.hasNextPage);
                setIsLoading(false);
                inProgress.current = false;

                if (loadMoreRequested.current) {
                    loadMoreRequested.current = false;

                    if (result.hasNextPage) {
                        await load();
                    }
                }
            };

            load();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [observer]);

    // On Remove.
    const onRemove = useCallback(
        async (product: ProductListItem) => {
            if (inProgress.current) {
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                await removeFromCollection(collection, {
                    id: product.productId,
                    name: product.name,
                    image:
                        Array.isArray(product.images) &&
                        product.images.length > 0
                            ? product.images[0]
                            : '/no-image.png',
                    price: product.salesPrice
                });
                setProducts(currentProducts => {
                    currentProducts = currentProducts.filter(
                        currentProduct =>
                            currentProduct.productId !== product.productId
                    );

                    return currentProducts;
                });

                const result = await jsonRequest({
                    url: '/api/customers/collection-products',
                    method: 'POST',
                    data: {
                        collectionId: collection.id,
                        skip: 0,
                        limit: 1
                    }
                });
                setTotal(result.total);
            } catch (error) {}

            inProgress.current = false;
            setIsLoading(false);
        },
        [collection, removeFromCollection]
    );

    return (
        <>
            <Seo title={t(collection.name)} />

            <div className="hidden xl:block">
                <h1 className="text-2xl font-semibold leading-6 text-default">
                    {t(collection.name)}
                </h1>
                <p className="mt-3 text-sm text-gray-500">
                    {t('{count} product(s)', {
                        count: total
                    })}
                </p>
            </div>

            <UiDivider
                orientation="horizontal"
                className="mb-6 mt-4 hidden border-gray-200 xl:block"
            />

            <div className="mt-4 block xl:hidden">
                <UiSelect
                    value={collection.id}
                    className="w-full border-gray-200"
                    onChange={e =>
                        router.push(`/account/my-collections/${e.target.value}`)
                    }
                >
                    {collections.map(c => (
                        <option key={c.id} value={c.id}>
                            {t(c.name)}
                        </option>
                    ))}
                </UiSelect>
            </div>

            {total > 0 && (
                <div className="mt-4 grid grid-cols-2 gap-2 xl:mt-0 xl:grid-cols-3 xl:gap-4">
                    {products.map(product => {
                        return (
                            <ProductCard
                                key={product.productId}
                                product={product}
                                isFavoriteShown={false}
                                isUnDiscountedPriceShown={false}
                                hasAddToCart={false}
                                hasSellingOptions={false}
                                onRemove={onRemove}
                                // @ts-ignore
                                isFake={product.isFake}
                            />
                        );
                    })}
                </div>
            )}

            {total > 0 && !isLoading && hasNextPage && <span ref={ref}></span>}

            {total < 1 && !isLoading && (
                <div className="flex flex-1 flex-col items-center justify-center py-12 xl:px-12 xl:py-24">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500 ">
                        <BookmarkIcon className="h-8 w-8" />
                    </div>

                    <h2 className="pt-8 text-center text-2xl font-semibold">
                        {t('No products found!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t(
                            "You haven't added any products to this collection yet."
                        )}
                    </p>
                </div>
            )}
        </>
    );
});

Collection.displayName = 'Collection';

export default Collection;
