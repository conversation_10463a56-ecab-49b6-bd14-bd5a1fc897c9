import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiDivider} from '@core/components/ui';
import Seo from '@components/common/Seo';
import Information from './Information';
import Password from './Password';
import DeleteAccount from './DeleteAccount';

type MyAccountProps = {
    countries: Record<string, any>[];
};

const MyAccount: FC<MyAccountProps> = memo(({countries}) => {
    const t = useTrans();

    return (
        <>
            <Seo title={t('My Account')} />

            <div className="hidden xl:block">
                <h1 className="text-2xl font-semibold leading-6 text-default">
                    {t('My Account')}
                </h1>
                <p className="mt-3 text-sm text-gray-500">
                    {t('Manage your account details.')}
                </p>
            </div>

            <UiDivider
                orientation="horizontal"
                className="mb-6 mt-4 hidden border-gray-200 xl:block"
            />

            <div className="mb-4 grid gap-12 xl:mb-0 xl:grid-cols-2 xl:gap-16">
                <div>
                    <h2 className="mb-2 mt-4 text-lg font-medium xl:mb-3 xl:mt-0">
                        {t('Account Information')}
                    </h2>

                    <Information countries={countries} />
                </div>

                <div>
                    <h2 className="mb-2 text-lg font-medium xl:mb-3">
                        {t('Change Password')}
                    </h2>

                    <Password />
                </div>
            </div>

            <UiDivider
                orientation="horizontal"
                className="my-8 hidden border-gray-200 xl:block"
            />

            <DeleteAccount />
        </>
    );
});

if (isDev) {
    MyAccount.displayName = 'MyAccount';
}

export default MyAccount;
