import {useCallback, useState} from 'react';
import {signOut} from 'next-auth/react';
import {Cookies} from 'react-cookie-consent';
import {useMobile, useTrans, useUI} from '@core/hooks';
import {UiButton, UiPortal, notification} from '@core/components/ui';
import {jsonRequest} from '@core/helpers';

const ConfirmDeletion = () => {
    const [isLoading, setIsLoading] = useState(false);

    const t = useTrans();
    const {closeModal} = useUI();
    const {isMobile} = useMobile();

    async function deleteCustomer() {
        setIsLoading(true);

        try {
            await jsonRequest({
                url: '/api/customers/delete-customer',
                method: 'POST',
                data: {}
            });

            Cookies.remove('cart-id');
            await signOut();
        } catch (err) {
            notification({
                title: t('Error'),
                description: t(
                    'An error occurred while deleting your account!'
                ),
                status: 'error'
            });
        } finally {
            setIsLoading(false);
        }
    }

    return (
        <>
            <div className="flex flex-col px-6">
                <p className="text-sm max-xl:mt-4">
                    {t(
                        "Please confirm you want to delete your account. All your data will be permanently removed, and this can't be undone."
                    )}
                </p>

                <div className="mb-4 ml-auto flex items-center gap-4">
                    {!isMobile && (
                        <UiButton
                            onClick={closeModal}
                            className="mt-2 xl:mt-6"
                            color="primary"
                            variant="outline"
                            size="lg"
                        >
                            {t('Cancel')}
                        </UiButton>
                    )}
                    <UiButton
                        onClick={deleteCustomer}
                        loading={isLoading}
                        disabled={isLoading}
                        className="mt-2 xl:mt-6"
                        color="danger"
                        variant="solid"
                        size="lg"
                    >
                        {t('Confirm Deletion')}
                    </UiButton>
                </div>
            </div>

            {isLoading && (
                <UiPortal>
                    <div className="fixed inset-0 z-50 bg-gray-900 bg-opacity-20 transition-opacity" />
                </UiPortal>
            )}
        </>
    );
};

const DeleteAccount = () => {
    const t = useTrans();
    const {openModal, openSideBar} = useUI();
    const {isMobile} = useMobile();

    const onOpenCustomerChange = useCallback(() => {
        if (isMobile) {
            openSideBar(t('Are you sure?'), <ConfirmDeletion />);
        } else {
            openModal(t('Are you sure?'), <ConfirmDeletion />);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isMobile]);

    return (
        <div className="flex flex-col max-xl:pb-8">
            <h2 className="mb-2 mt-4 text-lg font-medium xl:mb-3 xl:mt-0">
                {t('Delete My Account')}
            </h2>
            <p className="text-sm">
                {t(
                    'Remember that by deleting your account, you will lose access to all of your history, preferences, and any saved items. This action cannot be undone.'
                )}
            </p>

            <UiButton
                onClick={onOpenCustomerChange}
                className="ml-auto mt-2 bg-secondary-100 text-white hover:bg-secondary-100 focus:border-secondary-100 focus:bg-secondary-100 focus:ring-secondary-100 active:bg-secondary-100 xl:mt-6"
                variant="solid"
                size="lg"
            >
                {t('Delete My Account')}
            </UiButton>
        </div>
    );
};

export default DeleteAccount;
