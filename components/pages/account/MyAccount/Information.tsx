import {FC, memo, useCallback, useEffect, useRef, useState} from 'react';
import {FormProvider, useForm} from 'react-hook-form';
import {isDev, jsonRequest, regexp} from '@core/helpers';
import {useCustomer, useStore, useTrans} from '@core/hooks';
import {UiAlert, UiButton, UiForm, notification} from '@core/components/ui';

type InformationProps = {
    countries: Record<string, any>[];
};

const Information: FC<InformationProps> = memo(({countries}) => {
    const methods = useForm();
    const {
        register,
        formState: {errors},
        setValue,
        setError,
        getValues
    } = methods;
    const t = useTrans();
    const {setUpdatedCustomer} = useStore();
    const customer = useCustomer();
    const [errorMessage, setErrorMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const inProgress = useRef(false);

    useEffect(() => {
        if (typeof customer === 'undefined') return;

        if (typeof customer.firstName !== 'undefined')
            setValue('firstName', customer.firstName);
        if (typeof customer.lastName !== 'undefined')
            setValue('lastName', customer.lastName);

        if (typeof customer.email !== 'undefined')
            setValue('email', customer.email);

        if (
            !!customer.phoneCountryCode &&
            !!customer.phoneCode &&
            !!customer.phoneNumber
        ) {
            setValue('phone', {
                countryCode: customer.phoneCountryCode,
                code: customer.phoneCode,
                number: customer.phoneNumber
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [customer]);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgress.current) return;

            const phoneNumber = getValues('phone.number')?.replace(
                /[^\d]/g,
                ''
            );
            if (phoneNumber?.length !== 10) {
                setError('phone', {type: 'required'});
                return;
            }
            if (phoneNumber?.[0] !== '5' && data?.phone?.countryCode === 'TR') {
                setError('phone', {
                    type: 'required',
                    message: t(
                        'Turkey mobile numbers begin with +90 followed by a 5 in the second group. (e.g. +905XXXXXXXXX)'
                    )
                });
                return;
            }

            inProgress.current = true;
            setIsLoading(true);

            try {
                if (!!data.phone && typeof data.phone === 'object') {
                    data.phoneCountryCode = data.phone.countryCode;
                    data.phoneCode = data.phone.code;
                    data.phoneNumber = data.phone.number;
                    delete data.phone;
                }

                const result = await jsonRequest({
                    url: '/api/customers/update-customer',
                    method: 'POST',
                    data: {
                        payload: data
                    }
                });
                setUpdatedCustomer({...result});

                notification({
                    title: t('Information Updated'),
                    description: t(
                        'The account information has been successfully updated.'
                    ),
                    status: 'success'
                });
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            setIsLoading(false);
            inProgress.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [customer]
    );

    return (
        <FormProvider {...methods}>
            <UiForm onSubmit={methods.handleSubmit(onSubmit)}>
                {!!errorMessage && (
                    <UiAlert className="mb-8" color="danger">
                        {t(errorMessage)}
                    </UiAlert>
                )}

                <div className="space-y-2 xl:space-y-4">
                    <div className="flex space-x-4">
                        <UiForm.Field
                            label={t('First name')}
                            error={
                                errors.firstName &&
                                errors.firstName.type === 'required'
                                    ? t('First name is required')
                                    : undefined
                            }
                            {...register('firstName', {required: true})}
                        />

                        <UiForm.Field
                            label={t('Last name')}
                            error={
                                errors.lastName &&
                                errors.lastName.type === 'required'
                                    ? t('Last name is required')
                                    : undefined
                            }
                            {...register('lastName', {required: true})}
                        />
                    </div>

                    <UiForm.Field
                        label={t('Email address')}
                        error={
                            errors.email && errors.email.type === 'required'
                                ? t('Email address is required')
                                : errors.email &&
                                  errors.email.type === 'pattern'
                                ? t('Email address is invalid')
                                : undefined
                        }
                        {...register('email', {
                            required: true,
                            pattern: regexp.email
                        })}
                    />

                    <UiForm.Field
                        label={t('Phone')}
                        error={
                            errors.phone && errors.phone.type === 'required'
                                ? errors.phone.message?.toString() ||
                                  t('Phone number is invalid')
                                : undefined
                        }
                        phone
                        countries={countries}
                        {...register('phone', {required: false})}
                    />
                </div>

                <UiButton
                    className="mt-2 w-full xl:mt-6"
                    color="primary"
                    size="xl"
                    loading={isLoading}
                    disabled={isLoading}
                >
                    {t('Save Information')}
                </UiButton>
            </UiForm>
        </FormProvider>
    );
});

if (isDev) {
    Information.displayName = 'Information';
}

export default Information;
