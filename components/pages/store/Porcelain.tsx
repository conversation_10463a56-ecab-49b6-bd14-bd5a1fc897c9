import {FC, useState} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiButton, UiImage} from '@core/components/ui';

import MargaretTopLeftImage from '@assets/images/common/porcelian/margaret/margaret-top.png';
import MargaretTopRightImage from '@assets/images/common/porcelian/margaret/margaret-top-right.jpg';
import MargaretTopBottomImage from '@assets/images/common/porcelian/margaret/margaret-bottom.png';
import MargaretDreamImage from '@assets/images/common/porcelian/margaret/margaret-dream.png';
import AsagiKaydirImage from '@assets/images/common/porcelian/margaret/asagi-kaydir.png';
import GriRightImage from '@assets/images/common/porcelian/margaret/gri-right.png';
import GriBottomImage from '@assets/images/common/porcelian/margaret/gri-bottom.png';
import DreamKoleksiyonLeftImage from '@assets/images/common/porcelian/margaret/dream-koleksiyon-left.png';
import DreamKoleksiyonRightImage from '@assets/images/common/porcelian/margaret/dream-koleksiyon-right.png';

import DreamCorbaImage from '@assets/images/common/porcelian/dream-set/margaret-corba.png';
import DreamCukurImage from '@assets/images/common/porcelian/dream-set/margaret-cukur.png';
import DreamSalataImage from '@assets/images/common/porcelian/dream-set/margaret-salata.png';
import DreamServisImage from '@assets/images/common/porcelian/dream-set/margaret-servis.png';
import DreamTatliImage from '@assets/images/common/porcelian/dream-set/margaret-tatli.png';
import DreamTuzlukImage from '@assets/images/common/porcelian/dream-set/margaret-tuzluk.png';

import SienaGoldLeftImage from '@assets/images/common/porcelian/siena/siena-gold-left.png';
import SienaGoldRightImage from '@assets/images/common/porcelian/siena/siena-gold-right.png';

import SienaGoldKoleksiyonLeftImage from '@assets/images/common/porcelian/siena/siena-gold-koleksiyon-left.png';
import SienaGoldKoleksiyonRightImage from '@assets/images/common/porcelian/siena/siena-gold-koleksiyon-right.png';

import SienaGoldMiddeleImage from '@assets/images/common/porcelian/siena/siena-middle.png';
import SienaGoldButtonImage from '@assets/images/common/porcelian/siena/siena-gold-button.png';
import SienaGrayButtonImage from '@assets/images/common/porcelian/siena/siena-gray-button.png';

import SienaGrayMiddleImage from '@assets/images/common/porcelian/siena/siena-gray-middle.png';
import SienaGrayLeftImage from '@assets/images/common/porcelian/siena/siena-gray-left.png';
import SienaGrayRightImage from '@assets/images/common/porcelian/siena/siena-gray-right.png';

import SienaGrayServisImage from '@assets/images/common/porcelian/siena/siena-gray-servis.png';
import SienaGrayCukurImage from '@assets/images/common/porcelian/siena/siena-gray-cukur.png';
import SienaGrayCorbaImage from '@assets/images/common/porcelian/siena/siena-gray-corba.png';
import SienaGrayTatliImage from '@assets/images/common/porcelian/siena/siena-gray-tatli.png';
import SienaGraySalataImage from '@assets/images/common/porcelian/siena/siena-gray-salata.png';
import SienaGrayTuzlukImage from '@assets/images/common/porcelian/siena/siena-gray-tuzluk.png';

import CameliaTopImage from '@assets/images/common/porcelian/camelia/camelia-top.png';
import CameliaTop2Image from '@assets/images/common/porcelian/camelia/camelia-top2.png';

import CameliaServisImage from '@assets/images/common/porcelian/camelia/camelia-servis.png';
import CameliaCukurImage from '@assets/images/common/porcelian/camelia/camelia-cukur.png';
import CameliaCorbaImage from '@assets/images/common/porcelian/camelia/camelia-corba.png';
import CameliaServis2Image from '@assets/images/common/porcelian/camelia/camelia-servis2.png';
import CameliaTatliImage from '@assets/images/common/porcelian/camelia/camelia-tatli.png';
import CameliaSalataImage from '@assets/images/common/porcelian/camelia/camelia-salata.png';
import CameliaTuzlukImage from '@assets/images/common/porcelian/camelia/camelia-tuzluk.png';

import CameliaAltinLeftImage from '@assets/images/common/porcelian/camelia/camelia-altin-left.png';
import CameliaAltinRightImage from '@assets/images/common/porcelian/camelia/camelia-altin-right.png';

import CameliaAltinServisImage from '@assets/images/common/porcelian/camelia/camelia-altin-servis.png';
import CameliaAltinCukurImage from '@assets/images/common/porcelian/camelia/camelia-altin-cukur.png';
import CameliaAltinCorbaImage from '@assets/images/common/porcelian/camelia/camelia-altin-corba.png';
import CameliaAltinServis2Image from '@assets/images/common/porcelian/camelia/camelia-altin-servis2.png';
import CameliaAltinTatliImage from '@assets/images/common/porcelian/camelia/camelia-altin-tatli.png';
import CameliaAltinSalataImage from '@assets/images/common/porcelian/camelia/camelia-altin-salata.png';
import CameliaAltinTuzlukImage from '@assets/images/common/porcelian/camelia/camelia-altin-tuzluk.png';
import CameliaAltinServis3Image from '@assets/images/common/porcelian/camelia/camelia-altin-servis3.png';

import MaryImage from '@assets/images/common/porcelian/mary/mary.png';

import MaryGrayLeft from '@assets/images/common/porcelian/mary/mary-gray-left.png';
import MaryGrayRight from '@assets/images/common/porcelian/mary/mary-gray-right.png';

import MaryServisImage from '@assets/images/common/porcelian/mary/mary-servis.png';
import MaryCukurImage from '@assets/images/common/porcelian/mary/mary-cukur.png';
import MaryCorbaImage from '@assets/images/common/porcelian/mary/mary-corba.png';
import MaryServis2Image from '@assets/images/common/porcelian/mary/mary-servis2.png';
import MaryTatliImage from '@assets/images/common/porcelian/mary/mary-tatli.png';
import MarySalataImage from '@assets/images/common/porcelian/mary/mary-salata.png';
import MaryTuzlukImage from '@assets/images/common/porcelian/mary/mary-tuzluk.png';
import MaryServis3Image from '@assets/images/common/porcelian/mary/mary-servis3.png';

import LisaLeftImage from '@assets/images/common/porcelian/lisa/lisa-left.png';
import LisaRightImage from '@assets/images/common/porcelian/lisa/lisa-right.png';

import LisaServisImage from '@assets/images/common/porcelian/lisa/lisa-servis.png';
import LisaCukurImage from '@assets/images/common/porcelian/lisa/lisa-cukur.png';
import LisaCorbaImage from '@assets/images/common/porcelian/lisa/lisa-corba.png';
import LisaServis2Image from '@assets/images/common/porcelian/lisa/lisa-servis2.png';
import LisaTatliImage from '@assets/images/common/porcelian/lisa/lisa-tatli.png';
import LisaSalataImage from '@assets/images/common/porcelian/lisa/lisa-salata.png';
import LisaTuzlukImage from '@assets/images/common/porcelian/lisa/lisa-tuzluk.png';
import LisaServis3Image from '@assets/images/common/porcelian/lisa/lisa-servis3.png';

import CarlaLeftImage from '@assets/images/common/porcelian/carla/carla-gold-left.png';
import CarlaRightImage from '@assets/images/common/porcelian/carla/carla-gold-right.png';

import CarlaGoldServisImage from '@assets/images/common/porcelian/carla/carl-gold-servis.png';
import CarlaGoldCorbaImage from '@assets/images/common/porcelian/carla/carl-gold-corba.png';
import CarlaGoldServis2Image from '@assets/images/common/porcelian/carla/carl-gold-servis2.png';
import CarlaGoldTatliImage from '@assets/images/common/porcelian/carla/carl-gold-tatli.png';
import CarlaGoldCukurImage from '@assets/images/common/porcelian/carla/carl-gold-cukur.png';
import CarlaGoldTuzlukImage from '@assets/images/common/porcelian/carla/carl-gold-tuzluk.png';
import CarlaGoldServis3Image from '@assets/images/common/porcelian/carla/carl-gold-servis3.png';

const SienaData = {
    gold: {
        id: 'gold',
        name: 'SIENA GOLD MAJESTIC KOLEKSİYON PARÇALARI',
        mainImage: SienaGoldMiddeleImage,
        leftImage: SienaGoldKoleksiyonLeftImage,
        rightImage: SienaGoldKoleksiyonRightImage,
        products: [
            {
                id: 1,
                name: 'Servis Tabağı (25 cm)',
                description: 'Yemek sunumları için ideal genişlik',
                image: DreamServisImage
            },
            {
                id: 2,
                name: 'Çukur Tabak (19 cm)',
                description: 'Sulu yemekler için ideal derinlik',
                image: DreamCukurImage
            },
            {
                id: 3,
                name: 'Çorba Kasesi (13 cm)',
                description:
                    'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
                image: DreamCorbaImage
            },
            {
                id: 4,
                name: 'Tatlı Tabağı (20 cm)',
                description: 'Tatlı ve meze sunumları için ideal ölçü',
                image: DreamTatliImage
            },
            {
                id: 5,
                name: 'Salata Tabağı (25 cm)',
                description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
                image: DreamSalataImage
            },
            {
                id: 6,
                name: 'Tuzluk & Karabiberlik',
                description: 'Minimal form, maksimum işlev',
                image: DreamTuzlukImage
            }
        ]
    },
    gray: {
        id: 'gray',
        name: 'SIENA GÜMÜŞ MAJESTIC KOLEKSİYON PARÇALARI',
        mainImage: SienaGrayMiddleImage,
        leftImage: SienaGrayLeftImage,
        rightImage: SienaGrayRightImage,
        products: [
            {
                id: 1,
                name: 'Servis Tabağı (25 cm)',
                description: 'Yemek sunumları için ideal genişlik',
                image: SienaGrayServisImage
            },
            {
                id: 2,
                name: 'Çukur Tabak (19 cm)',
                description: 'Sulu yemekler için ideal derinlik',
                image: SienaGrayCukurImage
            },
            {
                id: 3,
                name: 'Çorba Kasesi (13 cm)',
                description:
                    'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
                image: SienaGrayCorbaImage
            },
            {
                id: 4,
                name: 'Tatlı Tabağı (20 cm)',
                description: 'Tatlı ve meze sunumları için ideal ölçü',
                image: SienaGrayTatliImage
            },
            {
                id: 5,
                name: 'Salata Tabağı (25 cm)',
                description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
                image: SienaGraySalataImage
            },
            {
                id: 6,
                name: 'Tuzluk & Karabiberlik',
                description: 'Minimal form, maksimum işlev',
                image: SienaGrayTuzlukImage
            }
        ]
    }
};

const DreamSetData = [
    {
        id: 1,
        name: 'Servis Tabağı (25 cm)',
        description: 'Yemek sunumları için ideal genişlik',
        image: DreamServisImage
    },
    {
        id: 2,
        name: 'Çukur Tabak (19 cm)',
        description: 'Sulu yemekler için ideal derinlik',
        image: DreamCukurImage
    },
    {
        id: 3,
        name: 'Çorba Kasesi (13 cm)',
        description:
            'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
        image: DreamCorbaImage
    },
    {
        id: 4,
        name: 'Tatlı Tabağı (20 cm)',
        description: 'Tatlı ve meze sunumları için ideal ölçü',
        image: DreamTatliImage
    },
    {
        id: 5,
        name: 'Salata Tabağı (25 cm)',
        description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
        image: DreamSalataImage
    },
    {
        id: 6,
        name: 'Tuzluk & Karabiberlik',
        description: 'Minimal form, maksimum işlev',
        image: DreamTuzlukImage
    }
];

const CameliaSetData = [
    {
        id: 1,
        name: 'Servis Tabağı (25 cm)',
        description: 'Yemek sunumları için ideal genişlik',
        image: CameliaServisImage
    },
    {
        id: 2,
        name: 'Çukur Tabak (19 cm)',
        description: 'Sulu yemekler için ideal derinlik',
        image: CameliaCukurImage
    },
    {
        id: 3,
        name: 'Çorba Kasesi (13 cm)',
        description:
            'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
        image: CameliaCorbaImage
    },
    {
        id: 4,
        name: 'Servis Tabağı (33 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: CameliaServis2Image
    },
    {
        id: 5,
        name: 'Tatlı Tabağı (20 cm)',
        description: 'Tatlı ve meze sunumları için ideal ölçü',
        image: CameliaTatliImage
    },
    {
        id: 6,
        name: 'Salata Tabağı (25 cm)',
        description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
        image: CameliaSalataImage
    },
    {
        id: 7,
        name: 'Tuzluk & Karabiberlik',
        description: 'Minimal form, maksimum işlev',
        image: CameliaTuzlukImage
    },
    {
        id: 8,
        name: 'Servis Tabağı (25 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: CameliaServis2Image
    }
];

const CameliaGoldSetData = [
    {
        id: 1,
        name: 'Servis Tabağı (25 cm)',
        description: 'Yemek sunumları için ideal genişlik',
        image: CameliaAltinServisImage
    },
    {
        id: 2,
        name: 'Çukur Tabak (19 cm)',
        description: 'Sulu yemekler için ideal derinlik',
        image: CameliaAltinCukurImage
    },
    {
        id: 3,
        name: 'Çorba Kasesi (13 cm)',
        description:
            'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
        image: CameliaAltinCorbaImage
    },
    {
        id: 4,
        name: 'Servis Tabağı (33 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: CameliaAltinServis2Image
    },
    {
        id: 5,
        name: 'Tatlı Tabağı (20 cm)',
        description: 'Tatlı ve meze sunumları için ideal ölçü',
        image: CameliaAltinTatliImage
    },
    {
        id: 6,
        name: 'Salata Tabağı (25 cm)',
        description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
        image: CameliaAltinSalataImage
    },
    {
        id: 7,
        name: 'Tuzluk & Karabiberlik',
        description: 'Minimal form, maksimum işlev',
        image: CameliaAltinTuzlukImage
    },
    {
        id: 8,
        name: 'Servis Tabağı (25 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: CameliaAltinServis3Image
    }
];

const MarySetData = [
    {
        id: 1,
        name: 'Servis Tabağı (25 cm)',
        description: 'Yemek sunumları için ideal genişlik',
        image: MaryServisImage
    },
    {
        id: 2,
        name: 'Çukur Tabak (19 cm)',
        description: 'Sulu yemekler için ideal derinlik',
        image: MaryCukurImage
    },
    {
        id: 3,
        name: 'Çorba Kasesi (13 cm)',
        description:
            'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
        image: MaryCorbaImage
    },
    {
        id: 4,
        name: 'Servis Tabağı (33 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: MaryServis2Image
    },
    {
        id: 5,
        name: 'Tatlı Tabağı (20 cm)',
        description: 'Tatlı ve meze sunumları için ideal ölçü',
        image: MaryTatliImage
    },
    {
        id: 6,
        name: 'Salata Tabağı (25 cm)',
        description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
        image: MarySalataImage
    },
    {
        id: 7,
        name: 'Tuzluk & Karabiberlik',
        description: 'Minimal form, maksimum işlev',
        image: MaryTuzlukImage
    },
    {
        id: 8,
        name: 'Servis Tabağı (25 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: MaryServis3Image
    }
];

const LisaSetData = [
    {
        id: 1,
        name: 'Servis Tabağı (25 cm)',
        description: 'Yemek sunumları için ideal genişlik',
        image: LisaServisImage
    },
    {
        id: 2,
        name: 'Çukur Tabak (19 cm)',
        description: 'Sulu yemekler için ideal derinlik',
        image: LisaCukurImage
    },
    {
        id: 3,
        name: 'Çorba Kasesi (13 cm)',
        description:
            'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
        image: LisaCorbaImage
    },
    {
        id: 4,
        name: 'Servis Tabağı (33 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: LisaServis2Image
    },
    {
        id: 5,
        name: 'Tatlı Tabağı (20 cm)',
        description: 'Tatlı ve meze sunumları için ideal ölçü',
        image: LisaTatliImage
    },
    {
        id: 6,
        name: 'Salata Tabağı (25 cm)',
        description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
        image: LisaSalataImage
    },
    {
        id: 7,
        name: 'Tuzluk & Karabiberlik',
        description: 'Minimal form, maksimum işlev',
        image: LisaTuzlukImage
    },
    {
        id: 8,
        name: 'Servis Tabağı (25 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: LisaServis3Image
    }
];

const CarlaSetData = [
    {
        id: 1,
        name: 'Servis Tabağı (25 cm)',
        description: 'Yemek sunumları için ideal genişlik',
        image: CarlaGoldServisImage
    },
    {
        id: 2,
        name: 'Çorba Kasesi (13 cm)',
        description:
            'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
        image: CarlaGoldCorbaImage
    },
    {
        id: 3,
        name: 'Servis Tabağı (33 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: CarlaGoldServis2Image
    },
    {
        id: 4,
        name: 'Tatlı Tabağı (20 cm)',
        description: 'Tatlı ve meze sunumları için ideal ölçü',
        image: CarlaGoldTatliImage
    },
    {
        id: 5,
        name: 'Çukur Tabak (19 cm)',
        description: 'Sulu yemekler için ideal derinlik',
        image: CarlaGoldCukurImage
    },
    {
        id: 6,
        name: 'Tuzluk & Karabiberlik',
        description: 'Minimal form, maksimum işlev',
        image: CarlaGoldTuzlukImage
    },
    {
        id: 7,
        name: 'Servis Tabağı (25 cm)',
        description: 'Ortak paylaşım tabakları için sofrada uyum sağlayan form',
        image: CarlaGoldServis3Image
    }
];

const Porcelain: FC = () => {
    const t = useTrans();
    const [selectedVariant, setSelectedVariant] = useState<'gold' | 'gray'>(
        'gold'
    );

    const currentSienaData = SienaData[selectedVariant];

    return (
        <section className=" grid gap-8 py-16 lg:gap-20">
            <div className="container grid items-center justify-center gap-5">
                <h1 className="text-center font-sans text-4xl text-primary-600 lg:text-6xl">
                    Nehir Porselen{' '}
                    <span className="font-spectral font-thin italic">
                        Koleksiyonu
                    </span>
                </h1>
                <div className="grid max-w-3xl items-center justify-center gap-5 text-center font-sans font-light text-secondary-800">
                    <p>
                        Zamansız tasarımlarıyla sofraları şıklığın bir parçasına
                        dönüştürerek, her nesle ilham veren bir miras. Nehir
                        porselen takımlarının isimleriyle hayat bulan her
                        koleksiyon, özel bir anıya ya da hikayeye dayanan bir
                        tasarım öyküsüdür.
                    </p>
                    <p>
                        Exculusive bone porselen malzemeyle üretilen, aşınmaya
                        karşı dayanıklı, antibakteriyel, uzun ömürlü ve 2 yıl
                        üretici garantili Nehir Porselen Koleksiyonlarını şimdi
                        keşfedin.
                    </p>
                </div>
            </div>
            <div className="container grid grid-cols-1 gap-3 lg:grid-cols-3">
                <UiImage
                    src={MargaretTopLeftImage}
                    alt=""
                    width={1000}
                    height={1000}
                    className="object-cover lg:col-span-1 lg:h-[500px] "
                />
                <UiImage
                    src={MargaretTopRightImage}
                    alt=""
                    width={1000}
                    height={1000}
                    className="lg:col-span-2 lg:h-[500px]"
                />
            </div>
            <div className="container grid gap-3">
                <p className="font-sans font-light text-secondary-800">
                    Margaret Black Dream 57 Parça Porselen Yemek Takımı
                </p>
                <UiImage
                    src={MargaretTopBottomImage}
                    alt=""
                    width={1000}
                    height={1000}
                    className="h-[460px] w-full object-cover lg:col-span-2 lg:h-[500px]"
                />
            </div>
            <div className="container flex w-full flex-col-reverse items-center justify-center gap-5 lg:flex-row lg:gap-24">
                <UiImage
                    src={MargaretDreamImage}
                    alt=""
                    width={600}
                    height={600}
                />
                <div className="flex max-w-xs flex-col items-center justify-center gap-3 lg:max-w-sm">
                    <h2 className=" text-center font-sans text-4xl text-primary-600 lg:text-start lg:text-6xl">
                        Margaret Black Dream{' '}
                        <span className="font-spectral font-thin italic">
                            Koleksiyonu
                        </span>
                    </h2>
                    <p className="text-center font-sans text-xl text-primary-600 lg:text-3xl">
                        Sofranızın yeni ikonu
                    </p>
                    <UiImage
                        src={AsagiKaydirImage}
                        alt=""
                        width={90}
                        height={240}
                    />
                </div>
            </div>
            <div className=" bg-custom-gradient p-9">
                <div className="container grid w-full  grid-cols-5">
                    <div className="col-span-3 space-y-20 ">
                        <div className="grid gap-8">
                            <h3 className="max-w-lg font-sans text-2xl text-secondary-800">
                                Koleksiyon, Biedermeier döneminin zarif
                                stilinden ilham alır, ancak çağdaş bir dokunuşla
                                yeniden şekillendirilmiştir. Her parça, Nehir’in
                                özgün işçilik anlayışını yansıtır. 
                            </h3>
                            <div className="grid max-w-md gap-5 text-sm font-light leading-5 text-secondary-800">
                                <p>
                                    Koleksiyon, Biedermeier döneminin zarif
                                    stilinden ilham alır, ancak çağdaş bir
                                    dokunuşla yeniden şekillendirilmiştir. Her
                                    parça, Nehir’in özgün işçilik anlayışını
                                    yansıtır. 
                                </p>
                                <p>
                                    Yüzyıllık bir geleneğin Nehir zarafetiyle
                                    harmanlandığı Margaret Black Dream
                                    Koleksiyonu masanızı sadece bir yemek alanı
                                    değil, bir sanat sergisine dönüştürür.
                                </p>
                            </div>
                        </div>
                        <div className="flex gap-7">
                            <UiImage
                                src={GriBottomImage}
                                alt=""
                                width={300}
                                height={300}
                            />
                            <div className="grid max-w-xs  text-sm font-light leading-5 text-secondary-800">
                                <p>
                                    Margaret Black Dream Koleksiyonu, parlak
                                    beyaz porselen üzerine işlenen kabartmalı
                                    beyaz işlemeler ve ince motiflerle
                                    bezenmiştir. Fransız neoklasik detaylarından
                                    ilham alınarak tasarlanan desenler,
                                    sofistike çizgiler ve arma sembolüyle
                                    birleşmiştir.
                                </p>
                                <p>
                                    Koleksiyonun içindeki arma, yalnızca bir
                                    sembol değil, aynı zamanda güç, zarafet ve
                                    köklü bir mirası da temsil eder.
                                </p>
                                <UiButton
                                    variant="outline"
                                    className="h-11 w-60 rounded-md border-secondary-800 hover:border-secondary-800 focus:border-secondary-800 focus:ring-secondary-800"
                                >
                                    ŞİMDİ ALIŞVERİŞ YAPIN
                                </UiButton>
                            </div>
                        </div>
                    </div>
                    <UiImage
                        src={GriRightImage}
                        alt=""
                        width={500}
                        height={500}
                        className=" col-span-2 h-[500px] !w-[500px] object-cover"
                    />
                </div>
            </div>
            <div className="container grid gap-3">
                <p className="font-sans font-light text-secondary-800">
                    Margaret Black Dream Koleksİyon PARÇALARI
                </p>
                <div className=" grid grid-cols-1 gap-3 lg:grid-cols-3">
                    <UiImage
                        src={DreamKoleksiyonLeftImage}
                        alt=""
                        width={1000}
                        height={1000}
                        className="lg:col-span-2 lg:h-[500px]"
                    />
                    <UiImage
                        src={DreamKoleksiyonRightImage}
                        alt=""
                        width={1000}
                        height={1000}
                        className="object-cover lg:col-span-1 lg:h-[500px] "
                    />
                </div>
            </div>
            <div className="container grid gap-10">
                <h2 className="text-center font-sans text-6xl text-primary-600">
                    Margaret Black Dream{' '}
                    <span className="font-spectral font-thin italic">
                        Set İçeriği
                    </span>
                </h2>
                <div className="grid grid-cols-3 gap-14">
                    {DreamSetData.map(dream => (
                        <div key={dream.id} className="grid  gap-4">
                            <UiImage
                                src={dream.image}
                                alt={dream.name}
                                width={420}
                                height={420}
                            />
                            <div className="grid gap-2">
                                <h5 className="text-center text-xl font-semibold text-secondary-800">
                                    {dream.name}
                                </h5>
                                <p className="text-center text-xl text-[#626262]">
                                    {dream.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className=" bg-primary-600 p-9">
                <div className="container grid gap-8">
                    <div className="flex justify-between text-white ">
                        <div className="grid gap-3">
                            <h6 className="text-2xl">
                                Sadelikten Doğan Işıltı
                            </h6>
                            <h3 className="text-5xl">
                                Siena Gold Majestic{' '}
                                <span className="font-spectral font-thin italic">
                                    Koleksiyonu
                                </span>
                            </h3>
                        </div>
                        <div className="flex flex-col items-end justify-end font-light">
                            <p>
                                Alman Bauhaus dönemi sonrası modernizmin yalın
                                geometrisinden ve
                            </p>
                            <p>
                                Mid-Century döneminin metalik dokunuşlarından
                                ilham alır.
                            </p>
                            <p>
                                Her bir parça, sadeliğin içindeki ihtişamı
                                ortaya koyar.
                            </p>
                        </div>
                    </div>
                    <div className=" grid grid-cols-1 gap-3 lg:grid-cols-3">
                        <UiImage
                            src={SienaGoldLeftImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="object-cover lg:col-span-1 lg:h-[400px] "
                        />
                        <UiImage
                            src={SienaGoldRightImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="lg:col-span-2 lg:h-[400px]"
                        />
                    </div>
                </div>
            </div>
            <div className="container grid gap-14">
                <div className="flex w-full flex-col-reverse items-center justify-center gap-5 lg:flex-row lg:gap-24">
                    <UiImage
                        src={currentSienaData.mainImage}
                        alt=""
                        width={600}
                        height={600}
                    />
                    <div className="flex  max-w-lg flex-col items-center justify-center gap-8 text-center">
                        <div className="flex">
                            <div className="flex flex-col items-center justify-center gap-1">
                                <UiButton
                                    variant="link"
                                    className={`h-20 w-20 rounded-full p-1 focus:ring-1 focus:ring-secondary-800 ${
                                        selectedVariant === 'gold'
                                            ? 'ring-2 ring-primary-600'
                                            : ''
                                    }`}
                                    onClick={() => setSelectedVariant('gold')}
                                >
                                    <UiImage
                                        src={SienaGoldButtonImage}
                                        alt=""
                                        width={64}
                                        height={64}
                                    />
                                </UiButton>
                                <p>Gold</p>
                            </div>
                            <div className="flex flex-col items-center justify-center gap-1">
                                <UiButton
                                    variant="link"
                                    className={`h-20 w-20 rounded-full p-1 focus:ring-1 focus:ring-secondary-800 ${
                                        selectedVariant === 'gray'
                                            ? 'ring-2 ring-primary-600'
                                            : ''
                                    }`}
                                    onClick={() => setSelectedVariant('gray')}
                                >
                                    <UiImage
                                        src={SienaGrayButtonImage}
                                        alt=""
                                        width={64}
                                        height={64}
                                    />
                                </UiButton>
                                <p>Gümüş</p>
                            </div>
                        </div>
                        <p className="font-sans font-light text-secondary-800">
                            Parlak beyaz yüzey, zamansız bir zarafetin
                            ifadesiyken; çevresini saran ince altın şeritler,
                            her sofrada zarif bir çerçeve gibi parlar. Siena,
                            gündelik sadeliği özel anların ışıltısıyla
                            buluşturur.
                        </p>
                        <h2 className=" text-center font-sans text-4xl text-primary-600 lg:text-start lg:text-6xl">
                            Rafine ve
                            <span className="font-spectral font-thin italic">
                                Zarif
                            </span>
                        </h2>
                        <p className="text-center font-sans text-xl text-secondary-800 lg:text-xl">
                            Siena Koleksiyonu, bugünün zarif sofraları için
                            tasarlandı. Işıltıyı abartmadan sevenler için
                        </p>
                        <UiButton
                            variant="solid"
                            className="h-12 w-72 rounded-md border-primary-800 bg-primary-600 text-white hover:border-primary-800 hover:bg-primary-500 focus:border-primary-800 focus:bg-primary-500 focus:ring-primary-800"
                        >
                            ŞİMDİ ALIŞVERİŞ YAPIN
                        </UiButton>
                    </div>
                </div>
                <div className="grid gap-3">
                    <p className="font-sans font-light text-secondary-800">
                        {currentSienaData.name}
                    </p>
                    <div className=" grid grid-cols-1 gap-3 lg:grid-cols-3">
                        <UiImage
                            src={currentSienaData.leftImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="lg:col-span-2 lg:h-[500px]"
                        />
                        <UiImage
                            src={currentSienaData.rightImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="object-cover lg:col-span-1 lg:h-[500px] "
                        />
                    </div>
                </div>
                <div className=" grid gap-10">
                    <h2 className="text-center font-sans text-6xl text-primary-600">
                        Siena {selectedVariant === 'gold' ? 'Gold' : 'Gümüş'}{' '}
                        <span className="font-spectral font-thin italic">
                            Set İçeriği
                        </span>
                    </h2>
                    <div className="grid grid-cols-3 gap-14">
                        {currentSienaData.products.map(product => (
                            <div key={product.id} className="grid  gap-4">
                                <UiImage
                                    src={product.image}
                                    alt={product.name}
                                    width={420}
                                    height={420}
                                />
                                <div className="grid gap-2">
                                    <h5 className="text-center text-xl font-semibold text-secondary-800">
                                        {product.name}
                                    </h5>
                                    <p className="text-center text-xl text-[#626262]">
                                        {product.description}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <div className="container mt-8 grid gap-20">
                <div className="grid grid-cols-2 place-items-center gap-20">
                    <UiImage
                        src={CameliaTopImage}
                        alt=""
                        width={600}
                        height={600}
                    />
                    <div className="grid max-w-xl place-items-center gap-6">
                        <h2 className="text-center font-sans text-6xl text-primary-600">
                            Bir efsane{' '}
                            <span className="font-spectral font-thin italic">
                                bir hikaye
                            </span>
                        </h2>
                        <h5 className="text-center font-sans text-3xl text-primary-600">
                            Camelia Platin Yaldızlı Koleksiyonu
                        </h5>
                        <p className="max-w-md text-center font-sans font-light text-secondary-800">
                            Camelia Platin Yaldızlı Koleksiyonu, zamansız
                            şıklığı, dokunsal zarafeti ve kadim bir efsanenin
                            şifalı izlerini bir araya getirir. 
                        </p>
                        <p className="max-w-md text-center font-sans font-light text-secondary-800">
                            İlhamını, Anadolu mitolojisinin bilgelik ve koruma
                            sembolü Şahmeran’dan alır. Yarı insan, yarı yılan
                            formuyla doğanın sırlarını simgeleyen bu efsane,
                            koleksiyonun her parçasında yeniden hayat bulur.
                        </p>
                    </div>
                </div>
                <div className="grid grid-cols-2 place-items-center gap-20">
                    <div className="grid max-w-xl gap-9">
                        <p className="max-w-lg text-start font-sans font-light text-secondary-800">
                            Koleksiyona özgü kabartmalı karo desenler, yılan
                            derisini andıran yapısıyla sadece görsel bir zarafet
                            değil, aynı zamanda güç, dönüşüm ve bereketin
                            simgesidir.
                        </p>
                        <p className="max-w-lg text-start font-sans font-light text-secondary-800">
                            Parlak beyaz zemin, efsanenin saf ışığını taşırken;
                            ince metalik şeritler, modern yaşamın sadeliğini
                            sofistike bir çerçeveyle tamamlar.
                        </p>
                        <p className="max-w-lg text-start font-sans font-light text-secondary-800">
                            Camelia Platin Yaldızlı, geçmişin sembollerini
                            bugünün estetiğiyle buluşturarak her sofrada zarafet
                            kadar anlam da ifade eder. 
                        </p>
                        <p className="max-w-lg text-start font-sans font-light text-secondary-800">
                            Camelia Platin Yaldızlı Koleksiyonu her daveti
                            hafızalara kazımak, sofralara uğur ve bereket
                            taşımak isteyenler için tasarlandı.
                        </p>
                        <UiButton
                            variant="solid"
                            className="h-12 w-72 rounded-md border-primary-800 bg-primary-600 text-white hover:border-primary-800 hover:bg-primary-500 focus:border-primary-800 focus:bg-primary-500 focus:ring-primary-800"
                        >
                            ŞİMDİ ALIŞVERİŞ YAPIN
                        </UiButton>
                    </div>
                    <UiImage
                        src={CameliaTop2Image}
                        alt=""
                        width={600}
                        height={600}
                    />
                </div>
            </div>
            <div className="container grid gap-10">
                <h2 className="text-center font-sans text-6xl text-primary-600">
                    Camelia Platin Yaldızlı{' '}
                    <span className="font-spectral font-thin italic">
                        Koleksiyon Set İçeriği
                    </span>
                </h2>
                <div className="grid grid-cols-4 gap-8">
                    {CameliaSetData.map(dream => (
                        <div key={dream.id} className="grid  gap-4">
                            <UiImage
                                src={dream.image}
                                alt={dream.name}
                                width={420}
                                height={420}
                            />
                            <div className="grid gap-2">
                                <h5 className="text-center text-xl font-semibold text-secondary-800">
                                    {dream.name}
                                </h5>
                                <p
                                    className="text-center 
                                 text-[#626262]"
                                >
                                    {dream.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className="container mt-8 grid gap-20">
                <div className="grid grid-cols-2 place-items-center gap-20">
                    <UiImage
                        src={CameliaAltinLeftImage}
                        alt=""
                        width={600}
                        height={600}
                    />
                    <div className="grid max-w-xl place-items-center gap-6">
                        <h2 className="text-center font-sans text-6xl text-primary-600">
                            Doğanın{' '}
                            <span className="font-spectral font-thin italic">
                                zerafetiyle şekillenen
                            </span>
                        </h2>
                        <h5 className="text-center font-sans text-3xl text-primary-600">
                            Camelia Altın Yaldızlı Koleksiyonu
                        </h5>
                        <p className="max-w-md text-center font-sans text-xl font-medium text-secondary-800">
                            Zarafetin ve inceliğin kusursuz yorumu
                        </p>
                        <p className="max-w-xl text-center font-sans font-light text-secondary-800">
                            Doğanın en rafine dokunuşlarından ilham alan bu özel
                            koleksiyon, çiçek bahçelerinin büyüleyici armonisini
                            sofistike bir sanat eserine dönüştürüyor. Ustalıkla
                            kabartılan desenler, porselenin beyaz yüzeyinde
                            adeta kristalize olmuş zarafet gibi yükselirken;
                            altın detaylar, ışığın en zarif yansımalarını
                            yakalayarak her parçaya asil bir ışıltı kazandırıyor
                        </p>
                    </div>
                </div>
                <div className="grid grid-cols-2 place-items-center gap-20">
                    <div className="grid max-w-xl gap-9">
                        <p className="max-w-md text-start font-sans font-light text-secondary-800">
                            Her detay, titizlikle işlenmiş konturları ve narin
                            dokusuyla göz alıcı bir denge ve kusursuzluk sunuyor
                        </p>
                        <p className="max-w-md text-start font-sans font-medium text-secondary-800">
                            Her sofrada incelikle tasarlanmış bir davet, her
                            sunumda zamansız bir ihtişam
                        </p>
                        <UiButton
                            variant="solid"
                            className="h-12 w-72 rounded-md border-primary-800 bg-primary-600 text-white hover:border-primary-800 hover:bg-primary-500 focus:border-primary-800 focus:bg-primary-500 focus:ring-primary-800"
                        >
                            ŞİMDİ ALIŞVERİŞ YAPIN
                        </UiButton>
                    </div>
                    <UiImage
                        src={CameliaAltinRightImage}
                        alt=""
                        width={600}
                        height={600}
                    />
                </div>
            </div>
            <div className="container grid gap-10">
                <h2 className="text-center font-sans text-6xl text-primary-600">
                    Camelia Altın Yaldızlı{' '}
                    <span className="font-spectral font-thin italic">
                        Koleksiyon Set İçeriği
                    </span>
                </h2>
                <div className="grid grid-cols-4 gap-8">
                    {CameliaGoldSetData.map(dream => (
                        <div key={dream.id} className="grid  gap-4">
                            <UiImage
                                src={dream.image}
                                alt={dream.name}
                                width={420}
                                height={420}
                            />
                            <div className="grid gap-2">
                                <h5 className="text-center text-xl font-semibold text-secondary-800">
                                    {dream.name}
                                </h5>
                                <p
                                    className="text-center 
                                 text-[#626262]"
                                >
                                    {dream.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className="container flex w-full flex-col-reverse items-center justify-center gap-5 lg:flex-row lg:gap-24">
                <UiImage src={MaryImage} alt="" width={600} height={600} />
                <div className="flex max-w-xs flex-col items-center justify-center gap-3 lg:max-w-md">
                    <h2 className=" text-center font-sans text-4xl text-primary-600 lg:text-start lg:text-6xl">
                        Margaret Mary{' '}
                        <span className="font-spectral font-thin italic">
                            Koleksiyonu
                        </span>
                    </h2>
                    <p className="max-w-[240px] text-start font-sans text-xl text-primary-600 lg:text-3xl">
                        Zamansız Bir İhtişamın Mirası
                    </p>
                    <UiImage
                        src={AsagiKaydirImage}
                        alt=""
                        width={90}
                        height={240}
                        className="-ml-24"
                    />
                </div>
            </div>
            <div className=" bg-custom-gradient p-9">
                <div className="container grid w-full  grid-cols-5">
                    <div className="col-span-3 space-y-20 ">
                        <div className="grid gap-8">
                            <h3 className="max-w-sm font-sans text-2xl text-secondary-800">
                                Margaret Mary Koleksiyonu, 20. yüzyılın başında
                                tasarım dünyasını değiştiren Art Nouveau’nun
                                enerjisini modern sofralara taşır
                            </h3>
                            <div className="grid max-w-md gap-5 text-sm font-light leading-5 text-secondary-800">
                                <p>
                                    Koleksiyonun yüzeyini saran floral desenler,
                                    o dönemin simgesi olan stilize çiçek ve
                                    yaprak motifleriyle bezeli bir sanat eserine
                                    dönüşür.
                                </p>
                                <p>
                                    Her parçada, doğanın zarif ritmi hissedilir.
                                    İnce çizgilerle örülmüş bu motifler, sanki
                                    bir duvar kağıdından ya da vitraydan
                                    porselene taşınmış gibidir. Beyazın saflığı
                                    ve desenin ritmik düzeni, zarafetin en
                                    sessiz halini yansıtır.
                                </p>
                            </div>
                        </div>
                        <div className="flex gap-7">
                            <UiImage
                                src={MaryGrayLeft}
                                alt=""
                                width={300}
                                height={300}
                            />
                            <div className="max-w-xs space-y-6  text-sm font-light leading-5 text-secondary-800">
                                <p>
                                    Margaret Mary Koleksiyonu, yalnızca bir
                                    porselen takımı değil; estetiğin gündelik
                                    yaşama sızdığı bir çağın yeniden yorumu.
                                </p>

                                <UiButton
                                    variant="outline"
                                    className="h-11 w-60 rounded-md border-secondary-800 hover:border-secondary-800 focus:border-secondary-800 focus:ring-secondary-800"
                                >
                                    ŞİMDİ ALIŞVERİŞ YAPIN
                                </UiButton>
                            </div>
                        </div>
                    </div>
                    <UiImage
                        src={MaryGrayRight}
                        alt=""
                        width={500}
                        height={500}
                        className=" col-span-2 h-[500px] !w-[500px] object-cover"
                    />
                </div>
            </div>
            <div className="container grid gap-10">
                <h2 className="text-center font-sans text-6xl text-primary-600">
                    Margaret Mary{' '}
                    <span className="font-spectral font-thin italic">
                        Koleksiyon Set İçeriği
                    </span>
                </h2>
                <div className="grid grid-cols-4 gap-8">
                    {MarySetData.map(dream => (
                        <div key={dream.id} className="grid  gap-4">
                            <UiImage
                                src={dream.image}
                                alt={dream.name}
                                width={420}
                                height={420}
                            />
                            <div className="grid gap-2">
                                <h5 className="text-center text-xl font-semibold text-secondary-800">
                                    {dream.name}
                                </h5>
                                <p
                                    className="text-center 
                                 text-[#626262]"
                                >
                                    {dream.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className="container mt-8 grid gap-20">
                <div className="grid grid-cols-2 place-items-center gap-20">
                    <UiImage
                        src={LisaLeftImage}
                        alt=""
                        width={600}
                        height={600}
                    />
                    <div className="grid max-w-xl place-items-center gap-6">
                        <h2 className="max-w-sm text-center font-sans text-6xl text-primary-600">
                            Geometriden{' '}
                            <span className="font-spectral font-thin italic">
                                Doğan Asalet
                            </span>
                        </h2>
                        <h5 className="text-center font-sans text-3xl text-primary-600">
                            Margaret Lisa Koleksiyonu
                        </h5>
                        <p className="max-w-md text-center font-sans text-xl font-medium text-secondary-800">
                            Kökeninde düzen, deseninde sonsuzluk var
                        </p>
                        <p className="max-w-md text-center font-sans font-light text-secondary-800">
                            Margaret Lisa Koleksiyonu, doğu mimarisinin
                            derinliklerinden ilham alır. Selçuklu çinilerinin
                            ritmik tekrarı ve taç kapıların zarif oyma
                            sanatları, porselenin yüzeyinde yeniden hayat bulur.
                        </p>
                    </div>
                </div>
                <div className="grid grid-cols-2 place-items-center gap-20">
                    <div className="grid max-w-xl gap-9">
                        <p className="max-w-md text-start font-sans font-light text-secondary-800">
                            Geometrinin içinden doğan bu desenler, yalnızca bir
                            süsleme değil; bir düzenin, bir akışın, bir
                            geleneğin izidir.Altın şeritlerle çevrelenen her
                            parça, geçmişin ihtişamını bugünün sadeliğiyle
                            buluşturur.
                        </p>
                        <p className="max-w-md text-start font-sans font-medium text-secondary-800">
                            Margaret Lisa Koleksiyonu, yalnızca bir sofra değil;
                            bir hafıza, bir ritüeldir.
                        </p>
                        <UiButton
                            variant="solid"
                            className="h-12 w-72 rounded-md border-primary-800 bg-primary-600 text-white hover:border-primary-800 hover:bg-primary-500 focus:border-primary-800 focus:bg-primary-500 focus:ring-primary-800"
                        >
                            ŞİMDİ ALIŞVERİŞ YAPIN
                        </UiButton>
                    </div>
                    <UiImage
                        src={LisaRightImage}
                        alt=""
                        width={600}
                        height={600}
                    />
                </div>
            </div>
            <div className="container grid gap-10">
                <h2 className="text-center font-sans text-6xl text-primary-600">
                    Margaret Lisa{' '}
                    <span className="font-spectral font-thin italic">
                        Koleksiyon Set İçeriği
                    </span>
                </h2>
                <div className="grid grid-cols-4 gap-8">
                    {LisaSetData.map(dream => (
                        <div key={dream.id} className="grid  gap-4">
                            <UiImage
                                src={dream.image}
                                alt={dream.name}
                                width={420}
                                height={420}
                            />
                            <div className="grid gap-2">
                                <h5 className="text-center text-xl font-semibold text-secondary-800">
                                    {dream.name}
                                </h5>
                                <p
                                    className="text-center 
                                 text-[#626262]"
                                >
                                    {dream.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className=" bg-custom-gradient p-9">
                <div className="container grid gap-8">
                    <div className="flex justify-between text-secondary-800 ">
                        <div className="grid gap-3">
                            <h6 className="text-2xl">Sonsuzluğun Işıltısı</h6>
                            <h3 className="text-5xl">
                                Carla Gold Yaldızlı{' '}
                                <span className="font-spectral font-thin italic">
                                    Koleksiyonu
                                </span>
                            </h3>
                        </div>
                        <div className="flex flex-col items-end justify-end font-light">
                            <p className="mb-4 text-lg">
                                Bir geceye serpilen yıldız tozu gibi etkileyici
                            </p>
                            <p>
                                Carla Gold Yaldızlı Koleksiyonu, ilhamını
                                gökyüzünün sonsuzluğundan
                            </p>
                            <p>
                                ve yıldızların savruluşundaki o rastlantısal
                                ahenkten alır.
                            </p>
                            <p>
                                Porselen yüzeyi çevreleyen altın serpintiler,
                                samanyolunu anımsatır.
                            </p>
                        </div>
                    </div>
                    <div className=" grid grid-cols-1 gap-3 lg:grid-cols-3">
                        <UiImage
                            src={CarlaLeftImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="object-cover lg:col-span-1 lg:h-[400px] "
                        />
                        <UiImage
                            src={CarlaRightImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="lg:col-span-2 lg:h-[400px]"
                        />
                    </div>
                </div>
            </div>
            <div className="container grid items-center justify-center gap-5">
                <div className="grid max-w-3xl place-items-center items-center justify-center gap-5 text-center font-sans font-light text-secondary-800">
                    <div>
                        <p>
                            Sanki yıldız tozu porselene konmuş, ya da altın
                            damlalar bir fırçadan özgürce düşmüş gibi…
                        </p>
                        <p>
                            Bu desenler ne tam düzenli ne de tamamen rastgele.
                        </p>
                    </div>
                    <p>
                        Her parça, kendi içinde bir ritme sahip; ama tüm
                        koleksiyon bir bütün olarak ahenkli.
                    </p>
                    <div>
                        {' '}
                        <p>
                            Carla Gold Yaldızlı Koleksiyonu, sofrada alışılmışın
                            dışında ama her zaman şık duran bir çizgi sunar.
                        </p>
                        <p>Gecenin zarafetini sofralara taşır.</p>
                    </div>
                    <UiButton
                        variant="solid"
                        className="mt-12 h-14 w-72 rounded-md border-primary-800 bg-primary-600 text-white hover:border-primary-800 hover:bg-primary-500 focus:border-primary-800 focus:bg-primary-500 focus:ring-primary-800"
                    >
                        ŞİMDİ ALIŞVERİŞ YAPIN
                    </UiButton>
                </div>
            </div>
            <div className="container grid gap-10">
                <h2 className="text-center font-sans text-6xl text-primary-600">
                    Margaret Lisa{' '}
                    <span className="font-spectral font-thin italic">
                        Koleksiyon Set İçeriği
                    </span>
                </h2>
                <div className="grid grid-cols-4 gap-8">
                    {ca.map(dream => (
                        <div key={dream.id} className="grid  gap-4">
                            <UiImage
                                src={dream.image}
                                alt={dream.name}
                                width={420}
                                height={420}
                            />
                            <div className="grid gap-2">
                                <h5 className="text-center text-xl font-semibold text-secondary-800">
                                    {dream.name}
                                </h5>
                                <p
                                    className="text-center 
                                 text-[#626262]"
                                >
                                    {dream.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};

if (isDev) {
    Porcelain.displayName = 'Porcelain';
}

export default Porcelain;
