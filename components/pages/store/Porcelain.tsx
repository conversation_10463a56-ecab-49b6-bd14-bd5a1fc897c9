import {FC} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiButton, UiImage} from '@core/components/ui';

import MargaretTopLeftImage from '@assets/images/common/porcelian/margaret/margaret-top.png';
import MargaretTopRightImage from '@assets/images/common/porcelian/margaret/margaret-top-right.jpg';
import MargaretTopBottomImage from '@assets/images/common/porcelian/margaret/margaret-bottom.png';
import MargaretDreamImage from '@assets/images/common/porcelian/margaret/margaret-dream.png';
import AsagiKaydirImage from '@assets/images/common/porcelian/margaret/asagi-kaydir.png';
import GriRightImage from '@assets/images/common/porcelian/margaret/gri-right.png';
import GriBottomImage from '@assets/images/common/porcelian/margaret/gri-bottom.png';
import DreamKoleksiyonLeftImage from '@assets/images/common/porcelian/margaret/dream-koleksiyon-left.png';
import DreamKoleksiyonRightImage from '@assets/images/common/porcelian/margaret/dream-koleksiyon-right.png';

import DreamCorbaImage from '@assets/images/common/porcelian/dream-set/margaret-corba.png';
import DreamCukurImage from '@assets/images/common/porcelian/dream-set/margaret-cukur.png';
import DreamSalataImage from '@assets/images/common/porcelian/dream-set/margaret-salata.png';
import DreamServisImage from '@assets/images/common/porcelian/dream-set/margaret-servis.png';
import DreamTatliImage from '@assets/images/common/porcelian/dream-set/margaret-tatli.png';
import DreamTuzlukImage from '@assets/images/common/porcelian/dream-set/margaret-tuzluk.png';

import SienaGoldLeftImage from '@assets/images/common/porcelian/siena/siena-gold-left.png';
import SienaGoldRightImage from '@assets/images/common/porcelian/siena/siena-gold-right.png';

import SienaGoldMiddeleImage from '@assets/images/common/porcelian/siena/siena-middle.png';
import SienaGoldButtonImage from '@assets/images/common/porcelian/siena/siena-gold-button.png';
import SienaGrayButtonImage from '@assets/images/common/porcelian/siena/siena-gray-button.png';

import SienaGrayMiddleImage from '@assets/images/common/porcelian/siena/siena-gray-middle.png';
import SienaGrayLeftImage from '@assets/images/common/porcelian/siena/siena-gray-left.png';
import SienaGrayRightImage from '@assets/images/common/porcelian/siena/siena-gray-right.png';

import SienaGrayServisImage from '@assets/images/common/porcelian/siena/siena-gray-servis.png';
import SienaGrayCukurImage from '@assets/images/common/porcelian/siena/siena-gray-cukur.png';
import SienaGrayCorbaImage from '@assets/images/common/porcelian/siena/siena-gray-corba.png';
import SienaGrayTatliImage from '@assets/images/common/porcelian/siena/siena-gray-tatli.png';
import SienaGraySalataImage from '@assets/images/common/porcelian/siena/siena-gray-salata.png';
import SienaGrayTuzlukImage from '@assets/images/common/porcelian/siena/siena-gray-tuzluk.png';

const SienaData = [
    {
        id: 1,
        name: 'Siena Gold Majestic koleksİYON PARÇALARI',
        mainImage: SienaGoldMiddeleImage,
        leftImage: SienaGoldLeftImage,
        rightImage: SienaGoldRightImage,
        products: [
            {
                id: 1,
                name: 'Servis Tabağı (25 cm)',
                description: 'Yemek sunumları için ideal genişlik',
                image: DreamServisImage
            },
            {
                id: 2,
                name: 'Çukur Tabak (19 cm)',
                description: 'Sulu yemekler için ideal derinlik',
                image: DreamCukurImage
            },
            {
                id: 3,
                name: 'Çorba Kasesi (13 cm)',
                description:
                    'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
                image: DreamCorbaImage
            },
            {
                id: 4,
                name: 'Tatlı Tabağı (20 cm)',
                description: 'Tatlı ve meze sunumları için ideal ölçü',
                image: DreamTatliImage
            },
            {
                id: 5,
                name: 'Salata Tabağı (25 cm)',
                description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
                image: DreamSalataImage
            },
            {
                id: 6,
                name: 'Tuzluk & Karabiberlik',
                description: 'Minimal form, maksimum işlev',
                image: DreamTuzlukImage
            }
        ]
    },
    {
        id: 1,
        name: 'Siena Gümüş Majestic koleksİYON PARÇALARI',
        mainImage: SienaGrayMiddleImage,
        leftImage: SienaGrayLeftImage,
        rightImage: SienaGrayRightImage,
        products: [
            {
                id: 1,
                name: 'Servis Tabağı (25 cm)',
                description: 'Yemek sunumları için ideal genişlik',
                image: SienaGrayServisImage
            },
            {
                id: 2,
                name: 'Çukur Tabak (19 cm)',
                description: 'Sulu yemekler için ideal derinlik',
                image: SienaGrayCukurImage
            },
            {
                id: 3,
                name: 'Çorba Kasesi (13 cm)',
                description:
                    'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
                image: SienaGrayCorbaImage
            },
            {
                id: 4,
                name: 'Tatlı Tabağı (20 cm)',
                description: 'Tatlı ve meze sunumları için ideal ölçü',
                image: SienaGrayTatliImage
            },
            {
                id: 5,
                name: 'Salata Tabağı (25 cm)',
                description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
                image: SienaGraySalataImage
            },
            {
                id: 6,
                name: 'Tuzluk & Karabiberlik',
                description: 'Minimal form, maksimum işlev',
                image: SienaGrayTuzlukImage
            }
        ]
    }
];

const DreamSetData = [
    {
        id: 1,
        name: 'Servis Tabağı (25 cm)',
        description: 'Yemek sunumları için ideal genişlik',
        image: DreamServisImage
    },
    {
        id: 2,
        name: 'Çukur Tabak (19 cm)',
        description: 'Sulu yemekler için ideal derinlik',
        image: DreamCukurImage
    },
    {
        id: 3,
        name: 'Çorba Kasesi (13 cm)',
        description:
            'Çorbalar için ideal derinlik ve sıcaklığı koruyan tasarım',
        image: DreamCorbaImage
    },
    {
        id: 4,
        name: 'Tatlı Tabağı (20 cm)',
        description: 'Tatlı ve meze sunumları için ideal ölçü',
        image: DreamTatliImage
    },
    {
        id: 5,
        name: 'Salata Tabağı (25 cm)',
        description: 'Salatalar için ideal derinlik ve sunum kolaylığı',
        image: DreamSalataImage
    },
    {
        id: 6,
        name: 'Tuzluk & Karabiberlik',
        description: 'Minimal form, maksimum işlev',
        image: DreamTuzlukImage
    }
];

const Porcelain: FC = () => {
    const t = useTrans();

    return (
        <section className=" grid gap-8 py-16 lg:gap-16">
            <div className="container grid items-center justify-center gap-5">
                <h1 className="text-center font-sans text-4xl text-primary-600 lg:text-6xl">
                    Nehir Porselen{' '}
                    <span className="font-spectral font-thin italic">
                        Koleksiyonu
                    </span>
                </h1>
                <div className="grid max-w-3xl items-center justify-center gap-5 text-center font-sans font-light text-secondary-800">
                    <p>
                        Zamansız tasarımlarıyla sofraları şıklığın bir parçasına
                        dönüştürerek, her nesle ilham veren bir miras. Nehir
                        porselen takımlarının isimleriyle hayat bulan her
                        koleksiyon, özel bir anıya ya da hikayeye dayanan bir
                        tasarım öyküsüdür.
                    </p>
                    <p>
                        Exculusive bone porselen malzemeyle üretilen, aşınmaya
                        karşı dayanıklı, antibakteriyel, uzun ömürlü ve 2 yıl
                        üretici garantili Nehir Porselen Koleksiyonlarını şimdi
                        keşfedin.
                    </p>
                </div>
            </div>
            <div className="container grid grid-cols-1 gap-3 lg:grid-cols-3">
                <UiImage
                    src={MargaretTopLeftImage}
                    alt=""
                    width={1000}
                    height={1000}
                    className="object-cover lg:col-span-1 lg:h-[500px] "
                />
                <UiImage
                    src={MargaretTopRightImage}
                    alt=""
                    width={1000}
                    height={1000}
                    className="lg:col-span-2 lg:h-[500px]"
                />
            </div>
            <div className="container grid gap-3">
                <p className="font-sans font-light text-secondary-800">
                    Margaret Black Dream 57 Parça Porselen Yemek Takımı
                </p>
                <UiImage
                    src={MargaretTopBottomImage}
                    alt=""
                    width={1000}
                    height={1000}
                    className="h-[460px] w-full object-cover lg:col-span-2 lg:h-[500px]"
                />
            </div>
            <div className="container flex w-full flex-col-reverse items-center justify-center gap-5 lg:flex-row lg:gap-24">
                <UiImage
                    src={MargaretDreamImage}
                    alt=""
                    width={600}
                    height={600}
                />
                <div className="flex max-w-xs flex-col items-center justify-center gap-3 lg:max-w-sm">
                    <h2 className=" text-center font-sans text-4xl text-primary-600 lg:text-start lg:text-6xl">
                        Margaret Black Dream{' '}
                        <span className="font-spectral font-thin italic">
                            Koleksiyonu
                        </span>
                    </h2>
                    <p className="text-center font-sans text-xl text-primary-600 lg:text-3xl">
                        Sofranızın yeni ikonu
                    </p>
                    <UiImage
                        src={AsagiKaydirImage}
                        alt=""
                        width={90}
                        height={240}
                    />
                </div>
            </div>
            <div className=" bg-custom-gradient p-9">
                <div className="container grid w-full  grid-cols-5">
                    <div className="col-span-3 space-y-20 ">
                        <div className="grid gap-8">
                            <h3 className="max-w-lg font-sans text-2xl text-secondary-800">
                                Koleksiyon, Biedermeier döneminin zarif
                                stilinden ilham alır, ancak çağdaş bir dokunuşla
                                yeniden şekillendirilmiştir. Her parça, Nehir’in
                                özgün işçilik anlayışını yansıtır. 
                            </h3>
                            <div className="grid max-w-md gap-5 text-sm font-light leading-5 text-secondary-800">
                                <p>
                                    Koleksiyon, Biedermeier döneminin zarif
                                    stilinden ilham alır, ancak çağdaş bir
                                    dokunuşla yeniden şekillendirilmiştir. Her
                                    parça, Nehir’in özgün işçilik anlayışını
                                    yansıtır. 
                                </p>
                                <p>
                                    Yüzyıllık bir geleneğin Nehir zarafetiyle
                                    harmanlandığı Margaret Black Dream
                                    Koleksiyonu masanızı sadece bir yemek alanı
                                    değil, bir sanat sergisine dönüştürür.
                                </p>
                            </div>
                        </div>
                        <div className="flex gap-7">
                            <UiImage
                                src={GriBottomImage}
                                alt=""
                                width={300}
                                height={300}
                            />
                            <div className="grid max-w-xs  text-sm font-light leading-5 text-secondary-800">
                                <p>
                                    Margaret Black Dream Koleksiyonu, parlak
                                    beyaz porselen üzerine işlenen kabartmalı
                                    beyaz işlemeler ve ince motiflerle
                                    bezenmiştir. Fransız neoklasik detaylarından
                                    ilham alınarak tasarlanan desenler,
                                    sofistike çizgiler ve arma sembolüyle
                                    birleşmiştir.
                                </p>
                                <p>
                                    Koleksiyonun içindeki arma, yalnızca bir
                                    sembol değil, aynı zamanda güç, zarafet ve
                                    köklü bir mirası da temsil eder.
                                </p>
                                <UiButton
                                    variant="outline"
                                    className="h-11 w-60 rounded-md border-secondary-800 hover:border-secondary-800 focus:border-secondary-800 focus:ring-secondary-800"
                                >
                                    ŞİMDİ ALIŞVERİŞ YAPIN
                                </UiButton>
                            </div>
                        </div>
                    </div>
                    <UiImage
                        src={GriRightImage}
                        alt=""
                        width={500}
                        height={500}
                        className=" col-span-2 h-[500px] !w-[500px] object-cover"
                    />
                </div>
            </div>
            <div className="container grid gap-3">
                <p className="font-sans font-light text-secondary-800">
                    Margaret Black Dream Koleksİyon PARÇALARI
                </p>
                <div className=" grid grid-cols-1 gap-3 lg:grid-cols-3">
                    <UiImage
                        src={DreamKoleksiyonLeftImage}
                        alt=""
                        width={1000}
                        height={1000}
                        className="lg:col-span-2 lg:h-[500px]"
                    />
                    <UiImage
                        src={DreamKoleksiyonRightImage}
                        alt=""
                        width={1000}
                        height={1000}
                        className="object-cover lg:col-span-1 lg:h-[500px] "
                    />
                </div>
            </div>
            <div className="container grid gap-10">
                <h2 className="text-center font-sans text-6xl text-primary-600">
                    Margaret Black Dream{' '}
                    <span className="font-spectral font-thin italic">
                        Set İçeriği
                    </span>
                </h2>
                <div className="grid grid-cols-3 gap-14">
                    {DreamSetData.map(dream => (
                        <div key={dream.id} className="grid  gap-4">
                            <UiImage
                                src={dream.image}
                                alt={dream.name}
                                width={420}
                                height={420}
                            />
                            <div className="grid gap-2">
                                <h5 className="text-center text-xl font-semibold text-secondary-800">
                                    {dream.name}
                                </h5>
                                <p className="text-center text-xl text-[#626262]">
                                    {dream.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
            <div className=" bg-primary-600 p-9">
                <div className="container grid gap-8">
                    <div className="flex justify-between text-white ">
                        <div className="grid gap-3">
                            <h6 className="text-2xl">
                                Sadelikten Doğan Işıltı
                            </h6>
                            <h3 className="text-5xl">
                                Siena Gold Majestic{' '}
                                <span className="font-spectral font-thin italic">
                                    Koleksiyonu
                                </span>
                            </h3>
                        </div>
                        <div className="flex flex-col items-end justify-end font-light">
                            <p>
                                Alman Bauhaus dönemi sonrası modernizmin yalın
                                geometrisinden ve
                            </p>
                            <p>
                                Mid-Century döneminin metalik dokunuşlarından
                                ilham alır.
                            </p>
                            <p>
                                Her bir parça, sadeliğin içindeki ihtişamı
                                ortaya koyar.
                            </p>
                        </div>
                    </div>
                    <div className=" grid grid-cols-1 gap-3 lg:grid-cols-3">
                        <UiImage
                            src={SienaGoldLeftImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="object-cover lg:col-span-1 lg:h-[400px] "
                        />
                        <UiImage
                            src={SienaGoldRightImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="lg:col-span-2 lg:h-[400px]"
                        />
                    </div>
                </div>
            </div>
            <div className="container grid gap-14">
                <div className="flex w-full flex-col-reverse items-center justify-center gap-5 lg:flex-row lg:gap-24">
                    <UiImage
                        src={SienaGoldMiddeleImage}
                        alt=""
                        width={600}
                        height={600}
                    />
                    <div className="flex  max-w-lg flex-col items-center justify-center gap-8 text-center">
                        <div className="flex">
                            <div className="flex flex-col items-center justify-center gap-1">
                                <UiButton
                                    variant="link"
                                    className="h-20 w-20 rounded-full p-1 focus:ring-1 focus:ring-secondary-800"
                                >
                                    <UiImage
                                        src={SienaGoldButtonImage}
                                        alt=""
                                        width={64}
                                        height={64}
                                    />
                                </UiButton>
                                <p>Gold</p>
                            </div>
                            <div className="flex flex-col items-center justify-center gap-1">
                                <UiButton
                                    variant="link"
                                    className="h-20 w-20 rounded-full p-1 focus:ring-1 focus:ring-secondary-800"
                                >
                                    <UiImage
                                        src={SienaGrayButtonImage}
                                        alt=""
                                        width={64}
                                        height={64}
                                    />
                                </UiButton>
                                <p>Gray</p>
                            </div>
                        </div>
                        <p className="font-sans font-light text-secondary-800">
                            Parlak beyaz yüzey, zamansız bir zarafetin
                            ifadesiyken; çevresini saran ince altın şeritler,
                            her sofrada zarif bir çerçeve gibi parlar. Siena,
                            gündelik sadeliği özel anların ışıltısıyla
                            buluşturur.
                        </p>
                        <h2 className=" text-center font-sans text-4xl text-primary-600 lg:text-start lg:text-6xl">
                            Rafine ve
                            <span className="font-spectral font-thin italic">
                                Zarif
                            </span>
                        </h2>
                        <p className="text-center font-sans text-xl text-secondary-800 lg:text-xl">
                            Siena Koleksiyonu, bugünün zarif sofraları için
                            tasarlandı. Işıltıyı abartmadan sevenler için
                        </p>
                        <UiButton
                            variant="solid"
                            className="h-12 w-72 rounded-md border-primary-800 bg-primary-600 text-white hover:border-primary-800 hover:bg-primary-500 focus:border-primary-800 focus:bg-primary-500 focus:ring-primary-800"
                        >
                            ŞİMDİ ALIŞVERİŞ YAPIN
                        </UiButton>
                    </div>
                </div>
                <div className="grid gap-3">
                    <p className="font-sans font-light text-secondary-800">
                        Margaret Black Dream Koleksİyon PARÇALARI
                    </p>
                    <div className=" grid grid-cols-1 gap-3 lg:grid-cols-3">
                        <UiImage
                            src={DreamKoleksiyonLeftImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="lg:col-span-2 lg:h-[500px]"
                        />
                        <UiImage
                            src={DreamKoleksiyonRightImage}
                            alt=""
                            width={1000}
                            height={1000}
                            className="object-cover lg:col-span-1 lg:h-[500px] "
                        />
                    </div>
                </div>
                <div className=" grid gap-10">
                    <h2 className="text-center font-sans text-6xl text-primary-600">
                        Margaret Black Dream{' '}
                        <span className="font-spectral font-thin italic">
                            Set İçeriği
                        </span>
                    </h2>
                    <div className="grid grid-cols-3 gap-14">
                        {SienaData.map(dream =>
                            dream.products.map(product => (
                                <div key={dream.id} className="grid  gap-4">
                                    <UiImage
                                        src={product.image}
                                        alt={product.name}
                                        width={420}
                                        height={420}
                                    />
                                    <div className="grid gap-2">
                                        <h5 className="text-center text-xl font-semibold text-secondary-800">
                                            {product.name}
                                        </h5>
                                        <p className="text-center text-xl text-[#626262]">
                                            {product.description}
                                        </p>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </div>
        </section>
    );
};

if (isDev) {
    Porcelain.displayName = 'Porcelain';
}

export default Porcelain;
