import {FC, memo, useCallback, useEffect, useState} from 'react';
import {CartItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {BagIcon, XIcon} from '@core/icons/outline';
import Price from '@components/common/Price';
import MobileQuantity from '@components/common/MobileQuantity';
type ItemProps = {
    item: CartItem;
};

const Item: FC<ItemProps> = memo(props => {
    const {item} = props;
    const {updateItem, removeItem} = useCart();
    const [deletedCampaingsProducts, setDeletedCampaingsProducts] = useState<
        string | null
    >(null);

    useEffect(() => {
        const isDeleted = localStorage.getItem('deleteCampaingsProduct');
        setDeletedCampaingsProducts(isDeleted ? isDeleted : null);
    }, [deletedCampaingsProducts]);

    const handleRemoveItem = (item: CartItem) => {
        if (item.productId === deletedCampaingsProducts) {
            localStorage.setItem(
                'isDeleteCampaingsProduct',
                JSON.stringify(true)
            );
        }

        const previouslyItems = localStorage.getItem('previouslyItems');
        let previouslyItemsArray: {
            productId: string;
            productName: string;
            price: number;
            discountedPrice: number | undefined;
            quantity: number;
            productSlug: string;
            productImage: string;
            link: string;
            code?: string;
            productStockQuantity?: number;
        }[] = previouslyItems ? JSON.parse(previouslyItems) : [];

        const itemExists = previouslyItemsArray.some(
            (previoulyItem: {productId: string}) =>
                previoulyItem.productId === item.productId
        );

        if (!itemExists) {
            previouslyItemsArray.unshift({
                productId: item.productId,
                productName: item.productName,
                price: item.price,
                discountedPrice: item.discountedPrice,
                quantity: item.quantity,
                productSlug: item.productSlug,
                productImage: item.productImage,
                link: item.productLink,
                code: item.productCode,
                productStockQuantity: item.productStockQuantity
            });

            if (previouslyItemsArray.length > 10) {
                previouslyItemsArray.pop();
            }

            localStorage.setItem(
                'previouslyItems',
                JSON.stringify(previouslyItemsArray)
            );
        }

        removeItem(item);
    };

    const onQuantityChange = useCallback(
        (item: CartItem, quantity: number) => updateItem({...item, quantity}),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return (
        <div className="relative flex items-stretch py-6">
            <div className="md:ml-6">
                <UiImage
                    src={
                        item.productImage ? item.productImage : '/no-image.png'
                    }
                    alt={item.productName}
                    width={140}
                    height={140}
                    fit="cover"
                    position="center"
                />
            </div>

            <div className="flex w-full flex-col">
                <div className="flex items-center justify-between">
                    <div className="ml-2 flex flex-1 flex-col justify-between uppercase md:ml-10">
                        <UiLink
                            className="block w-full !max-w-xs text-sm text-brand-black"
                            href={item.productLink || '#'}
                        >
                            <h2 className="font-dm-serif text-lg leading-5 tracking-wider">
                                {item.productName}
                            </h2>
                        </UiLink>
                    </div>

                    <div
                        className="flex h-6 w-6 cursor-pointer select-none items-center text-muted md:hidden"
                        onClick={() => handleRemoveItem(item)}
                    >
                        <XIcon
                            className="h-9 w-9 stroke-current stroke-[5px] font-bold text-brand-code"
                            aria-hidden="true"
                        />
                    </div>
                </div>

                <div className="flex items-start justify-end pt-8 md:pt-0">
                    <div className="flex w-1/3 flex-col items-center justify-center pr-10">
                        <MobileQuantity
                            className=" h-fit  border-none"
                            size="md"
                            quantity={item.quantity}
                            availableQuantity={item.productStockQuantity}
                            item={item}
                            handleRemoveItem={handleRemoveItem}
                            onChange={quantity =>
                                onQuantityChange(item, quantity)
                            }
                        />
                    </div>

                    <div className=" flex h-8 w-12 flex-col items-end justify-center">
                        <Price
                            className="block font-normal text-brand-black lg:text-2xl [&>span]:!flex-col [&>span]:text-secondary-100 md:[&>span]:text-xl md:[&>span]:text-brand-black"
                            price={item.price * item.quantity}
                            discountedPrice={
                                typeof item.discountedPrice === 'number'
                                    ? item.discountedPrice * item.quantity
                                    : undefined
                            }
                            decimal={0}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    Item.displayName = 'Item';
}

const Items: FC = memo(() => {
    const t = useTrans();
    const {cart} = useCart();

    return (
        <>
            {cart.items.length > 0 && (
                <section
                    className={cls(
                        'divide-y divide-gray-300 border-b border-gray-200',
                        {
                            'border-none':
                                cart.items.length - 1 || cart.items.length < 2
                        }
                    )}
                >
                    {cart.items.map(item => (
                        <Item key={item.productId} item={item} />
                    ))}
                </section>
            )}

            {cart.items.length < 1 && (
                <div className="flex flex-1 flex-col items-center justify-center px-12 py-24">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                        <BagIcon className="h-7 w-7" />
                    </div>

                    <h2 className="pt-12 text-center text-2xl font-semibold">
                        {t('There are no items in your cart!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t(
                            'You can add products to your cart from the detail page of the products on our site.'
                        )}
                    </p>
                </div>
            )}
        </>
    );
});

if (isDev) {
    Items.displayName = 'Items';
}

export default Items;
