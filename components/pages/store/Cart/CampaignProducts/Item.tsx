import Price from '@components/common/Price';
import CartAddToCartAction from '@components/common/ProductCard/CartAddToCartAction';
import {UiImage, UiLink} from '@core/components/ui';
import {cls} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {CheckCircleIcon, XCircleIcon} from '@core/icons/outline';
import {CartItem, ProductListItem} from '@core/types';
import React, {FC} from 'react';

type ItemProps = {
    item: CartItem;
};

const Item: FC<ItemProps> = props => {
    const {item} = props;

    const addToCartItem: ProductListItem = {
        productId: item.productId,
        availableQuantity: item.productStockQuantity
            ? item.productStockQuantity
            : item.quantity,
        code: item.productCode || '',
        name: item.productName,
        campaigns: item.campaigns,
        categoryName: item.productCategory || '',
        brandName: item.brandName,
        brandSlug: '',
        slug: item.productSlug,
        shortDescription: '',
        rating: item.productRating,
        reviewCount: item.productReviewCount,
        images: Array.isArray(item.productImage)
            ? item.productImage
            : item.productImage
            ? [item.productImage]
            : [],
        salesPrice: item.price || 0,
        discountedPrice: item.discountedPrice || 0,
        unDiscountedSalesPrice: item.discount || 0,
        discount: item.discount || 0,
        hasDiscount: item.discount ? true : false,
        quantity: item.quantity || 1,
        link: item.productLink,
        deliveryOptionIds: [],
        weight: 0,
        width: 0,
        height: 0,
        depth: 0,
        deliveryAtSpecifiedDate: false,
        deliveryAtSpecifiedTime: false,
        unitName: '',
        unitId: '',
        isKitProduct: false
    };

    const t = useTrans();
    const {cart} = useCart();

    const cartCheck = cart.items.find(
        cartItem => cartItem.productId === item.productId
    );

    const cartCheckQuantity = cartCheck?.quantity ?? 0;

    return (
        <div className="group relative items-stretch rounded border p-4 shadow-sm transition duration-200 hover:shadow xl:flex  xl:flex-col ">
            <div className="grid h-full w-full grid-cols-12 justify-between gap-0">
                <div className={cls('relative col-span-3 flex ')}>
                    <UiImage
                        className="h-full max-h-28 !w-20 rounded  border lg:!w-28"
                        src={
                            item.productImage
                                ? `${item.productImage}?w=360&q=75`
                                : '/no-image.png'
                        }
                        alt={item.productName}
                        fill
                        fit="cover"
                        position="center"
                    />
                </div>

                <div className="col-span-9 grid  gap-4">
                    <div className="ml-2 flex  justify-between gap-2  ">
                        <UiLink
                            className="block  text-sm"
                            href={`/${item.productSlug}`}
                        >
                            <h2 className="text-xs font-semibold lg:text-sm">
                                {item.productName}
                            </h2>
                            <div className=" mt-4 text-xs">
                                {item.quantity - cartCheckQuantity > 5 ? (
                                    <div className="flex items-center space-x-2 text-gray-600">
                                        <CheckCircleIcon className="h-3 w-3 text-green-600" />
                                        <div>{t('In Stock')}</div>
                                    </div>
                                ) : item.quantity - cartCheckQuantity > 0 ? (
                                    <div className="flex items-center space-x-2 text-gray-600">
                                        <CheckCircleIcon className="h-3 w-3 text-green-600" />
                                        <div>
                                            {item.quantity - cartCheckQuantity >
                                            1
                                                ? t('Last {count} products', {
                                                      count: !cartCheck
                                                          ? item.quantity
                                                          : item.quantity -
                                                            cartCheckQuantity
                                                  })
                                                : t('Last {count} product', {
                                                      count: !cartCheck
                                                          ? item.quantity
                                                          : item.quantity -
                                                            cartCheckQuantity
                                                  })}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex items-center space-x-2 text-gray-600">
                                        <XCircleIcon className="h-3 w-3 text-red-600" />
                                        <div>{t('Out Of Stock')}</div>
                                    </div>
                                )}
                            </div>
                        </UiLink>
                    </div>
                    <div className="ml-2 flex h-fit w-fit flex-1 flex-col justify-between gap-1  ">
                        {Array.isArray(item.campaigns) &&
                            item.campaigns.map((campaign: any) => (
                                <dt
                                    key={campaign.id}
                                    className="flex items-center gap-2 rounded-md bg-primary-50 px-2 py-1 text-xs text-primary-600"
                                >
                                    <svg
                                        className="h-3 w-3"
                                        viewBox="0 0 18 18"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <g
                                            mask="url(#a)"
                                            className="fill-primary-600"
                                        >
                                            <path
                                                d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                                fillOpacity=".55"
                                            />
                                            <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                        </g>
                                    </svg>
                                    <div>
                                        <p className="w-fit">
                                            {campaign.description}
                                        </p>
                                    </div>
                                </dt>
                            ))}
                    </div>
                    <div className="ml-2 flex items-end justify-between  gap-2 ">
                        <Price
                            className="block text-sm font-normal text-brand-black  lg:text-2xl [&>span]:!text-primary-600 md:[&>span]:text-base "
                            price={
                                item.hasDiscount
                                    ? item.unDiscountedSalesPrice ?? 0
                                    : item.price
                            }
                            dontWrapDiscountedPrice={true}
                            discountedPrice={
                                item.hasDiscount ? item.price : undefined
                            }
                            decimal={0}
                        />
                        <div className=" z-1 ">
                            <CartAddToCartAction
                                disabled={
                                    item.quantity < 1 ||
                                    item.quantity <= cartCheckQuantity
                                }
                                product={addToCartItem}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Item;
