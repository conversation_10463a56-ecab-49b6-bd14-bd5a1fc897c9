import {useTrans} from '@core/hooks';
import {BagIcon} from '@core/icons/outline';
import Item from './Item';

const CampaignProducts = ({campaignProducts}: any) => {
    const t = useTrans();

    return (
        <>
            {campaignProducts?.length > 0 && (
                <section className="grid grid-cols-1 gap-4 border-gray-200  xl:grid-cols-2">
                    {campaignProducts?.map((item: any) => {
                        return <Item key={item.productId} item={item} />;
                    })}
                </section>
            )}

            {campaignProducts?.length < 1 && (
                <div className="flex flex-1 flex-col items-center justify-center rounded border p-12">
                    <div className="flex h-24 w-24 items-center justify-center rounded-lg border border-dashed border-gray-500 text-gray-500">
                        <BagIcon className="h-7 w-7" />
                    </div>

                    <h2 className="pt-12 text-center text-2xl font-semibold">
                        {t('Your previously added product is not available!')}
                    </h2>

                    <p className="px-10 pt-2 text-center text-muted">
                        {t(
                            'You can view the products you deleted from your cart here.'
                        )}
                    </p>
                </div>
            )}
        </>
    );
};
export default CampaignProducts;
