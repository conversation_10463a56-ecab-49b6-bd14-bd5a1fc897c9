import {FC, memo, useMemo, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {useCart, useTrans} from '@core/hooks';
import {UiStickyBox, UiTab} from '@core/components/ui';
import Price from '@components/common/Price';
import Actions from './Actions';
import CouponApplicationForm from '@components/common/CouponApplicationForm';

const SideBar: FC = memo(() => {
    const t = useTrans();
    const {cart} = useCart();

    const isProceedToCheckOutEnabled = useMemo(
        () =>
            cart.items.length > 0 &&
            cart.items.filter(
                item => item.selected && !item.removed && item.quantity > 0
            ).length > 0 &&
            cart.subTotal > 0,
        [cart]
    );

    return (
        <div className="relative h-full">
            <>
                <div className="  h-full  ">
                    <UiStickyBox offsetTop={140} className="space-y-4">
                        <div className="flex w-full items-end justify-end">
                            <Actions />
                        </div>
                        <div className="rounded border border-gray-200 p-8 pb-6 shadow-sm">
                            <div className="w-full">
                                <dl className="space-y-4  font-medium text-base xl:space-y-6 xl:px-0">
                                    <div className="flex items-center justify-between  pt-6">
                                        <dt className="text-gray-600">
                                            {t('Total Product')}
                                        </dt>
                                        <dd>
                                            <Price
                                                className="font-semibold"
                                                price={
                                                    cart.subTotal +
                                                    cart.taxTotal
                                                }
                                                decimal={0}
                                            />
                                        </dd>
                                    </div>

                                    <div className="flex items-center justify-between">
                                        {cart.step !== 'information' ? (
                                            <dt className="text-gray-600">
                                                {t('Delivery amount')}
                                            </dt>
                                        ) : (
                                            <dt className="text-gray-600">
                                                {t('Cargo Total')}
                                            </dt>
                                        )}
                                        <dd>
                                            <Price
                                                className="font-semibold"
                                                price={cart.deliveryTotal}
                                                decimal={0}
                                            />
                                        </dd>
                                    </div>

                                    {cart.cashOnDeliveryServiceFee > 0 && (
                                        <div className="flex items-center justify-between">
                                            <dt className="text-gray-600">
                                                {t(
                                                    'Cash on delivery service fee'
                                                )}
                                            </dt>

                                            <dd>
                                                <Price
                                                    price={
                                                        cart.cashOnDeliveryServiceFee
                                                    }
                                                    decimal={0}
                                                />
                                            </dd>
                                        </div>
                                    )}

                                    {cart.dueDifference > 0 && (
                                        <div className="flex items-center justify-between">
                                            <dt className="text-gray-600">
                                                {t('Due difference amount')}
                                            </dt>

                                            <dd>
                                                <Price
                                                    price={cart.dueDifference}
                                                    decimal={0}
                                                />
                                            </dd>
                                        </div>
                                    )}

                                    {Array.isArray(cart.discounts) &&
                                        cart.discounts.length > 0 &&
                                        cart.discounts.map(discount => (
                                            <div
                                                key={discount.id}
                                                className="flex items-center justify-between gap-4"
                                            >
                                                <dt className="flex items-center gap-2 rounded-md bg-brand-budget px-3 py-1 text-sm text-secondary-100">
                                                    <svg
                                                        className="h-4 w-4"
                                                        viewBox="0 0 18 18"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <g
                                                            mask="url(#a)"
                                                            className="fill-secondary-100"
                                                        >
                                                            <path
                                                                d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                                                fillOpacity=".55"
                                                            />
                                                            <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                                        </g>
                                                    </svg>
                                                    <p className="w-fit">
                                                        {discount.description}
                                                    </p>
                                                </dt>

                                                <dd>
                                                    <Price
                                                        className="font-semibold text-primary-600"
                                                        price={-discount.amount}
                                                        decimal={0}
                                                    />
                                                </dd>
                                            </div>
                                        ))}

                                    {typeof cart.discountTotalIncludingProductDiscounts ===
                                        'number' &&
                                        cart.discountTotalIncludingProductDiscounts >
                                            0 && (
                                            <div className="flex items-center justify-between gap-4">
                                                <dt className="flex items-center gap-2 rounded-md bg-brand-budget px-3 py-1 text-sm text-secondary-100">
                                                    <svg
                                                        className="h-4 w-4"
                                                        viewBox="0 0 18 18"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <g
                                                            mask="url(#a)"
                                                            className="fill-secondary-100"
                                                        >
                                                            <path
                                                                d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                                                fillOpacity=".55"
                                                            />
                                                            <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                                        </g>
                                                    </svg>
                                                    <p className="text-secondary-100">
                                                        {t('Total Discount')}
                                                    </p>
                                                </dt>
                                                <dd>
                                                    <Price
                                                        className="font-semibold text-primary-600"
                                                        price={
                                                            -cart.discountTotalIncludingProductDiscounts
                                                        }
                                                        decimal={0}
                                                    />
                                                </dd>
                                            </div>
                                        )}
                                </dl>
                                <div className="mb-2 mt-6 flex items-center justify-between border-t border-gray-200 pt-4 text-lg font-semibold">
                                    <dt>{t('Total')}</dt>
                                    <dd>
                                        <Price
                                            price={cart.grandTotal}
                                            decimal={0}
                                        />
                                    </dd>
                                </div>
                            </div>
                            <div className="my-2">
                                {' '}
                                <CouponApplicationForm
                                    disabled={!isProceedToCheckOutEnabled}
                                />
                            </div>
                        </div>
                        <div className="flex w-full items-end justify-end">
                            <Actions />
                        </div>
                    </UiStickyBox>
                </div>
            </>
        </div>
    );
});

if (isDev) {
    SideBar.displayName = 'SideBar';
}

export default SideBar;
