import {FC, memo, useCallback, useEffect, useState} from 'react';
import {useSession} from 'next-auth/react';
import {Campaign, CartItem, Cart as CartType} from '@core/types';
import {cls, isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useCart, useStore, useTrans} from '@core/hooks';
import Breadcrumbs from '@components/common/Breadcrumbs';
import Seo from '@components/common/Seo';
import Items from './Items';
import SideBar from './Sidebar';

import {notification, UiTab} from '@core/components/ui';

import MyFavorites from '@core/pages/store/Cart/MyFavorites';
import LastViewed from '@core/pages/store/Cart/LastViewed';
import PreviouslyAdded from '@core/pages/store/Cart/PreviouslyAdded';
import {ConfettiIcon, LuPackage} from '@components/icons';
import CampaignProducts from './CampaignProducts';

type CartProps = {
    cart: CartType;
};

const Cart: FC<CartProps> = memo(props => {
    const {cart: initialCart} = props;
    const {data: session} = useSession();
    const t = useTrans();

    const {
        productCount,
        isLoading,
        setCart,
        addItem,
        campaignProducts,
        campaingsCart,
        updateItem: updateCartItem,
        cart: mainCart
    } = useCart();
    const {currency} = useStore();
    const [previouslyAddedItems, setPreviouslyAddedItems] = useState<
        CartItem[]
    >([]);

    const [lastViewedItems, setLastViewedItems] = useState<CartItem[]>([]);

    const [isDeletedCampaingsProducts, setIsDeletedCampaingsProducts] =
        useState<boolean | null>(null);

    useEffect(() => {
        campaingsCart();
    }, [campaingsCart]);

    useEffect(() => {
        try {
            const isDeleted = JSON.parse(
                localStorage.getItem('isDeleteCampaingsProduct') as string
            );
            if (typeof isDeleted === 'boolean') {
                setIsDeletedCampaingsProducts(isDeleted);
            } else {
                setIsDeletedCampaingsProducts(false);
            }
        } catch (error) {
            setIsDeletedCampaingsProducts(null);
        }
    }, []);

    const [activeTab, setActiveTab] = useState(
        initialCart?.showCampaignProducts ? 'campaings' : 'lastViewed'
    );

    // Set Initial cart.
    useEffect(() => {
        if (typeof initialCart !== 'undefined') {
            setCart(initialCart);
            // ---------- Google Tag Manager ----------
            pushIntoGTMDataLayer({
                event: 'view_cart',
                data: {
                    currency: currency.name === 'TL' ? 'TRY' : currency.name,
                    value: initialCart.items
                        .map((item: CartItem) => item.price * item.quantity)
                        .reduce((a, b) => a + b, 0),
                    items: initialCart.items.map(item => ({
                        item_id: item.productCode,
                        item_name: item.productName,
                        discount: item.discountedPrice
                            ? item.price - item.discountedPrice
                            : 0,
                        price: item.price,
                        item_brand: item.brandName,
                        item_category: item.productCategory,
                        quantity: item.quantity
                    }))
                }
            });
            // ----------------------------------------
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const [campaignAmount, setCampaignAmount] = useState(0);
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        const campaignMissingAmount =
            mainCart?.grandTotal >
            initialCart?.minimumPurchaseAmountForFreeShipping
                ? initialCart?.minimumPurchaseAmountForFreeShipping -
                  mainCart.grandTotal
                : initialCart?.minimumPurchaseAmountForFreeShipping -
                  (mainCart.grandTotal - mainCart.deliveryTotal);

        setCampaignAmount(campaignMissingAmount);

        const progressValue =
            mainCart?.grandTotal >
            initialCart?.minimumPurchaseAmountForFreeShipping
                ? (mainCart.grandTotal /
                      initialCart?.minimumPurchaseAmountForFreeShipping) *
                  100
                : ((mainCart.grandTotal - mainCart.deliveryTotal) /
                      initialCart?.minimumPurchaseAmountForFreeShipping) *
                  100;
        setProgress(progressValue > 100 ? 100 : progressValue);
    }, [mainCart, initialCart?.minimumPurchaseAmountForFreeShipping]);

    const isCampaignProduct = mainCart?.campaigns?.find(
        (campaing: Campaign) =>
            campaing.campaignType === campaignProducts[0]?.productId
    );

    const addToCart = useCallback(
        async (product: CartItem) => {
            const item: CartItem = {
                productId: product.productId,
                campaigns: product.campaigns,
                productSlug: product.productSlug,
                productImage: product.productImage,
                productName: product.productName,
                brandName: product.brandName,
                productCategory: product.productCategory,
                productStockQuantity: product.quantity ?? 0,
                productRating: product.productRating,
                productReviewCount: product.productReviewCount,
                productLink: `/${product.productSlug}`,
                isKitProduct: product.isKitProduct,
                price: product.salesPrice || 0,
                unitId: product.unitId,
                unitName: product.unitName,
                quantity: 1,
                weight: product.weight,
                width: product.width,
                height: product.height,
                depth: product.depth,
                volumetricWeight: 0,
                deliveryType: 'standard',
                deliveryOptionIds: product.deliveryOptionIds ?? [],
                deliveryPrice: 0,
                estimatedDeliveryDuration: product.estimatedDeliveryDuration,
                deliveryAtSpecifiedDate: product.deliveryAtSpecifiedDate,
                deliveryAtSpecifiedTime: product.deliveryAtSpecifiedTime,
                selected: true,
                removed: false
            };

            const inCartProduct = mainCart.items.find(
                cartItem => cartItem.productId === product.productId
            );

            let result = null;
            if (inCartProduct) {
                result = await updateCartItem({
                    ...item,
                    quantity: item.quantity + inCartProduct.quantity
                });
            } else {
                result = await addItem(item);
            }
        },
        [mainCart.items, addItem, updateCartItem]
    );

    useEffect(() => {
        if (mainCart.items.length === 0) {
            localStorage.removeItem('isDeleteCampaingsProduct');
        }
    }, [mainCart.items]);

    useEffect(() => {
        if (
            Array.isArray(campaignProducts) &&
            campaignProducts.length > 0 &&
            !isCampaignProduct &&
            mainCart.hasAutomaticRewardAddition &&
            !isDeletedCampaingsProducts &&
            isDeletedCampaingsProducts !== null
        ) {
            const updatedItem = {
                ...campaignProducts?.[0],
                quantity: 1,
                price: campaignProducts?.[0].price
            };
            notification({
                title: t('Kampanyalı Ürün Sepete Eklendi!'),
                description: t(
                    'Kampanya Kapsamında Ürününüz Sepete Eklenmiştir.'
                ),
                status: 'success',
                options: {
                    duration: 4000
                }
            });
            addToCart(updatedItem);
            localStorage.setItem(
                'isDeleteCampaingsProduct',
                JSON.stringify(true)
            );
            localStorage.setItem(
                'deleteCampaingsProduct',
                JSON.stringify(updatedItem.productId)
            );
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        isDeletedCampaingsProducts,
        campaignProducts,
        isCampaignProduct,
        mainCart.hasAutomaticRewardAddition
    ]);

    return (
        <>
            <Seo title={t('Alışveriş Sepetim')} />

            <div className="container">
                <Breadcrumbs
                    breadcrumbs={[
                        {name: t('Home'), slug: '/', href: '/'},
                        {name: t('My Cart'), slug: '/cart'}
                    ]}
                />

                <div className="relative">
                    <div className="grid grid-cols-12 gap-6 pb-12">
                        <div className="col-span-9 grid   gap-8">
                            <div className="h-fit  rounded border border-gray-200  shadow-sm">
                                <div
                                    className={cls(
                                        '  text-center text-sm text-secondary-100',
                                        {
                                            'bg-green-100': campaignAmount < 0,
                                            'bg-secondary-50':
                                                campaignAmount > 0
                                        }
                                    )}
                                >
                                    {initialCart?.minimumPurchaseAmountForFreeShipping &&
                                        initialCart.minimumPurchaseAmountForFreeShipping >
                                            0 && (
                                            <>
                                                <div
                                                    className="flex h-1.5 w-full overflow-hidden rounded  bg-gray-200 dark:bg-neutral-700"
                                                    role="progressbar"
                                                    aria-valuenow={progress}
                                                    aria-valuemin={0}
                                                    aria-valuemax={100}
                                                    aria-label={`Progress: ${progress}%`}
                                                >
                                                    <div
                                                        className={cls(
                                                            'flex w-[70%] flex-col justify-center overflow-hidden whitespace-nowrap rounded   text-center text-xs text-white  ',
                                                            {
                                                                'bg-green-600':
                                                                    campaignAmount <
                                                                    0,
                                                                'bg-secondary-100':
                                                                    campaignAmount >
                                                                    0
                                                            }
                                                        )}
                                                        style={{
                                                            width: `${progress}%`
                                                        }}
                                                    ></div>
                                                </div>

                                                <div>
                                                    {campaignAmount > 0 ? (
                                                        <div className="flex items-center justify-center py-2">
                                                            Sepetine &nbsp;
                                                            <strong>
                                                                {' '}
                                                                {campaignAmount.toFixed(
                                                                    1
                                                                )}
                                                                {'  '}
                                                                TL{' '}
                                                            </strong>{' '}
                                                            &nbsp; daha ekle,
                                                            ücretsiz kargo
                                                            kampanyasından
                                                            yararlan!
                                                        </div>
                                                    ) : (
                                                        <div className="flex items-center justify-center gap-2 py-2">
                                                            <LuPackage className=" stroke-green-600" />
                                                            <span className="text-green-600">
                                                                Kargo Bedava
                                                            </span>
                                                            <ConfettiIcon className="h-4 w-4 stroke-green-600" />
                                                        </div>
                                                    )}
                                                </div>
                                            </>
                                        )}
                                </div>

                                <div className="p-8 ">
                                    {productCount > 0 && (
                                        <div className="mb-8 flex items-center justify-between gap-4">
                                            <h1 className="text-2xl font-medium">
                                                {t('My Cart')} (
                                                {productCount > 1
                                                    ? t('{count} Products', {
                                                          count: productCount
                                                      })
                                                    : t('{count} Product', {
                                                          count: 1
                                                      })}
                                                )
                                            </h1>
                                        </div>
                                    )}

                                    <Items />
                                </div>
                            </div>

                            <div className="-mb-px flex justify-start">
                                <UiTab.Group>
                                    <div className="grid w-full gap-6">
                                        <div className=" w-full bg-white">
                                            <UiTab.List className="-mb-px flex justify-start space-x-4">
                                                {initialCart?.showCampaignProducts && (
                                                    <UiTab
                                                        onClick={() =>
                                                            setActiveTab(
                                                                'campaings'
                                                            )
                                                        }
                                                        className={cls(
                                                            activeTab ===
                                                                'campaings'
                                                                ? '  bg-secondary-100 px-5 py-2 text-white '
                                                                : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                            'cursor-pointer whitespace-nowrap rounded-full px-5 py-2 text-sm font-medium  transition'
                                                        )}
                                                    >
                                                        {t('Campaign Products')}
                                                    </UiTab>
                                                )}
                                                <UiTab
                                                    onClick={() =>
                                                        setActiveTab(
                                                            'lastViewed'
                                                        )
                                                    }
                                                    className={cls(
                                                        activeTab ===
                                                            'lastViewed'
                                                            ? '  bg-secondary-100 px-5 py-2 text-white '
                                                            : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                        'cursor-pointer whitespace-nowrap rounded-full px-5 py-2 text-sm font-medium  transition'
                                                    )}
                                                >
                                                    {t('Last Viewed Products')}
                                                </UiTab>
                                                <UiTab
                                                    onClick={() =>
                                                        setActiveTab(
                                                            'previouslyAdded'
                                                        )
                                                    }
                                                    className={cls(
                                                        activeTab ===
                                                            'previouslyAdded'
                                                            ? '  bg-secondary-100 px-5 py-2 text-white '
                                                            : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                        'cursor-pointer whitespace-nowrap rounded-full px-5 py-2 text-sm font-medium  transition'
                                                    )}
                                                >
                                                    {t('Previously Added')}
                                                </UiTab>

                                                {session?.user && (
                                                    <UiTab
                                                        onClick={() =>
                                                            setActiveTab(
                                                                'myFavorites'
                                                            )
                                                        }
                                                        className={cls(
                                                            activeTab ===
                                                                'myFavorites'
                                                                ? '  bg-secondary-100 px-5 py-2 text-white '
                                                                : 'border-transparent text-gray-700 hover:border-gray-300 hover:text-gray-800',
                                                            'cursor-pointer whitespace-nowrap rounded-full px-5 py-2 text-sm font-medium  transition'
                                                        )}
                                                    >
                                                        {t('My Favorites')}
                                                    </UiTab>
                                                )}
                                            </UiTab.List>
                                        </div>
                                        <UiTab.Panels>
                                            {initialCart?.showCampaignProducts && (
                                                <UiTab.Panel>
                                                    <CampaignProducts
                                                        campaignProducts={
                                                            campaignProducts
                                                        }
                                                    />
                                                </UiTab.Panel>
                                            )}
                                            <UiTab.Panel>
                                                <LastViewed
                                                    lastViewedItems={
                                                        lastViewedItems
                                                    }
                                                    setLastViewedItems={
                                                        setLastViewedItems
                                                    }
                                                />
                                            </UiTab.Panel>
                                            <UiTab.Panel>
                                                <PreviouslyAdded
                                                    previouslyAddedItems={
                                                        previouslyAddedItems
                                                    }
                                                    setPreviouslyAddedItems={
                                                        setPreviouslyAddedItems
                                                    }
                                                />
                                            </UiTab.Panel>

                                            <UiTab.Panel>
                                                <MyFavorites />
                                            </UiTab.Panel>
                                        </UiTab.Panels>
                                    </div>
                                </UiTab.Group>
                            </div>
                        </div>

                        <div className="col-span-3">
                            <SideBar />
                        </div>
                    </div>

                    {isLoading && (
                        <div className="absolute inset-0 flex h-full w-full  items-center justify-center bg-white bg-opacity-30"></div>
                    )}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    Cart.displayName = 'Cart';
}

export default Cart;
