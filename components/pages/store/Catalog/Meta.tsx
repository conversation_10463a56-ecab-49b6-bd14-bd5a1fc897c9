import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import Seo from '@components/common/Seo';
import useCatalog from './useCatalog';
import {NextSeo} from 'next-seo';

const Meta: FC = memo(() => {
    const {seo} = useCatalog();

    const additionalLinkTags = useMemo(() => {
        const links: any[] = [];

        if (typeof seo.prevUrl === 'string') {
            links.push({
                rel: 'prev',
                href: seo.prevUrl
            });
        }
        if (typeof seo.nextUrl === 'string') {
            links.push({
                rel: 'next',
                href: seo.nextUrl
            });
        }

        return links;
    }, [seo]);

    return (
        <NextSeo
            title={seo.title}
            description={seo.description}
            canonical={seo.canonical}
            additionalLinkTags={additionalLinkTags}
        />
    );
});

if (isDev) {
    Meta.displayName = 'Meta';
}

export default Meta;
