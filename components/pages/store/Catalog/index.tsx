import {memo, useMemo} from 'react';
import {
    Breadcrumb,
    Filter,
    Page,
    ProductListItem,
    SpecialPageProducts
} from '@core/types';
import {cls, isDev} from '@core/helpers';
import Breadcrumbs from '@components/common/Breadcrumbs';
import Comments from '@components/pages/common/Home/Comments';
import OnlineSpecial from '@components/pages/common/Home/OnlineSpecial';
import Meta from './Meta';
import Products from './Products';
import SpecialPage from './SpecialPage';
import Filters from './Filters';
import LoadingOverlay from './LoadingOverlay';
import {CatalogProvider} from './context';
import CatalogSearchBarPartial from './CatalogSearch';
import CatalogName from './Products/CatalogName';
import {useStore} from '@core/hooks';
import {UiImage, UiLink, UiSlider} from '@core/components/ui';
import {Autoplay, Pagination} from 'swiper/modules';
import {useRouter} from 'next/router';
import {NextSeo} from 'next-seo';

type CatalogPageProps = {
    breadcrumbs: Breadcrumb[];
    filters: Filter[];
    search?: string;
    products: ProductListItem[];
    pageNumber?: number;
    hasNextPage: boolean;
    totalProductCountText: string;
    isSpecialPage: boolean;
    productCatalogMap: SpecialPageProducts;
};

const CatalogPage: Page<CatalogPageProps> = memo(props => {
    const {
        breadcrumbs,
        filters,
        search,
        products,
        pageNumber,
        hasNextPage,
        totalProductCountText,
        isSpecialPage,
        productCatalogMap
    } = props;

    const {navigationItem, navigation} = useStore();

    const router = useRouter();

    const ckbItems = useMemo(() => {
        return navigation.filter(
            navigationItem =>
                navigationItem.type === 'story' &&
                navigationItem.section === 'catal-kasik-bicak-pages-story'
        );
    }, [navigation]);

    return (
        <CatalogProvider
            filters={filters}
            search={search}
            products={products}
            pageNumber={pageNumber}
            hasNextPage={hasNextPage}
            totalProductCountText={totalProductCountText}
        >
            <Meta />
            <NextSeo
                title={navigationItem?.seoTitle ?? navigationItem?.name}
                description={
                    navigationItem?.seoDescription ??
                    navigationItem?.description
                }
            />

            {Array.isArray(breadcrumbs) && breadcrumbs.length > 0 && (
                <div className="flex items-center max-xl:container">
                    <Breadcrumbs breadcrumbs={breadcrumbs} />
                </div>
            )}

            <div className={cls('relative', {container: !isSpecialPage})}>
                {!search && isSpecialPage && (
                    <SpecialPage productCatalogMap={productCatalogMap} />
                )}

                {!isSpecialPage && (
                    <>
                        <div
                            className={cls({'xl:mt-8': breadcrumbs.length < 1})}
                        >
                            {products.length > 0 && <CatalogName />}
                        </div>
                        {router?.query?.slug?.[0] === 'catal-kasik-bicak' && (
                            <div className=" ">
                                <UiSlider
                                    loop
                                    autoplay
                                    slidesPerView={2}
                                    className="ckb-swiper-pagination"
                                    spaceBetween={10}
                                    modules={[Autoplay, Pagination]}
                                    pagination={{
                                        dynamicBullets: true
                                    }}
                                    breakpoints={{
                                        768: {
                                            slidesPerView: 4,
                                            spaceBetween: 50
                                        },
                                        1200: {
                                            slidesPerView: 6,
                                            spaceBetween: 20
                                        }
                                    }}
                                >
                                    {ckbItems.map(item => (
                                        <UiSlider.Slide
                                            key={item.id}
                                            className="cursor-pointer"
                                            onClick={() =>
                                                router.push(item.href)
                                            }
                                        >
                                            <UiLink
                                                href={item.href}
                                                className="relative"
                                            >
                                                <UiImage
                                                    className="h-[75px] "
                                                    src={
                                                        (Array.isArray(
                                                            item.images
                                                        ) &&
                                                            item.images.length >
                                                                0 &&
                                                            item.images[0]) ||
                                                        '/no-images.png'
                                                    }
                                                    alt={item.name}
                                                    width={250}
                                                    height={100}
                                                />
                                            </UiLink>
                                            <div className="absolute left-1/2 top-1/2 ml-1 w-20 -translate-y-1/2 transform font-dm-serif text-xs  leading-tight  text-black lg:ml-2 lg:text-sm">
                                                {item.name.toUpperCase()}
                                            </div>
                                        </UiSlider.Slide>
                                    ))}
                                </UiSlider>
                            </div>
                        )}
                        {navigationItem &&
                            Array.isArray(navigationItem.banners) && (
                                <div
                                    className={cls({
                                        'mb-6':
                                            navigationItem.banners.length > 0
                                    })}
                                >
                                    <div className="relative grid h-full w-full place-items-stretch gap-4 lg:grid-cols-2">
                                        {navigationItem.banners.length > 0 &&
                                            navigationItem.banners
                                                .slice(0, 2)
                                                .map((banner, index) => (
                                                    <UiLink
                                                        href={banner.link ?? ''}
                                                        key={index}
                                                    >
                                                        <UiImage
                                                            src={`${banner.images[0]}?w=1280&q=90`}
                                                            alt={banner.name}
                                                            fit="cover"
                                                            position="center"
                                                            priority
                                                            className="h-28 w-full lg:h-52"
                                                            width={850}
                                                            height={100}
                                                        />
                                                    </UiLink>
                                                ))}
                                    </div>
                                </div>
                            )}

                        <div className="flex lg:pb-12 ">
                            {filters.length > 0 && <Filters />}
                            <Products />
                        </div>
                        <LoadingOverlay />
                    </>
                )}
                {typeof navigationItem?.content === 'string' &&
                    navigationItem.content.length > 0 && (
                        <div className="cprose container overflow-x-hidden py-12">
                            <div
                                className="prose max-w-full"
                                dangerouslySetInnerHTML={{
                                    __html: navigationItem.content
                                }}
                            />
                        </div>
                    )}

                {!search && (
                    <>
                        <Comments />
                        <OnlineSpecial />
                        <CatalogSearchBarPartial />
                    </>
                )}
            </div>
        </CatalogProvider>
    );
});

if (isDev) {
    CatalogPage.displayName = 'CatalogPage';
}

CatalogPage.initPageProps = async props => {
    return props;
};

export default CatalogPage;
