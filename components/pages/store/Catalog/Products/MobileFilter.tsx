import {
    Dispatch,
    FC,
    memo,
    SetStateAction,
    useCallback,
    useEffect,
    useMemo,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {FilterItem as FilterItemType, Filter as FilterType} from '@core/types';
import {clone, isDev, toLower, trim} from '@core/helpers';
import {useMobile, useStore, useTrans, useUI} from '@core/hooks';
import {UiButton, UiCheckbox, UiInput} from '@core/components/ui';
import {AppliedFilter, getQuery} from '@core/pages/store/Catalog/context';
import {ChevronRight, DownArrow} from '@components/icons';

type FilterItem = FilterItemType & {
    isSelected: boolean;
};

type Filter = Omit<FilterType, 'items'> & {
    items: FilterItem[];
    selectedFilter: FilterItem[];
    setSelectedFilter: (selectedFilter: Filter | undefined) => void;
};

type SelectedFilterProps = {
    selectedFilter: Filter;
    setSelectedFilter: (selectedFilter: Filter | undefined) => void;
    onItemsSelected: (selectedItems: FilterItem[]) => void;
};

function capitalizeFirstLetter(text: string) {
    return text
        .split(' ')
        .map(
            word =>
                word.charAt(0).toLocaleUpperCase('tr') +
                word.slice(1).toLocaleLowerCase('tr')
        )
        .join(' ');
}

const SelectedFilter: FC<SelectedFilterProps> = memo(
    ({selectedFilter, setSelectedFilter, onItemsSelected}) => {
        const t = useTrans();
        const [search, setSearch] = useState('');
        const [selectedItems, setSelectedItems] = useState<FilterItem[]>(() =>
            selectedFilter.items.filter(item => !!item.isSelected)
        );

        const filteredItems = useMemo(
            () =>
                (selectedFilter.items as FilterItem[])
                    .filter(
                        item =>
                            toLower(item.label).indexOf(
                                toLower(trim(search))
                            ) !== -1
                    )
                    .map(item => ({
                        ...item,
                        isSelected:
                            selectedItems.findIndex(
                                selectedItem =>
                                    selectedItem.value === item.value
                            ) !== -1
                    })),
            [selectedFilter.items, search, selectedItems]
        );

        const onItemSelectionChange = useCallback(
            (item: FilterItem) => {
                if (selectedFilter.type === 'price') {
                    setSelectedItems([
                        {isSelected: true, label: item.label, value: item.value}
                    ]);
                } else if (item.isSelected) {
                    setSelectedItems(currentSelectedItems =>
                        currentSelectedItems.filter(
                            currentSelectedItem =>
                                currentSelectedItem.value !== item.value
                        )
                    );
                } else {
                    setSelectedItems(currentSelectedItems => [
                        ...currentSelectedItems,
                        item
                    ]);
                }
            },
            [selectedFilter]
        );

        const {register, watch, setValue} = useForm();
        const {currency} = useStore();
        const router = useRouter();

        const minPrice = watch('minPrice');
        const maxPrice = watch('maxPrice');
        const hasAtLeastOnePriceValue = minPrice || maxPrice;

        useEffect(() => {
            const query = getQuery(router);

            const item = selectedItems.find(
                selectedItem => selectedItem.isSelected
            );

            const prices = item?.value ?? query['price'];
            if (typeof prices === 'string') {
                const [minPrice, maxPrice] = prices.trim().split('-');

                if (!isNaN(parseInt(minPrice))) {
                    setValue('minPrice', minPrice);
                }
                if (!isNaN(parseInt(maxPrice))) {
                    setValue('maxPrice', maxPrice);
                }
            } else {
                setValue('minPrice', '');
                setValue('maxPrice', '');
            }
        }, [router, selectedItems, setValue]);

        return (
            <div className="flex h-full w-full flex-col overflow-hidden">
                {selectedFilter.type === 'price' ? (
                    <div className="flex items-center gap-4 border-b p-4">
                        <UiInput
                            size="md"
                            placeholder={t('Minimum')}
                            className="rounded-none font-semibold"
                            maxLength={6}
                            {...register('minPrice', {
                                onChange(e) {
                                    const numericValue = e.target.value.replace(
                                        /\D/,
                                        ''
                                    );
                                    setValue('minPrice', numericValue);
                                }
                            })}
                        />
                        <div>-</div>
                        <UiInput
                            size="md"
                            placeholder={t('Maximum')}
                            className="rounded-none font-semibold"
                            maxLength={6}
                            {...register('maxPrice', {
                                onChange(e) {
                                    const numericValue = e.target.value.replace(
                                        /\D/,
                                        ''
                                    );
                                    setValue('maxPrice', numericValue);
                                }
                            })}
                        />
                    </div>
                ) : (
                    <div className="border-b border-gray-200 p-4">
                        <UiInput
                            className="rounded-none border-gray-100 bg-gray-100 shadow-sm transition focus:bg-white"
                            placeholder={t(`{artifact} search`, {
                                artifact: selectedFilter.label
                            })}
                            onChange={e => setSearch(e.target.value)}
                        />
                    </div>
                )}

                <div className="flex-1  overflow-y-auto pl-1.5">
                    {filteredItems.map(item => (
                        <button
                            className="flex w-full items-center px-4 py-2 text-left"
                            key={item.value}
                            onClick={() => onItemSelectionChange(item)}
                        >
                            {item.isSelected ? (
                                <>
                                    <UiCheckbox
                                        className="mr-3 h-5 w-5 rounded-none border-black checked:h-5 checked:w-5 hover:!bg-secondary-100 focus:!border-0 focus:!ring-0"
                                        checked={true}
                                    />
                                </>
                            ) : (
                                <div className="mr-3 h-5 w-5 !rounded-none border border-gray-300"></div>
                            )}

                            <div className="flex-1">
                                {capitalizeFirstLetter(item.label)}
                                {selectedFilter.type === 'price'
                                    ? currency.name
                                    : ''}
                            </div>

                            {selectedFilter.isColorAttribute && (
                                <div
                                    className="relative ml-4 h-5 w-5 overflow-hidden !rounded-none border"
                                    style={
                                        !!item.color
                                            ? {
                                                  backgroundColor: item.color
                                              }
                                            : {}
                                    }
                                >
                                    {!item.color && (
                                        <svg
                                            className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                            viewBox="0 0 100 100"
                                            preserveAspectRatio="none"
                                            stroke="currentColor"
                                        >
                                            <line
                                                x1={0}
                                                y1={100}
                                                x2={100}
                                                y2={0}
                                                vectorEffect="non-scaling-stroke"
                                            />
                                        </svg>
                                    )}
                                </div>
                            )}
                        </button>
                    ))}
                </div>

                <div className="space-y-2 border-t border-gray-200 p-4">
                    <UiButton
                        className="w-full bg-secondary-100 text-white hover:bg-secondary-100"
                        disabled={
                            selectedFilter.type === 'price'
                                ? !hasAtLeastOnePriceValue
                                : selectedItems.length < 1
                        }
                        onClick={() => {
                            if (selectedFilter.type === 'price') {
                                onItemsSelected([
                                    {
                                        isSelected: true,
                                        label: `${minPrice || '*'} - ${
                                            maxPrice || '*'
                                        }`,
                                        value: `${minPrice || '*'}-${
                                            maxPrice || '*'
                                        }`
                                    }
                                ]);
                            } else {
                                onItemsSelected(selectedItems);
                            }
                        }}
                    >
                        {t('Apply')}
                    </UiButton>

                    <UiButton
                        className="ring:!bg-secondary-50 w-full border-secondary-50 bg-secondary-50 text-gray-600 hover:bg-secondary-50"
                        onClick={() => setSelectedFilter(undefined)}
                    >
                        {t('Cancel')}
                    </UiButton>
                </div>
            </div>
        );
    }
);

if (isDev) {
    SelectedFilter.displayName = 'SelectedFilter';
}

type FiltersProps = {
    filters: Filter[];
    setSelectedFilter: (selectedFilter: Filter | undefined) => void;
    onApplyFilters: () => void;
    onClearAllFilters: () => void;
};

const Filters: FC<FiltersProps> = memo(
    ({filters, setSelectedFilter, onApplyFilters, onClearAllFilters}) => {
        const t = useTrans();
        const router = useRouter();

        const [hasPrice, setHasPrice] = useState(false);

        useEffect(() => {
            const query = getQuery(router);

            if (!!query.price && typeof query.price === 'string') {
                setHasPrice(true);
            }
        }, [router]);

        const sortedFilters = useMemo(() => {
            const priceFilter = filters.find(filter => filter.type === 'price');
            const otherFilters = filters.filter(
                filter => filter.type !== 'price'
            );
            return priceFilter ? [priceFilter, ...otherFilters] : filters;
        }, [filters]);

        return (
            <div className="flex h-full w-full flex-col overflow-hidden">
                <div className="flex-1 divide-y divide-gray-200 overflow-y-auto">
                    {sortedFilters.map(filter => {
                        let selectedItemCount = filter.items.filter(
                            item => !!item.isSelected
                        ).length;

                        if (
                            filter.type === 'price' &&
                            hasPrice &&
                            selectedItemCount === 0
                        ) {
                            selectedItemCount = 1;
                        }

                        return (
                            <button
                                className="flex w-full items-center justify-between px-4 py-3"
                                key={filter.field}
                                onClick={() => setSelectedFilter(filter)}
                            >
                                <div className="flex items-center justify-center gap-x-2">
                                    <ChevronRight className="h-3 w-3 stroke-secondary-100 stroke-[60px]" />
                                    <div className="mr-4 flex flex-1 items-center font-bold text-base">
                                        {capitalizeFirstLetter(t(filter.label))}
                                    </div>
                                </div>

                                <div className="flex items-center space-x-3">
                                    {selectedItemCount > 0 && (
                                        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-secondary-100 text-sm text-white">
                                            {selectedItemCount}
                                        </div>
                                    )}

                                    <p className="text-xs text-muted">Tümü</p>
                                </div>
                            </button>
                        );
                    })}
                </div>

                <div className="space-y-2 border-t border-gray-200 p-4">
                    <UiButton
                        variant="solid"
                        color="primary"
                        className="w-full !bg-secondary-100"
                        onClick={onApplyFilters}
                    >
                        {t('Apply Filters')}
                    </UiButton>

                    <UiButton
                        variant="light"
                        color="primary"
                        className="w-full hover:!bg-secondary-50"
                        onClick={onClearAllFilters}
                    >
                        {t('Clear All Filters')}
                    </UiButton>
                </div>
            </div>
        );
    }
);

if (isDev) {
    Filters.displayName = 'Filters';
}

type MobileFilterProps = {
    filters: Filter[];
    appliedFilters: AppliedFilter[];
    updateFilters: (appliedFilters: AppliedFilter[]) => void;
    clearFilters: () => void;
};

const MobileFilter: FC<MobileFilterProps> = memo(
    ({
        filters: initialFilters,
        updateFilters,
        clearFilters,
        appliedFilters
    }) => {
        const t = useTrans();
        const {closeSideBar} = useUI();
        const {setSideBarTitle} = useUI();
        const {selectedFilter, setSelectedFilter} = useMobile();
        const [filters, setFilters] = useState<Filter[]>(
            () =>
                clone(initialFilters).map(initialFilter => {
                    initialFilter.items = initialFilter.items.map(item => {
                        item.isSelected =
                            appliedFilters.findIndex(
                                appliedFilter =>
                                    appliedFilter.field ===
                                        initialFilter.field &&
                                    appliedFilter.selected.value === item.value
                            ) !== -1;

                        return item;
                    });

                    return initialFilter;
                }) as Filter[]
        );

        const onItemsSelected = useCallback(
            (selectedItems: FilterItem[]) => {
                if (typeof selectedFilter === 'undefined') return;

                const currentFilters = clone(filters).map(currentFilter => {
                    if (currentFilter.field === selectedFilter.field) {
                        currentFilter.items = currentFilter.items.map(item => {
                            item.isSelected =
                                selectedItems.findIndex(
                                    selectedItem =>
                                        selectedItem.value === item.value
                                ) !== -1;

                            return item;
                        });

                        if (
                            currentFilter.type === 'price' &&
                            currentFilter.items.every(item => !item.isSelected)
                        ) {
                            currentFilter.items = [
                                ...currentFilter.items,
                                ...selectedItems
                            ];
                        }
                    }

                    return currentFilter;
                });

                setFilters(currentFilters);
                setSelectedFilter(undefined);
            },
            [filters, selectedFilter, setSelectedFilter]
        );

        const onApplyFilters = useCallback(() => {
            const appliedFilters: AppliedFilter[] = [];

            for (const filter of filters) {
                for (const item of filter.items) {
                    if (item.isSelected) {
                        appliedFilters.push({
                            type: filter.type,
                            field: filter.field,
                            label: filter.label,
                            isColorAttribute: filter.isColorAttribute,
                            selected: {
                                label: item.label,
                                value: item.value,
                                color: item.color
                            }
                        });
                    }
                }
            }

            updateFilters(appliedFilters);
            closeSideBar();
        }, [filters, updateFilters, closeSideBar]);

        const onClearAllFilters = useCallback(() => {
            clearFilters();
            closeSideBar();
        }, [clearFilters, closeSideBar]);

        useEffect(() => {
            if (typeof selectedFilter !== 'undefined') {
                setSideBarTitle(t(selectedFilter.label));
            } else {
                setSideBarTitle(t('Filter'));
            }
        }, [selectedFilter, setSideBarTitle, t]);

        if (typeof selectedFilter !== 'undefined') {
            return (
                <SelectedFilter
                    selectedFilter={selectedFilter as Filter}
                    setSelectedFilter={setSelectedFilter}
                    onItemsSelected={onItemsSelected}
                />
            );
        }

        return (
            <Filters
                filters={filters}
                setSelectedFilter={setSelectedFilter}
                onApplyFilters={onApplyFilters}
                onClearAllFilters={onClearAllFilters}
            />
        );
    }
);

if (isDev) {
    MobileFilter.displayName = 'MobileFilter';
}

export default MobileFilter;
