import {FC, memo, useCallback} from 'react';
import {isDev} from '@core/helpers';
import {useStore} from '@core/hooks';
import useCatalog from '../useCatalog';

const CatalogName: FC = memo(() => {
    const {search} = useCatalog();
    const {navigationItem} = useStore();

    const capitalizeFirstLetter = useCallback(
        (text: string) =>
            text
                .split(' ')
                .map(
                    word =>
                        word.charAt(0).toLocaleUpperCase('tr') +
                        word.slice(1).toLocaleLowerCase('tr')
                )
                .join(' '),
        []
    );

    return (
        <div
            className="pb-4 font-dm-serif text-xl font-medium text-brand-black md:text-3xl xl:pb-8 xl:text-[50px]"
            dangerouslySetInnerHTML={{
                __html: !!search
                    ? capitalizeFirstLetter(search).replace(
                          `"${search}"`,
                          `&#8220;<h1 class="inline">${search}</h1>&#8221;`
                      )
                    : !!navigationItem && navigationItem.name
                    ? capitalizeFirstLetter(navigationItem.name).replace(
                          `"${navigationItem.name}"`,
                          `&#8220;<h1 class="inline">${navigationItem.name}</h1>&#8221;`
                      )
                    : ''
            }}
        ></div>
    );
});

if (isDev) {
    CatalogName.displayName = 'CatalogName';
}

export default CatalogName;
