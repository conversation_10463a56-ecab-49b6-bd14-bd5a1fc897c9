import {FC, Fragment, memo, useEffect, useMemo, useRef, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiListBox, UiTransition} from '@core/components/ui';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/outline';
import useCatalog from '../useCatalog';

const Toolbar: FC = memo(() => {
    const {navigationItem} = useStore();
    const t = useTrans();
    const {search, sortOptions, sort, updateSort} = useCatalog();

    const [selectedSort, setSelectedSort] = useState<string>(
        typeof sort === 'object'
            ? `${sort.field}|${sort.direction}`
            : sortOptions[3].value
    );
    const selectedSortLabel = useMemo(
        () => sortOptions.find(option => option.value === selectedSort)?.label,
        [selectedSort, sortOptions]
    );
    const isInitial = useRef(true);

    useEffect(() => {
        if (isInitial.current) {
            isInitial.current = false;

            return;
        }

        const [field, direction] = selectedSort.split('|');

        updateSort({field, direction: direction === 'desc' ? 'desc' : 'asc'});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedSort]);

    return (
        <div className="mb-4 hidden w-full border-b border-gray-200 py-2 xl:block">
            <div className="flex items-center justify-between">
                <div
                    className="font-dm-serif text-sm font-bold"
                    dangerouslySetInnerHTML={{
                        __html: !!search
                            ? t('Showing results for "{search}" search.', {
                                  search
                              }).replace(
                                  `"${search}"`,
                                  `&#8220;<p class="inline">${search}</p>&#8221;`
                              )
                            : !!navigationItem && navigationItem.name
                            ? t('Showing results for "{sectionName}".', {
                                  sectionName: navigationItem.name
                              }).replace(
                                  `"${navigationItem.name}"`,
                                  `&#8220;<p class="inline">${navigationItem.name}</p>&#8221;`
                              )
                            : t('Showing results.')
                    }}
                ></div>

                {!!navigationItem && !navigationItem.productSet && (
                    <div>
                        <UiListBox
                            value={selectedSort}
                            onChange={setSelectedSort}
                            as="div"
                            className="relative space-y-1"
                        >
                            {({open}) => (
                                <>
                                    <UiListBox.Button
                                        className={cls(
                                            'relative ml-auto inline-flex h-8 w-32 min-w-[200px] appearance-none items-center px-3 py-0 pr-6 text-sm text-default transition focus:outline-none',
                                            {
                                                ' !bg-white ring-0 ': open
                                            }
                                        )}
                                    >
                                        {!selectedSort && (
                                            <span className="truncate text-sm text-muted">
                                                {t('Choose a sort criteria.')}
                                            </span>
                                        )}
                                        {!!selectedSort && (
                                            <span className="truncate font-dm-serif text-sm text-brand-black">
                                                {t(selectedSortLabel as string)}
                                            </span>
                                        )}
                                        <span className="pointer-events-none absolute right-2 ml-5 flex items-center">
                                            {open ? (
                                                <ChevronUpIcon
                                                    className="h-3 w-3 stroke-current text-secondary-600"
                                                    aria-hidden="true"
                                                    style={{strokeWidth: 60}}
                                                />
                                            ) : (
                                                <ChevronDownIcon
                                                    className="h-3 w-3 stroke-current text-secondary-600"
                                                    aria-hidden="true"
                                                    style={{strokeWidth: 60}}
                                                />
                                            )}
                                        </span>
                                    </UiListBox.Button>

                                    <UiTransition
                                        show={open}
                                        as={Fragment}
                                        enter="transition"
                                        enterFrom="transform scale-95 opacity-0"
                                        enterTo="transform scale-100 opacity-100"
                                        leave="transition"
                                        leaveFrom="transform scale-100 opacity-100"
                                        leaveTo="transform scale-95 opacity-0"
                                    >
                                        <UiListBox.Options
                                            static
                                            className="absolute right-0 z-40 mt-2 max-h-64 w-full min-w-[200px] origin-top-left overflow-auto border border-gray-200 bg-white shadow-sm outline-none"
                                        >
                                            {sortOptions.map(
                                                (option, index) => (
                                                    <UiListBox.Option
                                                        className="relative"
                                                        key={option.value}
                                                        value={option.value}
                                                    >
                                                        {({
                                                            active,
                                                            selected,
                                                            disabled
                                                        }) => (
                                                            <button
                                                                disabled={
                                                                    disabled
                                                                }
                                                                aria-disabled={
                                                                    disabled
                                                                }
                                                                className={cls(
                                                                    'flex h-8 w-full flex-shrink-0 cursor-pointer items-center border-0 px-3 text-left text-sm   focus:outline-none ',
                                                                    active &&
                                                                        'bg-white',
                                                                    selected &&
                                                                        '!bg-secondary-50',
                                                                    index %
                                                                        2 !==
                                                                        0
                                                                        ? ''
                                                                        : 'bg-gray-100',
                                                                    index == 0
                                                                        ? '!bg-gray-100'
                                                                        : ''
                                                                )}
                                                            >
                                                                <span
                                                                    className={cls(
                                                                        'block flex-1 truncate font-dm-serif   text-brand-black',
                                                                        selected
                                                                            ? 'font-medium '
                                                                            : 'font-normal',
                                                                        index ==
                                                                            0
                                                                            ? '!text-muted'
                                                                            : ''
                                                                    )}
                                                                >
                                                                    {t(
                                                                        option.label
                                                                    )}
                                                                </span>
                                                                {selected && (
                                                                    <span
                                                                        className="absolute -left-1 h-6 rounded-full bg-gray-100  "
                                                                        style={{
                                                                            width: 2
                                                                        }}
                                                                    ></span>
                                                                )}
                                                            </button>
                                                        )}
                                                    </UiListBox.Option>
                                                )
                                            )}
                                        </UiListBox.Options>
                                    </UiTransition>
                                </>
                            )}
                        </UiListBox>
                    </div>
                )}
            </div>
        </div>
    );
});

if (isDev) {
    Toolbar.displayName = 'Toolbar';
}

export default Toolbar;
