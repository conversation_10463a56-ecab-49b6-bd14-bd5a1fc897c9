import {FC, memo, useState} from 'react';
import {isDev} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {UiButton, UiCheckbox, UiRadioGroup} from '@core/components/ui';
import {Sort} from '../context';

type MobileSortProps = {
    sortOptions: {value: string; label: string}[];
    sort: Sort;
    onSort: (value: string) => void;
};

const MobileSort: FC<MobileSortProps> = memo(({sortOptions, sort, onSort}) => {
    const t = useTrans();
    const {closeSideBar} = useUI();
    const [selectedSort, setSelectedSort] = useState<string>(
        typeof sort === 'object'
            ? `${sort.field}|${sort.direction}`
            : sortOptions[3].value
    );

    return (
        <div className="flex h-full w-full flex-col overflow-hidden">
            <div className="flex-1 overflow-y-auto">
                <UiRadioGroup
                    className="mt-4"
                    value={selectedSort}
                    onChange={s => setSelectedSort(s)}
                >
                    <div className="-mt-3 pl-3.5">
                        {sortOptions.map(option => (
                            <UiRadioGroup.Option
                                key={option.value}
                                value={option.value}
                                className="flex items-center py-2"
                            >
                                {({checked}) => (
                                    <>
                                        {checked ? (
                                            <UiCheckbox
                                                className="mr-3 rounded-none border-black hover:!bg-secondary-100 focus:!border-0 focus:!ring-0"
                                                style={{
                                                    width: '16px',
                                                    height: '16px'
                                                }}
                                                checked={true}
                                            />
                                        ) : (
                                            <div className="mr-3 h-4 w-4 rounded-none border border-gray-300"></div>
                                        )}

                                        <div className="flex-1">
                                            {t(option.label)}
                                        </div>
                                    </>
                                )}
                            </UiRadioGroup.Option>
                        ))}
                    </div>
                </UiRadioGroup>
            </div>

            <div className="border-t border-gray-200 p-4">
                <UiButton
                    className="w-full bg-secondary-100 text-white hover:bg-secondary-100"
                    onClick={() => {
                        onSort(selectedSort);
                        closeSideBar();
                    }}
                >
                    {t('Apply Sort')}
                </UiButton>
            </div>
        </div>
    );
});

if (isDev) {
    MobileSort.displayName = 'MobileSort';
}

export default MobileSort;
