import {FC, memo, useCallback, useEffect} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev} from '@core/helpers';
import {useIntersection, useTrans, useUI} from '@core/hooks';
import {UiButton} from '@core/components/ui';
import {ChevronDownIcon} from '@core/icons/outline';
import ProductCard from '@components/common/ProductCard';
import {Filter, DownArrow, ChevronLeft} from '@components/icons';
import MobileFilter from './MobileFilter';
import MobileSort from './MobileSort';
import Toolbar from './Toolbar';
import useCatalog from '../useCatalog';

const Products: FC = memo(() => {
    const t = useTrans();
    const router = useRouter();
    const {openSideBar} = useUI();
    const [ref, observer] = useIntersection({
        threshold: 0
    });
    const {
        appliedFilters,
        products,
        filters,
        initialSkip,
        hasNextPage,
        loadMore,
        loadPrevious,
        isLoading,
        isInitial,
        sortOptions,
        sort,
        updateSort,
        updateFilters,
        clearFilters,
        search,
        seo
    } = useCatalog();

    const onOpenMobileSort = useCallback(() => {
        openSideBar(
            t('Sort'),
            <MobileSort
                sortOptions={sortOptions}
                sort={sort}
                onSort={selectedSort => {
                    const [field, direction] = selectedSort.split('|');

                    updateSort({
                        field,
                        direction: direction === 'desc' ? 'desc' : 'asc'
                    });
                }}
            />
        );
    }, [openSideBar, sort, sortOptions, t, updateSort]);
    const onOpenMobileFilter = useCallback(() => {
        openSideBar(
            t('Filter'),
            <MobileFilter
                filters={filters as any}
                appliedFilters={appliedFilters}
                updateFilters={updateFilters}
                clearFilters={clearFilters}
            />
        );
    }, [appliedFilters, clearFilters, filters, openSideBar, t, updateFilters]);

    useEffect(() => {
        if (observer?.isIntersecting) {
            loadMore();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [observer]);

    return (
        <section className="flex-1">
            <div className="mb-4 flex items-center justify-between space-x-2 border-b pb-3 pt-3 font-dm-serif md:hidden">
                <div
                    className="flex items-center justify-center"
                    onClick={onOpenMobileFilter}
                >
                    <Filter className="mr-2.5 h-6 w-6" />
                    <p className="text-sm font-bold text-brand-black">
                        {t('Filter')}
                    </p>
                    <DownArrow className="ml-1 h-3 w-3" />
                    {appliedFilters.length > 0 && (
                        <div className="ml-3 flex h-5 w-5 items-center justify-center rounded-full bg-secondary-100 text-[10px] text-white">
                            {appliedFilters.length}
                        </div>
                    )}
                </div>

                <div
                    className="flex items-center justify-center gap-2"
                    onClick={onOpenMobileSort}
                >
                    <p className="text-sm font-bold text-brand-black">
                        {t('Sort')}
                    </p>
                    <ChevronDownIcon
                        className="h-3 w-3 stroke-current stroke-[60px] font-bold text-secondary-600"
                        aria-hidden="true"
                    />
                </div>
            </div>

            {products.length > 0 && !search && <Toolbar />}

            {initialSkip > 0 && (
                <>
                    <div className="lg:px-2.5">
                        <UiButton
                            size="lg"
                            className="mb-3 w-full rounded-none bg-secondary-100 text-white !ring-secondary-100 hover:bg-secondary-100"
                            onClick={loadPrevious}
                        >
                            {t('SHOW PREVIOUS PRODUCTS')}
                        </UiButton>
                        <a href={seo.prevUrl} className="hidden">
                            {t('SHOW PREVIOUS PRODUCTS')}
                        </a>
                    </div>
                </>
            )}

            {!!search && products.length > 0 && (
                <div className="mb-2 flex flex-col border-b border-secondary-100 pb-1">
                    <div
                        className="flex cursor-pointer items-center justify-start gap-1 text-sm text-secondary-100 hover:font-bold"
                        onClick={() => router.back()}
                    >
                        <span>
                            <ChevronLeft className="h-3 w-3 stroke-current stroke-[60]" />
                        </span>
                        <span>{t('Turn back')}</span>
                    </div>
                    <div className="pt-2 text-2xl font-bold text-secondary-100">
                        {t(`{products} product found.`, {
                            products: products.length
                        })}
                    </div>
                </div>
            )}

            <div
                className={cls('relative grid grid-cols-2 gap-4', {
                    'xl:grid-cols-3': filters.length > 0,
                    'xl:grid-cols-4': filters.length < 1
                })}
            >
                {products.map(product => (
                    <ProductCard
                        key={product.productId}
                        product={product}
                        // @ts-ignore
                        isFake={product.isFake}
                    />
                ))}

                {products.length > 0 && !isLoading && hasNextPage && (
                    <>
                        <div
                            ref={ref}
                            className={cls(
                                'absolute bottom-[600px] left-0 h-0 w-full bg-amber-400',
                                {
                                    'bottom-[300px]': search
                                }
                            )}
                        ></div>
                        <a href={seo.nextUrl} className="hidden">
                            {t('SHOW NEXT PRODUCTS')}
                        </a>
                    </>
                )}
            </div>

            {products.length < 1 && !isLoading && !isInitial && (
                <div className="flex flex-1 flex-col items-center justify-center px-12 pb-[220px] pt-20 ">
                    <h2 className="pb-6 pt-8 text-center text-2xl font-bold leading-10 text-brand-black md:pb-0 md:text-3xl xl:text-4xl">
                        {t('There were no results')}
                    </h2>
                </div>
            )}
        </section>
    );
});

if (isDev) {
    Products.displayName = 'Products';
}

export default Products;
