import {
    ChangeEventHandler,
    FC,
    FocusEventHandler,
    KeyboardEventHandler,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import storeConfig from '~/store.config';
import {ProductSearchResultItem} from '@core/types';
import {cls, debounce, isDev, jsonRequest, toUpper, trim} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiImage, UiInput, UiLink} from '@core/components/ui';
import {SearchIcon} from '@core/icons/outline';

const CatalogSearchResultItem: FC<{
    item: ProductSearchResultItem;
    onClick: () => void;
}> = memo(({item, onClick}) => {
    const t = useTrans();
    const description = useMemo(() => {
        if (item.type === 'product') {
            return t('Product');
        } else if (item.type === 'brand') {
            return t('Brand');
        } else if (item.type === 'navigation') {
            return t('Category');
        }

        return '';
    }, [t, item.type]);

    return (
        <UiLink
            className="group flex flex-shrink-0 cursor-pointer rounded-none border-0 p-2 transition hover:bg-gray-100 focus:outline-none"
            href={`/${item.slug}`}
            onClick={onClick}
        >
            {typeof item.image === 'string' && item.image.length > 0 ? (
                <div
                    className={cls(
                        'h-12 flex-shrink-0 overflow-hidden rounded-none',
                        {
                            'w-9':
                                storeConfig.catalog.productImageShape ===
                                'rectangle',
                            'w-12':
                                storeConfig.catalog.productImageShape !==
                                'rectangle'
                        }
                    )}
                >
                    <UiImage
                        className="h-full w-full rounded-none"
                        src={
                            item.isAdultProduct
                                ? '/adult-image.png'
                                : item.image
                        }
                        alt={item.name}
                        width={
                            storeConfig.catalog.productImageShape ===
                            'rectangle'
                                ? 36
                                : 48
                        }
                        height={48}
                        fit="cover"
                        position="center"
                        quality={75}
                    />
                </div>
            ) : (
                <div className="flex h-12 w-12 items-center justify-center bg-secondary-50 text-xl font-semibold text-secondary-600 transition group-hover:bg-secondary-100 group-hover:text-white">
                    {item.name
                        .split(' ')
                        .slice(0, 2)
                        .map(s => toUpper(s)[0])
                        .join('')}
                </div>
            )}

            <div className="ml-3 flex flex-1 flex-col justify-center">
                <div className="mb-1 text-sm font-medium">{item.name}</div>
                <div className="text-sm text-muted">{description}</div>
            </div>
        </UiLink>
    );
});

if (isDev) {
    CatalogSearchResultItem.displayName = 'CatalogSearchResultItem';
}

const CatalogSearchBarPartial: FC = memo(() => {
    const router = useRouter();
    const t = useTrans();
    const [searchResult, setSearchResult] = useState<ProductSearchResultItem[]>(
        []
    );
    const [searchQuery, setSearchQuery] = useState('');
    const [isResultShown, setIsResultShown] = useState(false);
    const [isBlurInProgress, setIsBlurInProgress] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isSearchPageLoading, setIsSearchPageLoading] = useState(false);
    const blurTimeoutIdx = useRef<any>();
    const inputRef = useRef<HTMLInputElement>(null);

    const searchDebounced = useRef(
        debounce(
            async (query: string) => {
                query = trim(query);

                try {
                    if (!(query === '' || query.length < 2)) {
                        const result = await jsonRequest({
                            url: '/api/catalog/search',
                            method: 'POST',
                            data: {searchQuery: query}
                        });

                        setSearchResult(() =>
                            result.map((item: ProductSearchResultItem) => ({
                                ...item,
                                name: !!item.brandName
                                    ? `${item.brandName} ${item.name}`
                                    : item.name
                            }))
                        );
                    } else {
                        setSearchResult([]);
                    }
                } catch (error: any) {}

                setIsLoading(false);
            },
            250,
            {leading: false, trailing: true}
        )
    );
    const onSearch: ChangeEventHandler<HTMLInputElement> = useCallback(e => {
        const query = e.target.value;

        setIsLoading(true);
        searchDebounced.current(query);
        setSearchQuery(query);
    }, []);
    const onFocus: FocusEventHandler<HTMLInputElement> = useCallback(e => {
        setIsBlurInProgress(false);
        setIsResultShown(true);

        try {
            clearTimeout(blurTimeoutIdx.current);
        } catch (error: any) {}
    }, []);
    const onBlur: FocusEventHandler<HTMLInputElement> = useCallback(e => {
        setIsBlurInProgress(true);
    }, []);
    useEffect(() => {
        if (isBlurInProgress) {
            blurTimeoutIdx.current = setTimeout(() => {
                setIsBlurInProgress(false);
            }, 200);
        }

        return () => {
            try {
                clearTimeout(blurTimeoutIdx.current);
            } catch (error: any) {}
        };
    }, [isBlurInProgress]);

    const onGoToSearchDetail = useCallback(async () => {
        setIsSearchPageLoading(true);
        setIsResultShown(false);
        setSearchResult([]);
        setIsLoading(false);
        setSearchQuery('');
        setIsBlurInProgress(false);
        try {
            clearTimeout(blurTimeoutIdx.current);
        } catch (error: any) {}

        await router.push(
            `/search?${new URLSearchParams({query: searchQuery}).toString()}`
        );

        if (!!inputRef.current) {
            inputRef.current.blur();
        }
        setIsSearchPageLoading(false);
    }, [router, searchQuery]);
    const onKeyDown: KeyboardEventHandler<HTMLInputElement> = useCallback(
        e => {
            if (e.code === 'Enter' || e.code === 'NumpadAdd') {
                onGoToSearchDetail();
            }
        },
        [onGoToSearchDetail]
    );

    return (
        <>
            <div className="gap-y-8 pb-5 lg:px-5">
                <div className="flex items-center justify-center pb-10 pt-5">
                    <p className="text-center font-dm-serif text-3xl font-bold text-brand-black lg:text-[33px]">
                        {t('Looking for something else?')}
                    </p>
                </div>
                <div>
                    <div className="flex h-full w-full items-center justify-center px-5 lg:px-0">
                        <UiInput.Group className="h-10 w-full max-w-3xl flex-1 xl:h-20">
                            <UiInput.LeftElement className="flex pl-4 pt-7  lg:hidden lg:pl-6 lg:pt-6 xl:pt-10">
                                <div className="">
                                    <SearchIcon className="h-5 w-5 text-default lg:h-6 lg:w-6" />
                                </div>
                            </UiInput.LeftElement>
                            <UiInput
                                placeholder={t(
                                    'Search for product, category or collection'
                                )}
                                className="xl:h-15 h-16 w-full max-w-3xl flex-1 !border-none bg-brand-search !pl-12 text-sm text-[#3b3b3b] placeholder:!text-secondary-400  focus:!ring-0  lg:placeholder:pl-0 xl:h-20 placeholder:xl:text-lg"
                                value={searchQuery}
                                onChange={onSearch}
                                onFocus={onFocus}
                                onBlur={onBlur}
                                onKeyDown={onKeyDown}
                            />
                            <UiInput.RightElement className="hidden pr-16 pt-5 sm:pt-5 lg:flex lg:pt-6 xl:pt-10">
                                <div className="">
                                    <SearchIcon className="h-6 w-6 text-default" />
                                </div>
                            </UiInput.RightElement>
                        </UiInput.Group>
                    </div>
                </div>
            </div>
        </>
    );
});

if (isDev) {
    CatalogSearchBarPartial.displayName = 'CatalogSearchBarPartial';
}

export default CatalogSearchBarPartial;
