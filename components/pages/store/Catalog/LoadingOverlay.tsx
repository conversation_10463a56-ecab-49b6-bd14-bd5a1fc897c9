import {FC, Fragment, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiTransition} from '@core/components/ui';
import useCatalog from './useCatalog';

const LoadingOverlay: FC = memo(() => {
    const {isLoading} = useCatalog();

    return (
        <UiTransition
            show={isLoading}
            as={Fragment}
            enter="transition duration-100"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
        >
            <div className="fixed inset-0 z-20 bg-white bg-opacity-40 backdrop-blur-sm" />
        </UiTransition>
    );
});

if (isDev) {
    LoadingOverlay.displayName = 'LoadingOverlay';
}

export default LoadingOverlay;
