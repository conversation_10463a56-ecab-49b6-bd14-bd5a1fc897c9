import {
    FC,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import type {Filter as FilterType} from '@core/types';
import {isDev, toLower, trim} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {SearchIcon, XIcon} from '@core/icons/outline';
import {UiCheckbox, UiDisclosure, UiInput} from '@core/components/ui';
import {DownArrow} from '@components/icons';
import PriceFilter from './PriceFilter';
import useCatalog from './useCatalog';

const Filter: FC<{filter: FilterType}> = memo(({filter}) => {
    const t = useTrans();
    const [search, setSearch] = useState('');
    const {appliedFilters, addFilter, removeFilter} = useCatalog();

    const filteredItems = useMemo(
        () =>
            filter.items.filter(
                item =>
                    toLower(item.label).indexOf(toLower(trim(search))) !== -1
            ),
        [filter.items, search]
    );

    const scrollToTop = useCallback(() => {
        const container = document.querySelector('.content-wrapper');
        if (container === null || container === undefined) return;

        container.scrollTo({top: 0, behavior: 'smooth'});
    }, []);

    const capitalizeFirstLetter = useCallback(
        (text: string) =>
            text
                .split(' ')
                .map(
                    word =>
                        word.charAt(0).toLocaleUpperCase('tr') +
                        word.slice(1).toLocaleLowerCase('tr')
                )
                .join(' '),
        []
    );

    return (
        <UiDisclosure
            as="div"
            key={filter.label + filter.field}
            defaultOpen={filter.label === 'Price' ? true : false}
            className="border-b border-gray-200 py-5"
        >
            <h3 className="-my-3 flow-root">
                <UiDisclosure.Button className="flex w-full items-center justify-between bg-white py-1.5 pl-3 pr-2 text-sm">
                    <div className="flex">
                        <span className="mr-1.5 flex items-center text-gray-500">
                            <DownArrow
                                className="h-3 w-3 text-secondary-100"
                                aria-hidden="true"
                            />
                        </span>
                        <span className="font-dm-serif text-lg font-normal text-brand-black">
                            {capitalizeFirstLetter(t(filter.label))}
                        </span>
                    </div>

                    <div>
                        <p className="pr-4 font-dm-serif text-xs text-secondary-900">
                            {t('All')}
                        </p>
                    </div>
                </UiDisclosure.Button>
            </h3>

            <UiDisclosure.Panel className="pt-6">
                {filter.items.length > 10 && (
                    <div className="px-2 pb-3">
                        <UiInput.Group className="flex items-center justify-center">
                            <UiInput.LeftElement>
                                <SearchIcon className="h-4 w-4 text-default" />
                            </UiInput.LeftElement>
                            <UiInput
                                className="rounded-none border-secondary-900 pl-1 text-3xs text-secondary-900 placeholder:text-3xs placeholder:font-extralight hover:border-secondary-900 focus:!border-secondary-900 focus:!ring-0"
                                size="xs"
                                placeholder={t(`{artifact} search`, {
                                    artifact: filter.label
                                })}
                                onChange={e => setSearch(e.target.value)}
                            />
                        </UiInput.Group>
                    </div>
                )}

                <div className="scroller h-max space-y-1 overflow-auto overflow-y-hidden px-2 pb-1">
                    {filter && filter.type === 'price' ? (
                        <PriceFilter filter={filter} />
                    ) : filter.isColorAttribute ? (
                        <div className="grid h-full space-y-1.5 pl-1">
                            {filteredItems.map((item, index) => (
                                <UiCheckbox
                                    key={index}
                                    className="!rounded-full !border !border-black !border-opacity-10 !ring-1 !ring-white !ring-offset-[4px] checked:!ring-black checked:hover:bg-secondary-100"
                                    style={{
                                        width: '30px',
                                        height: '30px',
                                        backgroundColor: item.color
                                    }}
                                    checked={
                                        appliedFilters.findIndex(
                                            (appliedFilter: any) =>
                                                appliedFilter.field ===
                                                    filter.field &&
                                                appliedFilter.selected.value ===
                                                    item.value
                                        ) !== -1
                                    }
                                    onChange={e => {
                                        scrollToTop();
                                        // @ts-ignore
                                        e.target.checked
                                            ? addFilter({
                                                  type: filter.type,
                                                  label: filter.label,
                                                  field: filter.field,
                                                  isColorAttribute:
                                                      filter.isColorAttribute,
                                                  selected: item
                                              })
                                            : removeFilter({
                                                  type: filter.type,
                                                  label: filter.label,
                                                  field: filter.field,
                                                  isColorAttribute:
                                                      filter.isColorAttribute,
                                                  selected: item
                                              });
                                    }}
                                >
                                    <span className="ml-1 select-none font-dm-serif leading-10 text-base text-brand-black">
                                        {item.label}
                                    </span>
                                </UiCheckbox>
                            ))}
                        </div>
                    ) : (
                        filteredItems.map(item => (
                            <div
                                key={item.value}
                                className="group flex items-center py-2.5"
                            >
                                <UiCheckbox
                                    className={`rounded-none bg-white focus:!border-gray-300 focus:!ring-0 ${
                                        appliedFilters.findIndex(
                                            appliedFilter =>
                                                appliedFilter.field ===
                                                    filter.field &&
                                                appliedFilter.selected.value ===
                                                    item.value
                                        ) !== -1
                                            ? 'hover:!bg-secondary-100'
                                            : 'hover:bg-white'
                                    }`}
                                    checked={
                                        appliedFilters.findIndex(
                                            (appliedFilter: any) =>
                                                appliedFilter.field ===
                                                    filter.field &&
                                                appliedFilter.selected.value ===
                                                    item.value
                                        ) !== -1
                                    }
                                    onChange={e => {
                                        scrollToTop();
                                        // @ts-ignore
                                        e.target.checked
                                            ? addFilter({
                                                  type: filter.type,
                                                  label: filter.label,
                                                  field: filter.field,
                                                  isColorAttribute:
                                                      filter.isColorAttribute,
                                                  selected: item
                                              })
                                            : removeFilter({
                                                  type: filter.type,
                                                  label: filter.label,
                                                  field: filter.field,
                                                  isColorAttribute:
                                                      filter.isColorAttribute,
                                                  selected: item
                                              });
                                    }}
                                >
                                    <span className="font-dm-serif text-base text-brand-black">
                                        {capitalizeFirstLetter(item.label)}
                                    </span>
                                </UiCheckbox>
                            </div>
                        ))
                    )}
                </div>
            </UiDisclosure.Panel>
        </UiDisclosure>
    );
});

if (isDev) {
    Filter.displayName = 'Filter';
}

const Filters: FC = memo(() => {
    const {filters, appliedFilters, removeFilter, clearFilters} = useCatalog();
    const {currency} = useStore();
    const t = useTrans();

    const sortedFilters = useMemo(() => {
        const priceFilter = filters.find(filter => filter.type === 'price');
        const otherFilters = filters.filter(filter => filter.type !== 'price');
        return priceFilter ? [priceFilter, ...otherFilters] : filters;
    }, [filters]);

    return (
        <aside
            className=" sticky top-36 mr-4 hidden  h-[--topbar-height]  w-[340px] overflow-y-scroll xl:block "
            style={{scrollbarWidth: 'none'}}
        >
            <div className="mb-40  box-content h-screen w-[340px] overflow-y-scroll  pr-4 transition duration-300 ">
                <p className="pb-8 font-dm-serif text-3xl text-brand-black">
                    {t('Filter')}
                </p>

                {appliedFilters.length > 0 && (
                    <div className="flex flex-wrap gap-2 py-2">
                        {appliedFilters.map(appliedFilter => (
                            <div
                                key={`${appliedFilter.field}-${appliedFilter.selected.value}`}
                                className="flex h-8 items-center justify-start gap-x-1 rounded-full !bg-secondary-200 px-3 text-xs font-medium leading-3"
                            >
                                <button
                                    className="group flex h-4 w-4 items-center justify-center"
                                    onClick={() => removeFilter(appliedFilter)}
                                >
                                    <XIcon className="h-3 w-3 text-gray-800" />
                                </button>
                                <span className="font-dm-serif">
                                    {t(appliedFilter.label)}:
                                </span>

                                <div className="flex items-center font-dm-serif">
                                    {appliedFilter.isColorAttribute ? (
                                        <div className="flex items-center">
                                            <div
                                                className="relative mr-1 h-3.5 w-3.5 overflow-hidden border"
                                                style={{
                                                    backgroundColor:
                                                        appliedFilter.selected
                                                            .color
                                                }}
                                            >
                                                {!appliedFilter.selected
                                                    .color && (
                                                    <svg
                                                        className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                                        viewBox="0 0 100 100"
                                                        preserveAspectRatio="none"
                                                        stroke="currentColor"
                                                    >
                                                        <line
                                                            x1={0}
                                                            y1={100}
                                                            x2={100}
                                                            y2={0}
                                                            vectorEffect="non-scaling-stroke"
                                                        />
                                                    </svg>
                                                )}
                                            </div>

                                            {appliedFilter.selected.label}
                                        </div>
                                    ) : (
                                        `${appliedFilter.selected.label} ${
                                            appliedFilter.type === 'price'
                                                ? currency.name
                                                : ''
                                        }`
                                    )}
                                </div>
                            </div>
                        ))}

                        <button
                            onClick={clearFilters}
                            className="flex h-8 items-center rounded border bg-gray-100 px-3 font-dm-serif text-xs font-medium transition hover:bg-gray-200"
                        >
                            {t('Clear All Filters')}
                        </button>
                    </div>
                )}

                {sortedFilters.map(filter => (
                    <Filter key={filter.label + filter.field} filter={filter} />
                ))}
            </div>
        </aside>
    );
});

if (isDev) {
    Filters.displayName = 'Filters';
}

export default Filters;
