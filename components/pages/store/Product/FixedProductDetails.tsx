import {FC, memo, useCallback, useState} from 'react';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiButton, UiImage, UiSpinner} from '@core/components/ui';

import Price from '@components/common/Price';
import useProduct from '@core/pages/store/Product/useProduct';
import FixedQuantity from './FixedQuantity';
import {HeartIcon} from '@components/icons';

const FixedProductDetails: FC = memo(() => {
    const t = useTrans();
    const {addToFavorites, removeFromFavorites} = useStore();
    const {
        selectedProduct,
        setQuantity,
        isAddToCartInProgress,
        addToCart,
        availableQuantity,
        inStock,
        customerProductParams,
        setCustomerProductParams
    } = useProduct();

    // Favorite.
    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);
    const onAddToFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await addToFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: true
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        addToFavorites,
        selectedProduct
    ]);
    const onRemoveFromFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await removeFromFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: false
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        removeFromFavorites,
        selectedProduct
    ]);

    return (
        <section className="fixed left-0 right-0 top-0 z-[80] hidden bg-secondary-100 py-2 xl:block">
            <div className="container flex items-center justify-between py-2">
                <div className="flex items-center gap-4 ">
                    <div className="relative h-12 w-12">
                        <UiImage
                            src={
                                (selectedProduct.images as string[])[0] ??
                                '/no-image.png'
                            }
                            alt={selectedProduct.name}
                            fill
                        />
                    </div>
                    <div className="font-hurme text-lg font-bold text-white">
                        {selectedProduct.name}
                    </div>
                </div>

                <div className="flex items-center gap-8">
                    {selectedProduct.salesPrice > 0 &&
                        selectedProduct.availableQuantity > 0 && (
                            <Price
                                price={
                                    selectedProduct.hasDiscount
                                        ? selectedProduct.unDiscountedSalesPrice
                                        : selectedProduct.salesPrice
                                }
                                discountedPrice={
                                    selectedProduct.hasDiscount
                                        ? selectedProduct.salesPrice
                                        : null
                                }
                                dontWrapDiscountedPrice
                                className=" sticky-details font-hurme font-semibold text-white [&>span]:text-2xl"
                                decimal={0}
                            />
                        )}
                    <div className="flex items-center gap-4">
                        {inStock ? (
                            <div className="flex items-center gap-4">
                                <FixedQuantity
                                    quantity={selectedProduct.quantity}
                                    availableQuantity={availableQuantity}
                                    onChange={quantity => setQuantity(quantity)}
                                />

                                <UiButton
                                    className="!h-11 w-52 rounded-sm border-none bg-white text-sm font-bold text-base text-secondary-100 !ring-0 transition hover:bg-white hover:text-brand-clr focus:!ring-0"
                                    loading={isAddToCartInProgress}
                                    disabled={!inStock}
                                    onClick={addToCart}
                                >
                                    {t('Add To Cart')}
                                </UiButton>
                            </div>
                        ) : (
                            <UiButton
                                className="!h-11 w-52 rounded-sm border-none bg-white text-sm font-bold text-base text-secondary-100 !ring-0 transition hover:bg-white hover:text-brand-clr focus:!ring-0 disabled:bg-white disabled:opacity-100"
                                loading={isAddToCartInProgress}
                                disabled={!inStock}
                                onClick={addToCart}
                            >
                                {t('In Stock Soon')}
                            </UiButton>
                        )}

                        {customerProductParams.isFavorite ? (
                            !isFavoriteUpdateInProgress ? (
                                <UiButton
                                    className="flex h-11 items-center rounded-sm border-white border-opacity-50 bg-white transition hover:bg-white"
                                    onClick={onRemoveFromFavorites}
                                >
                                    <HeartIcon className="h-3.5 w-3.5 fill-secondary-100 stroke-secondary-100 text-secondary-100 hover:text-brand-clr" />
                                </UiButton>
                            ) : (
                                <UiButton className="flex h-11 items-center rounded-sm border-white border-opacity-50 bg-white transition hover:bg-white">
                                    <div className="mr-1 w-4">
                                        <UiSpinner size="sm" />
                                    </div>
                                </UiButton>
                            )
                        ) : !isFavoriteUpdateInProgress ? (
                            <UiButton
                                className="flex h-11 items-center rounded-sm border-white border-opacity-50 bg-white transition hover:border-white hover:bg-white"
                                onClick={onAddToFavorites}
                            >
                                <HeartIcon className=" h-3.5 w-3.5 stroke-current  text-secondary-100 hover:text-brand-clr" />
                            </UiButton>
                        ) : (
                            <UiButton className="flex h-11 items-center rounded-sm border-white border-opacity-50 bg-white transition hover:bg-white">
                                <div className="w-4">
                                    <UiSpinner size="sm" />
                                </div>
                            </UiButton>
                        )}
                    </div>
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    FixedProductDetails.displayName = 'FixedProductDetails';
}

export default FixedProductDetails;
