import {memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {TruckFastIcon} from '@core/icons/solid';
import useProduct from '@core/pages/store/Product/useProduct';

const ShipmentOptions = memo(() => {
    const t = useTrans();
    const {locale} = useStore();
    const {
        product: {estimatedDeliveryDuration}
    } = useProduct();

    const getEstimatedShippingDate = useMemo(() => {
        const currentDate = new Date();
        currentDate.setDate(
            currentDate.getDate() + (estimatedDeliveryDuration ?? 2)
        );

        const formatter = new Intl.DateTimeFormat(locale, {
            day: 'numeric',
            month: 'long'
        });
        return formatter.format(currentDate);
    }, [locale, estimatedDeliveryDuration]);

    return typeof estimatedDeliveryDuration === 'number' ? (
        <div className="text-xs max-xl:mt-4">
            {estimatedDeliveryDuration === 1 && (
                <div className="flex items-center gap-3 rounded bg-green-50 p-2 max-xl:border max-xl:border-green-600">
                    <TruckFastIcon className="h-4 w-4 text-green-600" />
                    <p className="w-full text-green-600">
                        {t('If you order now, we will ship it tomorrow!')}
                    </p>
                </div>
            )}
            {estimatedDeliveryDuration >= 2 && (
                <div className="flex items-center gap-2">
                    <TruckFastIcon className="h-4 w-4 text-brand-clr" />
                    <p
                        dangerouslySetInnerHTML={{
                            __html: t(
                                'We will ship it on {getEstimatedShippingDate}!',
                                {getEstimatedShippingDate}
                            )
                        }}
                    />
                </div>
            )}
        </div>
    ) : null;
});

if (isDev) {
    ShipmentOptions.displayName = 'ShipmentOptions';
}

export default ShipmentOptions;
