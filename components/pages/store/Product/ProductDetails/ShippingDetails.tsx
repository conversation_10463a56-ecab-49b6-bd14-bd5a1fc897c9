import {memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useProduct from '@core/pages/store/Product/useProduct';
import {Play} from '@components/icons';
import {ChevronRightIcon} from '@core/icons/outline';

type Props = {
    setIsShown: (value: boolean) => void;
};

const ShippingDetails = memo((props: Props) => {
    const t = useTrans();
    const {setIsShown} = props;
    const {product} = useProduct();

    const videoRegex = /<iframe.*?src="(.*?)".*?<\/iframe>/;
    const videoMatch = (product.description || '').match(videoRegex);
    const videoUrl = videoMatch ? videoMatch[1] : null;

    return (
        <div className="absolute -bottom-3 -right-3 z-[9] flex h-fit select-none flex-col gap-1 text-[8px] lg:-right-4 xl:-bottom-3">
            {videoUrl && (
                <div
                    onClick={() => setIsShown(true)}
                    className="flex w-full cursor-pointer items-center justify-center gap-1 rounded-full border  border-primary-600 bg-primary-600 px-1 py-1 text-center leading-[12px] text-white"
                >
                    <div className="h-4 w-4 ">
                        <Play className="h-4 w-4 rounded-full border bg-white fill-primary-600 p-1 " />
                    </div>
                    <span className="text-xs font-semibold">
                        {t('Watch Product Video')}
                    </span>
                    <div>
                        <ChevronRightIcon className="h-3 w-3 stroke-current stroke-[30px] text-white" />
                    </div>
                </div>
            )}
        </div>
    );
});

if (isDev) {
    ShippingDetails.displayName = 'ShippingDetails';
}

export default ShippingDetails;
