import {memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useProduct from '@core/pages/store/Product/useProduct';
import {LuPackage} from '@components/icons';

const ProductCampaigns = memo(() => {
    const t = useTrans();
    const {campaigns} = useProduct();

    return (
        <div>
            {Array.isArray(campaigns) && campaigns.length > 0 ? (
                <div className="grid grid-cols-1 gap-2 text-xs lg:mt-1 lg:grid-cols-2">
                    {campaigns.map(campaign => (
                        <dt
                            key={campaign.id}
                            className="flex items-center gap-2 border border-secondary-100 bg-brand-budget  px-5 py-2 text-xs text-secondary-100 shadow-sm"
                        >
                            <LuPackage className=" stroke-secondary-100" />
                            <p className="w-fit font-semibold">
                                {campaign.description}
                            </p>
                        </dt>
                    ))}
                </div>
            ) : (
                <p className="text-xs">{t('No campaign found!')}</p>
            )}
        </div>
    );
});

if (isDev) {
    ProductCampaigns.displayName = 'ProductCampaigns';
}

export default ProductCampaigns;
