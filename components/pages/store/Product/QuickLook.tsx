import {FC, memo, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useProduct from '@core/pages/store/Product/useProduct';
import Product from '@core/types/Product';

const items = [
    {
        id: 'specs',
        name: '<PERSON><PERSON><PERSON><PERSON>'
    }
];

const QuickLook: FC<{product: Product}> = memo(({product}) => {
    const t = useTrans();
    const [selectedItem, setSelectedItem] = useState<string>('specs');
    const {selectedProduct} = useProduct();

    const videoRegex = /<iframe.*?src="(.*?)".*?<\/iframe>/;

    const textWithoutVideo = (selectedProduct.description || '')
        .replace(videoRegex, '')
        .trim();

    const formatDescription = (textWithoutVideo: string) => {
        return textWithoutVideo.replace(/;\s/g, '<br/>');
    };

    return (
        <div className="w-full pb-4">
            <div className="mt-4 flex flex-col gap-2 xl:flex-row xl:justify-between">
                <div className="flex items-start gap-x-3">
                    {items.map(item => (
                        <button
                            key={item.id}
                            onClick={() => setSelectedItem(item.id)}
                            className={cls('text-sm font-bold text-brand-clr', {
                                underline: selectedItem === item.id
                            })}
                        >
                            {item.name}
                        </button>
                    ))}
                </div>
                <div className="flex items-start justify-start gap-1 text-2xs font-semibold text-brand-clr opacity-50">
                    <p className="uppercase">{t('Product Code')}:</p>
                    <p>{product.barcode}</p>
                </div>
            </div>

            {selectedItem === 'specs' && (
                <div
                    className="prose pt-2 text-xs text-brand-black"
                    dangerouslySetInnerHTML={{__html: textWithoutVideo}}
                />
            )}
        </div>
    );
});

if (isDev) {
    QuickLook.displayName = 'QuickLook';
}

export default QuickLook;
