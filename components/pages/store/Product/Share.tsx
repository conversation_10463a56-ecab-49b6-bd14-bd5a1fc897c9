import {FC, memo, useCallback} from 'react';
import {isDev, trim} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useProduct from '@core/pages/store/Product/useProduct';
import {ShareLink} from '@components/icons';

const Share: FC = memo(() => {
    const t = useTrans();
    const {selectedProduct} = useProduct();

    const productLink = `${trim(process.env.NEXT_PUBLIC_SITE_URL, '/')}/${
        selectedProduct.slug
    }`;

    const startWhatsAppChat = useCallback(() => {
        const whatsappMessage = encodeURIComponent(
            ` ${selectedProduct.definition} - ${productLink}`
        );
        const whatsappLink = `https://api.whatsapp.com/send?phone=905353460959&text=${whatsappMessage}`;
        window.open(whatsappLink, '_blank');
    }, [selectedProduct.definition, productLink]);

    return (
        <button
            onClick={startWhatsAppChat}
            className="mb-1 flex h-9 w-20 items-center justify-center gap-1.5 rounded-full underline hover:no-underline xl:items-center xl:border xl:border-brand-black xl:border-opacity-50"
        >
            <p className="text-3xs text-brand-black">{t('Share')}</p>
            <ShareLink className="h-3.5 w-3.5" />
        </button>
    );
});

if (isDev) {
    Share.displayName = 'Share';
}

export default Share;
