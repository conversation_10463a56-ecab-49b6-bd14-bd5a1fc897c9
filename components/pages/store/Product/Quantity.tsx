import {
    ChangeEvent<PERSON><PERSON><PERSON>,
    FC,
    FocusEventHandler,
    memo,
    useCallback,
    useEffect,
    useState
} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiInput} from '@core/components/ui';
import {ChevronDownIcon, ChevronUpIcon} from '@core/icons/outline';

export type QuantityProps = {
    className?: string;
    size?: 'sm' | 'md';
    quantity: number;
    availableQuantity: number;
    onChange: (quantity: number) => void;
};

const Quantity: FC<QuantityProps> = memo(props => {
    const {
        size = 'md',
        quantity = 1,
        availableQuantity = 1,
        className,
        onChange
    } = props;
    const [qty, setQty] = useState(quantity.toString());

    useEffect(() => {
        if (qty !== quantity.toString()) {
            setQty(quantity.toString());
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [quantity]);

    const increaseQty = useCallback(() => {
        const parsed = parseFloat(qty);

        if (isNaN(parsed)) {
            return qty;
        }

        let newQty = Math.min(parsed + 1, availableQuantity);

        if (newQty < 1) {
            newQty = 1;
        }

        onChange(newQty);
        setQty(newQty.toString());
    }, [availableQuantity, qty, onChange]);
    const decreaseQty = useCallback(() => {
        const parsed = parseFloat(qty);

        if (isNaN(parsed)) {
            return qty;
        }

        const newQty = Math.max(1, parsed - 1);

        onChange(newQty);
        setQty(newQty.toString());
    }, [qty, onChange]);
    const onInputChange: ChangeEventHandler<HTMLInputElement> = useCallback(
        e => setQty(e.target.value),
        []
    );
    const onInputBlur: FocusEventHandler<HTMLInputElement> = useCallback(() => {
        let newQty = parseFloat(qty);

        if (isNaN(newQty)) {
            setQty(quantity.toString());

            return;
        }

        newQty = Math.max(1, Math.min(newQty, availableQuantity));

        onChange(newQty);
        setQty(newQty.toString());
    }, [availableQuantity, qty, quantity, onChange]);

    return (
        <UiInput.Group className={className} size={size === 'md' ? 'xl' : 'md'}>
            <div className="quantity-input">
                <UiInput
                    className={cls(
                        'focus:ring-none h-12 w-14 rounded-none	 rounded-bl-sm rounded-tl-sm border-brand-clr border-r-transparent border-opacity-50 text-lg hover:border-brand-clr hover:border-r-transparent hover:border-opacity-50 disabled:border-brand-clr disabled:border-r-transparent disabled:bg-white disabled:!text-black',
                        'text-center',
                        'placeholder-center'
                    )}
                    value={qty}
                    onChange={onInputChange}
                    onBlur={onInputBlur}
                    placeholder="Your Placeholder Text"
                />
            </div>

            <UiInput.RightAddon className="flex flex-col items-center justify-center rounded-none rounded-br-sm rounded-tr-sm border-brand-clr border-l-transparent border-opacity-50 bg-white px-1 pr-3">
                <div
                    className={cls(
                        'flex h-full w-full items-center text-black',
                        quantity >= availableQuantity
                            ? 'cursor-not-allowed text-gray-300'
                            : 'cursor-pointer'
                    )}
                    onClick={increaseQty}
                >
                    <ChevronUpIcon
                        className={cls(
                            'h-2.5 w-2.5 stroke-current stroke-[40px] ',
                            {
                                'h-4 w-4': size === 'sm'
                            }
                        )}
                    />
                </div>

                <div
                    className={cls(
                        'flex h-full w-full items-center text-black',
                        quantity <= 1
                            ? 'cursor-not-allowed text-gray-300'
                            : 'cursor-pointer'
                    )}
                    onClick={decreaseQty}
                >
                    <ChevronDownIcon
                        className={cls(
                            'h-2.5 w-2.5 stroke-current stroke-[40px]',
                            {
                                'h-4 w-4': size === 'sm'
                            }
                        )}
                    />
                </div>
            </UiInput.RightAddon>
        </UiInput.Group>
    );
});

if (isDev) {
    Quantity.displayName = 'Quantity';
}

export default Quantity;
