import {Dialog} from '@headlessui/react';
import {Cookies} from 'react-cookie-consent';
import {useRouter} from 'next/router';
import {useTrans, useUI} from '@core/hooks';
import {UiButton} from '@core/components/ui';

const AdultConsent = () => {
    const t = useTrans();
    const {closeModal} = useUI();
    const router = useRouter();

    return (
        <Dialog
            as="div"
            className="fixed inset-0 z-modal overflow-y-auto"
            onClose={() => {}}
            open={Cookies.get('isAdultEligible') !== 'true'}
        >
            <div
                className="flex min-h-screen text-center sm:block sm:px-6 xl:px-8"
                style={{fontSize: 0}}
            >
                <Dialog.Overlay className="hidden sm:fixed sm:inset-0 sm:block sm:bg-gray-900 sm:bg-opacity-95 sm:transition-opacity" />

                <span
                    className="hidden sm:inline-block sm:h-screen sm:align-middle"
                    aria-hidden="true"
                >
                    &#8203;
                </span>

                <div className="flex w-full max-w-lg transform text-left text-base transition sm:my-8 sm:inline-block sm:align-middle">
                    <div className="relative flex w-full flex-col items-stretch overflow-hidden bg-white sm:rounded-lg">
                        <div className="flex flex-col items-center gap-4 px-3 py-2 text-3xl font-semibold text-danger-700 xl:px-6 xl:py-4">
                            <div className="flex h-24 w-24 items-center justify-center rounded-full border-4 border-danger-700 text-4xl">
                                +18
                            </div>
                            <p>{t('Age Warning')}</p>
                        </div>

                        <div className="flex flex-col px-6">
                            <p className="my-3 text-center font-medium text-muted">
                                {t(
                                    'Persons under 18 years of age are prohibited from browsing this page and purchasing products. If you are under the age of 18 and are not legally an adult, please do not view or purchase these products.'
                                )}
                            </p>

                            <div className="my-6 flex w-full flex-col gap-3">
                                <UiButton
                                    onClick={() => {
                                        Cookies.set('isAdultEligible', false);
                                        closeModal();
                                        router.back();
                                    }}
                                    className="max-md:w-full"
                                    color="primary"
                                    variant="outline"
                                    size="lg"
                                >
                                    {t("I'm under 18")}
                                </UiButton>
                                <UiButton
                                    onClick={() => {
                                        Cookies.set('isAdultEligible', true);
                                        router.reload();
                                    }}
                                    className="max-md:w-full"
                                    color="primary"
                                    variant="outline"
                                    size="lg"
                                >
                                    {t("I'm over 18")}
                                </UiButton>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Dialog>
    );
};

export default AdultConsent;
