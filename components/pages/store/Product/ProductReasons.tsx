import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {UiImage} from '@core/components/ui';
import useProduct from '@core/pages/store/Product/useProduct';
import {useTrans} from '@core/hooks';

const ProductReasons: FC = memo(() => {
    const {product} = useProduct();
    const t = useTrans();

    const productReasons = useMemo(() => {
        if (Array.isArray(product.tags) && product.tags) {
            return product.tags
                .filter(
                    tag => Array.isArray(tag.images) && tag.images.length > 0
                )
                .map(tag => ({
                    id: tag.tagId,
                    name: tag.name,
                    description: tag.description,
                    src: (tag.images as string[])[0]
                }));
        }
        return [];
    }, [product]);

    return (
        productReasons.length > 0 && (
            <div className="bg-brand-pink py-10">
                <div className="container flex flex-col">
                    <div className="flex items-center justify-center">
                        <p className="max-w-md pb-3 pt-6 text-center font-dm-serif uppercase text-base text-brand-clr md:text-xl">
                            {t(
                                '{productName} your selection {reasonCount} reasons for',
                                {
                                    productName: product.name,
                                    reasonCount: productReasons.length
                                }
                            )}
                        </p>
                    </div>

                    <div className="flex flex-wrap items-start justify-center gap-8">
                        {productReasons?.map(tag => (
                            <div
                                key={tag.id}
                                className="flex items-center justify-center"
                            >
                                <div className="flex flex-col items-center justify-center">
                                    <div className="h-32 w-32 rounded-full">
                                        <UiImage
                                            className="rounded-full"
                                            src={tag.src}
                                            alt={tag.name}
                                            aspectH={2}
                                            aspectW={2}
                                            fill
                                        />
                                    </div>
                                    <div className="max-w-[150px] pt-2 text-center text-sm text-brand-black">
                                        {tag.name}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        )
    );
});

if (isDev) {
    ProductReasons.displayName = 'ProductReasons';
}

export default ProductReasons;
