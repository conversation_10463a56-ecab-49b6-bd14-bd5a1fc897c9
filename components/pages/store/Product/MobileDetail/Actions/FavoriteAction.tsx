import {FC, memo, useCallback, useState} from 'react';
import {useStore} from '@core/hooks';
import {UiSpinner} from '@core/components/ui';
import {HeartIcon as HeartSolidIcon} from '@core/icons/solid';
import useProduct from '@core/pages/store/Product/useProduct';
import {cls} from '@core/helpers';

const FavoriteAction: FC = memo(() => {
    const {addToFavorites, removeFromFavorites} = useStore();
    const {selectedProduct, customerProductParams, setCustomerProductParams} =
        useProduct();

    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);
    const onAddToFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await addToFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: true
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        addToFavorites,
        selectedProduct
    ]);
    const onRemoveFromFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await removeFromFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: false
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        removeFromFavorites,
        selectedProduct
    ]);

    return customerProductParams.isFavorite ? (
        !isFavoriteUpdateInProgress ? (
            <button
                className="flex h-9 w-9 items-center justify-center transition active:opacity-30"
                onClick={onRemoveFromFavorites}
            >
                <HeartSolidIcon
                    className={cls('h-5 w-5  text-gray-300', {
                        'text-primary-100': customerProductParams.isFavorite
                    })}
                />
            </button>
        ) : (
            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-300 transition active:opacity-30">
                <UiSpinner size="sm" />
            </div>
        )
    ) : !isFavoriteUpdateInProgress ? (
        <button
            className="flex h-9 w-9 items-center justify-center transition active:opacity-30"
            onClick={onAddToFavorites}
        >
            <HeartSolidIcon
                className={cls('h-5 w-5  text-gray-300', {
                    'text-primary-100': customerProductParams.isFavorite
                })}
            />
        </button>
    ) : (
        <div className="flex h-9 w-9 items-center justify-center">
            <HeartSolidIcon
                className={cls('h-5 w-5  text-gray-300', {
                    'text-primary-100': customerProductParams.isFavorite
                })}
            />
        </div>
    );
});

FavoriteAction.displayName = 'FavoriteAction';

export default FavoriteAction;
