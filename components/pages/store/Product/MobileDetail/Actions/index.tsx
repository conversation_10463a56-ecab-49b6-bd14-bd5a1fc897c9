import {FC, memo, useEffect, useState} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev} from '@core/helpers';
import {useCart, useMobile, useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import siteLogo from '@assets/images/common/site-logo.svg';
import MobileSearchBox from '@core/context/MobileContext/MobileSearchBox';
import {Favourite, HiOutlineShoppingBag, Menu, User} from '@components/icons';
import storeConfig from '~/store.config';

const Actions: FC = memo(() => {
    const router = useRouter();
    const {setIsMobileSearchShown} = useMobile();
    const {itemCount: cartItemsCount} = useCart();
    const {activeTab, isMobile} = useMobile();
    const t = useTrans();

    const [scrollPast, setScrollPast] = useState(false);
    useEffect(() => {
        const container = document.querySelector(
            '.mobile-product-content-wrapper'
        );
        const actionsContainer = document.querySelector(
            '.mobile-product-actions'
        );

        const onScroll = (e: Event) => {
            try {
                // @ts-ignore
                if (e.target.scrollTop > actionsContainer.clientHeight) {
                    if (!scrollPast) {
                        setScrollPast(true);
                    }
                } else {
                    if (scrollPast) {
                        setScrollPast(false);
                    }
                }
            } catch (e) {}
        };

        if (container !== null && actionsContainer !== null) {
            container.addEventListener('scroll', onScroll);
        }

        return () => {
            if (container !== null) {
                container.removeEventListener('scroll', onScroll);
            }
        };
    }, [scrollPast]);

    return (
        <div
            className={cls(
                'mobile-product-actions border-transparenttransition fixed left-0 top-0 z-20 flex w-full select-none flex-row items-center justify-between  border-b',
                {
                    'border-gray-200 bg-white shadow-sm': scrollPast
                }
            )}
        >
            <div className="flex w-full flex-col bg-white">
                <div className="flex w-full items-center justify-between  bg-white px-5 py-3">
                    <div className="flex items-center justify-center">
                        <UiLink href="/mobile/menu">
                            <Menu className="mt-4 h-8 w-8 stroke-current text-secondary-100" />
                        </UiLink>
                    </div>
                    <UiLink
                        href="/"
                        aria-label="Logo"
                        className="flex items-center justify-center pb-2"
                    >
                        <UiImage
                            src={siteLogo}
                            alt={storeConfig.title}
                            width={95}
                            height={40}
                            priority
                        />
                    </UiLink>
                    <div className="flex flex-row space-x-3">
                        <UiLink
                            className="flex flex-col items-center py-2.5"
                            href="/mobile/my-cart"
                        >
                            <div className="relative flex flex-1 flex-row items-center justify-between">
                                {activeTab === 'my-cart' ? (
                                    <>
                                        <HiOutlineShoppingBag className="h-7 w-7 text-secondary-600" />
                                    </>
                                ) : (
                                    <>
                                        <HiOutlineShoppingBag className="h-7 w-7" />
                                    </>
                                )}

                                {cartItemsCount >= 0 && (
                                    <span className="absolute left-[14px] top-[55%] flex -translate-x-1/2 -translate-y-1/2 transform items-center justify-center text-sm font-medium text-brand-black">
                                        {cartItemsCount}
                                    </span>
                                )}

                                <div className="ml-2 hidden text-[9px] font-bold uppercase text-brand-black sm:flex">
                                    {t('My Cart')}
                                </div>
                            </div>
                        </UiLink>
                        <UiLink
                            className="flex flex-row items-center justify-between py-2.5"
                            href="/account/my-favorites"
                        >
                            {activeTab === 'my-favorites' ? (
                                <>
                                    <Favourite className="h-8 w-8 text-secondary-600" />
                                </>
                            ) : (
                                <>
                                    <Favourite className="h-7 w-7" />
                                </>
                            )}

                            <div className="ml-2 hidden text-[9px] font-bold uppercase text-brand-black sm:flex">
                                {t('My Favorites')}
                            </div>
                        </UiLink>
                        <UiLink
                            className="flex flex-row items-center justify-between py-2.5"
                            href="/mobile/my-account"
                        >
                            {activeTab === 'my-account' ? (
                                <>
                                    <User className="h-7 w-7 text-secondary-600" />
                                </>
                            ) : (
                                <>
                                    <User className="h-7 w-7" />
                                </>
                            )}

                            <div className="ml-2 hidden text-[9px] font-bold uppercase text-brand-black sm:flex">
                                {t('Sign In')}
                            </div>
                        </UiLink>

                        <UiLink
                            className="flex flex-row items-center font-bold text-secondary-600 md:hidden"
                            href="/"
                        >
                            <p>EN</p>
                        </UiLink>
                    </div>
                </div>
                <div className="bg-white pb-2">
                    <MobileSearchBox />
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    Actions.displayName = 'Actions';
}

export default Actions;
