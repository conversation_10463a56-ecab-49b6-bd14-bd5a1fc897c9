import {FC, memo, useEffect, useMemo, useRef, useState} from 'react';
import {Cookies} from 'react-cookie-consent';
import storeConfig from '~/store.config';
import {cls, isDev} from '@core/helpers';
import {UiImage, UiImageProps, UiSlider, UiSpinner} from '@core/components/ui';
import {Navigation, SliderInterface, Thumbs} from '@core/components/ui/Slider';
import {XIcon} from '@core/icons/outline';
import FavoriteAction from './Actions/FavoriteAction';
import useProduct from '@core/pages/store/Product/useProduct';
import Video from '@components/common/Video';
import ShippingDetails from '../ProductDetails/ShippingDetails';
import {useTrans} from '@core/hooks';

type ImageWithLoadingProps = UiImageProps & {
    withSpinner?: boolean;
};

const ImageWithLoading: FC<ImageWithLoadingProps> = memo(
    ({withSpinner = false, ...rest}) => {
        const [isImageLoading, setIsImageLoading] = useState(true);

        return (
            <>
                {isImageLoading && (
                    <div className="absolute inset-0 z-10 h-full w-full">
                        {withSpinner ? (
                            <div className="flex h-full w-full items-center justify-center bg-black text-white">
                                <UiSpinner size="xl" />
                            </div>
                        ) : (
                            <div className="skeleton-card h-full w-full" />
                        )}
                    </div>
                )}

                <UiImage
                    {...rest}
                    onLoadingComplete={() => setIsImageLoading(false)}
                />
            </>
        );
    }
);

if (isDev) {
    ImageWithLoading.displayName = 'ImageWithLoading';
}

type ZoomedImageGalleryProps = {
    images: string[];
    productName: string;
};

const ZoomableImage: FC<{
    src: string;
    alt: string;
    priority: boolean;
    onZoomChange?: (isZoomed: boolean) => void;
}> = memo(({src, alt, priority, onZoomChange}) => {
    const [scale, setScale] = useState(1);
    const [positionX, setPositionX] = useState(0);
    const [positionY, setPositionY] = useState(0);
    const [isZooming, setIsZooming] = useState(false);
    const imageRef = useRef<HTMLDivElement>(null);
    const lastPosRef = useRef({x: 0, y: 0});
    const initialTouchDistanceRef = useRef(0);
    const lastTapRef = useRef(0);
    const tapPositionRef = useRef({x: 0, y: 0});
    const t = useTrans();

    useEffect(() => {
        setScale(1);
        setPositionX(0);
        setPositionY(0);
        setIsZooming(false);
    }, [src]);

    useEffect(() => {
        if (onZoomChange) {
            onZoomChange(scale > 1);
        }
    }, [scale, onZoomChange]);

    const handleTouchStart = (e: React.TouchEvent) => {
        if (e.touches.length === 2) {
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const distance = Math.hypot(
                touch2.clientX - touch1.clientX,
                touch2.clientY - touch1.clientY
            );
            initialTouchDistanceRef.current = distance;
            setIsZooming(true);
        } else if (e.touches.length === 1) {
            const touch = e.touches[0];
            const now = Date.now();

            lastPosRef.current = {
                x: touch.clientX,
                y: touch.clientY
            };

            const lastTap = lastTapRef.current;
            const timeDiff = now - lastTap;

            if (timeDiff < 300) {
                const dx = Math.abs(touch.clientX - tapPositionRef.current.x);
                const dy = Math.abs(touch.clientY - tapPositionRef.current.y);

                if (dx < 30 && dy < 30) {
                    if (scale === 1) {
                        setScale(2);

                        setPositionX(0);
                        setPositionY(0);
                    } else {
                        setScale(1);
                        setPositionX(0);
                        setPositionY(0);
                    }

                    lastTapRef.current = 0;
                    return;
                }
            }

            lastTapRef.current = now;
            tapPositionRef.current = {
                x: touch.clientX,
                y: touch.clientY
            };
        }
    };

    const handleTouchMove = (e: React.TouchEvent) => {
        if (scale > 1) {
            e.stopPropagation();
        }

        if (e.touches.length === 2 && isZooming) {
            e.preventDefault();

            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const currentDistance = Math.hypot(
                touch2.clientX - touch1.clientX,
                touch2.clientY - touch1.clientY
            );

            const rect = imageRef.current?.getBoundingClientRect();
            if (rect) {
                const prevScale = scale;
                const newScale = Math.max(
                    1,
                    Math.min(
                        3,
                        scale *
                            (currentDistance / initialTouchDistanceRef.current)
                    )
                );

                if (newScale !== prevScale) {
                    const scaleFactor = newScale / prevScale;
                    setPositionX(positionX * scaleFactor);
                    setPositionY(positionY * scaleFactor);
                }

                setScale(newScale);
                initialTouchDistanceRef.current = currentDistance;
            }
        } else if (e.touches.length === 1 && scale > 1) {
            e.preventDefault();

            const deltaX = e.touches[0].clientX - lastPosRef.current.x;
            const deltaY = e.touches[0].clientY - lastPosRef.current.y;

            const rect = imageRef.current?.getBoundingClientRect();
            if (rect) {
                const imageWidth = rect.width;
                const imageHeight = rect.height;

                const zoomedWidth = imageWidth * scale;
                const zoomedHeight = imageHeight * scale;

                const maxPanX = Math.max(0, (zoomedWidth - imageWidth) / 2);
                const maxPanY = Math.max(0, (zoomedHeight - imageHeight) / 2);

                const newPositionX = positionX + deltaX;
                const newPositionY = positionY + deltaY;

                setPositionX(
                    Math.max(-maxPanX, Math.min(maxPanX, newPositionX))
                );
                setPositionY(
                    Math.max(-maxPanY, Math.min(maxPanY, newPositionY))
                );
            }

            lastPosRef.current = {
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            };
        }
    };

    const handleTouchEnd = () => {
        setIsZooming(false);

        if (scale < 1) {
            setScale(1);
            setPositionX(0);
            setPositionY(0);
        }

        if (scale > 1 && scale < 1.1) {
            setScale(1);
            setPositionX(0);
            setPositionY(0);
        }
    };

    return (
        <div
            ref={imageRef}
            className="relative h-full w-full"
            style={{touchAction: scale > 1 ? 'none' : 'auto'}}
            onTouchStart={e => {
                if (scale > 1) {
                    e.stopPropagation();
                }
                handleTouchStart(e);
            }}
            onTouchMove={handleTouchMove}
            onTouchEnd={e => {
                if (scale > 1) {
                    e.stopPropagation();
                }
                handleTouchEnd();
            }}
        >
            <div
                style={{
                    transform: `scale(${scale}) translate(${
                        positionX / scale
                    }px, ${positionY / scale}px)`,
                    transition: isZooming ? 'none' : 'transform 0.2s',
                    width: '100%',
                    height: '100%',
                    position: 'relative'
                }}
            >
                <ImageWithLoading
                    withSpinner
                    src={src}
                    alt={alt}
                    priority={priority}
                    className="object-contain"
                    fill
                    fit="contain"
                    position="center"
                    unoptimized={true}
                />
            </div>
            {scale === 1 && (
                <div className="absolute bottom-16 left-1/2 z-10 -translate-x-1/2 rounded-full bg-blackAlpha-600 px-1 py-1 text-xs text-white opacity-70">
                    {t('Double tap to zoom in')}
                </div>
            )}
        </div>
    );
});

if (isDev) {
    ZoomableImage.displayName = 'ZoomableImage';
}

const CloseButton: FC<{onClose: () => void}> = ({onClose}) => {
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        const interval = setInterval(() => {
            setIsVisible(prev => !prev);
        }, 500);

        return () => clearInterval(interval);
    }, []);

    const handleClose = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        if (typeof onClose === 'function') {
            onClose();
        } else {
            console.error('onClose fonksiyonu tanımlı değil');
        }
    };

    return (
        <div
            style={{
                position: 'absolute',
                right: '20px',
                top: '40px',
                zIndex: 999999,
                pointerEvents: 'auto',
                transform: 'scale(1.2)'
            }}
            onClick={handleClose}
        >
            <button
                className="absolute right-2 top-2 z-10 flex h-10 w-10 items-center justify-center rounded-full bg-blackAlpha-600 text-white active:opacity-30"
                onClick={onClose}
            >
                <XIcon className="h-6 w-6" />
            </button>
        </div>
    );
};

const ZoomedImageGallery: FC<ZoomedImageGalleryProps> = memo(
    ({images, productName}) => {
        const [currentSlideIndex, setCurrentSlideIndex] = useState(1);
        const [isAnyImageZoomed, setIsAnyImageZoomed] = useState(false);
        const sliderRef = useRef<SliderInterface | null>(null);
        const t = useTrans();

        useEffect(() => {
            if (sliderRef.current && isAnyImageZoomed) {
                sliderRef.current.allowTouchMove = false;
                sliderRef.current.allowSlideNext = false;
                sliderRef.current.allowSlidePrev = false;
            } else if (sliderRef.current) {
                sliderRef.current.allowTouchMove = true;
                sliderRef.current.allowSlideNext = true;
                sliderRef.current.allowSlidePrev = true;
            }
        }, [isAnyImageZoomed]);

        return (
            <div className="fixed inset-0 z-[51] flex items-center justify-center bg-black text-white">
                <UiSlider
                    className="relative h-full w-full"
                    loop
                    spaceBetween={4}
                    slidesPerView={1}
                    slidesPerGroup={1}
                    onSwiper={swiper => {
                        sliderRef.current = swiper;
                    }}
                    onSlideChange={swiper => {
                        setCurrentSlideIndex(swiper.realIndex + 1);
                        setIsAnyImageZoomed(false);
                    }}
                >
                    {images.map((image, index) => (
                        <UiSlider.Slide key={image + index}>
                            <div className="relative h-screen w-full">
                                <ZoomableImage
                                    src={image}
                                    alt={productName}
                                    priority={index === 0}
                                    onZoomChange={setIsAnyImageZoomed}
                                />
                            </div>
                        </UiSlider.Slide>
                    ))}
                </UiSlider>

                <div className="absolute bottom-8 left-1/2 z-10 -translate-x-1/2 rounded-full bg-blackAlpha-600 px-3 py-1 text-sm text-white">
                    {currentSlideIndex} / {images.length}
                </div>

                {isAnyImageZoomed && (
                    <div className="absolute bottom-20 left-1/2 z-10 -translate-x-1/2 rounded-full bg-blackAlpha-600 px-3 py-1 text-xs text-white opacity-70">
                        {t('Scrolling disabled')}
                    </div>
                )}
            </div>
        );
    }
);

if (isDev) {
    ZoomedImageGallery.displayName = 'ZoomedImageGallery';
}

const ImageGallery: FC = memo(() => {
    const [isMounted, setIsMounted] = useState(false);
    const [isSliding, setIsSliding] = useState(false);
    useEffect(() => {
        setIsMounted(true);
    }, []);
    const [thumbsSwiper, setThumbsSwiper] = useState<SliderInterface>();

    const {selectedProduct, product} = useProduct();
    const [isZoomShown, setIsZoomShown] = useState(false);

    // Get product images.
    const images = useMemo(() => {
        let images = selectedProduct.images;

        if (product.isAdultProduct) {
            if (isMounted && Cookies.get('isAdultEligible') !== 'true') {
                images = ['/adult-image.png'];
            } else if (!isMounted) {
                images = ['/placeholder.png'];
            }
        }
        if (!Array.isArray(images) || images.length < 1) {
            images = ['/no-image.png'];
        }

        return images;
    }, [selectedProduct.images, product.isAdultProduct, isMounted]);

    const videoRegex = /<iframe.*?src="(.*?)".*?<\/iframe>/;
    const videoMatch = (selectedProduct.description || '').match(videoRegex);
    const videoUrl = videoMatch ? videoMatch[1] : null;

    const [isShown, setIsShown] = useState(false);

    const PlayIcon = ({className}: {className?: string}) => (
        <div className="absolute inset-0 flex cursor-pointer items-center justify-center  bg-opacity-50">
            <svg
                className={cls(
                    'h-24 w-24 rounded-full border-2 border-white bg-black/30 p-4 !text-white',
                    className
                )}
                fill="currentColor"
                viewBox="0 0 24 24"
            >
                <path d="M8 5v14l11-7z" />
            </svg>
        </div>
    );

    return (
        <>
            <div className="relative">
                <div className="absolute left-0 top-0 z-[5] h-mobile-header w-full bg-gradient-to-b from-gray-900 to-transparent pt-[140px] opacity-40" />

                <UiSlider
                    className="thumb-swiper"
                    modules={[Navigation, Thumbs]}
                    thumbs={{swiper: thumbsSwiper}}
                    loop
                    spaceBetween={4}
                    slidesPerView={1}
                    slidesPerGroup={1}
                    watchOverflow
                    onSlideChangeTransitionStart={() => setIsSliding(true)}
                    onSlideChangeTransitionEnd={() => setIsSliding(false)}
                >
                    {images.map((image, index) => (
                        <UiSlider.Slide key={image + index}>
                            <div
                                onClick={() =>
                                    index === 1 && videoUrl && setIsShown(true)
                                }
                                className={cls('', {
                                    'aspect-h-2 aspect-w-2':
                                        storeConfig.catalog
                                            .productImageShape === 'rectangle',
                                    'aspect-h-1 aspect-w-1':
                                        storeConfig.catalog
                                            .productImageShape !== 'rectangle'
                                })}
                            >
                                <div>
                                    <ImageWithLoading
                                        src={`${image}?w=720&q=75`}
                                        alt={selectedProduct.name}
                                        priority={index === 0}
                                        fill
                                        fit="cover"
                                        position="center"
                                        onClick={() =>
                                            !isSliding && setIsZoomShown(true)
                                        }
                                    />
                                </div>
                                {index === 1 && videoUrl && <PlayIcon />}
                            </div>
                        </UiSlider.Slide>
                    ))}
                </UiSlider>

                <div className="absolute right-0 top-3 z-10 flex items-center space-x-2  pr-3 text-xs shadow-sm">
                    <FavoriteAction />
                </div>
                <ShippingDetails setIsShown={setIsShown} />
            </div>

            <Video isShown={isShown} setIsShown={setIsShown}>
                {videoUrl && (
                    <div className="w-full">
                        <iframe
                            className="h-[350px] w-full max-w-full lg:h-[540px]"
                            allowFullScreen
                            src={videoUrl}
                        ></iframe>
                    </div>
                )}
            </Video>

            <div className="h-4  ">
                <UiSlider
                    className="thumb-swiper mt-6 h-16 select-none  px-2"
                    slidesPerView={8}
                    spaceBetween={5}
                    watchSlidesProgress
                    threshold={2}
                    modules={[Navigation, Thumbs]}
                    onSwiper={setThumbsSwiper}
                >
                    {images.map((image, index) => (
                        <UiSlider.Slide
                            key={image}
                            className="!h-9 !w-9 cursor-pointer"
                        >
                            <UiImage
                                className="!h-9 !w-9"
                                onClick={() =>
                                    index === 1 && videoUrl && setIsShown(true)
                                }
                                src={`${image}?w=80&q=50`}
                                alt={'productName'}
                                fit="cover"
                                position="center"
                                fill
                            />
                            {index === 1 && videoUrl && (
                                <PlayIcon className="text h-4 w-4 !p-0" />
                            )}
                        </UiSlider.Slide>
                    ))}
                </UiSlider>
            </div>

            {isZoomShown && (
                <>
                    <ZoomedImageGallery
                        images={images.map(image => {
                            if (image.includes('?')) {
                                return image.split('?')[0] + '?w=1200&q=100';
                            }
                            return `${image}?w=1200&q=100`;
                        })}
                        productName={selectedProduct.name}
                    />
                    <CloseButton
                        onClose={() => {
                            setIsZoomShown(false);
                        }}
                    />
                </>
            )}
        </>
    );
});

if (isDev) {
    ImageGallery.displayName = 'ImageGallery';
}

export default ImageGallery;
