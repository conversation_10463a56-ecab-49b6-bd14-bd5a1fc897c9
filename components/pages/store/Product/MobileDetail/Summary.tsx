import {FC, memo, useCallback, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiButton, UiImage, UiSpinner} from '@core/components/ui';
import Price from '@components/common/Price';
import useProduct from '@core/pages/store/Product/useProduct';
import {HeartIcon} from '@components/icons';

const Summary: FC = memo(() => {
    const t = useTrans();
    const {addToFavorites, removeFromFavorites} = useStore();
    const {
        selectedProduct,
        isAddToCartInProgress,
        addToCart,
        inStock,
        customerProductParams,
        setCustomerProductParams
    } = useProduct();

    // Favorite.
    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);
    const onAddToFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await addToFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: true
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        addToFavorites,
        selectedProduct
    ]);
    const onRemoveFromFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await removeFromFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: false
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        removeFromFavorites,
        selectedProduct
    ]);

    return (
        <section className="container fixed bottom-0 left-0 z-50 flex h-mobile-tab-bar w-full items-center justify-between gap-4 border-t border-secondary-100 bg-secondary-100">
            <div className="relative h-11 w-11">
                <UiImage
                    src={
                        (selectedProduct.images as string[])[0] ??
                        '/no-image.png'
                    }
                    alt={selectedProduct.name}
                    fill
                />
            </div>

            <div className="flex items-center justify-end gap-2">
                {selectedProduct.availableQuantity > 0 &&
                    selectedProduct.salesPrice > 0 && (
                        <Price
                            price={
                                selectedProduct.hasDiscount
                                    ? selectedProduct.unDiscountedSalesPrice
                                    : selectedProduct.salesPrice
                            }
                            discountedPrice={
                                selectedProduct.hasDiscount
                                    ? selectedProduct.salesPrice
                                    : null
                            }
                            dontWrapDiscountedPrice
                            className={cls(
                                'font-hurme  font-semibold text-base text-white [&>span]:text-white'
                            )}
                            decimal={0}
                        />
                    )}
                <div className="flex items-center gap-2">
                    {inStock ? (
                        <UiButton
                            className="h-9 rounded-sm border-none bg-white font-hurme text-xs font-semibold text-base text-secondary-100 !ring-0 transition hover:bg-white hover:text-brand-clr focus:!ring-0"
                            loading={isAddToCartInProgress}
                            disabled={!inStock}
                            onClick={addToCart}
                        >
                            {t('Add To Cart')}
                        </UiButton>
                    ) : (
                        <UiButton
                            className="h-9 rounded-sm border-none bg-white text-xs font-bold text-base text-secondary-100 !ring-0 transition hover:bg-white hover:text-brand-clr focus:!ring-0 disabled:bg-white disabled:opacity-100"
                            loading={isAddToCartInProgress}
                            disabled={!inStock}
                            onClick={addToCart}
                        >
                            {t('In Stock Soon')}
                        </UiButton>
                    )}

                    {customerProductParams.isFavorite ? (
                        !isFavoriteUpdateInProgress ? (
                            <UiButton
                                className="flex h-9 !w-12 items-center rounded-sm border-white border-opacity-50 bg-white transition hover:bg-white"
                                onClick={onRemoveFromFavorites}
                            >
                                <HeartIcon className="h-3.5 w-3.5 fill-secondary-100 stroke-secondary-100 text-secondary-100 hover:text-brand-clr" />
                            </UiButton>
                        ) : (
                            <UiButton className="flex h-9 !w-12 items-center rounded-sm border-white border-opacity-50 bg-white transition hover:bg-white">
                                <div className="mr-1 w-4">
                                    <UiSpinner size="sm" />
                                </div>
                            </UiButton>
                        )
                    ) : !isFavoriteUpdateInProgress ? (
                        <UiButton
                            className="flex h-9 !w-12 items-center rounded-sm border-white border-opacity-50 bg-white transition hover:border-white hover:bg-white"
                            onClick={onAddToFavorites}
                        >
                            <HeartIcon className=" h-3.5 w-3.5 stroke-current  text-secondary-100 hover:text-brand-clr" />
                        </UiButton>
                    ) : (
                        <UiButton className="flex h-9 !w-12 items-center rounded-sm border-white border-opacity-50 bg-white transition hover:bg-white">
                            <div className=" w-4">
                                <UiSpinner size="sm" />
                            </div>
                        </UiButton>
                    )}
                </div>
            </div>
        </section>
    );
});

if (isDev) {
    Summary.displayName = 'Summary';
}

export default Summary;
