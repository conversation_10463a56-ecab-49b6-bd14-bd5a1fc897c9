import {FC, memo, useCallback} from 'react';
import {isDev} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {Breadcrumb} from '@core/types';
import useProduct from '@core/pages/store/Product/useProduct';
import FooterPartial from '@components/layouts/Default/partials/Footer';
import CompanyStory from '@components/pages/common/Home/CompanyStory';
import CatalogSearchBarPartial from '../../Catalog/CatalogSearch';
import ProductCampaigns from '../ProductDetails/ProductCampaigns';
import ShipmentOptions from '../ProductDetails/ShipmentOptions';
import ImageGallery from './ImageGallery';
import Info from './Info';
import Options from './Options';
import ProductReviews from './ProductReviews';
import Summary from './Summary';
import DetailsComment from '../DetailsComment';
import ProductDetailBanner from '../ProductDetailBanner';
import QuickLook from '../QuickLook';
import Delivery from '../Delivery';
import FAQ from '../FAQ';
import ProductReasons from '../ProductReasons';
import Breadcrumbs from '../Breadcrumbs';
import Collections from '@components/common/Collections';
import Actions from '../Actions';

const MobileDetail: FC<{breadcrumbs: Breadcrumb[]}> = memo(({breadcrumbs}) => {
    const t = useTrans();
    const {openSideBar} = useUI();
    const {product, selectedProduct, inStock} = useProduct();
    const onOpenProductReviews = useCallback(() => {
        openSideBar(
            t('Product Reviews'),
            <ProductReviews selectedProduct={selectedProduct} />
        );
    }, [selectedProduct, t, openSideBar]);

    return (
        <div className="mobile-product-content-wrapper relative h-full w-full overflow-x-auto overflow-y-auto pb-12 ">
            <div className="container">
                {Array.isArray(breadcrumbs) && breadcrumbs.length > 0 && (
                    <div className="flex items-center">
                        <Breadcrumbs breadcrumbs={breadcrumbs} />
                    </div>
                )}

                <div className="py-4">
                    <ImageGallery />
                </div>

                <Info onOpenReviews={onOpenProductReviews} />

                {product.isConfigurable &&
                    (product.variants ?? []).length > 0 && <Options />}
                <div className="mb-2">
                    <Actions />
                </div>
                {inStock && (
                    <>
                        <ProductCampaigns />
                        <ShipmentOptions />
                    </>
                )}

                <QuickLook product={product} />
                <ProductDetailBanner />
                <DetailsComment />
                <ProductReasons />
                <Delivery />
                <FAQ />
            </div>

            <CompanyStory />
            <div className=" mt-12">
                <Collections />
            </div>
            <div className="pt-4">
                <CatalogSearchBarPartial />
            </div>
            <Summary />
            <FooterPartial />
        </div>
    );
});

if (isDev) {
    MobileDetail.displayName = 'MobileDetail';
}

export default MobileDetail;
