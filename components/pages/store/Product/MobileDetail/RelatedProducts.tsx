import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import ProductSlider from '@components/common/ProductSlider';
import useProduct from '@core/pages/store/Product/useProduct';

const RelatedProducts: FC = memo(() => {
    const t = useTrans();
    const {relatedProducts} = useProduct();

    return (
        <div className="my-6">
            <div className="container">
                <h2 className="mb-2 text-lg font-medium">
                    {t('Related Products')}
                </h2>

                <ProductSlider products={relatedProducts} />
            </div>
        </div>
    );
});

if (isDev) {
    RelatedProducts.displayName = 'RelatedProducts';
}

export default RelatedProducts;
