import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {CheckCircleIcon, XCircleIcon} from '@core/icons/outline';
import Price from '@components/common/Price';
import useProduct from '@core/pages/store/Product/useProduct';
import PaymentOptions from '../PaymentOptions';
import Share from '../Share';
import {UiRating} from '@core/components/ui';

type InfoProps = {
    onOpenReviews: () => void;
};

const Info: FC<InfoProps> = memo(({onOpenReviews}) => {
    const t = useTrans();
    const {availableQuantity, selectedProduct, inStock} = useProduct();
    const title = useMemo(() => {
        let title = '';

        if (!!selectedProduct.colorAttributeValue) {
            if (!!selectedProduct.brandName) {
                title = ` ${selectedProduct.colorAttributeValue} ${selectedProduct.name} `;
            } else {
                title = `${selectedProduct.colorAttributeValue} ${selectedProduct.name} `;
            }
        } else {
            if (!!selectedProduct.brandName) {
                title = ` ${selectedProduct.name} `;
            } else {
                title = `${selectedProduct.name} `;
            }
        }

        return title;
    }, [selectedProduct]);

    return (
        <div className="mb-6 mt-6 border-b border-gray-200 pb-2">
            <div className="flex items-center justify-between">
                <h1 className="font-hurme text-lg font-semibold text-brand-black">
                    {title}
                </h1>
                <Share />
            </div>
            {/* <div className="mt-3 flex items-center space-x-3">
                <div className=" flex cursor-pointer items-center text-sm font-semibold text-gray-500">
                    {selectedProduct.rating === 5
                        ? '5.0'
                        : selectedProduct.rating}
                </div>
                <div className="flex items-center">
                    <UiRating
                        size="sm"
                        initialRating={selectedProduct.rating}
                        readonly
                    />
                </div>

                <button
                    className="flex items-center text-xs text-gray-500 underline transition duration-100"
                    onClick={onOpenReviews}
                >
                    {t('{reviewCount} reviews', {
                        reviewCount: selectedProduct.reviewCount
                    })}
                </button>
            </div> */}
            <div className="mt-4 grid grid-cols-2">
                {
                    <>
                        <div>
                            <div className="flex items-center gap-2.5 text-xl font-medium">
                                {selectedProduct.hasDiscount &&
                                    selectedProduct.discount > 0 && (
                                        <p className="rounded border-2 border-discount p-1 text-sm leading-4 text-discount">
                                            %
                                            {Math.round(
                                                selectedProduct.discount
                                            )}
                                        </p>
                                    )}
                                {selectedProduct &&
                                    selectedProduct.salesPrice > 0 && (
                                        <Price
                                            className="font-hurme text-lg font-semibold [&>span]:text-xl  [&>span]:text-secondary-100"
                                            price={
                                                selectedProduct.hasDiscount
                                                    ? selectedProduct.unDiscountedSalesPrice
                                                    : selectedProduct.salesPrice
                                            }
                                            discountedPrice={
                                                selectedProduct.hasDiscount
                                                    ? selectedProduct.salesPrice
                                                    : null
                                            }
                                            dontWrapDiscountedPrice
                                            decimal={0}
                                        />
                                    )}
                            </div>
                            <div className="mt-2 text-sm">
                                {availableQuantity > 5 ? (
                                    <div className="flex items-center space-x-1 text-gray-600">
                                        <CheckCircleIcon className="mr-1 h-4 w-4 text-green-600" />
                                        <div>{t('In Stock')}</div>
                                    </div>
                                ) : availableQuantity > 0 ? (
                                    <div className="flex items-center space-x-1 text-gray-600">
                                        <CheckCircleIcon className="mr-1 h-4 w-4 text-green-600" />
                                        <div>
                                            {availableQuantity > 1
                                                ? t('Last {count} products', {
                                                      count: availableQuantity
                                                  })
                                                : t('Last {count} product', {
                                                      count: availableQuantity
                                                  })}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex items-center space-x-1 text-gray-600">
                                        <XCircleIcon className="mr-1 h-4 w-4 text-red-600" />
                                        <div>{t('Out Of Stock')}</div>
                                    </div>
                                )}
                            </div>
                        </div>
                        <PaymentOptions />
                    </>
                }
            </div>
        </div>
    );
});

if (isDev) {
    Info.displayName = 'Info';
}

export default Info;
