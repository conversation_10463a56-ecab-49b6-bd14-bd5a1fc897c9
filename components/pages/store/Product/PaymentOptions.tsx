import {FC, memo, useCallback, useRef, useState} from 'react';
import {isDev} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {ChevronRightIcon} from '@core/icons/outline';
import useProduct from '@core/pages/store/Product/useProduct';
import {UiImage, UiSlider} from '@core/components/ui';
import Price from '@components/common/Price';
import InstallmentOptions from '@components/common/InstallmentOptions';
import {Autoplay, SliderInterface} from '@core/components/ui/Slider';

const PaymentOptions: FC = memo(() => {
    const t = useTrans();
    const {openSideBar} = useUI();
    const {installments} = useProduct();
    const [currentInstallmentIndex, setCurrentInstallmentIndex] = useState(0);
    const swiperRef = useRef<SliderInterface>();

    const onOpenMiniCart = useCallback(() => {
        openSideBar(
            t('Payment Options'),
            <div className="container relative flex h-auto w-full flex-col text-start text-sm text-brand-black">
                <InstallmentOptions installments={installments} />
            </div>,
            'large'
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [installments]);

    const filteredInstallments = installments.filter(
        item =>
            item.cardBrandCode === 'axess' ||
            item.cardBrandCode === 'bonus' ||
            item.cardBrandCode === 'maximum'
    );

    const updatedInstallments = filteredInstallments.map(installment => {
        const maxInstallment = installment.installments.reduce((max, inst) => {
            if (
                inst.installmentCount > max.installmentCount &&
                inst.installmentAmount > 0
            ) {
                return inst;
            }
            return max;
        }, installment.installments[0]);

        return {
            cardBrandLogo: installment.cardBrandLogo,
            maxInstallmentCount: maxInstallment.installmentCount,
            maxInstallmentAmount: maxInstallment.installmentAmount
        };
    });

    const currentInstallment =
        updatedInstallments[currentInstallmentIndex] || {};

    return (
        <div className="flex flex-col-reverse items-end justify-center gap-x-4 xl:flex-row xl:items-center">
            <div className="h-16 w-32 py-2">
                <UiSlider
                    className=""
                    modules={[Autoplay]}
                    spaceBetween={1}
                    slidesPerView={1}
                    autoplay
                    loop
                    onSwiper={swiper => {
                        swiperRef.current = swiper;
                    }}
                >
                    {updatedInstallments.map((brand, index) => (
                        <UiSlider.Slide
                            className="group"
                            key={brand.cardBrandLogo + index}
                        >
                            <div className="flex w-32 justify-end">
                                <UiImage
                                    className="h-10 w-24"
                                    src={
                                        brand.cardBrandLogo
                                            ? `${brand.cardBrandLogo}`
                                            : '/no-image.png'
                                    }
                                    alt="banka"
                                    width={120}
                                    height={90}
                                    fit="cover"
                                    position="center"
                                />
                            </div>
                        </UiSlider.Slide>
                    ))}
                </UiSlider>
            </div>
            <div>
                <div className="flex items-center justify-between gap-1 border-b border-brand-clr">
                    <p
                        className="cursor-pointer text-2xs font-bold uppercase text-brand-clr"
                        onClick={onOpenMiniCart}
                    >
                        {t('Installment Options')}
                    </p>
                    <ChevronRightIcon className="h-2 w-2 stroke-current stroke-[60px] text-brand-clr" />
                </div>
                <div className="mt-1">
                    <p className="text-xs font-bold text-secondary-100">
                        <Price
                            price={currentInstallment.maxInstallmentAmount ?? 0}
                        />{' '}
                        x {currentInstallment.maxInstallmentCount}{' '}
                        {t('Installment')}
                    </p>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    PaymentOptions.displayName = 'PaymentOptions';
}

export default PaymentOptions;
