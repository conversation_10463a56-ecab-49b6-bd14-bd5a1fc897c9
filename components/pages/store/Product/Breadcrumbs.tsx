import {FC, useMemo} from 'react';
import {Breadcrumb} from '@core/types';
import useProduct from '@core/pages/store/Product/useProduct';
import CoreBreadcrumbs from '@components/common/Breadcrumbs';

type BreadcrumbsProps = {
    breadcrumbs: Breadcrumb[];
};

const Breadcrumbs: FC<BreadcrumbsProps> = ({
    breadcrumbs: initialBreadcrumbs
}) => {
    const {selectedProduct} = useProduct();
    const breadcrumbs = useMemo(() => {
        if (initialBreadcrumbs.length > 1) {
            const lastItem = initialBreadcrumbs[initialBreadcrumbs.length - 1];

            if (!!selectedProduct.colorAttributeValue) {
                if (!!selectedProduct.brandName) {
                    lastItem.name = ` ${selectedProduct.colorAttributeValue} ${selectedProduct.name} `;
                } else {
                    lastItem.name = `${selectedProduct.colorAttributeValue} ${selectedProduct.name} `;
                }
            } else {
                if (!!selectedProduct.brandName) {
                    lastItem.name = ` ${selectedProduct.name} `;
                } else {
                    lastItem.name = `${selectedProduct.name} `;
                }
            }
        }

        return initialBreadcrumbs;
    }, [initialBreadcrumbs, selectedProduct]);

    return <CoreBreadcrumbs breadcrumbs={breadcrumbs} />;
};

export default Breadcrumbs;
