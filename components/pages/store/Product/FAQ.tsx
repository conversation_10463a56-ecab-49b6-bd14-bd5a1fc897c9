import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {ChevronDownIcon, ChevronRightIcon} from '@core/icons/outline';
import {Disclosure, Transition} from '@headlessui/react';

const FAQ: FC = memo(() => {
    const t = useTrans();

    const questions = [
        {
            question: t('When will my order reach me?'),
            answer: t('Your products are delivered within 2-5 business days.')
        },
        {
            question: t('How can I return the product?'),
            answer: t(
                'You can easily return the product you purchased with Nehir assurance within 14 days from the delivery date with your invoice and a note containing the reason for the return. Click for detailed information.'
            )
        },
        {
            question: t(
                'How long does it take to process returns and exchanges?'
            ),
            answer: t(
                'After the products reach us, returns or exchanges are made within 7 business days.'
            )
        }
    ];

    return (
        <div className="my-10 grid xl:container  lg:px-24 xl:my-20">
            <div className="w-full justify-self-center border-b  border-gray-300 xl:w-4/12 ">
                <h2 className="pb-3 font-dm-serif text-3xl  text-brand-black">
                    {t('Frequently asked Questions')}
                </h2>
            </div>

            <div className="w-full  justify-self-center xl:w-4/12">
                {questions.map((item, index) => (
                    <Disclosure key={index}>
                        {({open}) => (
                            <>
                                <div className="grid py-4">
                                    <Disclosure.Button className="flex w-full items-center justify-between pr-4 text-sm text-brand-black">
                                        <div className="flex items-start justify-start text-left">
                                            {item.question}
                                        </div>
                                        <div className="flex items-center">
                                            {open ? (
                                                <ChevronDownIcon className="ml-2 h-2.5 w-2.5 stroke-current stroke-[60px] text-brand-black" />
                                            ) : (
                                                <ChevronRightIcon className="ml-2 h-2.5 w-2.5 stroke-current stroke-[60px] text-brand-black" />
                                            )}
                                        </div>
                                    </Disclosure.Button>
                                </div>

                                <Transition
                                    show={open}
                                    enter="transition duration-500 ease-in"
                                    enterFrom="transform scale-95 opacity-0"
                                    enterTo="transform scale-100 opacity-100"
                                    leave="transition duration-300 ease-out"
                                    leaveFrom="transform scale-100 opacity-100"
                                    leaveTo="transform scale-95 opacity-0"
                                >
                                    <Disclosure.Panel>
                                        <p className=" bg-pink-50/30 p-2 pl-8 leading-5 text-brand-black">
                                            {item.answer}
                                        </p>
                                    </Disclosure.Panel>
                                </Transition>
                            </>
                        )}
                    </Disclosure>
                ))}
            </div>
        </div>
    );
});

if (isDev) {
    FAQ.displayName = 'FAQ';
}

export default FAQ;
