import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {MinusIcon, PlusIcon} from '@core/icons/outline';
import {Disclosure, Transition} from '@headlessui/react';
import useProduct from '@core/pages/store/Product/useProduct';
import {useScrollIntoView, useStore} from '@core/hooks';
import {UiButton, UiRating} from '@core/components/ui';
import {Info} from '@components/icons';

const DetailsComment: FC = memo(() => {
    const t = useTrans();
    const {selectedProduct, product} = useProduct();
    const questions = [
        {
            question: t('Product Dimensions')
        }
    ];

    const {scrollIntoView: scrollToProductReviews} =
        useScrollIntoView<HTMLDivElement>({
            target: 'productReviews',
            offset: 108
        });
    const {locale} = useStore();

    const featuredReview = useMemo(() => {
        const review = product.featuredReview;

        if (typeof review !== 'undefined') {
            if (review.content.length > 70) {
                review.content = `${review.content.slice(0, 70)}...`;
            }

            const formatter = new Intl.DateTimeFormat(locale, {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
            const date = new Date(Date.parse(review.createdAt));
            review.dateFornatted = formatter.format(date);
        }

        return review;
    }, [locale, product.featuredReview]);

    return (
        <div className="flex flex-col items-start justify-center gap-y-0.5 xl:container xl:px-0">
            {questions.map((item, index) => (
                <Disclosure key={index} defaultOpen>
                    {({open}) => (
                        <>
                            <div className="flex h-12 w-full items-center justify-between rounded-sm bg-brand-search py-4 lg:h-16	 ">
                                <Disclosure.Button className="flex w-full items-center justify-between pr-4 text-sm text-brand-black">
                                    {item.question ===
                                        t('Product Dimensions') && (
                                        <div className="flex items-center pl-2 text-sm font-bold">
                                            {item.question}
                                        </div>
                                    )}

                                    {item.question === t('Comments') && (
                                        <>
                                            <div className="flex items-center gap-x-1 pl-2 text-sm ">
                                                <div className="font-bold">
                                                    {item.question}{' '}
                                                    <span>
                                                        (
                                                        {
                                                            selectedProduct.reviewCount
                                                        }
                                                        )
                                                    </span>
                                                </div>

                                                {selectedProduct.reviewCount ===
                                                    0 && (
                                                    <>
                                                        <div className="border-3 mx-1 border-l-2 border-white sm:h-8 xl:h-12"></div>
                                                        <div className="ml-1 text-xs text-brand-clr">
                                                            {t(
                                                                'Be the first to comment'
                                                            )}
                                                        </div>
                                                    </>
                                                )}
                                            </div>
                                        </>
                                    )}

                                    <div className="flex items-center">
                                        {open ? (
                                            <MinusIcon className="ml-2 h-3 w-3 stroke-current stroke-[70px] text-brand-black" />
                                        ) : (
                                            <PlusIcon className="ml-2 h-3 w-3 stroke-current stroke-[70px] text-brand-black" />
                                        )}
                                    </div>
                                </Disclosure.Button>
                            </div>

                            <Transition
                                show={open}
                                enter="transition duration-300 ease-in"
                                enterFrom="transform scale-95 opacity-0"
                                enterTo="transform scale-100 opacity-100"
                                leave="transition duration-300 ease-out"
                                leaveFrom="transform scale-100 opacity-100"
                                leaveTo="transform scale-95 opacity-0"
                            >
                                <Disclosure.Panel>
                                    <div>
                                        {item.question ===
                                            t('Product Dimensions') &&
                                            Array.isArray(
                                                selectedProduct.additionalInformations
                                            ) &&
                                            (selectedProduct
                                                .additionalInformations.length >
                                            0 ? (
                                                <div className="grid grid-cols-1 p-4">
                                                    {selectedProduct?.additionalInformations?.map(
                                                        (feature, index) => (
                                                            <div
                                                                className="flex flex-col gap-1.5 text-xs"
                                                                key={
                                                                    feature.code +
                                                                    index
                                                                }
                                                            >
                                                                {feature.value
                                                                    .split(';')
                                                                    .map(
                                                                        (
                                                                            item,
                                                                            subIndex
                                                                        ) => (
                                                                            <div
                                                                                className="text-brand-clr"
                                                                                key={
                                                                                    subIndex
                                                                                }
                                                                            >
                                                                                {item.trim()}
                                                                            </div>
                                                                        )
                                                                    )}
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            ) : (
                                                <div className="p-4 text-xs">
                                                    {t(
                                                        'Product Details Not Found!'
                                                    )}
                                                </div>
                                            ))}
                                        {item.question === t('Comments') && (
                                            <div className="px-4">
                                                <div className="">
                                                    {typeof featuredReview !==
                                                    'undefined' ? (
                                                        <div className="border border-gray-200 shadow-sm">
                                                            <div className="flex flex-col p-2">
                                                                <div className="text-sm leading-5">
                                                                    <span className="inline-block">
                                                                        <UiRating
                                                                            className="mr-2"
                                                                            initialRating={
                                                                                featuredReview.rating
                                                                            }
                                                                            size="xs"
                                                                            readonly
                                                                        />
                                                                    </span>
                                                                    {
                                                                        featuredReview.content
                                                                    }
                                                                </div>

                                                                <div className="mt-3 text-xs text-muted">
                                                                    {`${featuredReview.author}  |  ${featuredReview.dateFornatted}`}
                                                                </div>

                                                                <UiButton
                                                                    className="mt-4 w-full rounded-none bg-primary-100 text-white ring-primary-100 hover:bg-primary-100 focus:border-primary-100 focus:!ring-primary-100"
                                                                    size="sm"
                                                                    onClick={() =>
                                                                        scrollToProductReviews()
                                                                    }
                                                                >
                                                                    {t(
                                                                        'More Reviews'
                                                                    )}
                                                                </UiButton>
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <div className="flex items-center justify-center pt-4 text-xs text-brand-clr">
                                                            <Info />
                                                            <div className="flex-1">
                                                                {t(
                                                                    'You must have purchased this product to be able to review it.'
                                                                )}
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </Disclosure.Panel>
                            </Transition>
                        </>
                    )}
                </Disclosure>
            ))}
        </div>
    );
});

if (isDev) {
    DetailsComment.displayName = 'DetailsComment';
}

export default DetailsComment;
