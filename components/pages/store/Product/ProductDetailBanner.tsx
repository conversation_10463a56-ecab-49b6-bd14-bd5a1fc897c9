import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {BudgetAdvantage, Loyalty, ProductReturn} from '@components/icons';

const ProductDetailBanner: FC = memo(() => {
    const t = useTrans();

    return (
        <div className="mb-2 mt-4 flex items-center justify-between px-2 xl:px-10">
            <div className="flex max-w-[100px] flex-col items-center justify-center gap-2">
                <ProductReturn className="h-8 w-8" />
                <p className="text-center text-2xs font-medium text-brand-black md:text-xs">
                    {t('Free Return and Exchange Guarantee')}
                </p>
            </div>
            <div className="mx-4 h-16 border-l border-brand-clr/10"></div>
            <div className="flex max-w-[100px] flex-col items-center justify-center gap-2">
                <BudgetAdvantage className="h-8 w-8" />
                <p className="text-center text-2xs font-medium text-brand-black md:text-xs">
                    {t('Best Price Advantage')}
                </p>
            </div>
            <div className="mx-4 h-16 border-l border-brand-clr/10"></div>
            <div className="flex max-w-[100px] flex-col items-center justify-center gap-2">
                <Loyalty className="h-8 w-8" />
                <p className="text-center text-2xs font-medium text-brand-black lg:text-xs">
                    {t('100% Safe Shopping')}
                </p>
            </div>
        </div>
    );
});

if (isDev) {
    ProductDetailBanner.displayName = 'ProductDetailBanner';
}

export default ProductDetailBanner;
