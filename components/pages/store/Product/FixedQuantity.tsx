import {
    Change<PERSON><PERSON><PERSON><PERSON><PERSON>,
    FC,
    FocusEventHandler,
    memo,
    useCallback,
    useEffect,
    useState
} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiInput} from '@core/components/ui';
import {MinusIcon, PlusIcon} from '@core/icons/outline';

export type QuantityProps = {
    quantity: number;
    availableQuantity: number;
    onChange: (quantity: number) => void;
};

const FixedQuantity: FC<QuantityProps> = memo(props => {
    const {quantity = 1, availableQuantity = 1, onChange} = props;
    const [qty, setQty] = useState(quantity.toString());

    useEffect(() => {
        if (qty !== quantity.toString()) {
            setQty(quantity.toString());
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [quantity]);

    const increaseQty = useCallback(() => {
        const parsed = parseFloat(qty);

        if (isNaN(parsed)) {
            return qty;
        }

        let newQty = Math.min(parsed + 1, availableQuantity);

        if (newQty < 1) {
            newQty = 1;
        }

        onChange(newQty);
        setQty(newQty.toString());
    }, [availableQuantity, qty, onChange]);
    const decreaseQty = useCallback(() => {
        const parsed = parseFloat(qty);

        if (isNaN(parsed)) {
            return qty;
        }

        const newQty = Math.max(1, parsed - 1);

        onChange(newQty);
        setQty(newQty.toString());
    }, [qty, onChange]);
    const onInputChange: ChangeEventHandler<HTMLInputElement> = useCallback(
        e => setQty(e.target.value),
        []
    );
    const onInputBlur: FocusEventHandler<HTMLInputElement> = useCallback(() => {
        let newQty = parseFloat(qty);

        if (isNaN(newQty)) {
            setQty(quantity.toString());

            return;
        }

        newQty = Math.max(1, Math.min(newQty, availableQuantity));

        onChange(newQty);
        setQty(newQty.toString());
    }, [availableQuantity, qty, quantity, onChange]);

    return (
        <UiInput.Group className="rounded-sm border border-white" size="lg">
            <UiInput.LeftAddon
                className={cls(
                    'flex h-10 items-center border-0 bg-transparent px-2 !text-white',
                    quantity <= 1
                        ? 'cursor-not-allowed text-white'
                        : 'cursor-pointer'
                )}
                onClick={decreaseQty}
            >
                <MinusIcon className={cls('h-3 w-3 text-white')} />
            </UiInput.LeftAddon>

            <UiInput
                className="h-10 w-14 border-0 bg-transparent px-2 text-center text-white focus:!ring-0"
                value={qty}
                onChange={onInputChange}
                onBlur={onInputBlur}
            />

            <UiInput.RightAddon
                className={cls(
                    'flex h-10 items-center border-0 bg-transparent px-2 !text-white',
                    quantity >= availableQuantity
                        ? 'cursor-not-allowed text-white'
                        : 'cursor-pointer'
                )}
                onClick={increaseQty}
            >
                <PlusIcon className={cls('h-3 w-3 text-white')} />
            </UiInput.RightAddon>
        </UiInput.Group>
    );
});

if (isDev) {
    FixedQuantity.displayName = 'FixedQuantity';
}

export default FixedQuantity;
