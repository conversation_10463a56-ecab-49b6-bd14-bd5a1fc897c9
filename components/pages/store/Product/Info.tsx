import {FC, memo, useMemo} from 'react';
import {isDev} from '@core/helpers';
import Price from '@components/common/Price';
import useProduct from '@core/pages/store/Product/useProduct';
import PaymentOptions from './PaymentOptions';
import Share from './Share';
import {UiRating} from '@core/components/ui';
import {useScrollIntoView, useTrans} from '@core/hooks';

const Info: FC = memo(() => {
    const {selectedProduct} = useProduct();
    const t = useTrans();

    const {scrollIntoView: scrollToProductReviews} =
        useScrollIntoView<HTMLDivElement>({
            target: 'productReviews',
            offset: 108
        });

    const title = useMemo(() => {
        let title = '';

        if (!!selectedProduct.colorAttributeValue) {
            title = `${selectedProduct.colorAttributeValue} ${selectedProduct.name} `;
        } else {
            title = `${selectedProduct.name} `;
        }

        return title;
    }, [selectedProduct]);

    return (
        <div className="mb-4 border-b border-brand-clr border-opacity-25">
            <div className="flex items-center justify-between">
                {selectedProduct.brandName ? (
                    <h1 className="-mt-0.5 flex-1 font-hurme text-3xl font-semibold text-brand-clr">
                        <span className="inline"> {title}</span>
                    </h1>
                ) : (
                    <h1 className="-mt-0.5 flex-1 font-hurme text-3xl font-semibold text-brand-clr">
                        {title}
                    </h1>
                )}
                <Share />
            </div>
            {/* <div className="mt-2 flex items-center space-x-2">
                <div className=" flex cursor-pointer items-center text-sm font-semibold text-gray-500">
                    {selectedProduct.rating === 5
                        ? '5.0'
                        : selectedProduct.rating}
                </div>

                <div className="flex items-center">
                    <UiRating
                        size="sm"
                        initialRating={selectedProduct.rating}
                        readonly
                    />
                </div>

                <button
                    className="flex cursor-pointer items-center text-xs text-gray-500 transition duration-100 hover:text-primary-600 hover:underline"
                    onClick={() => scrollToProductReviews()}
                >
                    {t('{reviewCount} reviews', {
                        reviewCount: selectedProduct.reviewCount
                    })}
                </button>
            </div> */}

            <div className=" -mt-1.5 flex items-center justify-between gap-2 font-medium">
                <div className="mt-2 flex items-center gap-3 text-xl font-medium">
                    {selectedProduct.hasDiscount &&
                        selectedProduct.discount > 0 && (
                            <p className="rounded border-2 border-discount p-1 text-sm leading-4 text-discount">
                                %{Math.round(selectedProduct.discount)}
                            </p>
                        )}

                    {selectedProduct && selectedProduct.salesPrice > 0 && (
                        <Price
                            price={
                                selectedProduct.hasDiscount
                                    ? selectedProduct.unDiscountedSalesPrice
                                    : selectedProduct.salesPrice
                            }
                            discountedPrice={
                                selectedProduct.hasDiscount
                                    ? selectedProduct.salesPrice
                                    : null
                            }
                            dontWrapDiscountedPrice
                            className=" font-hurme font-semibold text-secondary-100 [&>span]:text-2xl"
                            decimal={0}
                        />
                    )}
                </div>

                <PaymentOptions />
            </div>
        </div>
    );
});

if (isDev) {
    Info.displayName = 'Info';
}

export default Info;
