import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import ColorOptions from './ColorOptions';
import ImageOptions from './ImageOptions';
import OtherOptions from './OtherOptions';
import SizeOptions from './SizeOptions';
import useProduct from '@core/pages/store/Product/useProduct';

const Options: FC = memo(() => {
    const {productOptions} = useProduct();

    return (
        <div className="space-y-3">
            {productOptions.map(option => (
                <div key={option.code}>
                    {option.type === 'color' && option.showVariantImage && (
                        <ImageOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                    {option.type === 'color' && !option.showVariantImage && (
                        <ColorOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}

                    {option.type === 'other' && (
                        <OtherOptions
                            code={option.code}
                            label={option.label}
                            selections={option.selections}
                        />
                    )}
                </div>
            ))}
        </div>
    );
});

if (isDev) {
    Options.displayName = 'Options';
}

export default Options;
