import {cls} from '@core/helpers';
import {UiTab} from '@core/components/ui';
import {categories} from './sizes';

interface DynamicContentProps {
    category: 'man' | 'woman' | 'girl' | 'boy';
}

const DynamicContent = ({category}: DynamicContentProps) => {
    return (
        <UiTab.Group>
            <UiTab.List
                as="ul"
                className="mt-4 flex flex-wrap items-center gap-5"
            >
                {categories[category].map(category => (
                    <UiTab as="li" key={category.id}>
                        {({selected}) => (
                            <button className="flex cursor-pointer flex-col items-center gap-2 lg:min-w-[72px]">
                                <div
                                    className={cls(
                                        'rounded-full bg-gray-300 p-4 text-white',
                                        {'bg-secondary-500': selected}
                                    )}
                                >
                                    <category.icon
                                        className={cls(
                                            'h-8 w-8 fill-transparent stroke-white stroke-[12px]',
                                            {'stroke-gray-600': !selected}
                                        )}
                                    />
                                </div>
                                <p className="text-xs text-gray-500">
                                    {category.name}
                                </p>
                            </button>
                        )}
                    </UiTab>
                ))}
            </UiTab.List>

            <UiTab.Panels className="mt-8">
                {categories[category].map(category => (
                    <UiTab.Panel key={category.id}>
                        <div className="overflow-x-auto">
                            <table className="w-full table-auto overflow-auto border border-gray-200">
                                <thead className="bg-gray-100">
                                    <tr>
                                        {category.thead.map(head => (
                                            <th
                                                key={head}
                                                className="py-2 text-xs font-medium lg:text-sm"
                                            >
                                                {head}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody>
                                    {category.trow.map((row, index) => (
                                        <tr key={index}>
                                            {row.map((size, index) => (
                                                <td
                                                    className="border border-gray-200 p-2 text-center text-sm"
                                                    key={index}
                                                >
                                                    {size}
                                                </td>
                                            ))}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </UiTab.Panel>
                ))}
            </UiTab.Panels>
        </UiTab.Group>
    );
};

export default DynamicContent;
