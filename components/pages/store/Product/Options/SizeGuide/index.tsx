import {Fragment, useState} from 'react';
import {cls} from '@core/helpers';
import {useMobile, useTrans} from '@core/hooks';
import {UiDialog, UiTab, UiTransition} from '@core/components/ui';
import {XIcon} from '@core/icons/outline';
import DynamicContent from './DynamicContent';
import BoyImage from './images/Boy.webp';
import GirlImage from './images/Girl.webp';
import ManImage from './images/Man.webp';
import WomanImage from './images/Woman.webp';
import StaticContent from './StaticContent';

const sizeGuideCategories = [
    {id: 1, title: '<PERSON><PERSON><PERSON>'},
    {id: 2, title: '<PERSON><PERSON><PERSON><PERSON>'},
    {id: 3, title: '<PERSON><PERSON><PERSON>'},
    {id: 4, title: '<PERSON><PERSON><PERSON> Çocuk'}
];

const SizeGuide = () => {
    const [showDrawer, setShowDrawer] = useState(false);

    const {isMobile} = useMobile();
    const t = useTrans();

    return (
        <>
            <button
                onClick={() => {
                    setShowDrawer(true);
                }}
                className="text-sm font-medium text-secondary-600 hover:underline"
            >
                {t('Size guide')}
            </button>

            <UiTransition.Root show={showDrawer} as={Fragment}>
                <UiDialog
                    as="div"
                    className="relative z-modal"
                    onClose={() => setShowDrawer(false)}
                >
                    <UiTransition.Child
                        as={Fragment}
                        enter="transition duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="transition duration-300"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-gray-900 bg-opacity-20 transition-opacity" />
                    </UiTransition.Child>

                    <div className="fixed inset-0 overflow-hidden">
                        <div className="absolute inset-0 overflow-hidden">
                            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full">
                                <UiTransition.Child
                                    as={Fragment}
                                    enter="transform transition duration-500"
                                    enterFrom={
                                        isMobile
                                            ? 'translate-y-full'
                                            : 'translate-x-full'
                                    }
                                    enterTo={
                                        isMobile
                                            ? 'translate-y-0'
                                            : 'translate-x-0'
                                    }
                                    leave="transform transition duration-500"
                                    leaveFrom={
                                        isMobile
                                            ? 'translate-y-0'
                                            : 'translate-x-0'
                                    }
                                    leaveTo={
                                        isMobile
                                            ? 'translate-y-full'
                                            : 'translate-x-full'
                                    }
                                >
                                    <UiDialog.Panel className="pointer-events-auto w-screen xl:max-w-xl">
                                        <div className="flex h-full flex-col overflow-y-auto bg-white shadow-xl">
                                            <div className="hidden border-b p-6 xl:block">
                                                <div className="flex items-center justify-between">
                                                    <UiDialog.Title className="flex select-none items-center text-xl font-medium">
                                                        {t(
                                                            ' Beden Ölçüm Tablosu'
                                                        )}
                                                    </UiDialog.Title>
                                                    <div className="ml-3 flex h-8 items-center">
                                                        <button
                                                            type="button"
                                                            className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition hover:bg-gray-200 hover:text-gray-500"
                                                            onClick={() =>
                                                                setShowDrawer(
                                                                    false
                                                                )
                                                            }
                                                        >
                                                            <XIcon
                                                                className="h-5 w-5"
                                                                aria-hidden="true"
                                                            />
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex h-mobile-header w-full select-none border-b border-gray-200 bg-white xl:hidden">
                                                <div className="container">
                                                    <div className="flex h-full items-center">
                                                        <div className="flex items-center">
                                                            <button
                                                                className="font-semibold text-secondary-600 transition active:opacity-30"
                                                                onClick={() =>
                                                                    setShowDrawer(
                                                                        false
                                                                    )
                                                                }
                                                            >
                                                                {t('Close')}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex-1 overflow-y-auto overflow-x-hidden">
                                                <div className="pt-4">
                                                    <UiTab.Group>
                                                        <UiTab.List
                                                            as="ul"
                                                            className="flex justify-between overflow-x-auto border-b px-4 lg:justify-start lg:px-0"
                                                        >
                                                            {sizeGuideCategories.map(
                                                                category => (
                                                                    <UiTab
                                                                        key={
                                                                            category.id
                                                                        }
                                                                        as="li"
                                                                    >
                                                                        {({
                                                                            selected
                                                                        }) => (
                                                                            <button
                                                                                className={cls(
                                                                                    'whitespace-nowrap border-b-2 border-transparent py-2 font-semibold lg:px-5',
                                                                                    {
                                                                                        'border-secondary-500':
                                                                                            selected
                                                                                    }
                                                                                )}
                                                                            >
                                                                                {t(
                                                                                    category.title
                                                                                )}
                                                                            </button>
                                                                        )}
                                                                    </UiTab>
                                                                )
                                                            )}
                                                        </UiTab.List>

                                                        <UiTab.Panels className="my-4 px-6">
                                                            <UiTab.Panel>
                                                                <StaticContent
                                                                    image={
                                                                        ManImage
                                                                    }
                                                                />
                                                                <DynamicContent category="man" />
                                                            </UiTab.Panel>
                                                            <UiTab.Panel>
                                                                <StaticContent
                                                                    image={
                                                                        WomanImage
                                                                    }
                                                                />
                                                                <DynamicContent category="woman" />
                                                            </UiTab.Panel>
                                                            <UiTab.Panel>
                                                                <StaticContent
                                                                    image={
                                                                        GirlImage
                                                                    }
                                                                />
                                                                <DynamicContent category="girl" />
                                                            </UiTab.Panel>
                                                            <UiTab.Panel>
                                                                <StaticContent
                                                                    image={
                                                                        BoyImage
                                                                    }
                                                                />
                                                                <DynamicContent category="boy" />
                                                            </UiTab.Panel>
                                                        </UiTab.Panels>
                                                    </UiTab.Group>
                                                </div>
                                            </div>
                                        </div>
                                    </UiDialog.Panel>
                                </UiTransition.Child>
                            </div>
                        </div>
                    </div>
                </UiDialog>
            </UiTransition.Root>
        </>
    );
};

export default SizeGuide;
