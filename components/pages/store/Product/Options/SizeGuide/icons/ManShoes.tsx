const <PERSON><PERSON>hoes = ({...props}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 517.48 196.31"
            {...props}
        >
            <path d="M291.76,194.17c-18.05,.98-48.93-3.14-75.11-20.4-42.2-27.82-88.59-29.32-135.95-26.27-18.14,1.17-26.32-5.15-27.24-22.75-.39-7.44-2.1-14.82-2.44-22.26-2.32-50.9-2.27-51.45,47.41-53.14,22.95-.78,40.23-12.42,54.94-27.14C171.78,3.77,192.62,.03,217.01,2.83c14.84,1.7,22.38,4.65,10.37,19.46-1.41,1.74-2.57,3.68-3.8,5.56-4.49,6.88-6.96,14.37,1.55,19.34,8,4.67,13.27-.54,18.64-6.99,6.23-7.48,7.21-26.15,25.35-13.06,12.45,8.98,20.21,16.53,6.86,31.18-5.91,6.49-15.57,16.35-2.91,24.45,12.11,7.75,17.12-4.47,21.86-12.38,6.34-10.58,11.32-15.68,23.76-5.62,10.49,8.48,19.23,14.08,6.53,27.03-3.08,3.15-5.24,7.3-7.4,11.22-3.3,6-2.82,11.94,3.09,15.82,5.89,3.86,12.97,2.66,15.42-3.57,10.43-26.58,28.7-21.75,51.36-18.32,28.57,4.32,58.27,1.32,87.48,1.03,12.83-.13,23.58,3.8,31.72,13.67,14.39,17.46,10.55,32.36-10.39,39.37-62.34,20.87-124.79,41.39-204.74,43.15Z" />
            <path d="M27.62,76.09c.29,21.46-.18,37.83,3.07,54.08,1.2,6.01,8.02,15.17-5.52,16.95-9.34,1.23-15.47-1.27-17.67-11.73C1.15,105.09,.5,74.84,6.24,44.41c1.04-5.52,2.47-11.39,9.6-7.88,5.79,2.85,15.95,1.8,14.24,13.48-1.5,10.3-1.98,20.74-2.46,26.08Z" />
            <path d="M64.21,169.83c15.73,0,31.46,.29,47.18-.1,8.6-.22,11.97,2.96,11.8,11.73-.16,8.22-2.59,12.45-11.5,12.4-32.21-.2-64.41-.22-96.62,.06-9.59,.09-13.16-3.63-13.08-13.32,.08-9.61,5.05-10.95,12.78-10.84,16.47,.23,32.95,.07,49.43,.08Z" />
        </svg>
    );
};

export default ManShoes;
