const ChildShoes = ({...props}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 429.16 205.92"
            {...props}
        >
            <path d="M7.42,188.56c30.98,0,54.85,.2,78.71-.13,6.94-.09,16.59,.44,16.72-9.27,.15-10.78-9.9-8.19-16.54-8.27-23.29-.3-46.61-.6-69.89,.08-11.65,.34-16.1-3.26-13.88-15.14,4.94-26.44,14.63-48.82,41.99-59.98,26.69-10.89,53.13-22.53,78.95-35.32,15.34-7.6,26.11-6.86,37.2,7.37,10.3,13.21,22.89,24.74,35.22,36.22,7.64,7.12,17.54,14.8,27.52,13.4,41.36-5.81,44.44,24.36,51.66,53.26-33.23,0-66-.26-98.76,.21-6.97,.1-18.69-4.35-18.75,8.1-.05,11.39,11.12,9.41,18.65,9.43,74.71,.2,149.43,.1,224.14,.17,4.74,0,9.49,.61,16.82,1.12-13.47,13.22-28.92,13.68-43.24,13.75-106.84,.52-213.69,.45-320.54,.07-13.88-.05-27.95-2.33-45.98-15.05Z" />
            <path d="M364.35,170.78c-16.78,0-33.58-.52-50.33,.19-10.15,.43-16.94-3.55-21.48-11.95-7.95-14.72-19.78-31.31-12.07-46.71,6.95-13.87,26.08-7.5,40.05-7.79,25.55-.53,51.13,.12,76.69-.24,12.25-.17,19.78,5.34,23.04,16.67,4.4,15.28,9.76,30.71,5.08,46.71-.62,2.13-8.51,2.89-13.06,2.99-15.97,.35-31.95,.14-47.93,.14Z" />
            <path d="M287.39,85.12c-45.18,26.98-64.11,24.43-97.93-11.68-15.27-16.3-30.67-32.49-46.31-49.04q62.08-32.33,105.95,17.62c12.61,14.34,25.37,28.56,38.29,43.1Z" />
            <path d="M294.46,61.59c30.45-.92,56.5-12.17,81.88-26.24,19.05-10.56,30.56-3.19,31.24,18.54,.97,31.02-2.91,34.93-34.59,34.94-12.02,0-24.19-1.23-36.02,.27-20.75,2.62-35.63-2.89-42.51-27.51Z" />
            <path d="M129.5,187.03c-4.24-1.31-7.1-3.88-6.55-8.56,.69-5.81,4.43-8.22,10.02-7.01,4.91,1.06,5.87,5.13,5.06,9.25-.85,4.29-3.93,6.59-8.53,6.32Z" />
        </svg>
    );
};

export default ChildShoes;
