const Man<PERSON>eans = ({...props}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 336.03 498.24"
            {...props}
        >
            <path d="M47.47,495.85c-5.87,.04-11.75,.05-17.62-.17-6.47-.23-14.69,0-20.41-3.6-11.92-7.5-6.2-30.8-5.18-41.9C15.09,332.61,27.07,215.14,38,97.57c1.07-11.52,4.02-15.95,16.23-17.31,14.99-1.67,29.58-9.97,34.36-24.4,6.28-18.97,17.93-20.97,34.9-20.46,36.23,1.09,35.77,.33,36.36,35.97,1.17,71.11-18.35,139.36-28.62,208.86-9.54,64.54-22.21,128.62-31.81,193.16-1.21,8.13-3.73,19.52-12.84,22.1-3.62,1.03-7.47,.79-11.23,.6-9.3-.45-18.59-.29-27.89-.23Z" />
            <path d="M285.83,495.87c-4.77,.02-9.55,0-14.32-.1-7.4-.17-15.95,.11-22.82-3.13-13.98-6.59-14.52-29.25-16.75-42.18-18.72-108.37-37.6-216.73-54.86-325.33-3.99-25.12-1.79-51.24-2.31-76.9-.06-2.87-.14-6.3,1.27-8.54,5.64-8.94,59.99-4.68,63.98,5.47,8.34,21.19,23.12,33.91,45.62,35.88,11.65,1.02,9.66,8.64,10.33,15.49,10.01,102.19,19.96,204.39,29.95,306.58,2.42,24.81,4.04,49.75,7.68,74.39,1.55,10.53-.81,18.54-12.7,18.47-11.69-.07-23.38-.14-35.08-.1Z" />
            <path d="M166.96,19.99c-35.3,0-70.6,.22-105.89-.24-5.17-.07-14.31,4.6-14.45-7.11-.13-10.83,7.15-10.61,14.95-10.6,70.6,.11,141.19,.14,211.79-.04,8.24-.02,15.05,.73,14.44,11.13-.66,11.15-9.69,6.55-14.94,6.61-35.29,.46-70.59,.24-105.89,.24Z" />
            <path d="M65.28,37.04c1.74,.69,13.9-5.69,12.91,4.85-1.34,14.27-13.69,21.11-27.72,23.21-9.75,1.46-7.76-5.38-7.97-11.07-.47-12.99,3.22-21.44,22.77-16.98Z" />
            <path d="M291.84,51.29c.14,8.39,1.88,15.26-7.89,13.8-14.03-2.1-26.38-8.94-27.72-23.2-.99-10.5,10.98-4.99,17.18-6.11,14.43-2.62,19.98,4.38,18.43,15.51Z" />
        </svg>
    );
};

export default ManJeans;
