import {StaticImageData} from 'next/image';
import {useTrans} from '@core/hooks';
import {UiDisclosure, UiImage} from '@core/components/ui';
import {MinusIcon, PlusIcon} from '@core/icons/outline';

const StaticContent = ({image}: {image: StaticImageData | string}) => {
    const t = useTrans();

    return (
        <div className="rounded-lg border">
            <UiDisclosure>
                {({open}) => (
                    <>
                        <UiDisclosure.Button className="w-full px-4 py-2">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {!open && (
                                        <PlusIcon className="h-7 w-7 rounded-full bg-gray-100 p-1.5" />
                                    )}
                                    {open && (
                                        <MinusIcon className="h-7 w-7 rounded-full bg-gray-100 p-1.5" />
                                    )}
                                    <p className="py-2 text-left text-sm font-medium text-black lg:text-base">
                                        {t('<PERSON>en Ölçümü Nasıl Alacağım ?')}
                                    </p>
                                </div>
                            </div>
                        </UiDisclosure.Button>
                        <UiDisclosure.Panel className="px-4 pb-4 text-gray-500">
                            <div className="flex flex-col gap-4 xl:flex-row">
                                <ol className="grid gap-3">
                                    <li className="flex flex-col gap-1 text-sm">
                                        <p>1. {t('Göğüs')}</p>
                                        <p>
                                            {t(
                                                'Mezurayı kol evinin altından,göğsün tepe noktasına yerleştirerek,yere parelel ölçüm alınız.'
                                            )}
                                        </p>
                                    </li>
                                    <li className="flex flex-col gap-1 text-sm">
                                        <p>2. {t('Bel')}</p>
                                        <p>
                                            {t(
                                                'Ellerinizi belinize koyarak bel noktanızı tespit ediniz. O noktadan yere parelel ölçüm alınız.'
                                            )}
                                        </p>
                                    </li>
                                    <li className="flex flex-col gap-1 text-sm">
                                        <p>3. {t('Basen')}</p>
                                        <p>
                                            {t(
                                                'Mezurayı kalçanızın en geniş ve dik yerine yerleştirerek yere parelel ölçüm alınız.'
                                            )}
                                        </p>
                                    </li>
                                    <li className="flex flex-col gap-1 text-sm">
                                        <p>4. {t('Boyun Çevresi')}</p>
                                        <p>
                                            {t(
                                                'Boyun çevresini ortalayacak şekilde,yere parelel ölçüm alınız.'
                                            )}
                                        </p>
                                    </li>
                                    <li className="flex flex-col gap-1 text-sm">
                                        <p>5. {t('İç Bacak Boyu')}</p>
                                        <p>
                                            {t(
                                                'Mezurayı bacağı kırmadan,en üst çatal boyundan,tabana kadar ölçü alınız.'
                                            )}
                                        </p>
                                    </li>
                                    <li className="flex flex-col gap-1 text-sm">
                                        <p>6. {t('Beden Boyu')}</p>
                                        <p>
                                            {t(
                                                'Boyunuzu ayaklarınız çıplak iken, düz bir zemin üzerinde,sabit bir yere dik dayanarak,başın en tepe noktasını belirledikten sonra,tabana kadar ölçü alınız.'
                                            )}
                                        </p>
                                    </li>
                                </ol>

                                <div className="aspect-1 w-full">
                                    <UiImage
                                        src={image}
                                        alt={t('Size Guide')}
                                    />
                                </div>
                            </div>

                            <p className="mt-4 text-sm font-semibold">
                                {t('Genel Bilgiler')}
                            </p>
                            <p className="mt-2 text-sm">
                                {t(
                                    'Ölçüsü alınacak kişinin normal duruşunda olması gerekir.Ölçüm iç giyim üzerinden yapılmalıdır.İki taraflı ölçülerde omuz genişliği gibi,sağ taraf ölçülür.Ölçümde mezura,ne çok gergin ne de çok bol olmalıdır.Mezur ölçüm yapılırken,bölgenin üzerinde rahatca kaymalıdır.Ölçü alınır iken,relaks halde ölçü alınmalıdır.Nefes tutulmamalı, karın içeri çekilmemeli ve göğüs kafesi şişirmemelidir.'
                                )}
                            </p>
                        </UiDisclosure.Panel>
                    </>
                )}
            </UiDisclosure>

            <div className="h-[1px] w-full bg-gray-200"></div>

            <UiDisclosure>
                {({open}) => (
                    <>
                        <UiDisclosure.Button className="w-full px-4 py-2">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {!open && (
                                        <PlusIcon className="h-7 w-7 rounded-full bg-gray-100 p-1.5" />
                                    )}
                                    {open && (
                                        <MinusIcon className="h-7 w-7 rounded-full bg-gray-100 p-1.5" />
                                    )}
                                    <p className="py-2 text-left text-sm font-medium text-black xl:text-base">
                                        {t('Ayak Ölçümü Nasıl Alacağım ?')}
                                    </p>
                                </div>
                            </div>
                        </UiDisclosure.Button>
                        <UiDisclosure.Panel className="px-4 pb-4 text-gray-500">
                            <ol className="grid gap-3">
                                <li className="flex flex-col gap-1 text-sm">
                                    <p>1. {'Adım'}</p>
                                    <p>
                                        {t(
                                            'Sert bir zemin üzerinde, duvara aynı hizada yer tespit et. Boş bir kağıdı oraya koyup mümkünse bantla.'
                                        )}
                                    </p>
                                </li>
                                <li className="flex flex-col gap-1 text-sm">
                                    <p>2. {t('Adım')}</p>
                                    <p>
                                        {t(
                                            'Bir kalem yardımı ile "topuk-en uzun parmak uzunluğu" şekildeki gibi,ayak uzun parmağınızın hizasından ve topuk uç hizanızdan düz şekilde çizin.'
                                        )}
                                    </p>
                                </li>
                                <li className="flex flex-col gap-1 text-sm">
                                    <p>3. {t('Adım')}</p>
                                    <p>
                                        {t(
                                            ' İki ayak için de işaretlemiş olduğun yerleri, "topuk-en uzun parmak" uzunluğunu bir cetvelle ölç.'
                                        )}
                                    </p>
                                </li>
                            </ol>

                            <p className="mt-4 text-sm font-semibold">
                                {t('Genel Bilgiler')}
                            </p>
                            <p className="mt-2 text-sm">
                                {t(
                                    'Ayakkabılarının içine giyeceğin türde çoraplar giy ve öğleden sonra ayaklarını ölç (ayaklar gün içinde genelde şişer).'
                                )}
                                <br />
                                {t(
                                    '*Ağırlığının eşit dağılmasını sağla. İki ayak ölçüsü arasında fark olması normaldir.Numaranızı uzun olan ayağa göre belirlemelisiniz.'
                                )}
                                <br />
                                {t(
                                    '*Topuk-en uzun parmak uzunluğu ölçünüz.Ölçünüz (cm) belirtilenden,bir miktar daha kısa olacaktır. Bunun farkındayız ve durumu hesaba kattık.'
                                )}
                            </p>
                        </UiDisclosure.Panel>
                    </>
                )}
            </UiDisclosure>
        </div>
    );
};

export default StaticContent;
