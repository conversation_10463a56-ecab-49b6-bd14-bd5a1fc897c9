import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import useProduct from '@core/pages/store/Product/useProduct';

function isColorLight(color: string) {
    const hex = color.replace('#', '');
    const c_r = parseInt(hex.substr(0, 2), 16);
    const c_g = parseInt(hex.substr(2, 2), 16);
    const c_b = parseInt(hex.substr(4, 2), 16);
    const brightness = (c_r * 299 + c_g * 587 + c_b * 114) / 1000;

    return brightness > 240;
}

type ColorOptionsProps = {
    code: string;
    label: string;
    selections: {
        value: string;
        color?: string;
        inStock?: boolean;
    }[];
};

const ColorOptions: FC<ColorOptionsProps> = memo(props => {
    const {code, label, selections} = props;
    const {selectedProduct, setAttribute} = useProduct();
    const value = useMemo(
        () => (selectedProduct.attributes ?? {})[code],
        [selectedProduct, code]
    );

    return (
        <>
            <div className="flex items-center">
                <h3 className="text-sm font-medium text-default">{label}:</h3>
                <div className="ml-1.5 text-sm text-muted">{value}</div>
            </div>

            <div className="-m-1.5 mt-2.5 pl-2">
                <div className="flex select-none flex-wrap items-center">
                    {selections.map(selection => (
                        <button
                            key={selection.value}
                            className={cls(
                                'relative m-1 flex items-center justify-center',
                                'cursor-pointer rounded-full focus:outline-none',
                                {
                                    'ring-1 ring-black ring-offset-[4px]':
                                        selection.value === value
                                }
                            )}
                            onClick={() => setAttribute(code, selection.value)}
                        >
                            {!!selection.color ? (
                                <div
                                    aria-hidden="true"
                                    className="h-8 w-8 rounded-full border border-black border-opacity-10"
                                    style={{
                                        backgroundColor: selection.color
                                    }}
                                />
                            ) : (
                                <div
                                    aria-hidden="true"
                                    className="relative h-8 w-8 overflow-hidden rounded-full border border-black border-opacity-10"
                                >
                                    <svg
                                        className="absolute inset-0 h-full w-full stroke-2 text-red-600"
                                        viewBox="0 0 100 100"
                                        preserveAspectRatio="none"
                                        stroke="currentColor"
                                    >
                                        <line
                                            x1={0}
                                            y1={100}
                                            x2={100}
                                            y2={0}
                                            vectorEffect="non-scaling-stroke"
                                        />
                                    </svg>
                                </div>
                            )}
                        </button>
                    ))}
                </div>
            </div>
        </>
    );
});

if (isDev) {
    ColorOptions.displayName = 'ColorOptions';
}

export default ColorOptions;
