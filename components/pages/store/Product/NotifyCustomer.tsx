import {useCallback, useState} from 'react';
import {UiButton} from '@core/components/ui';
import {useScrollIntoView, useStore, useTrans} from '@core/hooks';
import useProduct from '@core/pages/store/Product/useProduct';
import {useQuickLook} from '@components/common/QuickLook/context';

const NotifyCustomer = () => {
    const t = useTrans();
    const {selectedProduct, customerProductParams, setCustomerProductParams} =
        useProduct();

    const {addToCollection} = useStore();

    const [isNotifyCustomerInProgress, setIsNotfiyCustomerInProgress] =
        useState(false);
    const onNotifyCustomer = useCallback(async () => {
        if (isNotifyCustomerInProgress) return;

        setIsNotfiyCustomerInProgress(true);

        const result = await addToCollection(
            {id: 'is-notify-customer', isNotifyCustomer: true},
            {
                id: selectedProduct.productId,
                name: selectedProduct.name,
                image:
                    (selectedProduct.images ?? []).length > 0
                        ? selectedProduct.images![0]
                        : '/no-image.png',
                price: selectedProduct.salesPrice
            }
        );

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isNotifyCustomer: true
            });
        }

        setIsNotfiyCustomerInProgress(false);
    }, [
        isNotifyCustomerInProgress,
        selectedProduct,
        customerProductParams,
        setCustomerProductParams,
        addToCollection
    ]);

    const {scrollIntoView} = useScrollIntoView({
        target: 'relatedProducts',
        offset: 108
    });

    return (
        <div className="grid grid-cols-2 items-center gap-3 xl:flex ">
            <UiButton
                className=" rounded-none bg-[#9F2842] text-xs text-white transition-all duration-300 ease-in-out hover:bg-black hover:text-white active:bg-black xl:block xl:w-52 xl:text-base xl:text-white"
                size="xl"
            >
                {t('In Stock Soon')}
            </UiButton>

            <UiButton
                className="btn-sm  rounded-none border-primary-100  bg-[#9F2842] !text-xs text-white transition-all duration-300 ease-in-out hover:bg-black  hover:text-white active:bg-black  disabled:cursor-not-allowed disabled:bg-green-600 disabled:opacity-100 lg:!text-base lg:!text-white xl:w-60"
                size="xl"
                loading={isNotifyCustomerInProgress}
                disabled={
                    isNotifyCustomerInProgress ||
                    customerProductParams.isNotifyCustomer
                }
                onClick={onNotifyCustomer}
            >
                {customerProductParams.isNotifyCustomer
                    ? t('Received Your Request')
                    : t('Notify Me')}
            </UiButton>
        </div>
    );
};

export default NotifyCustomer;
