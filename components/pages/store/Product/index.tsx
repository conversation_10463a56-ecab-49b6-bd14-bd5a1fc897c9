import {memo, useEffect, useMemo} from 'react';
import {
    Breadcrumb,
    Campaign,
    Page,
    Product,
    ProductListItem
} from '@core/types';
import {cls, isDev, pushIntoGTMDataLayer} from '@core/helpers';
import {useMobile, useStore} from '@core/hooks';
import Info from './Info';
import {ProductProvider} from '@core/pages/store/Product/context';
import AdultConsent from './AdultConsent';
import Meta from './Meta';
import ProductReviews from './ProductReviews';
import ImageGallery from './ImageGallery';
import SideBar from './SideBar';
import Actions from './Actions';
import Configurator from './Configurator';
import Breadcrumbs from './Breadcrumbs';
import Options from './Options';
import Stats from './Stats';
import MobileDetail from './MobileDetail';
import Delivery from './Delivery';
import FAQ from './FAQ';
import QuickLook from './QuickLook';
import ProductDetailBanner from './ProductDetailBanner';
import DetailsComment from './DetailsComment';
import CompanyStory from '@components/pages/common/Home/CompanyStory';
import CatalogSearchBarPartial from '../Catalog/CatalogSearch';
import ProductReasons from './ProductReasons';
import Collections from '@components/common/Collections';
import {Cookies} from 'react-cookie-consent';
import {useRouter} from 'next/router';
type ProductPageProps = {
    slug: string;
    breadcrumbs: Breadcrumb[];
    campaigns: Campaign[];
    product: Product;
    selectedAttributes?: Record<string, any>;
    relatedProducts: ProductListItem[];
};

const ProductPage: Page<ProductPageProps> = memo(props => {
    const {
        slug,
        breadcrumbs,
        campaigns,
        product,
        selectedAttributes,
        relatedProducts
    } = props;
    const {isMobile} = useMobile();
    const {currency, navigation} = useStore();
    const router = useRouter();

    const installmentText = useMemo(() => {
        return navigation
            ?.filter(
                navigationItem =>
                    navigationItem.type === 'collection' &&
                    navigationItem.section === 'installment'
            )
            .map(navigationItem => ({
                title: navigationItem.name
            }))?.[0];
    }, [navigation]);

    useEffect(() => {
        // ---------- Google Tag Manager ----------
        pushIntoGTMDataLayer({
            event: 'view_item',
            data: {
                currency: currency.name === 'TL' ? 'TRY' : currency.name,
                value: product.salesPrice - product.discount,
                items: [
                    {
                        item_id: product.code,
                        item_name: product.name,
                        discount: product.discount ? product.discount : 0,
                        item_brand: product.brandName,
                        item_category: product.categoryName,
                        price: product.salesPrice
                    }
                ]
            }
        });
        // ----------------------------------------
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const variantItem = product.variants?.filter(
        variant => '/' + variant.slug === router.asPath
    )[0];

    useEffect(() => {
        const previouslyItems = localStorage.getItem('lastViewed');
        let previouslyItemsArray: {
            productId: string;
            productName: string;
            price: number;
            discountedPrice: number | undefined;
            quantity: number;
            productSlug: string;
            productImage: string;
            link: string;
            code?: string;
            productStockQuantity?: number;
        }[] = previouslyItems ? JSON.parse(previouslyItems) : [];

        const currentItem = variantItem
            ? {
                  productId: variantItem.productId,
                  productName: variantItem.name,
                  price: variantItem.salesPrice,
                  discountedPrice: variantItem.unDiscountedSalesPrice,
                  quantity: variantItem.quantity,
                  productSlug: variantItem.slug,
                  productImage: variantItem.images?.[0] || '',
                  link: variantItem.slug || '',
                  code: variantItem.code,
                  productStockQuantity: variantItem.quantity,
                  hasDiscount: product.hasDiscount
              }
            : {
                  productId: product.productId,
                  productName: product.name,
                  price: product.salesPrice,
                  discountedPrice: product.unDiscountedSalesPrice,
                  quantity: product.quantity,
                  productSlug: product.slug,
                  productImage: product.images?.[0] || '',
                  link: product.link || '',
                  code: product.code,
                  productStockQuantity: product.productStockQuantity,
                  hasDiscount: product.hasDiscount
              };

        const existingIndex = previouslyItemsArray.findIndex(
            (previouslyItem: {productId: string}) =>
                previouslyItem.productId === currentItem.productId
        );

        if (existingIndex > -1) {
            previouslyItemsArray.splice(existingIndex, 1);
        }

        previouslyItemsArray.unshift(currentItem);

        if (previouslyItemsArray.length > 10) {
            previouslyItemsArray.pop();
        }

        localStorage.setItem(
            'lastViewed',
            JSON.stringify(previouslyItemsArray)
        );

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [product, variantItem]);

    return (
        <ProductProvider
            slug={slug}
            product={product}
            selectedAttributes={selectedAttributes}
            relatedProducts={relatedProducts}
            campaigns={campaigns}
        >
            <Meta />

            {Array.isArray(breadcrumbs) && breadcrumbs.length > 0 && (
                <Breadcrumbs breadcrumbs={breadcrumbs} />
            )}

            <div className="container relative hidden xl:block">
                <div
                    className={cls(
                        Array.isArray(breadcrumbs) && breadcrumbs.length > 0
                            ? 'pb-8'
                            : 'py-8'
                    )}
                >
                    {product.isPCMProduct && !isMobile ? (
                        <Configurator />
                    ) : (
                        <div className="grid xl:grid-cols-12 xl:gap-4 xl:rounded xl:p-0">
                            <div className="xl:col-span-6">
                                <ImageGallery />
                            </div>

                            <div className="xl:col-span-5">
                                <Info />

                                <div className="flex items-center justify-between pb-1">
                                    <Stats />
                                    <div className="font-hurme font-semibold text-secondary-100">
                                        {installmentText?.title || ''}
                                    </div>
                                </div>
                                {product.isConfigurable &&
                                    (product.variants ?? []).length > 0 && (
                                        <Options />
                                    )}

                                <Actions />

                                <SideBar />
                                <QuickLook product={product} />
                                <ProductDetailBanner />
                                <DetailsComment />
                            </div>
                        </div>
                    )}
                </div>
            </div>

            <ProductReasons />
            <Delivery />
            <FAQ />
            <CompanyStory />
            <div className="my-12 ">
                <Collections />
            </div>

            {/* <ProductReviews /> */}

            <CatalogSearchBarPartial />

            {!product.isPCMProduct && (
                <div className="fixed inset-0 z-50 block bg-white pt-[118px] xl:hidden">
                    {isMobile && <MobileDetail breadcrumbs={breadcrumbs} />}
                </div>
            )}
            {!!product.isPCMProduct && (
                <div className="fixed inset-0 z-50 block overflow-y-auto bg-white xl:hidden">
                    {isMobile && <Configurator />}
                </div>
            )}
            {product.isAdultProduct && <AdultConsent />}
        </ProductProvider>
    );
});

if (isDev) {
    ProductPage.displayName = 'ProductPage';
}

export default ProductPage;
