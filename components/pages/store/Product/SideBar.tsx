import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import ProductCampaigns from './ProductDetails/ProductCampaigns';
import ShipmentOptions from './ProductDetails/ShipmentOptions';
import useProduct from '@core/pages/store/Product/useProduct';

const SideBar: FC = memo(() => {
    const {inStock} = useProduct();

    return inStock ? (
        <div className="space-y-4">
            <ProductCampaigns />
            <ShipmentOptions />
        </div>
    ) : null;
});

if (isDev) {
    SideBar.displayName = 'SideBar';
}

export default SideBar;
