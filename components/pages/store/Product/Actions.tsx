import {
    FC,
    memo,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import dynamic from 'next/dynamic';
import {cls, isDev} from '@core/helpers';
import {useIntersection, useMobile, useStore, useTrans} from '@core/hooks';
import {Ui<PERSON>utton, UiSpinner} from '@core/components/ui';
import {HeartIcon} from '@core/icons/outline';
import {HeartIcon as HeartSolidIcon} from '@core/icons/solid';
import useProduct from '@core/pages/store/Product/useProduct';
import NotifyCustomer from './NotifyCustomer';
import Quantity from './Quantity';

const FixedProductDetails = dynamic(() => import('./FixedProductDetails'));

const Actions: FC = memo(() => {
    const t = useTrans();
    const {addToFavorites, removeFromFavorites, navigation} = useStore();
    const {isMobile} = useMobile();
    const {
        selectedProduct,
        setQuantity,
        isAddToCartInProgress,
        addToCart,
        availableQuantity,
        inStock,
        customerProductParams,
        setCustomerProductParams
    } = useProduct();

    // Favorite.
    const [isFavoriteUpdateInProgress, setIsFavoriteUpdateInProgress] =
        useState(false);
    const onAddToFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await addToFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: true
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        addToFavorites,
        selectedProduct
    ]);
    const onRemoveFromFavorites = useCallback(async () => {
        if (isFavoriteUpdateInProgress) {
            return;
        }

        setIsFavoriteUpdateInProgress(true);

        const result = await removeFromFavorites({
            id: selectedProduct.productId,
            name: selectedProduct.name,
            image:
                (selectedProduct.images ?? []).length > 0
                    ? selectedProduct.images![0]
                    : '/no-image.png',
            price: selectedProduct.salesPrice
        });

        if (result) {
            setCustomerProductParams({
                ...customerProductParams,
                isFavorite: false
            });
        }
        setIsFavoriteUpdateInProgress(false);
    }, [
        customerProductParams,
        setCustomerProductParams,
        isFavoriteUpdateInProgress,
        removeFromFavorites,
        selectedProduct
    ]);

    const installmentText = useMemo(() => {
        return navigation
            ?.filter(
                navigationItem =>
                    navigationItem.type === 'collection' &&
                    navigationItem.section === 'installment'
            )
            .map(navigationItem => ({
                title: navigationItem.name
            }))?.[0];
    }, [navigation]);

    const firstUpdate = useRef(true);
    const [showFixedDetails, setShowFixedDetails] = useState(false);
    const [ref, observer] = useIntersection();
    useEffect(() => {
        if (firstUpdate.current) {
            firstUpdate.current = false;
            return;
        }

        if (observer?.isIntersecting) {
            setShowFixedDetails(false);
        } else {
            setShowFixedDetails(true);
        }
    }, [observer?.isIntersecting]);

    return (
        <div ref={ref} className=" pb-3">
            {installmentText?.title && (
                <div className="flex w-full font-hurme font-semibold text-secondary-100 xl:hidden">
                    {installmentText?.title || ''}
                </div>
            )}
            {showFixedDetails && <FixedProductDetails />}

            {!!inStock ? (
                <div className="flex select-none items-center gap-4 pt-3">
                    <Quantity
                        quantity={selectedProduct.quantity}
                        availableQuantity={availableQuantity}
                        onChange={quantity => setQuantity(quantity)}
                    />

                    <UiButton
                        className="h-12 w-56 flex-1 rounded-none	border-secondary-100 bg-secondary-100 text-sm font-bold text-white transition-all duration-300 ease-in-out hover:bg-black hover:text-white"
                        loading={isAddToCartInProgress}
                        disabled={!inStock}
                        onClick={addToCart}
                    >
                        {t('ADD TO CART')}
                    </UiButton>

                    <div
                        className={cls('', {
                            hidden: isMobile
                        })}
                    >
                        {customerProductParams.isFavorite ? (
                            !isFavoriteUpdateInProgress ? (
                                <UiButton
                                    className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white "
                                    onClick={onRemoveFromFavorites}
                                >
                                    <HeartSolidIcon className="mr-1 h-4 w-4 stroke-current  text-secondary-600" />
                                    <div className="truncate text-xs font-medium   ">
                                        {t('Remove From Favorites')}
                                    </div>
                                </UiButton>
                            ) : (
                                <UiButton className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white ">
                                    <div className="mr-1 w-4">
                                        <UiSpinner size="sm" />
                                    </div>
                                    <div className="truncate text-xs font-medium">
                                        {t('Remove From Favorites')}
                                    </div>
                                </UiButton>
                            )
                        ) : !isFavoriteUpdateInProgress ? (
                            <UiButton
                                className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white "
                                onClick={onAddToFavorites}
                            >
                                <HeartIcon className="mr-1 h-4 w-4 stroke-current " />
                                <div className="truncate text-xs font-medium">
                                    {t('Add To Favorites')}
                                </div>
                            </UiButton>
                        ) : (
                            <UiButton className="flex h-12 items-center rounded-sm border-brand-clr border-opacity-50 transition hover:border-black hover:bg-white ">
                                <div className="mr-1 w-4">
                                    <UiSpinner size="sm" />
                                </div>
                                <div className="truncate text-xs font-medium ">
                                    {t('Add To Favorites')}
                                </div>
                            </UiButton>
                        )}
                    </div>
                </div>
            ) : (
                <NotifyCustomer />
            )}
        </div>
    );
});

if (isDev) {
    Actions.displayName = 'Actions';
}

export default Actions;
