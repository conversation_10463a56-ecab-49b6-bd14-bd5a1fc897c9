import {FC, memo, useEffect} from 'react';
import {isDev} from '@core/helpers';
import {useIntersection} from '@core/hooks';
import Report from './Report';
import useProduct from '@core/pages/store/Product/useProduct';
import Reviews from './Reviews';

const ProductReviews: FC = memo(() => {
    const {setActiveTab} = useProduct();

    const [ref, observer] = useIntersection({
        threshold: 0.5
    });
    useEffect(() => {
        if (observer?.isIntersecting) {
            setActiveTab('productReviews');
        }
    }, [observer, setActiveTab]);

    return (
        <div
            ref={ref}
            id="productReviews"
            className="container mb-12 mt-12 bg-white xl:rounded-none xl:border xl:border-gray-200 xl:p-8 xl:shadow-sm"
        >
            <div className="grid grid-cols-1 gap-8 xl:grid-cols-12">
                <Report />

                <Reviews />
            </div>
        </div>
    );
});

if (isDev) {
    ProductReviews.displayName = 'ProductReviews';
}

export default ProductReviews;
