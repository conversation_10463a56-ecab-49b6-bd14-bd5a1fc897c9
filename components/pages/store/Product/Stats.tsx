import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useProduct from '@core/pages/store/Product/useProduct';

const Stats: FC = memo(() => {
    const t = useTrans();
    const {availableQuantity} = useProduct();

    return (
        <div className="flex select-none items-center justify-between  text-xs">
            {availableQuantity > 5 ? (
                <div className="flex items-center space-x-1 text-secondary-100">
                    <div>{t('In Stock')}</div>
                </div>
            ) : availableQuantity > 0 ? (
                <div className="flex items-center space-x-1 text-secondary-100">
                    <div>
                        {availableQuantity > 1
                            ? t('Last {count} products', {
                                  count: availableQuantity
                              })
                            : t('Last {count} products', {
                                  count: availableQuantity
                              })}
                    </div>
                </div>
            ) : (
                <div className="flex items-center space-x-1 text-secondary-100">
                    <div>{t('Out Of Stock!')}</div>
                </div>
            )}
        </div>
    );
});

if (isDev) {
    Stats.displayName = 'Stats';
}

export default Stats;
