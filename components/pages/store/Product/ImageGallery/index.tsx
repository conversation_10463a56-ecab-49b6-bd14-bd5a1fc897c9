import {FC, memo, useEffect, useMemo, useState} from 'react';
import {Cookies} from 'react-cookie-consent';
import storeConfig from '~/store.config';
import {cls, isDev} from '@core/helpers';
import {useElementSize} from '@core//hooks';
import {UiImage, UiSlider, UiStickyBox} from '@core/components/ui';
import {Navigation, SliderInterface, Thumbs} from '@core/components/ui/Slider';
import SelectedImage from './SelectedImage';
import useProduct from '@core/pages/store/Product/useProduct';
import {Product} from '@core/types';
import Video from '@components/common/Video';
import ShippingDetails from '../ProductDetails/ShippingDetails';

type GalleryProps = {
    images: string[];
    productName: string;
    product: Product;
};

const Gallery: FC<GalleryProps> = memo(({productName, images, product}) => {
    const [isSliding, setIsSliding] = useState(false);
    const [thumbsSwiper, setThumbsSwiper] = useState<SliderInterface>();

    const {
        ref: containerRef,
        width: containerWidth,
        height: containerHeight
    } = useElementSize();

    const videoRegex = /<iframe.*?src="(.*?)".*?<\/iframe>/;
    const videoMatch = (product.description || '').match(videoRegex);
    const videoUrl = videoMatch ? videoMatch[1] : null;

    const [isShown, setIsShown] = useState(false);

    const PlayIcon = ({className}: {className?: string}) => (
        <div className="absolute inset-0 flex cursor-pointer items-center  justify-center bg-opacity-50">
            <svg
                className={cls(
                    'h-24 w-24 rounded-full border-2 border-white bg-black/30 p-4 !text-white',
                    className
                )}
                fill="currentColor"
                viewBox="0 0 24 24"
            >
                <path d="M8 5v14l11-7z" />
            </svg>
        </div>
    );

    return (
        <div className="relative flex h-fit gap-8">
            <div>
                <div className="selected-slider flex justify-start">
                    <UiSlider
                        className="thumb-swiper h-[502px] w-[60px] select-none justify-start"
                        slidesPerView={7}
                        spaceBetween={3}
                        direction="vertical"
                        watchSlidesProgress
                        modules={[Navigation, Thumbs]}
                        onSwiper={setThumbsSwiper}
                    >
                        {images.map((image, index) => (
                            <UiSlider.Slide key={image}>
                                <UiImage
                                    className="!h-16 w-14 cursor-pointer"
                                    src={`${image}?w=180&q=50`}
                                    alt={productName}
                                    position="left"
                                    fill
                                />
                                {index === 1 && videoUrl && (
                                    <PlayIcon className="text h-6 w-6 cursor-pointer !p-0" />
                                )}
                            </UiSlider.Slide>
                        ))}
                    </UiSlider>
                </div>
            </div>
            <div className="w-10/12">
                <UiStickyBox offsetTop={12}>
                    <div
                        ref={containerRef}
                        className="thumb-swiper-wrapper !rounded-none"
                    >
                        <UiSlider
                            modules={[Navigation, Thumbs]}
                            thumbs={{swiper: thumbsSwiper}}
                            className="h-full w-full"
                            navigation={{}}
                            loop
                            onSlideChangeTransitionStart={() =>
                                setIsSliding(true)
                            }
                            onSlideChangeTransitionEnd={() =>
                                setIsSliding(false)
                            }
                        >
                            {images.map((image, index) => (
                                <UiSlider.Slide
                                    key={image}
                                    onClick={() =>
                                        index === 1 &&
                                        videoUrl &&
                                        setIsShown(true)
                                    }
                                    className={cls({
                                        'aspect-h-3 aspect-w-3':
                                            storeConfig.catalog
                                                .productImageShape ===
                                            'rectangle',
                                        'aspect-h-1 aspect-w-1':
                                            storeConfig.catalog
                                                .productImageShape !==
                                            'rectangle'
                                    })}
                                >
                                    <SelectedImage
                                        src={image}
                                        alt={productName}
                                        preload={index === 0}
                                        containerRef={containerRef}
                                        containerWidth={containerWidth}
                                        containerHeight={containerHeight}
                                        isSliding={isSliding}
                                        images={images}
                                    />
                                    {index === 1 && videoUrl && <PlayIcon />}
                                </UiSlider.Slide>
                            ))}
                        </UiSlider>
                    </div>
                    <ShippingDetails setIsShown={setIsShown} />
                </UiStickyBox>
            </div>
            <Video isShown={isShown} setIsShown={setIsShown}>
                {videoUrl && (
                    <div className="w-full">
                        <iframe
                            className="h-[540px] w-full "
                            allowFullScreen
                            src={videoUrl.replace(
                                'youtube.com',
                                'youtube-nocookie.com'
                            )}
                        ></iframe>
                    </div>
                )}
            </Video>
        </div>
    );
});

if (isDev) {
    Gallery.displayName = 'Gallery';
}

const ImageGallery: FC = memo(() => {
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    const {selectedProduct, product} = useProduct();

    // Get product images.
    const images = useMemo(() => {
        let images = selectedProduct.images;

        if (product.isAdultProduct) {
            if (isMounted && Cookies.get('isAdultEligible') !== 'true') {
                images = ['/adult-image.png'];
            } else if (!isMounted) {
                images = ['/placeholder.png'];
            }
        }
        if (!Array.isArray(images) || images.length < 1) {
            images = ['/no-image.png'];
        }

        return images;
    }, [selectedProduct.images, product.isAdultProduct, isMounted]);

    return (
        <Gallery
            key={JSON.stringify(images)}
            images={images}
            productName={selectedProduct.name}
            product={selectedProduct}
        />
    );
});

if (isDev) {
    ImageGallery.displayName = 'ImageGallery';
}

export default ImageGallery;
