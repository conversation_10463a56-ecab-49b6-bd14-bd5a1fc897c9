import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiImage} from '@core/components/ui';
import delivery from '@assets/images/common/product-delivery/delivery.webp';
import {BenefitChecked} from '@components/icons';

const Delivery: FC = memo(() => {
    const t = useTrans();

    return (
        <div className="my-12  bg-brand-pink">
            <div className="grid grid-cols-2 xl:container lg:grid-cols-11 lg:gap-x-28 lg:pl-5 xl:gap-x-36">
                <UiImage
                    src={delivery}
                    className="col-span-4 "
                    height={100}
                    width={900}
                    priority
                    alt={'title'}
                />

                <div className="col-span-6 max-w-lg py-10 xl:max-w-full">
                    <div>
                        <h3 className="font-dm-serif text-2xl text-brand-clr lg:text-4xl">
                            {t('Shipping and Delivery')}
                        </h3>
                        <p className="text-product max-w-lg pt-2 text-sm lg:pt-4">
                            {t(
                                'While preparing your orders, we carefully package the product with its protective box. We transport the product safely and deliver it to the cargo company we work with. We also inform the cargo company that the products must be transported carefully. We deliver your product to your location precisely with the cargo company.'
                            )}
                        </p>
                    </div>

                    <div className="flex max-w-lg flex-col gap-x-5 py-8 lg:flex-row xl:max-w-full">
                        <div className="flex flex-col gap-y-1 lg:w-1/2">
                            <div className="flex items-center justify-start">
                                <BenefitChecked className="mr-1 !h-6 !w-6" />
                                <p className="flex-1 text-sm">
                                    {t(
                                        'Free Shipping on orders of 1000 TL and above'
                                    )}
                                </p>
                            </div>
                            <div className="flex items-center justify-start">
                                <BenefitChecked className="mr-1 h-6 w-6" />
                                <p className="flex-1 text-sm">
                                    {t('Careful Packaging and Delivery')}
                                </p>
                            </div>
                        </div>

                        <div className="max-w-64 mt-1 flex flex-col gap-y-1 lg:mt-0 lg:w-1/2">
                            <div className="flex items-center justify-start">
                                <BenefitChecked className="mr-1 h-6 w-6" />
                                <p className="text-sm">
                                    {t('Nehir Guarantee')}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="max-w-lg">
                        <h3 className="font-dm-serif text-2xl text-brand-clr lg:text-4xl">
                            {t('Warranty and Security')}
                        </h3>
                        <p className="text-product max-w-lg pt-2 text-sm lg:pt-4">
                            {t(
                                'We guarantee that our products will be used like the first day for years. We do not use any substances harmful to human health in our production.'
                            )}
                        </p>
                        <p className="text-product max-w-lg pt-2 text-sm lg:pt-4">
                            {t(
                                'With SSL private security technology, your information is encrypted for all purchases you make from Nehir.com.tr, thus securing Nehir. You can use your products safely for many years with Nehir warranty.'
                            )}
                        </p>
                    </div>

                    <div className="flex max-w-lg flex-col gap-x-5 py-8 lg:flex-row xl:max-w-full">
                        <div className="flex flex-col gap-y-1 lg:w-1/2">
                            <div className="flex items-center justify-start">
                                <BenefitChecked className="mr-1 h-6 w-6" />
                                <p className="text-sm">
                                    {t('Nehir Guarantee')}
                                </p>
                            </div>
                            <div className="flex items-center justify-start">
                                <BenefitChecked className="mr-1 h-6 w-6" />
                                <p className="flex-1 text-sm">
                                    {t(
                                        'We do not keep your credit card information in the system.'
                                    )}
                                </p>
                            </div>
                        </div>
                        <div className="mt-1 flex flex-col lg:mt-0 lg:w-1/2">
                            <div className="flex items-center justify-start">
                                <BenefitChecked className="mr-1 h-6 w-6" />
                                <p className="flex-1 text-sm">
                                    {t('Free Return and Exchange Guarantee')}
                                </p>
                            </div>
                            <div className="flex items-center justify-start"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    Delivery.displayName = 'Delivery';
}

export default Delivery;
