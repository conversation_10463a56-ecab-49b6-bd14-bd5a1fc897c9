import {FC, memo} from 'react';
import {Cart, CartItem} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiImage} from '@core/components/ui';
import Price from '@components/common/Price';

type SidebarProps = {
    cart: Cart;
    getDeliveryDate: (item: CartItem) => string;
};

const SideBar: FC<SidebarProps> = memo(props => {
    const {cart, getDeliveryDate} = props;
    const t = useTrans();

    return (
        <>
            <h2 className="hidden text-lg font-medium leading-5 xl:block">
                {t('Order Summary')}
            </h2>

            <div className="px-4 py-6 xl:mt-6 xl:px-0">
                <ul role="list" className="-my-6 divide-y divide-gray-200">
                    {cart.items.map(item => {
                        const hasDiscount =
                            item.discountedPrice !== undefined &&
                            typeof item.discountedPrice === 'number';

                        return (
                            <li
                                className="flex items-center py-6"
                                key={item.productId}
                            >
                                <div className="relative h-16 w-12 rounded">
                                    <UiImage
                                        className="h-full w-full rounded"
                                        src={`${item.productImage}?w=180&q=50`}
                                        alt={item.productName}
                                        width={48}
                                        height={64}
                                        fit="cover"
                                        position="center"
                                        quality={75}
                                    />

                                    <span
                                        className="absolute flex items-center justify-center rounded-full border border-gray-100 bg-gray-900 text-xs font-medium text-white"
                                        style={{
                                            top: '-10px',
                                            right: '-10px',
                                            paddingLeft: '2.5px',
                                            paddingRight: '2.5px',
                                            minWidth: '1.25rem',
                                            minHeight: '1.25rem'
                                        }}
                                    >
                                        {item.quantity}
                                    </span>
                                </div>

                                <div className="ml-4 flex-1">
                                    <h3 className="text-sm font-medium text-default xl:text-base">
                                        {item.productName}
                                    </h3>

                                    {Array.isArray(item.productAttributes) &&
                                        item.productAttributes.length > 0 && (
                                            <div className="mt-1 text-sm">
                                                <div className="flex items-center space-x-3">
                                                    {item.productAttributes.map(
                                                        attribute => (
                                                            <div
                                                                className="flex items-center text-muted"
                                                                key={
                                                                    attribute.value
                                                                }
                                                            >
                                                                <div className="mr-0.5">
                                                                    {
                                                                        attribute.label
                                                                    }
                                                                    :
                                                                </div>
                                                                <div>
                                                                    {
                                                                        attribute.value
                                                                    }
                                                                </div>
                                                            </div>
                                                        )
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                    {item.isPCMProduct &&
                                        !!item.pcmPayload &&
                                        Object.keys(item.pcmPayload).length >
                                            0 && (
                                            <div className="mb-2 mt-1.5 flex flex-col justify-center text-sm">
                                                {item.pcmPayload.summary.items.map(
                                                    (summaryItem: any) => (
                                                        <div
                                                            className="flex"
                                                            key={
                                                                summaryItem.code
                                                            }
                                                        >
                                                            <div className="mr-2">
                                                                {
                                                                    summaryItem.label
                                                                }
                                                                :
                                                            </div>
                                                            <div className="text-muted">
                                                                {
                                                                    summaryItem.value
                                                                }
                                                            </div>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        )}

                                    {cart.step !== 'information' && (
                                        <div className="mt-1.5 flex items-center text-xs xl:mt-1 xl:text-sm">
                                            <div className="mr-1.5 text-muted">
                                                {item.deliveryType === 'special'
                                                    ? t('Delivery date')
                                                    : t(
                                                          'Estimated delivery date'
                                                      )}
                                            </div>

                                            <div className="text-primary-600">
                                                {getDeliveryDate(item)}
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <Price
                                    className={cls({
                                        '[&>span]:flex-col [&>span]:items-end [&>span]:text-primary-600':
                                            hasDiscount
                                    })}
                                    price={item.price}
                                    discountedPrice={
                                        hasDiscount
                                            ? item.discountedPrice
                                            : undefined
                                    }
                                />
                            </li>
                        );
                    })}
                </ul>
            </div>

            <dl className="space-y-4 px-4 text-sm font-medium xl:space-y-6 xl:px-0">
                <div className="flex items-center justify-between border-t border-gray-200 pt-6">
                    <dt className="text-gray-600">{t('Subtotal')}</dt>
                    <dd>
                        <Price price={cart.subTotal} />
                    </dd>
                </div>

                <div className="flex items-center justify-between">
                    {cart.step !== 'information' ? (
                        <dt className="text-gray-600">{t('Tax amount')}</dt>
                    ) : (
                        <dt className="text-gray-600">{t('Tax estimate')}</dt>
                    )}
                    <dd>
                        <Price price={cart.taxTotal} />
                    </dd>
                </div>

                <div className="flex items-center justify-between">
                    {cart.step !== 'information' ? (
                        <dt className="text-gray-600">
                            {t('Delivery amount')}
                        </dt>
                    ) : (
                        <dt className="text-gray-600">
                            {t('Delivery estimate')}
                        </dt>
                    )}
                    <dd>
                        <Price price={cart.deliveryTotal} />
                    </dd>
                </div>

                {cart.cashOnDeliveryServiceFee > 0 && (
                    <div className="flex items-center justify-between">
                        <dt className="text-gray-600">
                            {t('Cash on delivery service fee')}
                        </dt>

                        <dd>
                            <Price price={cart.cashOnDeliveryServiceFee} />
                        </dd>
                    </div>
                )}

                {cart.dueDifference > 0 && (
                    <div className="flex items-center justify-between">
                        <dt className="text-gray-600">
                            {t('Due difference amount')}
                        </dt>

                        <dd>
                            <Price price={cart.dueDifference} />
                        </dd>
                    </div>
                )}

                {Array.isArray(cart.discounts) &&
                    cart.discounts.length > 0 &&
                    cart.discounts.map(discount => (
                        <div
                            key={discount.id}
                            className="flex items-center justify-between gap-4"
                        >
                            <dt className="flex items-center gap-2 rounded-md bg-brand-budget px-3 py-1 text-sm text-secondary-100">
                                <svg
                                    className="h-4 w-4"
                                    viewBox="0 0 18 18"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <g
                                        mask="url(#a)"
                                        className="fill-secondary-100"
                                    >
                                        <path
                                            d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                            fillOpacity=".55"
                                        />
                                        <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                    </g>
                                </svg>
                                <p className="w-fit">{discount.description}</p>
                            </dt>

                            <dd>
                                <Price
                                    className="text-primary-600"
                                    price={-discount.amount}
                                />
                            </dd>
                        </div>
                    ))}

                {typeof cart.discountTotalIncludingProductDiscounts ===
                    'number' &&
                    cart.discountTotalIncludingProductDiscounts > 0 && (
                        <div className="flex items-center justify-between gap-4">
                            <dt className="flex items-center gap-2 rounded-md bg-brand-budget px-3 py-1 text-sm text-secondary-100">
                                <svg
                                    className="h-4 w-4"
                                    viewBox="0 0 18 18"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <g
                                        mask="url(#a)"
                                        className="fill-secondary-100"
                                    >
                                        <path
                                            d="M17.765 3.087a1.993 1.993 0 0 0-1.779-1.089h-5.16a2 2 0 0 0-1.42.592L2.59 9.475c0 .002 0 .002-.002.002a2.033 2.033 0 0 0-.5.874L.74 8.048a1.998 1.998 0 0 1 .712-2.735L9.815.424A1.988 1.988 0 0 1 11.34.22l4.985 1.335a1.998 1.998 0 0 1 1.439 1.532Z"
                                            fillOpacity=".55"
                                        />
                                        <path d="M10.826 1.998h5.16a1.998 1.998 0 0 1 1.999 1.998v5.168c0 .53-.21 1.039-.585 1.413l-6.812 6.812a1.999 1.999 0 0 1-2.818.01l-5.162-5.096a2 2 0 0 1-.02-2.825c.002 0 .003 0 .003-.002l6.814-6.884a1.998 1.998 0 0 1 1.421-.594Zm2.663 5.995a1.5 1.5 0 1 0-.001-3 1.5 1.5 0 0 0 0 3Z" />
                                    </g>
                                </svg>
                                <p className="text-secondary-100">
                                    {t('Total Discount')}
                                </p>
                            </dt>
                            <dd>
                                <Price
                                    className="text-primary-600"
                                    price={
                                        -cart.discountTotalIncludingProductDiscounts
                                    }
                                />
                            </dd>
                        </div>
                    )}

                <div className="flex items-center justify-between border-t border-gray-200 pt-4 font-medium text-base">
                    <dt>{t('Total')}</dt>
                    <dd>
                        <Price price={cart.grandTotal} />
                    </dd>
                </div>
            </dl>
        </>
    );
});

if (isDev) {
    SideBar.displayName = 'SideBar';
}

export default SideBar;
