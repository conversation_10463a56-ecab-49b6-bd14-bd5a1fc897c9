import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import useCheckout from '../useCheckout';
import Delivery from './Delivery';
import Information from './Information';
import Payment from './Payment';

const Content: FC = memo(() => {
    const {cart} = useCheckout();

    return (
        <>
            {cart.step === 'information' && <Information />}
            {cart.step === 'delivery' && <Delivery />}
            {cart.step === 'payment' && <Payment />}
        </>
    );
});

if (isDev) {
    Content.displayName = 'Content';
}

export default Content;
