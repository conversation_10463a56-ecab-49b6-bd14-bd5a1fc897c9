import {FC, memo, useEffect, useState} from 'react';
import {
    CardCvcElement,
    CardExpiryElement,
    CardNumberElement,
    Elements,
    useElements,
    useStripe
} from '@stripe/react-stripe-js';
import {cls, isDev, jsonRequest} from '@core/helpers';
import {useTrans} from '@core/hooks';
import useCheckout from '../../../../../useCheckout';
import {getStripe} from './utils';

const CART_OPTIONS = {
    style: {
        base: {
            fontSize: '16px',
            fontWeight: 400,
            color: '#000',
            fontSmoothing: 'antialiased',
            '::placeholder': {
                color: '#6B7280',
                fontWeight: 400
            }
        }
    }
};

const CardNumber = () => {
    const t = useTrans();
    const [error, setError] = useState('');
    const [isFocused, setIsFocused] = useState(false);

    return (
        <div
            className={cls('form-field form-field-lg form-field-outline', {
                'border-primary-600 ring-1 ring-primary-600': isFocused
            })}
            aria-invalid={!!error}
        >
            <CardNumberElement
                onChange={e => {
                    if (!!e.error && e.error.message) {
                        setError(e.error.message);
                    } else {
                        setError('');
                    }
                }}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                options={{
                    ...CART_OPTIONS,
                    showIcon: true,
                    iconStyle: 'default',
                    classes: {
                        base: 'w-full'
                    },
                    placeholder: t('Card number')
                }}
            />
        </div>
    );
};

const CardExpiry = () => {
    const t = useTrans();
    const [error, setError] = useState('');
    const [isFocused, setIsFocused] = useState(false);

    return (
        <div
            className={cls('form-field form-field-lg form-field-outline', {
                'border-primary-600 ring-1 ring-primary-600': isFocused
            })}
            aria-invalid={!!error}
        >
            <CardExpiryElement
                onChange={e => {
                    if (!!e.error && e.error.message) {
                        setError(e.error.message);
                    } else {
                        setError('');
                    }
                }}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                options={{
                    ...CART_OPTIONS,
                    classes: {
                        base: 'w-full'
                    },
                    placeholder: t('Card expiry')
                }}
            />
        </div>
    );
};

const CardCvc = () => {
    const t = useTrans();
    const [error, setError] = useState('');
    const [isFocused, setIsFocused] = useState(false);

    return (
        <div
            className={cls('form-field form-field-lg form-field-outline', {
                'border-primary-600 ring-1 ring-primary-600': isFocused
            })}
            aria-invalid={!!error}
        >
            <CardCvcElement
                onChange={e => {
                    if (!!e.error && e.error.message) {
                        setError(e.error.message);
                    } else {
                        setError('');
                    }
                }}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                options={{
                    ...CART_OPTIONS,
                    classes: {
                        base: 'w-full'
                    },
                    placeholder: t('CVC/CVV')
                }}
            />
        </div>
    );
};

const StripeForm: FC = memo(() => {
    const stripe = useStripe();
    const elements = useElements();
    const t = useTrans();
    const {setProcessPayment, setErrorMessage} = useCheckout();
    const [clientSecret, setClientSecret] = useState('');

    useEffect(() => {
        (async () => {
            try {
                const result = await jsonRequest({
                    url: '/api/checkout/create-stripe-payment-intent',
                    method: 'POST',
                    data: {}
                });

                setClientSecret(result.clientSecret);
            } catch (error: any) {
                setErrorMessage(error.message);
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setProcessPayment(async () => {
            if (!clientSecret || !stripe || !elements) {
                // Stripe.js has not yet loaded.
                // Make sure to disable form submission until Stripe.js has loaded.
                throw new Error('Payment method is not ready yet!');
            }

            const payload = await stripe.confirmCardPayment(clientSecret, {
                payment_method: {
                    // @ts-ignore
                    card: elements.getElement(CardNumberElement)
                }
            });

            if (!!payload.error) {
                throw new Error(payload.error.message);
            }

            return {documentNo: payload.paymentIntent.id};
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [clientSecret, setProcessPayment]);

    return (
        <div className="space-y-4">
            <CardNumber />

            <div className="flex space-x-4">
                <CardExpiry />

                <CardCvc />
            </div>
        </div>
    );
});

if (isDev) {
    StripeForm.displayName = 'StripeForm';
}

type StripeProps = {
    publicKey: string;
};

const Stripe: FC<StripeProps> = memo(({publicKey}) => {
    const stripePromise = getStripe(publicKey);

    return (
        // @ts-ignore
        <Elements stripe={stripePromise}>
            <div className="mt-5 border-t border-gray-200 pt-4">
                <StripeForm />
            </div>
        </Elements>
    );
});

if (isDev) {
    Stripe.displayName = 'Stripe';
}

export default Stripe;
