import {FC, memo} from 'react';
import {PaymentMethod} from '@core/types';
import {isDev} from '@core/helpers';
import CommonCreditCard from './CommonCreditCard';
import Stripe from './Stripe';

type CreditCardProps = {
    paymentMethod: PaymentMethod;
};

const CreditCard: FC<CreditCardProps> = memo(({paymentMethod}) => {
    if (
        paymentMethod.integrationType === 'stripe' &&
        typeof paymentMethod.integrationParams === 'object' &&
        typeof paymentMethod.integrationParams.publicKey === 'string'
    ) {
        return <Stripe publicKey={paymentMethod.integrationParams.publicKey} />;
    }

    return <CommonCreditCard />;
});

if (isDev) {
    CreditCard.displayName = 'CreditCard';
}

export default CreditCard;
