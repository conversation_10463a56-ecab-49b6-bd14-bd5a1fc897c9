import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiForm} from '@core/components/ui';

type FormProps = {
    onChange: (field: string, value: string) => void;
    onFocus: (field: string) => void;
    onBlur: () => void;
};

const Form: FC<FormProps> = memo(({onChange, onFocus, onBlur}) => {
    const t = useTrans();

    return (
        <div className="mt-4">
            <UiForm.Field
                /* name is used in cardbox component. If you change it check that component as well */
                name="cardNumber"
                label={t('Card number')}
                format="#### #### #### ####"
                mask="-"
                onChange={e => onChange('cardNumber', e.target.value)}
                onFocus={() => onFocus('cardNumber')}
                onBlur={() => onBlur()}
            />
            <UiForm.Field
                name="cardHolder"
                className="mt-4"
                label={t('Card holder')}
                onChange={e => onChange('cardHolder', e.target.value)}
                onFocus={() => onFocus('cardHolder')}
                onBlur={() => onBlur()}
            />
            <div className="mt-4 flex gap-4">
                <UiForm.Field
                    name="cardExpiry"
                    label={t('Card expiry')}
                    format="## / ##"
                    mask="-"
                    onChange={e => onChange('cardExpiry', e.target.value)}
                    onFocus={() => onFocus('cardExpiry')}
                    onBlur={() => onBlur()}
                />
                <UiForm.Field
                    name="cardCvv"
                    label={t('CVC/CVV')}
                    format="####"
                    onChange={e => onChange('cardCvv', e.target.value)}
                    onFocus={() => onFocus('cardCvv')}
                    onBlur={() => onBlur()}
                />
            </div>
        </div>
    );
});

if (isDev) {
    Form.displayName = 'Form';
}

export default Form;
