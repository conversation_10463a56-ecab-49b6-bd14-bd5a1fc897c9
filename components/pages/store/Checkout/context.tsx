import {
    createContext,
    Dispatch,
    FC,
    memo,
    PropsWithChildren,
    SetStateAction,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {
    Cart,
    CartItem,
    Contact,
    DeliveryOption,
    PaymentMethod
} from '@core/types';
import {isDev, jsonRequest, pushIntoGTMDataLayer} from '@core/helpers';
import {useCart, useCustomer, useStore, useTrans} from '@core/hooks';

type ProcessPayment = () => Promise<Record<string, any>>;

type CheckoutContextType = {
    cart: Cart;
    countries: Record<string, any>[];
    initialStates?: string[];
    initialCities: string[];
    deliveryOptions: DeliveryOption[];
    paymentMethods: PaymentMethod[];
    contacts?: Contact[];
    errorMessage: string;
    isLoading: boolean;
    orderParams: Record<string, any>;
    salesContractText: string;
    preliminaryInformationForm: string;

    setErrorMessage: Dispatch<SetStateAction<string>>;
    setIsLoading: Dispatch<SetStateAction<boolean>>;
    setOrderParams: Dispatch<SetStateAction<Record<string, any>>>;
    getDeliveryDate: (item: CartItem) => string;

    updateStep: (step: string) => Promise<void>;

    saveGuestInformation: (data: Record<string, any>) => Promise<void>;
    saveCustomerInformation: (cart: Cart, contacts: Contact[]) => Promise<void>;
    changeAddress: (
        type: 'billing-address' | 'delivery-address',
        addressId: string
    ) => Promise<void>;
    updateContacts: (contacts: Contact[]) => void;
    updateDeliveryType: (
        deliveryType: 'standard' | 'special' | 'store-delivery',
        deliveryOptionId: string | null,
        items?: {productId: string; deliveryDate?: Date; deliveryTime?: Date}[]
    ) => Promise<void>;
    saveDelivery: ({
        deliveryPayload
    }: {
        deliveryPayload: Record<string, any>;
    }) => Promise<void>;
    setProcessPayment: (fn: ProcessPayment | undefined) => void;
    updatePaymentMethodId: (paymentMethodId: string) => Promise<void>;
    updateSubPaymentMethodId: (subPaymentMethodId: string) => Promise<void>;
    updateInstallmentCount: (installmentCount: number) => Promise<void>;
    approveOrder: () => Promise<void>;
};

export const CheckoutContext = createContext<CheckoutContextType>(null as any);

if (isDev) {
    CheckoutContext.displayName = 'CheckoutContext';
}

type CheckoutProviderProps = {
    cart: Cart;
    countries: Record<string, any>[];
    initialStates?: string[];
    initialCities: string[];
    deliveryOptions: DeliveryOption[];
    paymentMethods: PaymentMethod[];
    contacts?: Contact[];
};

export const CheckoutProvider: FC<PropsWithChildren<CheckoutProviderProps>> =
    memo(props => {
        const {
            cart: initialCart,
            countries,
            initialStates,
            initialCities,
            deliveryOptions: initialDeliveryOptions,
            paymentMethods: initialPaymentMethods,
            contacts: initialContacts,
            children
        } = props;
        const router = useRouter();
        const {locale, currency} = useStore();
        const t = useTrans();
        const customer = useCustomer();
        const {cart, setCart} = useCart();
        const [deliveryOptions, setDeliveryOptions] = useState(
            initialDeliveryOptions
        );
        const [paymentMethods, setPaymentMethods] = useState(
            initialPaymentMethods
        );
        const [contacts, setContacts] = useState(() => initialContacts);
        const processPayment = useRef(async () => {
            return {};
        });
        const [errorMessage, setErrorMessage] = useState('');
        const [isLoading, setIsLoading] = useState(false);
        const inProgress = useRef(false);
        const isInitial = useRef(true);
        const [orderParams, setOrderParams] = useState({
            orderId: '',
            orderCode: '',
            isNewCustomer: true
        });
        const [salesContractText, setSalesContractText] = useState('');
        const [preliminaryInformationForm, setPreliminaryInformationForm] =
            useState('');

        // Set Initial cart.
        useEffect(() => {
            setCart(initialCart);
            isInitial.current = false;

            if (cart.step === 'payment') {
                (async () => {
                    const {
                        salesContractText: sct,
                        preliminaryInformationForm: pif
                    } = await jsonRequest({
                        url: '/api/checkout/get-texts',
                        method: 'POST',
                        data: {locale}
                    });

                    setSalesContractText(sct);
                    setPreliminaryInformationForm(pif);
                })();
            }

            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        const processedCart = useMemo(() => {
            cart.items = cart.items
                .filter(
                    item =>
                        item.selected &&
                        !item.removed &&
                        item.quantity > 0 &&
                        item.productStockQuantity >= item.quantity
                )
                .map(item => {
                    if (typeof item.deliveryDate !== 'undefined') {
                        item.deliveryDate = new Date(item.deliveryDate);
                    }
                    if (typeof item.deliveryTime !== 'undefined') {
                        item.deliveryTime = new Date(item.deliveryTime);
                    }

                    return item;
                });

            return cart;
        }, [cart]);

        const getDeliveryDate = useCallback(
            (item: CartItem) => {
                if (
                    item.deliveryType === 'special' &&
                    typeof item.deliveryDate !== 'undefined'
                ) {
                    const date = new Date(
                        item.deliveryDate.getFullYear(),
                        item.deliveryDate.getMonth(),
                        item.deliveryDate.getDate()
                    );

                    if (typeof item.deliveryTime !== 'undefined') {
                        date.setHours(item.deliveryTime.getHours());
                        date.setMinutes(item.deliveryTime.getMinutes());

                        return new Intl.DateTimeFormat(locale, {
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        }).format(date);
                    }

                    return new Intl.DateTimeFormat(locale, {
                        month: 'long',
                        day: 'numeric'
                    }).format(date);
                } else {
                    const now = new Date();
                    const date = new Date(
                        new Date().setDate(
                            now.getDate() +
                                (item.estimatedDeliveryDuration ?? 0)
                        )
                    );

                    return new Intl.DateTimeFormat(locale, {
                        month: 'long',
                        day: 'numeric'
                    }).format(date);
                }
            },
            [locale]
        );
        const saveGuestInformation = useCallback(
            async (data: Record<string, any>) => {
                if (inProgress.current) return;

                inProgress.current = true;

                const payload: Record<string, any> = {};

                payload.firstName = data.firstName;
                payload.lastName = data.lastName;
                payload.email = data.email;
                payload.isSubscribedToNewsletter =
                    !!data.isSubscribedToNewsletter;
                payload.useDeliveryAddressAsBillingAddress =
                    !!data.useDeliveryAddressAsBillingAddress;
                payload.invoiceType = data.invoiceType;
                payload.companyName = data.companyName;
                payload.taxIdentificationNumber = data.taxIdentificationNumber;
                payload.taxOffice = data.taxOffice;

                if (!!data.phone && typeof data.phone === 'object') {
                    payload.phoneCountryCode = data.phone.countryCode;
                    payload.phoneCode = data.phone.code;
                    payload.phoneNumber = data.phone.number;
                }

                payload.deliveryAddress = {};
                payload.deliveryAddress.street = data['deliveryAddress-street'];
                payload.deliveryAddress.street2 =
                    data['deliveryAddress-street2'];
                payload.deliveryAddress.city = data['deliveryAddress-city'];
                payload.deliveryAddress.district =
                    data['deliveryAddress-district'];
                payload.deliveryAddress.subDistrict =
                    data['deliveryAddress-subDistrict'];
                payload.deliveryAddress.state = data['deliveryAddress-state'];
                payload.deliveryAddress.postalCode =
                    data['deliveryAddress-postalCode'];
                payload.deliveryAddress.countryId =
                    data['deliveryAddress-countryId'];
                payload.deliveryAddress.countryName = countries.find(
                    country => country.id === data['deliveryAddress-countryId']
                )?.name;

                payload.billingAddress = {};
                payload.billingAddress.street = data['billingAddress-street'];
                payload.billingAddress.street2 = data['billingAddress-street2'];
                payload.billingAddress.city = data['billingAddress-city'];
                payload.billingAddress.district =
                    data['billingAddress-district'];
                payload.billingAddress.subDistrict =
                    data['billingAddress-subDistrict'];
                payload.billingAddress.state = data['billingAddress-state'];
                payload.billingAddress.postalCode =
                    data['billingAddress-postalCode'];
                payload.billingAddress.countryId =
                    data['billingAddress-countryId'];
                payload.billingAddress.countryName = countries.find(
                    country => country.id === data['billingAddress-countryId']
                )?.name;

                if (payload.useDeliveryAddressAsBillingAddress) {
                    payload.billingAddress = payload.deliveryAddress;
                }

                try {
                    const newCart = await jsonRequest({
                        url: '/api/checkout/save-information',
                        method: 'POST',
                        data: {payload}
                    });
                    const dos = await jsonRequest({
                        url: '/api/checkout/delivery-options',
                        method: 'POST',
                        data: {}
                    });

                    setCart(newCart);
                    setDeliveryOptions(dos);
                    setErrorMessage('');
                    inProgress.current = false;

                    const container =
                        document.querySelector('.content-wrapper');
                    if (container !== null) {
                        container.scrollTo({top: 0});
                    }
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [countries, setCart]
        );
        const saveCustomerInformation = useCallback(
            async (cart: Cart, contacts: Contact[]) => {
                if (inProgress.current) return;

                inProgress.current = true;

                const deliveryContact = contacts?.find(
                    c => c.id === cart.deliveryAddressId
                );
                const billingContact = contacts?.find(
                    c => c.id === cart.billingAddressId
                );

                if (!deliveryContact) {
                    inProgress.current = false;

                    throw new Error(t('Delivery address must be selected!'));
                }
                if (!billingContact) {
                    inProgress.current = false;

                    throw new Error(t('Billing address must be selected!'));
                }

                const payload: Record<string, any> = {};

                payload.firstName = customer?.firstName;
                payload.lastName = customer?.lastName;
                payload.email = customer?.email;
                payload.useDeliveryAddressAsBillingAddress = false;
                payload.deliveryAddressId = cart.deliveryAddressId;
                payload.billingAddressId = cart.billingAddressId;
                payload.deliveryAddress = deliveryContact?.address;
                payload.billingAddress = billingContact?.address;
                payload.companyName = billingContact?.companyName;
                payload.taxIdentificationNumber =
                    billingContact?.taxIdentificationNumber;
                payload.taxOffice = billingContact?.taxOffice;
                payload.identityNumber = billingContact?.identityNumber;

                try {
                    const newCart = await jsonRequest({
                        url: '/api/checkout/save-information',
                        method: 'POST',
                        data: {payload}
                    });
                    const dos = await jsonRequest({
                        url: '/api/checkout/delivery-options',
                        method: 'POST',
                        data: {}
                    });

                    setCart(newCart);
                    setDeliveryOptions(dos);
                    setErrorMessage('');
                    inProgress.current = false;

                    const container =
                        document.querySelector('.content-wrapper');
                    if (container !== null) {
                        container?.scrollTo({top: 0});
                    }
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [
                customer?.firstName,
                customer?.lastName,
                customer?.email,
                t,
                setCart
            ]
        );
        const updateContacts = useCallback((contacts: Contact[]) => {
            setContacts([...contacts]);
        }, []);
        const changeAddress = useCallback(
            async (
                type: 'billing-address' | 'delivery-address',
                addressId: string
            ) => {
                if (inProgress.current) return;

                inProgress.current = true;

                try {
                    let newCart;

                    if (type === 'delivery-address') {
                        newCart = await jsonRequest({
                            url: '/api/checkout/change-delivery-address',
                            method: 'POST',
                            data: {deliveryAddressId: addressId}
                        });
                    } else {
                        newCart = await jsonRequest({
                            url: '/api/checkout/change-billing-address',
                            method: 'POST',
                            data: {billingAddressId: addressId}
                        });
                    }

                    setCart(newCart);
                    setErrorMessage('');
                    inProgress.current = false;
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [setCart]
        );
        const updateStep = useCallback(
            async (step: string) => {
                if (inProgress.current) return;

                inProgress.current = true;

                try {
                    const newCart = await jsonRequest({
                        url: '/api/checkout/update-step',
                        method: 'POST',
                        data: {step}
                    });

                    if (cart.step === 'payment') {
                        const {
                            salesContractText: sct,
                            preliminaryInformationForm: pif
                        } = await jsonRequest({
                            url: '/api/checkout/get-texts',
                            method: 'POST',
                            data: {locale}
                        });

                        setSalesContractText(sct);
                        setPreliminaryInformationForm(pif);
                    }

                    setCart(newCart);
                    setErrorMessage('');

                    const container =
                        document.querySelector('.content-wrapper');
                    if (container !== null) {
                        container.scrollTo({top: 0});
                    }

                    inProgress.current = false;
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [cart.step, locale, setCart]
        );
        const updateDeliveryType = useCallback(
            async (
                deliveryType: 'standard' | 'special' | 'store-delivery',
                deliveryOptionId: string,
                items?: {
                    productId: string;
                    deliveryDate?: Date;
                    deliveryTime?: Date;
                }[]
            ) => {
                if (inProgress.current) return;

                inProgress.current = true;

                try {
                    const newCart = await jsonRequest({
                        url: '/api/checkout/update-delivery-type',
                        method: 'POST',
                        data: {deliveryType, items, deliveryOptionId}
                    });
                    const dos = await jsonRequest({
                        url: '/api/checkout/delivery-options',
                        method: 'POST',
                        data: {}
                    });

                    setCart(newCart);
                    setDeliveryOptions(dos);
                    setErrorMessage('');
                    inProgress.current = false;
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [setCart]
        );
        const saveDelivery = useCallback(
            async ({
                deliveryPayload
            }: {
                deliveryPayload: Record<string, any>;
            }) => {
                if (inProgress.current) return;

                inProgress.current = true;

                try {
                    const newCart = await jsonRequest({
                        url: '/api/checkout/save-delivery',
                        method: 'POST',
                        data: {
                            ...deliveryPayload
                        }
                    });
                    const pms = await jsonRequest({
                        url: '/api/checkout/payment-methods',
                        method: 'POST',
                        data: {}
                    });

                    setCart(newCart);
                    setPaymentMethods(pms);
                    setErrorMessage('');

                    const {
                        salesContractText: sct,
                        preliminaryInformationForm: pif
                    } = await jsonRequest({
                        url: '/api/checkout/get-texts',
                        method: 'POST',
                        data: {locale}
                    });

                    setSalesContractText(sct);
                    setPreliminaryInformationForm(pif);

                    const container =
                        document.querySelector('.content-wrapper');
                    if (container !== null) {
                        container.scrollTo({top: 0});
                    }

                    inProgress.current = false;
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [locale, setCart]
        );
        const setProcessPayment = useCallback((fn: ProcessPayment) => {
            processPayment.current = fn;
        }, []);
        const updatePaymentMethodId = useCallback(
            async (paymentMethodId: string) => {
                if (inProgress.current) return;

                inProgress.current = true;

                try {
                    const newCart = await jsonRequest({
                        url: '/api/checkout/update-payment-method-id',
                        method: 'POST',
                        data: {paymentMethodId}
                    });

                    setCart(newCart);
                    setErrorMessage('');
                    inProgress.current = false;
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [setCart]
        );
        const updateSubPaymentMethodId = useCallback(
            async (subPaymentMethodId: string) => {
                if (inProgress.current) return;

                inProgress.current = true;

                try {
                    const newCart = await jsonRequest({
                        url: '/api/checkout/update-sub-payment-method-id',
                        method: 'POST',
                        data: {subPaymentMethodId}
                    });

                    setCart(newCart);
                    setErrorMessage('');
                    inProgress.current = false;
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [setCart]
        );
        const updateInstallmentCount = useCallback(
            async (installmentCount: number) => {
                if (inProgress.current) return;

                inProgress.current = true;

                try {
                    const newCart = await jsonRequest({
                        url: '/api/checkout/update-installment-count',
                        method: 'POST',
                        data: {installmentCount}
                    });

                    setCart(newCart);
                    setErrorMessage('');
                    inProgress.current = false;
                } catch (error) {
                    inProgress.current = false;

                    throw error;
                }
            },
            [setCart]
        );
        const approveOrder = useCallback(async () => {
            if (inProgress.current) return;

            inProgress.current = true;

            setIsLoading(true);

            try {
                let payload: Record<string, any> = {};
                if (!!processPayment.current) {
                    const paymentPayload = await processPayment.current();

                    payload = {
                        ...payload,
                        ...(paymentPayload ?? {})
                    };
                }

                let result = null;
                if (!payload.skipOrderApproval) {
                    result = await jsonRequest({
                        url: '/api/checkout/approve-payment',
                        method: 'POST',
                        data: {payload}
                    });
                }

                // ---------- Google Tag Manager ----------
                pushIntoGTMDataLayer({
                    event: 'purchase',
                    data: {
                        transaction_id:
                            typeof result === 'object' && result !== null
                                ? result.orderCode
                                : cart.id,
                        tax: 0,
                        shipping: cart.deliveryTotal,
                        currency:
                            currency.name === 'TL' ? 'TRY' : currency.name,
                        value: cart.items.reduce(
                            (acc, cur) =>
                                acc +
                                (cur.discountedPrice ?? cur.price) *
                                    cur.quantity,
                            0
                        ),
                        items: cart.items.map(item => ({
                            item_id: item.productCode,
                            item_name: item.productName,
                            discount: item.discountedPrice
                                ? item.price - item.discountedPrice
                                : 0,
                            price: item.discountedPrice
                                ? item.discountedPrice
                                : item.price,
                            item_brand: item.brandName,
                            item_category: item.productCategory,
                            quantity: item.quantity
                        }))
                    }
                });

                const thankYouPayload = {
                    cart: cart,
                    ...(typeof result === 'object' && result !== null
                        ? {
                              orderParams: {
                                  orderId: result.orderId,
                                  orderCode: result.orderCode,
                                  isNewCustomer: result.isNewCustomer
                              }
                          }
                        : {}),
                    paymentMethods,
                    deliveryOptions
                };
                localStorage.setItem(
                    'checkout-result',
                    JSON.stringify(thankYouPayload)
                );

                await router.replace('/thank-you');

                setCart({
                    status: 'draft',
                    step: 'information',
                    subTotal: 0,
                    discountTotal: 0,
                    taxTotal: 0,
                    deliveryTotal: 0,
                    cashOnDeliveryServiceFee: 0,
                    grandTotal: 0,
                    itemCount: 0,
                    productCount: 0,
                    discounts: [],
                    items: [],
                    deliveryType: 'standard',
                    deliveryOptionId: '',
                    cardBrand: 'none',
                    installmentCount: 1,
                    dueDifference: 0,
                    minimumPurchaseAmountForFreeShipping: 0
                });
                setErrorMessage('');
                setIsLoading(false);
                inProgress.current = false;

                const container = document.querySelector('.content-wrapper');
                if (container !== null) {
                    container.scrollTo({top: 0});
                }
            } catch (error) {
                const container = document.querySelector('.content-wrapper');
                if (container !== null) {
                    container.scrollTo({top: 0});
                }

                inProgress.current = false;
                setIsLoading(false);

                throw error;
            }
        }, [deliveryOptions, paymentMethods, router, setCart, cart, currency]);

        const value: any = useMemo(
            () => ({
                cart: processedCart,
                countries,
                initialStates,
                initialCities,
                deliveryOptions,
                paymentMethods,
                contacts,
                errorMessage,
                isLoading,
                orderParams,
                salesContractText,
                preliminaryInformationForm,

                setErrorMessage,
                setIsLoading,
                setOrderParams,
                getDeliveryDate,
                updateStep,
                saveGuestInformation,
                saveCustomerInformation,
                updateContacts,
                changeAddress,
                updateDeliveryType,
                saveDelivery,
                setProcessPayment,
                updatePaymentMethodId,
                updateSubPaymentMethodId,
                updateInstallmentCount,
                approveOrder
            }),
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [
                processedCart,
                countries,
                initialStates,
                initialCities,
                deliveryOptions,
                paymentMethods,
                contacts,
                errorMessage,
                isLoading,
                orderParams,
                salesContractText,
                preliminaryInformationForm,
                setProcessPayment,
                setErrorMessage,
                setIsLoading,
                setOrderParams,
                getDeliveryDate,
                approveOrder,
                saveGuestInformation,
                saveCustomerInformation,
                changeAddress,
                updateContacts
            ]
        );

        return (
            <CheckoutContext.Provider value={value}>
                {children}
            </CheckoutContext.Provider>
        );
    });

if (isDev) {
    CheckoutProvider.displayName = 'CheckoutProvider';
}
