import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {ShieldCheckIcon} from '@core/icons/outline';

const UserNav: FC = memo(() => {
    const t = useTrans();

    return (
        <div className="flex items-center justify-end text-lg">
            <ShieldCheckIcon className="mr-3 h-7 w-7 text-green-500" />
            {t('Secure Checkout')}
        </div>
    );
});

if (isDev) {
    UserNav.displayName = 'UserNav';
}

export default UserNav;
