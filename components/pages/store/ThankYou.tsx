import {FC, useCallback, useEffect, useMemo, useState} from 'react';
import {useRouter} from 'next/router';
import storeConfig from '~/store.config';
import {Cart, CartItem, DeliveryOption, PaymentMethod} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import {CheckIcon} from '@core/icons/solid';
import Price from '@components/common/Price';

const ThankYou: FC = () => {
    const t = useTrans();
    const router = useRouter();
    const {locale} = useStore();
    const [payload, setPayload] = useState<{
        cart: Cart;
        paymentMethods: PaymentMethod[];
        deliveryOptions: DeliveryOption[];
        orderParams?: Record<string, any>;
    } | null>(null);

    const paymentMethod = useMemo(
        () =>
            payload?.paymentMethods.find(
                paymentMethod =>
                    paymentMethod.id === payload?.cart.paymentMethodId
            ),
        [payload?.cart.paymentMethodId, payload?.paymentMethods]
    );
    const bankAccount = useMemo(() => {
        if (!!payload?.cart.subPaymentMethodId && !!paymentMethod) {
            return paymentMethod.bankAccounts?.find(
                bankAccount =>
                    bankAccount.paymentMethodId ===
                    payload?.cart?.subPaymentMethodId
            );
        }
    }, [payload?.cart.subPaymentMethodId, paymentMethod]);
    const deliveryOption = useMemo(
        () =>
            payload?.deliveryOptions.find(
                deliveryOption =>
                    deliveryOption.deliveryOptionId ===
                    payload?.cart.deliveryOptionId
            ),
        [payload?.cart.deliveryOptionId, payload?.deliveryOptions]
    );

    const getDurationText = useCallback((deliveryOption: DeliveryOption) => {
        let text = '';

        if (deliveryOption.minDeliveryDays === deliveryOption.maxDeliveryDays) {
            text = deliveryOption.minDeliveryDays.toString();

            if (deliveryOption.minDeliveryDays > 1) {
                text += ` ${t('Days')}`;
            } else {
                text += ` ${t('Day')}`;
            }
        } else {
            text = `${deliveryOption.minDeliveryDays} - ${deliveryOption.maxDeliveryDays}`;

            if (
                deliveryOption.minDeliveryDays > 1 ||
                deliveryOption.maxDeliveryDays > 1
            ) {
                text += ` ${t('Days')}`;
            } else {
                text += ` ${t('Day')}`;
            }
        }

        return text;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getDeliveryDate = useCallback(
        (item: CartItem) => {
            if (
                item.deliveryType === 'special' &&
                typeof item.deliveryDate !== 'undefined'
            ) {
                const date = new Date(
                    item.deliveryDate.getFullYear(),
                    item.deliveryDate.getMonth(),
                    item.deliveryDate.getDate()
                );

                if (typeof item.deliveryTime !== 'undefined') {
                    date.setHours(item.deliveryTime.getHours());
                    date.setMinutes(item.deliveryTime.getMinutes());

                    return new Intl.DateTimeFormat(locale, {
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    }).format(date);
                }

                return new Intl.DateTimeFormat(locale, {
                    month: 'long',
                    day: 'numeric'
                }).format(date);
            } else {
                const now = new Date();
                const date = new Date(
                    new Date().setDate(
                        now.getDate() + (item.estimatedDeliveryDuration ?? 0)
                    )
                );

                return new Intl.DateTimeFormat(locale, {
                    month: 'long',
                    day: 'numeric'
                }).format(date);
            }
        },
        [locale]
    );

    useEffect(() => {
        try {
            const payload = JSON.parse(
                localStorage.getItem('checkout-result') as string
            );

            if (payload !== null) {
                payload.cart.items = payload.cart.items.map((item: any) => {
                    if (
                        item.deliveryType === 'special' &&
                        typeof item.deliveryDate !== 'undefined'
                    ) {
                        item.deliveryDate = new Date(item.deliveryDate);

                        if (typeof item.deliveryTime !== 'undefined') {
                            item.deliveryTime = new Date(item.deliveryTime);
                        }
                    }

                    return item;
                });

                setPayload(payload);
            } else {
                router.replace('/');
            }
        } catch (error) {
            router.replace('/');
        }

        localStorage.removeItem('checkout-result');
        localStorage.removeItem('isDeleteCampaingsProduct');
        localStorage.removeItem('deleteCampaingsProduct');
        // eslint-disable-next-line
    }, []);

    if (typeof payload?.cart.status !== 'string') return null;

    return (
        <div className="flex w-full flex-1 flex-col items-center pb-4 pt-12 xl:mx-auto xl:max-w-3xl xl:pb-24 xl:pt-24">
            <div
                className="
                        flex h-20 w-20 items-center justify-center
                        rounded-full bg-green-600 text-white"
            >
                <CheckIcon className="h-10 w-10" />
            </div>

            <p className="mt-10 text-3xl font-extrabold">{t('Thank You!')}</p>

            <p className="mt-4 px-4 text-center text-base text-gray-500 xl:mt-3 xl:px-0">
                {payload?.orderParams?.orderCode
                    ? t('Your order #{code} has created successfully.', {
                          code: payload.orderParams.orderCode
                      })
                    : t('Your order has created successfully.')}{' '}
                {t('Your order details have been sent to your email address.')}{' '}
                {t('Thank you for shopping on our site.')}
            </p>

            <div className="mt-6 w-full border-t  border-gray-200 px-4 xl:mt-16 xl:px-0">
                {payload?.cart.items.map(item => (
                    <div
                        key={item.productId}
                        className="flex space-x-4 border-b border-gray-200 py-4 xl:space-x-6 xl:py-10"
                    >
                        <UiImage
                            className={cls('block h-48 flex-none rounded-lg', {
                                'w-36':
                                    storeConfig.catalog.productImageShape ===
                                    'rectangle',
                                'w-48':
                                    storeConfig.catalog.productImageShape !==
                                    'rectangle'
                            })}
                            src={`${item.productImage}?w=240&q=75`}
                            alt={item.productName}
                            width={
                                storeConfig.catalog.productImageShape ===
                                'rectangle'
                                    ? 144
                                    : 192
                            }
                            height={192}
                            fit="cover"
                            position="center"
                        />

                        <div className="flex flex-auto flex-col">
                            <div>
                                <h4 className="text-sm font-medium xl:text-base">
                                    {item.productName}
                                </h4>

                                <div className="mt-2 text-sm text-gray-600">
                                    {Array.isArray(item.productAttributes) &&
                                        item.productAttributes.length > 0 && (
                                            <div className="flex items-center space-x-3">
                                                {item.productAttributes.map(
                                                    attribute => (
                                                        <div
                                                            className="flex items-center text-muted"
                                                            key={
                                                                attribute.value
                                                            }
                                                        >
                                                            <div className="mr-0.5">
                                                                {
                                                                    attribute.label
                                                                }
                                                                :
                                                            </div>
                                                            <div>
                                                                {
                                                                    attribute.value
                                                                }
                                                            </div>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        )}
                                </div>

                                {payload.cart?.deliveryType !==
                                    'store-delivery' && (
                                    <div className="mt-1 flex items-center text-sm">
                                        <div className="mr-1.5 text-muted">
                                            {item.deliveryType === 'special'
                                                ? t('Delivery date')
                                                : t('Estimated delivery date')}
                                        </div>

                                        <div className="text-primary-600">
                                            {getDeliveryDate(item)}
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className="mt-6 flex flex-1 items-end">
                                <dl className="flex space-x-4 divide-x divide-gray-200 text-sm sm:space-x-6">
                                    <div className="flex">
                                        <dt className="font-medium text-gray-900">
                                            {t('Quantity')}
                                        </dt>
                                        <dd className="ml-2 text-gray-700">
                                            {item.quantity}
                                        </dd>
                                    </div>

                                    <div className="flex flex-col pl-4 sm:flex-row sm:pl-6">
                                        <dt className="font-medium text-gray-900">
                                            {t('Price')}
                                        </dt>
                                        <dd className="text-gray-700 sm:ml-2">
                                            <Price
                                                className={cls(
                                                    'font-semibold',
                                                    !!item.discountedPrice &&
                                                        '[&>span]:text-primary-600'
                                                )}
                                                price={item.price}
                                                discountedPrice={
                                                    item.discountedPrice ||
                                                    undefined
                                                }
                                            />
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>
                ))}

                <div className="xl:ml-40 xl:pl-6">
                    <dl className="grid grid-cols-2 gap-x-4 py-6 text-sm xl:gap-x-6 xl:py-10">
                        <div>
                            <dt className="font-medium text-gray-900">
                                {payload.cart?.deliveryType === 'store-delivery'
                                    ? t('Store address')
                                    : t('Delivery address')}
                            </dt>
                            <dd className="mt-2 text-gray-700">
                                <address className="not-italic">
                                    <div>
                                        {payload?.cart.deliveryAddress?.street}
                                    </div>
                                    {!!payload?.cart.deliveryAddress
                                        ?.street2 && (
                                        <div>
                                            {
                                                payload?.cart.deliveryAddress
                                                    ?.street2
                                            }
                                        </div>
                                    )}
                                    {payload?.cart.deliveryAddress
                                        ?.subDistrict && (
                                        <div>
                                            {
                                                payload?.cart.deliveryAddress
                                                    ?.subDistrict
                                            }
                                        </div>
                                    )}
                                    <div className="flex items-center space-x-2">
                                        {payload?.cart.deliveryAddress
                                            ?.district && (
                                            <span>
                                                {
                                                    payload?.cart
                                                        .deliveryAddress
                                                        ?.district
                                                }
                                            </span>
                                        )}
                                        <span>
                                            {
                                                payload?.cart.deliveryAddress
                                                    ?.city
                                            }
                                        </span>
                                        {payload?.cart.deliveryAddress
                                            ?.state && (
                                            <span>
                                                {
                                                    payload?.cart
                                                        .deliveryAddress?.state
                                                }
                                            </span>
                                        )}
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <span>
                                            {
                                                payload?.cart.deliveryAddress
                                                    ?.postalCode
                                            }
                                        </span>
                                        <span>
                                            {
                                                payload?.cart.deliveryAddress
                                                    ?.countryName
                                            }
                                        </span>
                                    </div>
                                </address>
                            </dd>
                        </div>

                        <div>
                            <dt className="font-medium text-gray-900">
                                {t('Billing address')}
                            </dt>
                            <dd className="mt-2 text-gray-700">
                                <address className="not-italic">
                                    <div>
                                        {payload?.cart.billingAddress?.street}
                                    </div>
                                    {!!payload?.cart.billingAddress
                                        ?.street2 && (
                                        <div>
                                            {
                                                payload?.cart.billingAddress
                                                    ?.street2
                                            }
                                        </div>
                                    )}
                                    {payload?.cart.billingAddress
                                        ?.subDistrict && (
                                        <div>
                                            {
                                                payload?.cart.billingAddress
                                                    ?.subDistrict
                                            }
                                        </div>
                                    )}
                                    <div className="flex items-center space-x-2">
                                        {payload?.cart.billingAddress
                                            ?.district && (
                                            <span>
                                                {
                                                    payload?.cart.billingAddress
                                                        ?.district
                                                }
                                            </span>
                                        )}
                                        <span>
                                            {payload?.cart.billingAddress?.city}
                                        </span>
                                        {payload?.cart.billingAddress
                                            ?.state && (
                                            <span>
                                                {
                                                    payload?.cart.billingAddress
                                                        ?.state
                                                }
                                            </span>
                                        )}
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <span>
                                            {
                                                payload?.cart.billingAddress
                                                    ?.postalCode
                                            }
                                        </span>
                                        <span>
                                            {
                                                payload?.cart.billingAddress
                                                    ?.countryName
                                            }
                                        </span>
                                    </div>
                                </address>
                            </dd>
                        </div>
                    </dl>

                    <dl className="grid grid-cols-2 gap-x-4 border-t border-gray-200 py-6 text-sm xl:gap-x-6 xl:py-10">
                        <div>
                            <dt className="font-medium text-gray-900">
                                {t('Payment method')}
                            </dt>
                            <dd className="mt-2 text-gray-700">
                                <p>{t(paymentMethod?.name as string)}</p>

                                {!!bankAccount && (
                                    <>
                                        <p>{bankAccount?.bankName}</p>
                                        <p>{bankAccount?.bankBranchName}</p>
                                        <p>{bankAccount?.iban}</p>
                                    </>
                                )}
                            </dd>
                        </div>
                        <div>
                            <dt className="font-medium text-gray-900">
                                {t('Delivery option')}
                            </dt>
                            <dd className="mt-2 text-gray-700">
                                <p>{t(deliveryOption?.name as string)}</p>
                                {payload.cart?.deliveryType !==
                                    'store-delivery' && (
                                    <p>
                                        {getDurationText(
                                            deliveryOption as DeliveryOption
                                        )}
                                    </p>
                                )}
                            </dd>
                        </div>
                    </dl>

                    <dl className="space-y-4 border-t border-gray-200 pt-6 text-sm xl:space-y-6 xl:pt-10">
                        <div className="flex justify-between">
                            <dt className="text-gray-900">{t('Subtotal')}</dt>
                            <dd className="text-gray-700">
                                <Price
                                    price={payload?.cart.subTotal as number}
                                />
                            </dd>
                        </div>
                        <div className="flex justify-between">
                            <dt className="text-gray-900">
                                {' '}
                                {t('Tax amount')}
                            </dt>
                            <dd className="text-gray-700">
                                <Price
                                    price={payload?.cart.taxTotal as number}
                                />
                            </dd>
                        </div>
                        <div className="flex justify-between">
                            <dt className="text-gray-900">
                                {' '}
                                {t('Delivery amount')}
                            </dt>
                            <dd className="text-gray-700">
                                <Price
                                    price={
                                        payload?.cart.deliveryTotal as number
                                    }
                                />
                            </dd>
                        </div>
                        {(payload?.cart.cashOnDeliveryServiceFee as number) >
                            0 && (
                            <div className="flex justify-between">
                                <dt className="text-gray-900">
                                    {' '}
                                    {t('Cash on delivery service fee')}
                                </dt>
                                <dd className="text-gray-700">
                                    <Price
                                        price={
                                            payload?.cart
                                                .cashOnDeliveryServiceFee as number
                                        }
                                    />
                                </dd>
                            </div>
                        )}
                        <div className="flex justify-between">
                            <dt className="font-medium text-gray-900">
                                {t('Total')}
                            </dt>
                            <dd className="font-medium text-gray-900">
                                <Price
                                    price={payload?.cart.grandTotal as number}
                                />
                            </dd>
                        </div>
                    </dl>

                    <div className="mt-6 flex flex-col border-t border-gray-200 pt-4 xl:mt-10 xl:flex-row xl:justify-between xl:pt-10">
                        <UiLink
                            href="/account/my-orders"
                            data-color="primary"
                            className="btn btn-xl btn-light"
                        >
                            {t('View Your Orders')}
                        </UiLink>

                        <UiLink
                            href="/"
                            data-color="primary"
                            className="btn btn-xl btn-solid mt-2 xl:mt-0"
                        >
                            {t('Continue Shopping')}
                            <span className="ml-3 inline-block"> &rarr;</span>
                        </UiLink>
                    </div>
                </div>
            </div>
        </div>
    );
};

if (isDev) {
    ThankYou.displayName = 'ThankYou';
}

export default ThankYou;
