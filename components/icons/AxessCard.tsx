const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 79 24" {...props}>
            <path fill="#FEC10E" d="m19.273 0 7.723 11.159L34.719 0z" />
            <path
                fill="#231F20"
                d="M9.495 5.96 0 23.06h4.691l1.667-3.422h8.164l1.737 3.422h4.663l-9.579-17.1zm.924 5.158 2.857 5.502H7.562z"
            />
            <path
                fill="#231F20"
                d="m31.349 23.06-4.408-5.578-3.744 5.578h-4.739l6.376-8.76-6.048-8.374h4.587l3.623 5.233 3.623-5.233h4.559L29.007 14.3l7.177 8.76z"
            />
            <path
                d="M51.786 15.718H39.553q.11 2.108 1.46 3.237 1.281 1.088 3.43 1.088 2.727 0 4.16-1.432l1.543 3.058q-2.108 1.707-6.295 1.707-4.01 0-6.268-2.355-2.216-2.314-2.217-6.336 0-3.814 2.342-6.39 2.411-2.689 6.17-2.688 3.61 0 5.897 2.121 2.341 2.19 2.342 5.772 0 .73-.331 2.217m-12.094-3.002h8.416q-.413-3.76-4.16-3.76-3.43 0-4.256 3.76m13.188 9.243 1.419-3.18q1.805 1.417 4.05 1.417 2.34 0 2.34-1.667 0-1.156-1.211-1.983-.55-.386-2.231-1.088-4.437-1.832-4.436-5.138 0-2.37 1.915-3.623 1.64-1.088 4.117-1.088 2.673 0 5.028 1.2L62.715 9.92q-1.31-1.13-3.651-1.13-2.094 0-2.094 1.667 0 .882 1.46 1.666.456.262 2.177.937 4.242 1.667 4.242 5.207 0 2.602-2.066 3.966-1.762 1.144-4.518 1.143-1.695 0-2.797-.303-1.005-.274-2.59-1.115m12.722 0 1.419-3.18q1.805 1.417 4.05 1.417 2.34 0 2.34-1.667 0-1.156-1.211-1.983-.55-.386-2.231-1.088-4.436-1.832-4.436-5.138 0-2.37 1.915-3.623 1.64-1.088 4.117-1.088 2.673 0 5.028 1.2L75.434 9.92q-1.309-1.13-3.65-1.13-2.094 0-2.094 1.667 0 .882 1.46 1.666.454.262 2.177.937 4.242 1.667 4.242 5.207-.001 2.602-2.066 3.966-1.763 1.144-4.518 1.143-1.695 0-2.797-.303-1.005-.274-2.59-1.115"
                fill="#231F20"
            />
        </svg>
    );
};

export default Icon;
