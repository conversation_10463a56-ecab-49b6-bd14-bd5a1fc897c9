const CreatifSvg = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="39"
            height="37"
            viewBox="0 0 39 37"
            {...props}
        >
            <g fill="none" fillRule="evenodd">
                <g fill="#FFC500">
                    <g>
                        <g>
                            <g>
                                <path
                                    d="M35.107 18.57c-.104.035-.195.1-.262.187-1.554 2.058-4.194 2.979-6.69 2.333-.047-.012-.093-.018-.14-.018-.084 0-.165.02-.24.055-1.897.924-4.062 1.136-6.103.602-.045-.012-.09-.017-.137-.017-.214 0-.408.122-.501.314-1.35 2.793-4.198 4.724-7.508 4.724-2.472 0-4.682-1.073-6.213-2.782-.068-.077-.157-.133-.257-.16-1.877-.56-3.382-1.968-4.066-3.803l-.11-.176C1.092 17.853 0 15.242 0 12.37.01 6.225 4.982 1.254 11.13 1.242h.064c.102.001.202-.027.29-.08C12.73.401 14.166-.002 15.628 0c1.049-.001 2.087.205 3.055.607.068.03.14.045.214.045.069 0 .137-.014.202-.038 2.736-1.054 5.825-.568 8.105 1.274.099.08.221.123.348.123.017 0 .033 0 .05-.002.222-.018.44-.03.663-.03h.003c3.64 0 6.706 2.451 7.646 5.791.037.138.128.256.252.326 1.695 1.018 2.836 2.868 2.834 4.988.002 2.538-1.629 4.69-3.893 5.486zM9.665 9.392H8.497c-.07-.005-.136.03-.169.091l-1.682 2.485V9.537v-.005c-.004-.08-.071-.143-.153-.14H5.488c-.08-.001-.148.063-.149.145v5.38c0 .084.07.153.153.154h1.001c.086 0 .153-.07.153-.155v-2.475l1.55 2.523c.04.06.078.106.17.106h1.315c.068 0 .107-.032.107-.078-.001-.03-.013-.06-.032-.082l-1.825-2.744 1.82-2.583c.02-.032.033-.069.037-.107 0-.046-.039-.084-.123-.084zm4.965 5.502l-1.153-2.155c.556-.22.984-.717.984-1.527v-.1c0-1.352-1-1.788-2.298-1.788-.552 0-1.2.044-1.555.1-.233.036-.342.107-.342.365v5.127c0 .085.068.154.153.155h.994c.084 0 .152-.07.152-.155v-1.94c.146 0 .381.017.543.017l.085-.008 1.022 1.931c.039.077.07.155.153.155h1.17c.083 0 .113-.04.113-.092 0-.03-.007-.059-.02-.085zm3.931-.728c0-.083-.068-.15-.153-.152h-1.553c-.359 0-.488-.121-.488-.518v-.81h1.98c.084 0 .152-.068.153-.153v-.726c0-.084-.07-.152-.153-.151h-1.98v-.697c0-.397.13-.52.488-.52h1.553c.085-.001.153-.07.153-.153v-.71c0-.1-.047-.136-.153-.152-.268-.04-.65-.1-1.476-.1-.985 0-1.864.245-1.864 1.635v2.537c0 1.39.87 1.643 1.858 1.643.826 0 1.214-.061 1.482-.1.106-.015.153-.06.153-.16v-.713zm2.806-4.841c-.527 0-1.095.116-1.27.848L18.96 14.91c0 .013-.008.029-.008.037 0 .077.053.123.13.123h1.07c.077-.004.14-.062.152-.138l.275-1.254h1.568l.276 1.254c.011.077.075.134.152.138h1.07c.075 0 .129-.047.129-.123-.004-.012-.007-.025-.009-.037l-1.138-4.738c-.176-.732-.733-.848-1.26-.848zm6.27.212c0-.084-.078-.144-.16-.144H23.68c-.081-.001-.147.063-.148.144v.788l.001.005c.004.082.072.142.152.139h1.246v4.446c0 .086.068.153.153.155h1.003c.084-.002.152-.07.15-.155V10.47h1.24c.083 0 .16-.06.16-.144v-.788zm1.778 0v-.01c-.003-.077-.069-.138-.146-.134h-1.022c-.086 0-.139.06-.139.144v.784c0 .084.054.153.139.153h1.026c.082-.004.145-.072.142-.153v-.784zm0 1.575v-.01c-.003-.078-.069-.138-.146-.135h-1.022c-.086 0-.139.061-.139.145v3.804c0 .085.054.155.139.155h1.027c.081-.004.145-.073.141-.156v-3.803zm4.243-1.529c0-.1-.047-.135-.153-.153-.267-.037-.651-.1-1.474-.1-.986 0-1.865.246-1.865 1.636v3.948c0 .085.067.153.152.155h.995c.084-.002.152-.07.153-.155v-2.23h1.979c.084 0 .152-.067.152-.152v-.726c0-.084-.069-.151-.152-.152h-1.98v-.688c0-.397.13-.52.49-.52h1.55c.084 0 .153-.068.153-.152v-.711zm-12.399.94c.015-.077.04-.113.108-.113s.084.036.099.113l.466 2.11h-1.14l.467-2.11zm-9.151 1.413c-.084 0-.458-.008-.543-.014v-1.468c.092-.008.49-.021.598-.021.641 0 .999.21.999.678v.1c0 .52-.253.725-1.054.725zm6.614 15.092c1.405-.85 2.58-2.03 3.423-3.441.577.1 1.16.15 1.746.15.194 0 .392-.006.586-.015l-.452 1.097 4.771-1.003L21.138 37l2.136-8.788-5.025 1.003.473-2.187z"
                                    transform="translate(-1904 -12431) translate(-265 11723) translate(343 126) translate(1826 582)"
                                />
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default CreatifSvg;
