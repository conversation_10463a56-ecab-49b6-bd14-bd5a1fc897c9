const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            viewBox="0 0 58 66"
            {...props}
        >
            <defs>
                <path
                    id="fta43wgjza"
                    d="M0 0.076L41.912 0.076 41.912 39.449 0 39.449z"
                />
                <path
                    id="ioe53hb22c"
                    d="M0.015 0.027L36.733 0.027 36.733 46.925 0.015 46.925z"
                />
            </defs>
            <g fill="none" fillRule="evenodd">
                <g transform="translate(-1410 -7625) translate(0 7625) translate(335) translate(1075) translate(.103 26)">
                    <mask id="r4eu48swrb" fill="#fff">
                        <use xlinkHref="#fta43wgjza" />
                    </mask>
                    <path
                        fill="#D3C7AE"
                        d="M21.723 37.383V16.69l8.15-3.52 2.887-1.198 5.763-2.25 1.855-.77v19.692l-18.655 8.74zM1.534 8.951l18.655 7.746v20.686l-18.655-8.74V8.952zm9.509-3.811l1.405-.476L16.49 6.45c.1.044.205.065.307.065.296 0 .577-.173.703-.464.17-.391-.006-.847-.394-1.019l-2.492-1.1 6.692-2.265 17.678 6.19-1.03.428-5.755 2.247-.015.006-1.05.437-4.873-2.033c-.391-.163-.84.024-1.002.418-.162.395.024.848.416 1.011l3.45 1.44-8.174 3.53L2.961 7.87l8.082-2.731zm30.87 2.634c-.01-.321-.215-.603-.516-.709L21.56.12c-.16-.056-.335-.057-.496-.003l-7.457 2.526L.523 7.062c-.303.103-.512.385-.522.708v.026H0v21.835l20.956 9.818 20.956-9.818V7.796h-.001l.001-.022z"
                        mask="url(#r4eu48swrb)"
                    />
                </g>
                <g transform="translate(-1410 -7625) translate(0 7625) translate(335) translate(1075) translate(20.464)">
                    <mask id="i59jg73zkd" fill="#fff">
                        <use xlinkHref="#ioe53hb22c" />
                    </mask>
                    <path
                        fill="#D3C7AE"
                        d="M27.735 45.393c5.802-11.505 2.188-18.818-1.949-22.958-3.278-3.282-8.166-4.081-11.618-4.132v8.906l-1.169-1.169-2.09-2.091-3.711-3.716-5.653-5.658L14.168 1.558v8.64c5.422.09 10.816 2.186 14.943 6.318 8.137 8.144 8.553 21.387-1.376 28.877m2.458-29.96c-3.904-3.908-9.004-6.253-14.495-6.694V1.558c0-.624-.378-1.185-.955-1.419-.186-.076-.381-.112-.574-.112-.406 0-.804.161-1.098.464L.447 13.51c-.582.6-.575 1.557.016 2.149l5.653 5.658 3.712 3.715 2.09 2.092 1.168 1.17c.292.292.683.448 1.082.448.197 0 .395-.038.584-.116.572-.238.945-.796.945-1.415v-7.303c5.069.41 7.79 2.392 9.008 3.61 2.296 2.3 3.736 4.961 4.278 7.912.723 3.935-.156 8.401-2.614 13.274-.32.636-.16 1.409.385 1.865.283.237.632.357.981.357.323 0 .647-.103.92-.309 4.779-3.605 7.63-8.737 8.03-14.452.423-6.079-1.942-12.177-6.492-16.731"
                        mask="url(#i59jg73zkd)"
                    />
                </g>
            </g>
        </svg>
    );
};

export default Icon;
