const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="92"
            height="90"
            viewBox="0 0 92 90"
            {...props}
        >
            <g fill="none" fillRule="evenodd">
                <g>
                    <g>
                        <g>
                            <g fill="#F7F7F7">
                                <g transform="translate(-915 -7134) translate(0 6881) translate(915 253) translate(.5) translate(.5)">
                                    <rect width="90" height="90" rx="45" />
                                </g>
                            </g>
                            <path
                                fill="#232323"
                                d="M49 36L62.5 54 35.5 54z"
                                transform="translate(-915 -7134) translate(0 6881) translate(915 253) rotate(90 49 45)"
                            />
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default Icon;
