const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg" {...props}>
            <g fill="none">
                <path d="M0 0h28v28H0z" />
                <path
                    d="M23.114 8H5.431a.875.875 0 0 0-.87.778l-1.556 14a.875.875 0 0 0 .87.972H24.67a.875.875 0 0 0 .87-.972l-1.556-14a.875.875 0 0 0-.87-.778Z"
                    stroke="#000"
                />
                <path
                    d="M9.625 9.375v-3.5a4.375 4.375 0 1 1 8.75 0v3.5"
                    stroke="#000"
                />
            </g>
        </svg>
    );
};

export default Icon;
