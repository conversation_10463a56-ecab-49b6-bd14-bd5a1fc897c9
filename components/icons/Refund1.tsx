const Refund1Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            width="200px"
            height="154px"
            viewBox="0 0 200 154"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            {...props}
        >
            <title>Group 9 Copy 6</title>
            <defs>
                <rect id="path-1" x="0" y="0" width="200" height="145"></rect>
            </defs>
            <g
                id="SUNUM-3"
                stroke="none"
                strokeWidth="1"
                fill="none"
                fillRule="evenodd"
            >
                <g id="Group-9-Copy-6">
                    <path
                        d="M104.746,14 L104.746,11.606 L96.871,11.606 C97.235,11.298 97.627,10.983 98.047,10.661 C98.467,10.339 98.831,10.066 99.139,9.842 C100.763,8.736 102.044,7.7245 102.982,6.8075 C103.92,5.8905 104.389,4.949 104.389,3.983 C104.389,3.297 104.228,2.681 103.906,2.135 C103.584,1.589 103.08,1.1585 102.394,0.8435 C101.708,0.5285 100.805,0.371 99.685,0.371 C98.901,0.371 98.1975,0.455 97.5745,0.623 C96.9515,0.791 96.43,0.994 96.01,1.232 L96.01,1.232 L95.863,4.214 L96.073,4.214 L97.081,2.261 C97.361,1.729 97.6865,1.3545 98.0575,1.1375 C98.4285,0.9205 98.831,0.812 99.265,0.812 C99.965,0.812 100.49,1.1095 100.84,1.7045 C101.19,2.2995 101.365,3.08 101.365,4.046 C101.365,4.97 101.1165,5.8345 100.6195,6.6395 C100.1225,7.4445 99.37,8.365 98.362,9.401 L98.362,9.401 L96.9865,10.7765 C96.5035,11.2595 96.01,11.746 95.506,12.236 L95.506,12.236 L95.506,14 L104.746,14 Z"
                        id="2"
                        fill="#C2A364"
                        fillRule="nonzero"
                    ></path>
                    <g id="Oval" transform="translate(0.000000, 9.000000)">
                        <mask id="mask-2" fill="white">
                            <use xlinkHref="#path-1"></use>
                        </mask>
                        <g id="Mask"></g>
                        <circle
                            stroke="#C2A364"
                            mask="url(#mask-2)"
                            cx="100"
                            cy="88"
                            r="91"
                        ></circle>
                        <g id="Group-11" mask="url(#mask-2)">
                            <g transform="translate(55.000000, 39.000000)">
                                <rect
                                    id="Rectangle-Copy-2"
                                    stroke="#C2A364"
                                    strokeWidth="1"
                                    fill="#FEFCFC"
                                    fillRule="evenodd"
                                    x="0"
                                    y="0"
                                    width="70"
                                    height="80"
                                    rx="4"
                                ></rect>
                                <line
                                    x1="47"
                                    y1="16"
                                    x2="13"
                                    y2="16"
                                    id="Line-Copy-2"
                                    stroke="#C2A364"
                                    strokeWidth="1.5"
                                    fill="none"
                                    strokeLinecap="round"
                                ></line>
                                <line
                                    x1="47"
                                    y1="24"
                                    x2="13"
                                    y2="24"
                                    id="Line-Copy-5"
                                    stroke="#C2A364"
                                    strokeWidth="1.5"
                                    fill="none"
                                    strokeLinecap="round"
                                ></line>
                                <line
                                    x1="32"
                                    y1="32"
                                    x2="13"
                                    y2="32"
                                    id="Line-Copy-6"
                                    stroke="#C2A364"
                                    strokeWidth="1.5"
                                    fill="none"
                                    strokeLinecap="round"
                                ></line>
                                <rect
                                    id="Rectangle-Copy-5"
                                    stroke="#C2A364"
                                    strokeWidth="1"
                                    fill="#FEFCFC"
                                    fillRule="evenodd"
                                    x="30"
                                    y="41"
                                    width="70"
                                    height="55"
                                    rx="4"
                                ></rect>
                                <path
                                    d="M30,41 L100,41 L100,48 C100,50.209139 98.209139,52 96,52 L34,52 C31.790861,52 30,50.209139 30,48 L30,41 L30,41 Z"
                                    id="Rectangle-Copy-6"
                                    stroke="#C2A364"
                                    strokeWidth="1"
                                    fill="#FFFFFF"
                                    fillRule="evenodd"
                                    transform="translate(65.000000, 46.500000) scale(1, -1) translate(-65.000000, -46.500000) "
                                ></path>
                                <path
                                    d="M36.0014531,47.5472155 C35.6992008,47.5472155 35.4531364,47.3021792 35.4531364,47 C35.4531364,46.6978208 35.6992008,46.4527845 36.0014531,46.4527845 C36.3027367,46.4527845 36.5468636,46.6978208 36.5468636,47 C36.5468636,47.3021792 36.3027367,47.5472155 36.0014531,47.5472155 M36.0014531,45 C34.8980383,45 34,45.8968523 34,47 C34,48.1021792 34.8980383,49 36.0014531,49 C37.1038992,49 38,48.1021792 38,47 C38,45.8968523 37.1038992,45 36.0014531,45"
                                    id="Fill-55"
                                    stroke="none"
                                    fill="#C2A364"
                                    fillRule="evenodd"
                                ></path>
                                <path
                                    d="M43.0014531,47.5472155 C42.6992008,47.5472155 42.4531364,47.3021792 42.4531364,47 C42.4531364,46.6978208 42.6992008,46.4527845 43.0014531,46.4527845 C43.3027367,46.4527845 43.5468636,46.6978208 43.5468636,47 C43.5468636,47.3021792 43.3027367,47.5472155 43.0014531,47.5472155 M43.0014531,45 C41.8980383,45 41,45.8968523 41,47 C41,48.1021792 41.8980383,49 43.0014531,49 C44.1038992,49 45,48.1021792 45,47 C45,45.8968523 44.1038992,45 43.0014531,45"
                                    id="Fill-55-Copy"
                                    stroke="none"
                                    fill="#C2A364"
                                    fillRule="evenodd"
                                ></path>
                                <path
                                    d="M50.0014531,47.5472155 C49.6992008,47.5472155 49.4531364,47.3021792 49.4531364,47 C49.4531364,46.6978208 49.6992008,46.4527845 50.0014531,46.4527845 C50.3027367,46.4527845 50.5468636,46.6978208 50.5468636,47 C50.5468636,47.3021792 50.3027367,47.5472155 50.0014531,47.5472155 M50.0014531,45 C48.8980383,45 48,45.8968523 48,47 C48,48.1021792 48.8980383,49 50.0014531,49 C51.1038992,49 52,48.1021792 52,47 C52,45.8968523 51.1038992,45 50.0014531,45"
                                    id="Fill-55-Copy-2"
                                    stroke="none"
                                    fill="#C2A364"
                                    fillRule="evenodd"
                                ></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default Refund1Icon;
