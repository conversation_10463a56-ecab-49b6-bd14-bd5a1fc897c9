const Refund4Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            width="200px"
            height="154px"
            viewBox="0 0 200 154"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            {...props}
        >
            <title>Group 9 Copy 5</title>
            <defs>
                <rect id="path-1" x="0" y="0" width="200" height="145"></rect>
            </defs>
            <g
                id="SUNUM-3"
                stroke="none"
                strokeWidth="1"
                fill="none"
                fillRule="evenodd"
            >
                <g id="Group-9-Copy-5">
                    <path
                        d="M103.066,14 L103.066,10.598 L105.061,10.598 L105.061,9.044 L103.066,9.044 L103.066,0.518 L101.008,0.518 L94.855,9.401 L94.855,10.598 L100.378,10.598 L100.378,14 L103.066,14 Z M100.378,9.044 L95.779,9.044 L100.378,2.429 L100.378,9.044 Z"
                        id="4"
                        fill="#C2A364"
                        fillRule="nonzero"
                    ></path>
                    <g id="Oval" transform="translate(0.000000, 9.000000)">
                        <mask id="mask-2" fill="white">
                            <use xlinkHref="#path-1"></use>
                        </mask>
                        <g id="Mask"></g>
                        <circle
                            stroke="#C2A364"
                            mask="url(#mask-2)"
                            cx="100"
                            cy="88"
                            r="91"
                        ></circle>
                        <g id="Group-24" mask="url(#mask-2)">
                            <g transform="translate(36.000000, 58.000000)">
                                <g
                                    id="Group-18"
                                    strokeWidth="1"
                                    fill="none"
                                    fillRule="evenodd"
                                    transform="translate(38.000000, 0.000000)"
                                >
                                    <g
                                        id="Group-17"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                    >
                                        <path
                                            d="M40.1710718,47.9130804 L53.8860972,47.9130804 C55.8794679,47.9130804 57.4953144,46.2972339 57.4953144,44.3038632 L57.4953144,11.8209082 L57.4953144,3.88063035 C57.4953144,1.88725968 55.8794679,0.271413135 53.8860972,0.271413135 L38.7273849,0.271413135 L7.71951697,-0.000137588979 C5.71423588,-0.0173242427 4.07889956,1.60357521 4.07889956,3.60921722 L4.07889956,20.4805031"
                                            id="Stroke-1"
                                        ></path>
                                        <rect
                                            id="Rectangle"
                                            x="0"
                                            y="26.1627907"
                                            width="39.7674419"
                                            height="31.3953488"
                                            rx="2.09302326"
                                        ></rect>
                                        <rect
                                            id="Rectangle-Copy-4"
                                            x="0"
                                            y="26.1627907"
                                            width="39.7674419"
                                            height="6.27906977"
                                            rx="2.09302326"
                                        ></rect>
                                        <path
                                            d="M82.0379915,47.9106984 C82.0379915,52.6947158 78.1598876,56.5728197 73.3758702,56.5728197 C68.5918528,56.5728197 64.7137489,52.6947158 64.7137489,47.9106984 C64.7137489,43.1266809 68.5918528,39.248577 73.3758702,39.248577 C78.1598876,39.248577 82.0379915,43.1266809 82.0379915,47.9106984 Z"
                                            id="Stroke-3"
                                        ></path>
                                        <line
                                            x1="51.7205669"
                                            y1="47.9106984"
                                            x2="64.7137489"
                                            y2="47.9106984"
                                            id="Stroke-5"
                                        ></line>
                                        <path
                                            d="M82.0379915,47.9106984 L85.6472087,47.9106984 C87.6405794,47.9106984 89.2564259,46.2948518 89.2564259,44.3014811 L89.2564259,30.5467543 C89.2564259,29.6437282 88.9178814,28.7735459 88.3075627,28.1080062 L74.4485295,12.9889953 C73.7649438,12.2429701 72.7998391,11.8185262 71.7881755,11.8185262 L57.4953144,11.8185262"
                                            id="Stroke-7"
                                        ></path>
                                        <path
                                            d="M89.2564259,29.1427688 L71.2103398,29.1427688 C69.2169692,29.1427688 67.6011226,27.5269223 67.6011226,25.5335516 L67.6011226,16.1495868"
                                            id="Stroke-9"
                                        ></path>
                                    </g>
                                </g>
                                <path
                                    d="M3.79,30.49 C4.15,30.49 4.44,30.22 4.44,29.86 C4.44,29.51 4.15,29.24 3.79,29.24 C3.44,29.24 3.15,29.51 3.15,29.86 C3.15,30.22 3.44,30.49 3.79,30.49 Z M4.42,38 L4.42,31.03 L3.17,31.03 L3.17,38 L4.42,38 Z M6.76,38 L7.38,36.65 L10.38,36.65 L11,38 L12.33,38 L8.89,30.69 L5.44,38 L6.76,38 Z M9.91,35.57 L7.85,35.57 L8.88,33.3 L9.91,35.57 Z M15.8,38 C16.95,38 17.85,37.68 18.49,37.05 C19.13,36.42 19.45,35.58 19.45,34.53 C19.45,33.48 19.13,32.64 18.48,32 C17.84,31.35 16.94,31.03 15.79,31.03 L13.3,31.03 L13.3,38 L15.8,38 Z M15.71,36.87 L14.55,36.87 L14.55,32.16 L15.7,32.16 C17.45,32.16 18.2,33.06 18.2,34.53 C18.2,35.96 17.44,36.87 15.71,36.87 Z M25.14,38 L25.14,36.87 L21.88,36.87 L21.88,34.95 L24.87,34.95 L24.87,33.82 L21.88,33.82 L21.88,32.16 L25.07,32.16 L25.07,31.03 L20.63,31.03 L20.63,38 L25.14,38 Z M1.695,48 L1.695,45.96 L2.575,44.99 L4.745,48 L6.205,48 L3.415,44.1 L6.125,41.03 L4.645,41.03 L1.695,44.37 L1.695,41.03 L0.445,41.03 L0.445,48 L1.695,48 Z M9.835,48.14 C10.865,48.14 11.745,47.79 12.455,47.1 C13.165,46.41 13.525,45.55 13.525,44.53 C13.525,43.51 13.165,42.65 12.445,41.96 C11.735,41.26 10.865,40.91 9.835,40.91 C8.805,40.91 7.935,41.26 7.225,41.96 C6.515,42.65 6.155,43.51 6.155,44.53 C6.155,45.55 6.515,46.41 7.225,47.1 C7.935,47.79 8.805,48.14 9.835,48.14 Z M9.835,47.01 C9.145,47.01 8.555,46.77 8.085,46.3 C7.615,45.83 7.385,45.24 7.385,44.53 C7.385,43.83 7.615,43.24 8.085,42.76 C8.555,42.28 9.145,42.04 9.835,42.04 C10.525,42.04 11.105,42.28 11.575,42.76 C12.045,43.24 12.285,43.83 12.285,44.53 C12.285,45.23 12.045,45.82 11.575,46.3 C11.105,46.77 10.525,47.01 9.835,47.01 Z M17.185,48 C18.335,48 19.235,47.68 19.875,47.05 C20.515,46.42 20.835,45.58 20.835,44.53 C20.835,43.48 20.515,42.64 19.865,42 C19.225,41.35 18.325,41.03 17.175,41.03 L14.685,41.03 L14.685,48 L17.185,48 Z M17.095,46.87 L15.935,46.87 L15.935,42.16 L17.085,42.16 C18.835,42.16 19.585,43.06 19.585,44.53 C19.585,45.96 18.825,46.87 17.095,46.87 Z M24.785,48.11 C25.615,48.11 26.285,47.87 26.815,47.39 C27.345,46.91 27.605,46.22 27.605,45.33 L27.605,41.03 L26.355,41.03 L26.355,45.2 C26.355,46.4 25.755,46.98 24.785,46.98 C23.795,46.98 23.205,46.4 23.205,45.2 L23.205,41.03 L21.955,41.03 L21.955,45.33 C21.955,46.22 22.215,46.91 22.745,47.39 C23.275,47.87 23.955,48.11 24.785,48.11 Z"
                                    id="İADEKODU"
                                    fill="#C2A364"
                                    fillRule="nonzero"
                                ></path>
                                <polygon
                                    id="+"
                                    fill="#C2A364"
                                    fillRule="nonzero"
                                    points="14.765 25.3 14.765 21.25 18.485 21.25 18.485 19.84 14.765 19.84 14.765 15.82 13.235 15.82 13.235 19.84 9.515 19.84 9.515 21.25 13.235 21.25 13.235 25.3"
                                ></polygon>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default Refund4Icon;
