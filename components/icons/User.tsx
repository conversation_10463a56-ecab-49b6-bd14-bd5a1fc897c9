const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="28"
            height="28"
            viewBox="0 0 28 28"
            {...props}
        >
            <g fill="none" fillRule="evenodd">
                <g transform="translate(-1357 -65) translate(0 32) translate(1357 33)">
                    <path d="M0 0H28V28H0z" />
                    <circle cx="14" cy="10.5" r="7" stroke="#000" />
                    <path
                        stroke="#000"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M3.39 23.624C5.578 19.834 9.622 17.5 14 17.5s8.421 2.334 10.61 6.124"
                    />
                </g>
            </g>
        </svg>
    );
};

export default Icon;
