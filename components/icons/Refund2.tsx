const Refund2Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            width="200px"
            height="154px"
            viewBox="0 0 200 154"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            {...props}
        >
            <title>Group 9 Copy 4</title>
            <defs>
                <rect id="path-1" x="0" y="0" width="200" height="145"></rect>
            </defs>
            <g
                id="SUNUM-3"
                stroke="none"
                strokeWidth="1"
                fill="none"
                fillRule="evenodd"
            >
                <g id="Group-9-Copy-4">
                    <path
                        d="M99.748,14.315 C100.812,14.315 101.7255,14.147 102.4885,13.811 C103.2515,13.475 103.8395,13.013 104.2525,12.425 C104.6655,11.837 104.872,11.179 104.872,10.451 C104.872,9.541 104.55,8.764 103.906,8.12 C103.262,7.476 102.226,7.091 100.798,6.965 C102.058,6.727 102.982,6.2965 103.57,5.6735 C104.158,5.0505 104.452,4.396 104.452,3.71 C104.452,2.758 104.0705,1.9635 103.3075,1.3265 C102.5445,0.6895 101.428,0.371 99.958,0.371 C99.272,0.371 98.6525,0.4375 98.0995,0.5705 C97.5465,0.7035 97.046,0.868 96.598,1.064 L96.598,1.064 L96.388,4.151 L96.598,4.172 L97.543,2.408 C97.767,1.988 98.0575,1.624 98.4145,1.316 C98.7715,1.008 99.216,0.854 99.748,0.854 C100.28,0.854 100.707,1.0815 101.029,1.5365 C101.351,1.9915 101.512,2.758 101.512,3.836 C101.512,4.942 101.3475,5.719 101.0185,6.167 C100.6895,6.615 100.154,6.839 99.412,6.839 L99.412,6.839 L98.299,6.839 L98.299,7.259 L99.559,7.259 C100.371,7.259 100.945,7.5005 101.281,7.9835 C101.617,8.4665 101.785,9.261 101.785,10.367 C101.785,11.641 101.5855,12.5475 101.1865,13.0865 C100.7875,13.6255 100.266,13.895 99.622,13.895 C98.53,13.895 97.676,13.342 97.06,12.236 L97.06,12.236 L96.136,10.535 L95.926,10.535 L95.695,13.265 C96.269,13.545 96.8535,13.79 97.4485,14 C98.0435,14.21 98.81,14.315 99.748,14.315 Z"
                        id="3"
                        fill="#C2A364"
                        fillRule="nonzero"
                    ></path>
                    <g id="Oval" transform="translate(0.000000, 9.000000)">
                        <mask id="mask-2" fill="white">
                            <use xlinkHref="#path-1"></use>
                        </mask>
                        <g id="Mask"></g>
                        <circle
                            stroke="#C2A364"
                            mask="url(#mask-2)"
                            cx="100"
                            cy="88"
                            r="91"
                        ></circle>
                        <g id="Group-4" mask="url(#mask-2)">
                            <g transform="translate(51.000000, 39.000000)">
                                <g
                                    id="Group-11-Copy"
                                    transform="translate(23.625165, 0.000000)"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                >
                                    <g
                                        id="Group-5"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                    >
                                        <polyline
                                            id="Stroke-1"
                                            points="0.0765036388 38.4023327 0.0765036388 12.4294049 12.4788077 0.0228314345 50.321 0.0228314345 50.321 38.4023327"
                                        ></polyline>
                                        <polyline
                                            id="Stroke-3"
                                            points="0.0763919138 12.4278081 12.4795339 12.4278081 12.4795339 0.0231956169"
                                        ></polyline>
                                    </g>
                                    <line
                                        x1="12.6000882"
                                        y1="20.614662"
                                        x2="39.3752755"
                                        y2="20.614662"
                                        id="Stroke-7"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                    ></line>
                                    <g
                                        id="Group-2"
                                        transform="translate(12.600088, 31.500220)"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                    >
                                        <line
                                            x1="15.7501102"
                                            y1="0.139518693"
                                            x2="20.4751433"
                                            y2="0.139518693"
                                            id="Stroke-8"
                                        ></line>
                                        <line
                                            x1="23.6251653"
                                            y1="0.139518693"
                                            x2="28.3501984"
                                            y2="0.139518693"
                                            id="Stroke-9"
                                        ></line>
                                        <line
                                            x1="0"
                                            y1="0.139518693"
                                            x2="12.6000882"
                                            y2="0.139518693"
                                            id="Stroke-10"
                                        ></line>
                                    </g>
                                </g>
                                <g
                                    id="Group-5"
                                    transform="translate(48.550375, 37.033736) rotate(90.000000) translate(-48.550375, -37.033736) translate(42.050375, 32.033736)"
                                ></g>
                                <g
                                    id="Group-16"
                                    transform="translate(0.000000, 32.037156)"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                >
                                    <line
                                        x1="48.8253416"
                                        y1="14.2466906"
                                        x2="86.6256061"
                                        y2="4.79662447"
                                        id="Stroke-1"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                    ></line>
                                    <g
                                        id="Group-6"
                                        transform="translate(48.825342, 0.000000)"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                    >
                                        <polyline
                                            id="Stroke-2"
                                            points="37.8002645 22.1217457 37.8002645 47.4101227 0 61.4970212 0 36.2968449"
                                        ></polyline>
                                        <polyline
                                            id="Stroke-4"
                                            points="0 14.2466906 11.0250771 29.9968008 48.8253416 17.3967126 37.8002645 4.79662447 25.1643806 0.662220543"
                                        ></polyline>
                                    </g>
                                    <line
                                        x1="48.8253416"
                                        y1="14.2466906"
                                        x2="11.0250771"
                                        y2="4.79662447"
                                        id="Stroke-7"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                    ></line>
                                    <g
                                        id="Group-12"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                    >
                                        <polyline
                                            id="Stroke-8"
                                            points="11.0250771 22.1217457 11.0250771 47.4101227 48.8253416 61.4970212"
                                        ></polyline>
                                        <polyline
                                            id="Stroke-10"
                                            points="17.8762176 23.3554518 -0.000157501103 17.3971851 11.0249196 4.79709698 23.6251653 0.662220543"
                                        ></polyline>
                                    </g>
                                    <polyline
                                        id="Stroke-13"
                                        stroke="#C2A364"
                                        strokeWidth="1.5"
                                        points="48.8253416 14.2466906 37.8002645 29.9968008 28.5045495 26.8987541"
                                    ></polyline>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default Refund2Icon;
