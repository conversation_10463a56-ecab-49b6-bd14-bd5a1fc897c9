const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 34 30" {...props}>
            <g stroke="#9B9999" fill="none">
                <path d="M26.298 9.555a10.58 10.58 0 0 0-9.173-5.332c-5.819 0-10.554 4.735-10.554 10.554a.28.28 0 0 0 .28.279h1.886l-3.532 3.877-3.55-3.877h1.62c.261 0 .36-.124.36-.28C3.636 7.34 9.689 1.289 17.126 1.289M7.875 19.862a10.56 10.56 0 0 0 9.25 5.468c5.82 0 10.554-4.733 10.554-10.553 0-.146-.099-.277-.333-.277h-1.619l3.55-3.878 3.532 3.878H30.92c-.206 0-.307.138-.307.277 0 7.438-6.05 13.489-13.489 13.489" />
                <path d="m11.883 14.903 2.882 2.94a1.61 1.61 0 0 0 2.425-.146l5.447-7.077" />
            </g>
        </svg>
    );
};

export default Icon;
