const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 102 27" {...props}>
            <g fill="none" fillRule="evenodd">
                <g fill="#D8127D" fillRule="nonzero">
                    <g>
                        <path
                            d="M46.645 6.495h3.52l-1.7 11.647-.383 2.618h-2.056l1.821 2.71-3.59 2.849-.016-.027-4.673-8.148 4.681-.003.583.867.126-.867 1.687-11.646zM36.142 18.14l-3.108 3.839-1.238-3.024.042-.058.12-.164.432-.593h3.752zm44.268 0c-.281.652-.676 1.278-1.21 1.704-1.116.888-2.318 1.342-5.228 1.342-2.91 0-4.044-1.743-4.218-2.876l-.024-.166 3.796-.004c.102.23.252.416.463.518.645.314 1.69.262 2.126-.121.139-.122.274-.245.401-.393l3.894-.004zM31.186 7.227c1.22.906 1.22 2.475 1.08 3.468-.11.794-.812 5.56-1.09 7.444l-4.097.002c.496-.221.813-.44.813-.44l.61-4.008s-.418.026-.872.06l-.275.022-.264.022c-.593.052-1.5.14-2.667.576-1.168.435-1.62 1.341-1.62 2.282 0 .53.18 1.099.674 1.487h.003c.318.25.766.424 1.379.465.838.056 1.647-.207 2.219-.463l4.098-.003-.113.763-.941.732c-1.168.854-2.771 1.324-4.706 1.498-1.934.174-4.426-.331-5.367-2.457-.078-.175-.145-.353-.202-.534l.908-.001h-.908c-.634-2.025.033-4.401 2.433-5.617 2.27-1.15 5.539-1.249 6.36-1.255h.192s.297-1.185-.348-1.76c-.719-.64-1.464-.436-3.085-.174-1.62.261-3.433 1.037-3.433 1.037l.418-2.955s1.098-.784 3.852-1.202c2.753-.418 3.73.104 4.95 1.01zM15.466 6.05c1.412 0 2.728.452 3.303 1.019.575.567.766.907.984 1.89 0 0 .114.715-.035 1.744-.095.666-.681 4.686-1.082 7.435l-3.722.002.225-1.538.078-.534c.325-2.23.65-4.47.685-4.72.07-.514.218-1.586-.14-2.057-.357-.47-1.001-.592-1.49-.558-.488.035-1.499.183-1.769 1.796-.173 1.032-.757 4.936-1.155 7.61l-3.707.001c.407-2.771.997-6.797 1.037-7.115.061-.488.148-1.638-.505-2.056-.654-.418-1.83-.322-2.31.34-.479.662-.584.984-.75 1.995-.104.641-.634 4.271-1.007 6.84L3.73 20.75H.008l.385-2.606 1.856-.002-1.856-.002c.1-.684.214-1.447.328-2.219l.085-.58.7-4.734c.122-.82.785-2.318 1.821-3.146C4.364 6.634 5.69 6.06 7.293 6.06c1.603 0 2.544.392 3.084.689.54.296.89.67.89.67s.443-.435 1.315-.792c.871-.358 1.472-.575 2.884-.575zm-4.118 12.09l-.387 2.61H7.257l.384-2.61h3.707zm7.288 0l-.381 2.61h-3.721l.38-2.608 3.722-.002zm46.256-12.09c1.411 0 2.727.452 3.302 1.019.575.567.767.907.985 1.89l.01.074c.023.206.075.834-.045 1.67l-.716 4.917-.084.58-.283 1.938-3.721.002.224-1.538.078-.534c.325-2.23.651-4.47.685-4.72.07-.514.218-1.586-.139-2.057-.357-.47-1.002-.592-1.49-.558-.488.035-1.5.183-1.769 1.796-.107.64-.373 2.387-.655 4.26l-.094.627-.406 2.722-3.707.002c.407-2.771.997-6.797 1.036-7.115.061-.488.149-1.638-.505-2.056-.653-.418-1.83-.322-2.309.34-.48.662-.584.984-.75 1.995-.104.641-.634 4.271-1.007 6.84l-.378 2.606h-3.72l.384-2.606 1.857-.002-1.857-.002.328-2.219.086-.58.7-4.734c.122-.82.784-2.318 1.821-3.146 1.037-.828 2.361-1.403 3.965-1.403 1.603 0 2.545.392 3.085.689.405.222.702.488.826.607l.055.056.008.007s.444-.435 1.315-.792c.872-.358 1.473-.575 2.885-.575zm-4.118 12.09l-.387 2.61h-3.704l.384-2.61h3.707zm7.287 0l-.38 2.61h-3.722l.381-2.608 3.721-.002zm29.038-12.09c1.411 0 2.727.452 3.302 1.019.575.567.767.907.984 1.89 0 0 .114.715-.034 1.744-.096.666-.682 4.686-1.083 7.435l-3.721.002.224-1.538.078-.534c.325-2.23.651-4.47.685-4.72.07-.514.218-1.586-.139-2.057-.358-.47-1.002-.592-1.49-.558-.489.035-1.499.183-1.77 1.796-.107.64-.372 2.387-.654 4.26l-.094.627-.407 2.722-3.707.002c.407-2.771.998-6.797 1.037-7.115.061-.488.148-1.638-.505-2.056-.654-.418-1.83-.322-2.31.34-.479.662-.584.984-.749 1.995-.105.641-.634 4.271-1.008 6.84l-.377 2.606H81.64l.385-2.606 1.857-.002-1.857-.002 1.113-7.533c.123-.82.785-2.318 1.822-3.146 1.037-.828 2.361-1.403 3.965-1.403 1.603 0 2.544.392 3.084.689.54.296.89.67.89.67s.444-.435 1.315-.792c.871-.358 1.473-.575 2.885-.575zM92.98 18.14l-.387 2.61H88.89l.383-2.61h3.707zm7.288 0l-.381 2.61h-3.721l.38-2.608 3.722-.002zM82.337 6.495s-.31 2.152-.636 4.435l-.123.859c-.286 1.998-.557 3.903-.618 4.36-.073.546-.244 1.284-.55 1.992h-3.894c.272-.316.508-.758.662-1.59.097-.523.39-2.445.692-4.47l.13-.87c.359-2.416.694-4.716.694-4.716h3.643zm-7.485.017l-.633 4.315-.086.592-.754 5.132c-.07.469-.062 1.118.147 1.59H69.73c-.138-1.07-.016-2.118.216-3.577.244-1.534 1.21-8.052 1.21-8.052h3.696zM47.682.535c.444.33.828 1.02.749 1.734-.079.714-.47 1.333-1.098 2.126l-.047.059-.132.164c-1.148 1.421-6.113 7.465-6.252 7.62-.148.165-.157.426-.122.644.035.218.392.689.523.88.097.141 1.968 2.925 2.946 4.379h-4.68c-.66-1.148-1.142-1.987-1.213-2.108l-.007-.014c-.087-.148-.218-.357-.505.01-.11.14-.842 1.044-1.702 2.109l-3.752.003c1.189-1.632 3.757-5.158 3.824-5.259.087-.13.113-.453-.008-.68-.098-.18-1.802-3.095-2.547-4.382l-.164-.286c-.095-.164-.157-.274-.174-.307-.13-.253-.296-.75-.061-1.743.235-.993.854-1.29 1.133-1.333.279-.044.55.061.871.557.323.497 2.963 4.453 3.076 4.628.113.174.296.226.445.017.148-.21 5.516-7.607 5.777-7.964.261-.358.923-1.072 1.56-1.168.636-.096 1.115-.018 1.56.314zm-7.97-.07c1.072 0 1.685 1.217 1.37 2.719-.316 1.501-1.44 2.718-2.513 2.718-1.071 0-1.685-1.217-1.37-2.718.316-1.502 1.44-2.719 2.512-2.719z"
                            transform="translate(-456.000000, -83.000000) translate(456.000000, 83.000000)"
                        />
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default Icon;
