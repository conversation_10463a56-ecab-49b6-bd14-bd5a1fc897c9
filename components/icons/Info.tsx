const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            width="23"
            height="23"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g fill="none" fillRule="evenodd">
                <path d="M0 0h23v23H0z" />
                <circle
                    stroke="#000"
                    strokeWidth="1.2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    cx="11.5"
                    cy="11.5"
                    r="8.625"
                />
                <path
                    stroke="#000"
                    strokeWidth="1.2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M10.781 10.781h.719v5.031h.719"
                />
                <circle
                    fill="#000"
                    fillRule="nonzero"
                    cx="11.32"
                    cy="7.547"
                    r="1.078"
                />
            </g>
        </svg>
    );
};

export default Icon;
