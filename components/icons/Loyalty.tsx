const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 27" {...props}>
            <g stroke="#9B9999" fill="none">
                <path d="M22.825 22.9c-3.522.133-5.939 2.75-13.073 2.75m1.074-10.912c-1.558-1.588-.833-3.589-.576-4.313.331-.935.35-1.727.093-2.192A53 53 0 0 1 9.285 6.22C7.932 3.405 9.727.346 11.45.506c.84.078 1.305 1.051 1.433 1.977.224 1.625.902 3.445 2.025 4.665 1.434 1.558 2.386 2.872 3.5 4.246.692.854 2.315 1.216 3.481 1.368M6.26 12.265a1.898 1.898 0 0 1-1.15 3.106l-2.413.381a1.9 1.9 0 0 1-2.172-1.579c-.163-1.036.49-2.04 1.595-2.438 3.222-1.157 6.436-1.368 6.3-1.368" />
                <path d="M7.49 15.9a1.899 1.899 0 0 1-1.136 3.123l-3.52.541a1.9 1.9 0 0 1-.592-3.751l.454-.06m.662 3.731a1.834 1.834 0 0 0-1.53 2.097 1.84 1.84 0 0 0 2.103 1.53l2.337-.37a1.838 1.838 0 0 0 1.399-2.549" />
                <path d="M4.706 22.987c-.909.144-1.519 1.05-1.366 2.012.151.962 1.012 1.625 1.921 1.481l1.533-.252c.91-.143 1.523-1.039 1.371-2.001a1.8 1.8 0 0 0-.206-.605m18.126.166-1.775.164a1.4 1.4 0 0 1-1.523-1.267l-.895-9.734a1.4 1.4 0 0 1 1.265-1.522l1.776-.163a1.4 1.4 0 0 1 1.522 1.265l.896 9.736c.07.769-.496 1.45-1.266 1.52z" />
            </g>
        </svg>
    );
};

export default Icon;
