const Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="50"
            height="50"
            viewBox="0 0 50 50"
            {...props}
        >
            <g fill="none" fillRule="evenodd">
                <g fill="#414141" fillRule="nonzero">
                    <path
                        d="M35.104 4.65c-2.636-.121-3.429-.143-10.104-.143-6.675 0-7.464.025-10.1.143-6.782.31-9.94 3.521-10.25 10.25-.118 2.636-.146 3.425-.146 10.1s.028 7.464.146 10.104c.31 6.71 3.454 9.939 10.25 10.25 2.632.117 3.425.146 10.1.146 6.679 0 7.468-.025 10.104-.146 6.782-.308 9.935-3.529 10.25-10.25.117-2.636.142-3.429.142-10.104 0-6.675-.025-7.464-.142-10.1-.315-6.725-3.475-9.94-10.25-10.25zm3.242 10.007c-1.657 0-3-1.343-3-3s1.343-3 3-3c1.658 0 3 1.343 3 3 0 1.654-1.342 3-3 3zm11.504 20.65c-.41 9.09-5.471 14.125-14.54 14.543-2.667.121-3.52.15-10.31.15-6.79 0-7.64-.029-10.307-.15C5.607 49.432.568 44.39.15 35.307.029 32.643 0 31.79 0 25c0-6.79.029-7.64.15-10.307C.568 5.607 5.61.568 14.693.153 17.36.03 18.21 0 25 0c6.79 0 7.643.029 10.31.154C44.4.57 49.444 5.625 49.85 14.693 49.971 17.36 50 18.21 50 25c0 6.79-.029 7.643-.15 10.307z"
                        transform="translate(-906 -8032) translate(0 7625) translate(0 365) translate(906 42)"
                    />
                    <path
                        d="M25 12.164c7.09 0 12.84 5.747 12.84 12.836 0 7.09-5.75 12.84-12.84 12.84S12.16 32.092 12.16 25c0-7.09 5.75-12.836 12.84-12.836zm0 4.504c-4.6 0-8.332 3.728-8.332 8.332 0 4.604 3.732 8.332 8.332 8.332 4.6 0 8.332-3.728 8.332-8.332 0-4.604-3.732-8.332-8.332-8.332z"
                        transform="translate(-906 -8032) translate(0 7625) translate(0 365) translate(906 42)"
                    />
                </g>
            </g>
        </svg>
    );
};

export default Icon;
