const Refund3Icon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg
            width="200px"
            height="154px"
            viewBox="0 0 200 154"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            {...props}
        >
            <title>Group 9 Copy 7</title>
            <defs>
                <rect id="path-1" x="0" y="0" width="200" height="145"></rect>
            </defs>
            <g
                id="SUNUM-3"
                stroke="none"
                strokeWidth="1"
                fill="none"
                fillRule="evenodd"
            >
                <g id="Group-9-Copy-7">
                    <path
                        d="M103.213,14 L103.213,13.79 L102.352,13.517 C101.82,13.363 101.554,13.013 101.554,12.467 L101.554,12.467 L101.554,3.668 L101.638,0.665 L101.365,0.476 C100.693,0.882 99.9755,1.239 99.2125,1.547 C98.4495,1.855 97.655,2.093 96.829,2.261 L96.829,2.261 L96.829,2.534 L98.53,2.303 L98.53,12.467 C98.53,12.999 98.264,13.356 97.732,13.538 L97.732,13.538 L96.913,13.79 L96.913,14 L103.213,14 Z"
                        id="1"
                        fill="#C2A364"
                        fillRule="nonzero"
                    ></path>
                    <g id="Oval" transform="translate(0.000000, 9.000000)">
                        <mask id="mask-2" fill="white">
                            <use xlinkHref="#path-1"></use>
                        </mask>
                        <g id="Mask"></g>
                        <circle
                            stroke="#C2A364"
                            mask="url(#mask-2)"
                            cx="100"
                            cy="88"
                            r="91"
                        ></circle>
                        <g id="Group-15" mask="url(#mask-2)">
                            <g transform="translate(52.000000, 46.000000)">
                                <g
                                    id="Group-7"
                                    stroke="none"
                                    strokeWidth="1"
                                    fill="none"
                                    fillRule="evenodd"
                                >
                                    <rect
                                        id="Rectangle"
                                        stroke="#C2A364"
                                        fill="#FEFCFC"
                                        x="0"
                                        y="0"
                                        width="97"
                                        height="80"
                                        rx="4"
                                    ></rect>
                                    <path
                                        d="M0,0 L97,0 L97,10 C97,12.209139 95.209139,14 93,14 L4,14 C1.790861,14 2.705415e-16,12.209139 0,10 L0,0 L0,0 Z"
                                        id="Rectangle-Copy"
                                        stroke="#C2A364"
                                        fill="#FFFFFF"
                                        transform="translate(48.500000, 7.000000) scale(1, -1) translate(-48.500000, -7.000000) "
                                    ></path>
                                    <path
                                        d="M8.00145314,7.5472155 C7.69920078,7.5472155 7.45313635,7.30217918 7.45313635,7 C7.45313635,6.69782082 7.69920078,6.4527845 8.00145314,6.4527845 C8.30273674,6.4527845 8.54686365,6.69782082 8.54686365,7 C8.54686365,7.30217918 8.30273674,7.5472155 8.00145314,7.5472155 M8.00145314,5 C6.89803827,5 6,5.8968523 6,7 C6,8.10217918 6.89803827,9 8.00145314,9 C9.10389925,9 10,8.10217918 10,7 C10,5.8968523 9.10389925,5 8.00145314,5"
                                        id="Fill-55"
                                        fill="#C2A364"
                                    ></path>
                                    <path
                                        d="M15.0014531,7.5472155 C14.6992008,7.5472155 14.4531364,7.30217918 14.4531364,7 C14.4531364,6.69782082 14.6992008,6.4527845 15.0014531,6.4527845 C15.3027367,6.4527845 15.5468636,6.69782082 15.5468636,7 C15.5468636,7.30217918 15.3027367,7.5472155 15.0014531,7.5472155 M15.0014531,5 C13.8980383,5 13,5.8968523 13,7 C13,8.10217918 13.8980383,9 15.0014531,9 C16.1038992,9 17,8.10217918 17,7 C17,5.8968523 16.1038992,5 15.0014531,5"
                                        id="Fill-55-Copy"
                                        fill="#C2A364"
                                    ></path>
                                    <path
                                        d="M22.0014531,7.5472155 C21.6992008,7.5472155 21.4531364,7.30217918 21.4531364,7 C21.4531364,6.69782082 21.6992008,6.4527845 22.0014531,6.4527845 C22.3027367,6.4527845 22.5468636,6.69782082 22.5468636,7 C22.5468636,7.30217918 22.3027367,7.5472155 22.0014531,7.5472155 M22.0014531,5 C20.8980383,5 20,5.8968523 20,7 C20,8.10217918 20.8980383,9 22.0014531,9 C23.1038992,9 24,8.10217918 24,7 C24,5.8968523 23.1038992,5 22.0014531,5"
                                        id="Fill-55-Copy-2"
                                        fill="#C2A364"
                                    ></path>
                                    <rect
                                        id="Rectangle"
                                        stroke="#C2A364"
                                        x="18"
                                        y="44"
                                        width="61"
                                        height="21"
                                        rx="2"
                                    ></rect>
                                    <path
                                        d="M30.165,50.49 C30.525,50.49 30.815,50.22 30.815,49.86 C30.815,49.51 30.525,49.24 30.165,49.24 C29.815,49.24 29.525,49.51 29.525,49.86 C29.525,50.22 29.815,50.49 30.165,50.49 Z M30.795,58 L30.795,51.03 L29.545,51.03 L29.545,58 L30.795,58 Z M33.135,58 L33.755,56.65 L36.755,56.65 L37.375,58 L38.705,58 L35.265,50.69 L31.815,58 L33.135,58 Z M36.285,55.57 L34.225,55.57 L35.255,53.3 L36.285,55.57 Z M42.175,58 C43.325,58 44.225,57.68 44.865,57.05 C45.505,56.42 45.825,55.58 45.825,54.53 C45.825,53.48 45.505,52.64 44.855,52 C44.215,51.35 43.315,51.03 42.165,51.03 L39.675,51.03 L39.675,58 L42.175,58 Z M42.085,56.87 L40.925,56.87 L40.925,52.16 L42.075,52.16 C43.825,52.16 44.575,53.06 44.575,54.53 C44.575,55.96 43.815,56.87 42.085,56.87 Z M51.515,58 L51.515,56.87 L48.255,56.87 L48.255,54.95 L51.245,54.95 L51.245,53.82 L48.255,53.82 L48.255,52.16 L51.445,52.16 L51.445,51.03 L47.005,51.03 L47.005,58 L51.515,58 Z M56.805,58 L56.805,56.87 L54.035,56.87 L54.035,51.03 L52.785,51.03 L52.785,58 L56.805,58 Z M62.405,58 L62.405,56.87 L59.145,56.87 L59.145,54.95 L62.135,54.95 L62.135,53.82 L59.145,53.82 L59.145,52.16 L62.335,52.16 L62.335,51.03 L57.895,51.03 L57.895,58 L62.405,58 Z M64.925,58 L64.925,55.52 L66.165,55.52 L67.655,58 L69.065,58 L67.375,55.22 C68.135,54.9 68.645,54.15 68.645,53.24 C68.645,52.61 68.425,52.09 67.985,51.67 C67.545,51.24 67.015,51.03 66.385,51.03 L63.675,51.03 L63.675,58 L64.925,58 Z M66.255,54.4 L64.925,54.4 L64.925,52.16 L66.355,52.16 C66.955,52.16 67.405,52.62 67.405,53.26 C67.405,53.93 66.935,54.4 66.255,54.4 Z"
                                        id="İADELER"
                                        fill="#C2A364"
                                        fillRule="nonzero"
                                    ></path>
                                </g>
                                <polygon
                                    id="Stroke-1"
                                    stroke="#C2A364"
                                    strokeWidth="1"
                                    fill="#FEFCFC"
                                    fillRule="evenodd"
                                    strokeLinejoin="round"
                                    points="95.5554914 77.7427859 91.4159891 83.6251312 87 64 106.252961 69.4425761 100.233522 73.3341291 108 81.5581584 103.353172 86"
                                ></polygon>
                                <line
                                    x1="26"
                                    y1="26.5"
                                    x2="18"
                                    y2="26.5"
                                    id="Line-Copy"
                                    stroke="#C2A364"
                                    strokeWidth="1.5"
                                    fill="none"
                                    strokeLinecap="round"
                                ></line>
                                <line
                                    x1="22"
                                    y1="30.5"
                                    x2="18"
                                    y2="30.5"
                                    id="Line-Copy-3"
                                    stroke="#C2A364"
                                    strokeWidth="1.5"
                                    fill="none"
                                    strokeLinecap="round"
                                ></line>
                                <line
                                    x1="61.5"
                                    y1="26.5"
                                    x2="35"
                                    y2="26.5"
                                    id="Line-Copy-4"
                                    stroke="#C2A364"
                                    strokeWidth="1.5"
                                    fill="none"
                                    strokeLinecap="round"
                                ></line>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default Refund3Icon;
