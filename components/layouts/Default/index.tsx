import {FC, memo, PropsWithChildren} from 'react';
import {cls, isDev} from '@core/helpers';
import FooterPartial from './partials/Footer';
import HeaderPartial from './partials/Header';
import NavBarPartial from './partials/NavBar';
import TopBarPartial from './partials/TopBar/TopBar';
import {LayoutProvider} from '@core/layouts/Default/context';

// export const dynamic = 'force-dynamic';
// export const revalidate = 0;

type DefaultLayoutProps = {
    pageProps: Record<string, any>;
};

const DefaultLayout: FC<PropsWithChildren<DefaultLayoutProps>> = memo(
    ({pageProps, children}) => {
        return (
            <LayoutProvider>
                <div className="relative w-full xl:flex  xl:flex-col xl:flex-nowrap">
                    <div className="hidden xl:block">
                        <TopBarPartial />
                    </div>
                    <div
                        className={cls('top-0 z-10 hidden bg-white xl:block', {
                            sticky: pageProps.pageType !== 'product'
                        })}
                    >
                        <HeaderPartial />
                        <NavBarPartial />
                    </div>

                    <main className="xl:flex-1 ">{children}</main>

                    <FooterPartial />
                </div>
            </LayoutProvider>
        );
    }
);

if (isDev) {
    DefaultLayout.displayName = 'DefaultLayout';
}

export default DefaultLayout;
