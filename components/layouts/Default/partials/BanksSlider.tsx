import 'swiper/css/pagination';
import {FC, memo, useMemo} from 'react';
import {cls, isDev} from '@core/helpers';
import {Autoplay, Pagination} from 'swiper/modules';
import {UiSlider} from '@core/components/ui';
import {useMobile, useStore, useTrans} from '@core/hooks';
import {AxessCard, MaximumCard, BonusCard} from '@components/icons';

const banks = [
    {
        id: 1,
        icon: <AxessCard className="h-7	w-32" />
    },
    {
        id: 2,
        icon: <MaximumCard className="h-7 w-32" />
    },
    {
        id: 3,
        icon: <BonusCard />
    },
    {
        id: 4,
        icon: <AxessCard className="h-7	w-32" />
    },
    {
        id: 5,
        icon: <MaximumCard className="h-7 w-32" />
    },
    {
        id: 6,
        icon: <BonusCard />
    }
];

const BanksSlider: FC = memo(() => {
    const {isMobile} = useMobile();
    const t = useTrans();
    const {navigation} = useStore();

    const installmentText = useMemo(() => {
        return navigation
            ?.filter(
                navigationItem =>
                    navigationItem.type === 'collection' &&
                    navigationItem.section === 'installment'
            )
            .map(navigationItem => ({
                title: navigationItem.name
            }))?.[0];
    }, [navigation]);

    return (
        <div className="container pb-12">
            <div className="h-px bg-secondary-500 opacity-25 md:hidden"></div>
            <UiSlider
                loop
                autoplay
                slidesPerView={1}
                className="production-swiper-pagination"
                spaceBetween={10}
                modules={[Autoplay, Pagination]}
                pagination={{
                    dynamicBullets: true
                }}
                breakpoints={{
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 5
                    },
                    1200: {
                        slidesPerView: 3,
                        spaceBetween: 5
                    }
                }}
            >
                {banks.map(link => (
                    <UiSlider.Slide key={link.id}>
                        <div className="mx-auto flex h-36 w-full max-w-xs flex-col items-center justify-center px-4 pb-4 pt-12 xl:max-w-none xl:px-8">
                            <div
                                className={cls(
                                    'mb-6 flex h-14 w-full flex-col items-center md:flex-row',
                                    {'justify-center': !installmentText?.title}
                                )}
                            >
                                <div>{link.icon}</div>
                                {installmentText?.title && (
                                    <>
                                        <div className="mx-4 h-12 md:border-l md:border-secondary-300/25"></div>
                                        <p className="text-3xs font-semibold text-brand-clr opacity-50 md:text-xs">
                                            {t(installmentText?.title ?? '')}
                                        </p>
                                    </>
                                )}
                            </div>
                        </div>
                    </UiSlider.Slide>
                ))}
            </UiSlider>
        </div>
    );
});

if (isDev) {
    BanksSlider.displayName = 'BanksSlider';
}

export default BanksSlider;
