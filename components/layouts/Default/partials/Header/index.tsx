import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import HeaderLogoPartial from './Logo';
import HeaderSearchBarPartial from './SearchBar';
import HeaderUserNavPartial from './UserNav';

const HeaderPartial: FC = memo(() => {
    return (
        <header className="hidden h-20 w-full items-center justify-center bg-white xl:container sm:hidden md:hidden lg:flex">
            <div className="flex-1">
                <HeaderSearchBarPartial />
            </div>

            <div className="w-1/4 md:hidden lg:block">
                <HeaderLogoPartial />
            </div>

            <div className="flex-1 ">
                <HeaderUserNavPartial />
            </div>
        </header>
    );
});

if (isDev) {
    HeaderPartial.displayName = 'HeaderPartial';
}

export default HeaderPartial;
