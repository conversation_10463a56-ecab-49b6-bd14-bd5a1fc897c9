import {FC, memo} from 'react';
import siteLogo from '@assets/images/common/site-logo.svg';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';

const HeaderLogoPartial: FC = memo(() => {
    return (
        <div className="flex h-14 items-center justify-center pb-5 pt-2">
            <UiLink
                className="h-logo cursor-pointer"
                href="/"
                aria-label="Logo"
            >
                <UiImage
                    src={siteLogo}
                    alt={storeConfig.title}
                    width={120.9}
                    height={41.6}
                    priority
                />
            </UiLink>
        </div>
    );
});

if (isDev) {
    HeaderLogoPartial.displayName = 'HeaderLogoPartial';
}

export default HeaderLogoPartial;
