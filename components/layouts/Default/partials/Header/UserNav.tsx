import {FC, Fragment, memo, useCallback, useState} from 'react';
import dynamic from 'next/dynamic';
import {useRouter} from 'next/router';
import {signOut} from 'next-auth/react';
import {Cookies} from 'react-cookie-consent';
import {isDev} from '@core/helpers';
import {useCart, useCustomer, useTrans, useUI} from '@core/hooks';
import {User, Favourite, HiOutlineShoppingBag} from '@components/icons';

import {UiDivider, UiLink, UiMenu, UiTransition} from '@core/components/ui';
import React from 'react';

const MiniCart = dynamic(() => import('@components/common/MiniCart/index'));

const HeaderUserNavPartial: FC = memo(() => {
    const [cartHover, setCartHover] = useState(false);
    const [userHover, setUserHover] = useState(false);
    const t = useTrans();
    const router = useRouter();
    const {openSideBar} = useUI();
    const customer = useCustomer();
    const {itemCount: cartItemsCount} = useCart();

    const onOpenMiniCart = useCallback(() => {
        const title = `
            ${t('My Cart')} (${
            cartItemsCount > 1
                ? t('{count} Products', {count: cartItemsCount})
                : t('{count} Product', {count: cartItemsCount})
        })
        `;

        const content = <MiniCart />;

        openSideBar(title, content, 600);
    }, [cartItemsCount, t, openSideBar]);

    return (
        <nav className="flex w-11/12 justify-end gap-x-2 pr-3">
            {customer && (
                <UiMenu as="div" className="relative inline-block">
                    <UiMenu.Button
                        as="div"
                        aria-label={customer.name}
                        className="group relative flex cursor-pointer items-center p-2  text-brand-black outline-none hover:text-secondary-600 focus:outline-none active:outline-none"
                    >
                        <User
                            className="h-7 w-7 cursor-pointer"
                            name={customer.name}
                        />

                        <div className="ml-1.5 !text-[10px] font-bold	uppercase leading-[9px]">
                            {t('My Account')}
                        </div>
                    </UiMenu.Button>

                    <UiTransition
                        as={Fragment}
                        enter="transition"
                        enterFrom="transform opacity-0"
                        enterTo="transform opacity-100"
                        leave="transition"
                        leaveFrom="transform opacity-100"
                        leaveTo="transform opacity-0"
                    >
                        <UiMenu.Items
                            className="absolute right-0 z-dropdown mt-2 min-w-max origin-top-right rounded border border-gray-200 bg-white shadow-sm outline-none"
                            style={{minWidth: '14rem'}}
                        >
                            <div className="px-1 py-1">
                                <div role="group">
                                    <div className="mx-3 my-2 text-sm ">
                                        <div className="font-medium">
                                            {customer.name}
                                        </div>
                                        <div className="text-muted">
                                            {customer.email}
                                        </div>
                                    </div>

                                    <UiDivider
                                        orientation="horizontal"
                                        className="border-gray-200"
                                    />

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-orders"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Orders')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-favorites"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Favorites')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-collections"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Collections')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-reviews"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Reviews')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-addresses"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Addresses')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-account"
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                        >
                                            {t('My Account')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <button
                                            className="flex h-8 w-full flex-shrink-0 cursor-pointer items-center rounded border-0 px-3 text-left text-sm font-normal transition hover:bg-gray-100 focus:outline-none"
                                            onClick={() => {
                                                Cookies.remove('cart-id');
                                                signOut();
                                            }}
                                        >
                                            {t('Sign Out')}
                                        </button>
                                    </UiMenu.Item>
                                </div>
                            </div>
                        </UiMenu.Items>
                    </UiTransition>
                </UiMenu>
            )}
            {!customer ? (
                <>
                    <button
                        onClick={() =>
                            router.push(`/auth?redirect=${router.asPath}`)
                        }
                        className="relative flex cursor-pointer items-center rounded p-2 text-brand-black outline-none hover:text-secondary-600"
                        onMouseEnter={() => setUserHover(true)}
                        onMouseLeave={() => setUserHover(false)}
                    >
                        <User className="h-7 w-7" />

                        <p className="ml-1.5 !text-[10px] font-bold uppercase leading-[9px]">
                            {t('Sign In')}
                        </p>
                    </button>

                    <UiLink
                        href="/account/my-favorites"
                        className="relative flex cursor-pointer items-center rounded p-2 text-brand-black outline-none hover:text-secondary-600"
                    >
                        <Favourite className="h-7 w-7" />

                        <p className="ml-1.5  !text-[10px] font-bold uppercase leading-[9px]">
                            {t('My Favorites')}
                        </p>
                    </UiLink>
                </>
            ) : (
                <UiLink
                    href="/account/my-favorites"
                    className="relative flex cursor-pointer items-center rounded p-2 text-brand-black outline-none hover:text-secondary-600"
                >
                    <Favourite className="h-7 w-7" />

                    <p className="ml-1.5  !text-[10px] font-bold uppercase leading-[9px]">
                        {t('My Favorites')}
                    </p>
                </UiLink>
            )}

            <button
                className="relative flex cursor-pointer items-center rounded p-2 text-brand-black outline-none hover:text-secondary-600 focus:outline-none active:outline-none"
                onClick={onOpenMiniCart}
                onMouseEnter={() => setCartHover(true)}
                onMouseLeave={() => setCartHover(false)}
                style={{position: 'relative'}}
            >
                <HiOutlineShoppingBag className="h-7 w-7" />

                <div className="ml-1.5 !text-[10px] font-bold	uppercase leading-[9px]">
                    {t('My Cart')}
                </div>

                {cartItemsCount >= 0 && (
                    <span className="absolute left-[22px] top-[55%] flex -translate-x-1/2 -translate-y-1/2 transform items-center justify-center text-xs font-medium text-brand-black">
                        {cartItemsCount}
                    </span>
                )}
            </button>
        </nav>
    );
});

if (isDev) {
    HeaderUserNavPartial.displayName = 'HeaderUserNavPartial';
}

export default HeaderUserNavPartial;
