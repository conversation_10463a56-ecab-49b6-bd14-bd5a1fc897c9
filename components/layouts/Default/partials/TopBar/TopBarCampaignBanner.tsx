import {FC, memo, useMemo} from 'react';
import {NavigationItem} from '@core/types';
import {isDev} from '@core/helpers';
import {useMobile, useStore} from '@core/hooks';
import {UiImage} from '@core/components/ui';

interface TopBarCampaignBannerProps {
    forSpecialPage?: boolean;
    items?: NavigationItem[];
}

const TopBarCampaignBanner: FC<TopBarCampaignBannerProps> = memo(
    ({forSpecialPage = false, items}) => {
        const {navigation} = useStore();
        const {isMobile} = useMobile();

        const storyItems = useMemo(() => {
            if (forSpecialPage) {
                return (items ?? []).map(navigationItem => ({
                    title: navigationItem.name,
                    src: (navigationItem.images as string[])[0],
                    link: navigationItem.href
                }));
            }

            return navigation
                ?.filter(
                    navigationItem =>
                        navigationItem.type === 'story' &&
                        navigationItem.section === 'top-campaign-banner' &&
                        Array.isArray(navigationItem.images) &&
                        navigationItem.images.length > 0 &&
                        navigationItem.images[0] &&
                        navigationItem.images[1] &&
                        navigationItem.depth === 0
                )
                .map(navigationItem => ({
                    title: navigationItem.name,
                    src: navigationItem.images as string[],
                    link: navigationItem.href
                }));
        }, [forSpecialPage, items, navigation]);

        return Array.isArray(storyItems) && storyItems.length > 0 ? (
            <div className="aspect-h-2 aspect-w-14 lg:!aspect-h-1">
                {isMobile ? (
                    <UiImage
                        className="object-cover"
                        src={`${storyItems[0].src[1]}`}
                        alt={storyItems[0].title}
                        fill
                        priority
                    />
                ) : (
                    <UiImage
                        className="object-cover"
                        src={`${storyItems[0].src[0]}`}
                        alt={storyItems[0].title}
                        fill
                        priority
                    />
                )}
            </div>
        ) : null;
    }
);

if (isDev) {
    TopBarCampaignBanner.displayName = 'TopBarCampaignBanner';
}

export default TopBarCampaignBanner;
