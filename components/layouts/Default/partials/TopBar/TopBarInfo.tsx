import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiLink} from '@core/components/ui';
import {useTrans} from '@core/hooks';

const linkData = [
    {text: 'CUSTOMER SERVICE', link: '/cozum-merkezi/islem-rehberi'},
    {text: 'ABOUT US', link: '/kurumsal/hakkimizda'}
];

const TopBarInfo: FC = memo(() => {
    const t = useTrans();

    return (
        <div className="flex space-x-1">
            {linkData.map((link, index) => (
                <div key={index} className="flex items-center">
                    <UiLink
                        href={link.link}
                        className="px-2 text-3xs font-bold !leading-8 transition duration-150 ease-in-out hover:text-secondary-100	"
                    >
                        {t(link.text)}
                    </UiLink>
                </div>
            ))}
        </div>
    );
});

if (isDev) {
    TopBarInfo.displayName = 'TopBarInfo';
}

export default TopBarInfo;
