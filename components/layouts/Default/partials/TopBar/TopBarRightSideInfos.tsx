import {FC, memo} from 'react';
import {useTrans} from '@core/hooks';
import {LuPackage} from '@components/icons';
import {isDev} from '@core/helpers';
import Link from 'next/link';

const TopBarRightSideInfos: FC = memo(() => {
    const t = useTrans();
    return (
        <div className="flex justify-end space-x-10">
            <div className="flex items-center space-x-1">
                <Link href="/account/my-orders" className="flex items-center">
                    <LuPackage className="h-3 w-3" />
                    <p className="cursor-pointer pl-0.5 text-3xs font-bold !leading-8 hover:text-secondary-100	">
                        {t('ORDER TRACKING')}
                    </p>
                </Link>
            </div>

            <div className="flex items-center">
                <a
                    href="https://api.whatsapp.com/send?phone=************"
                    target="_blank"
                    className="text-3xs font-bold !leading-8 hover:text-secondary-100	"
                >
                    {t('WHATSAPP SUPPORT LINE')}
                </a>
            </div>
        </div>
    );
});

if (isDev) {
    TopBarRightSideInfos.displayName = 'TopBarRightSideInfos';
}

export default TopBarRightSideInfos;
