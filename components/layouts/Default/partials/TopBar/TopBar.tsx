import {FC, memo, useMemo, useRef, useState, useEffect} from 'react';
import {UiImage, UiSlider} from '@core/components/ui';
import {Autoplay, SliderInterface} from '@core/components/ui/Slider';
import TopBarInfo from './TopBarInfo';
import TopBarRightSideInfos from './TopBarRightSideInfos';
import {useStore, useTrans} from '@core/hooks';
import {isDev} from '@core/helpers';
import TopBarCampaignBanner from './TopBarCampaignBanner';
import CountdownTimer from '@components/common/CountdownTimer';

const TopBarPartial: FC = memo(() => {
    const swiperRef = useRef<SliderInterface>();
    const t = useTrans();

    const {navigation} = useStore();

    const topBarItems = useMemo(() => {
        return navigation
            ?.filter(
                navigationItem =>
                    navigationItem.type === 'story' &&
                    navigationItem.section === 'top-bar' &&
                    navigationItem.depth === 0
            )
            .map(navigationItem => ({
                title: navigationItem.name,
                link: navigationItem.href
            }));
    }, [navigation]);

    const topBarOpportunity = useMemo(() => {
        return navigation
            ?.filter(
                navigationItem =>
                    navigationItem.type === 'story' &&
                    navigationItem.section === 'page-top-opportunity' &&
                    navigationItem.depth === 0
            )
            .map(navigationItem => ({
                title: navigationItem.name,
                link: navigationItem.href,
                images: navigationItem.images,
                description: navigationItem.description
            }))?.[0];
    }, [navigation]);

    // Tarih kontrolü için state'ler
    const [isDateChecked, setIsDateChecked] = useState(false);
    const [isExpired, setIsExpired] = useState(true); // Varsayılan olarak süre geçmiş kabul et

    // Tarih kontrolü
    useEffect(() => {
        if (!topBarOpportunity?.description) {
            setIsDateChecked(true);
            setIsExpired(true);
            return;
        }

        const description = topBarOpportunity.description;
        let targetDate;

        const parts = description.split(' ');
        if (parts.length === 2) {
            const dateParts = parts[0].split('-');
            const timeParts = parts[1].split(':');

            if (dateParts.length === 3 && timeParts.length >= 1) {
                const day = parseInt(dateParts[0], 10);
                const month = parseInt(dateParts[1], 10) - 1;
                const year = parseInt(dateParts[2], 10);
                const hour = parseInt(timeParts[0], 10);
                const minute =
                    timeParts.length > 1 ? parseInt(timeParts[1], 10) : 0;

                targetDate = new Date(year, month, day, hour, minute, 0);
            }
        }

        if (!targetDate || isNaN(targetDate.getTime())) {
            setIsDateChecked(true);
            setIsExpired(true);
            return;
        }

        const now = new Date();
        const expired = targetDate.getTime() <= now.getTime();

        setIsExpired(expired);
        setIsDateChecked(true);
    }, [topBarOpportunity?.description]);

    const showCountdown = isDateChecked && !isExpired;

    return (
        <>
            {topBarOpportunity && (
                <div className="container relative">
                    <UiImage
                        src={
                            topBarOpportunity && topBarOpportunity?.images
                                ? topBarOpportunity?.images?.[0]
                                : '/no-images.png'
                        }
                        alt={topBarOpportunity?.title}
                        className=" "
                        width={1900}
                        height={1000}
                    />
                    {showCountdown && (
                        <div className="absolute right-2 top-1/2 z-50 -translate-y-1/2 transform sm:right-4 md:right-8">
                            <CountdownTimer
                                title={topBarOpportunity?.title || ''}
                                targetDate={
                                    topBarOpportunity?.description || ''
                                }
                                className="sm:px-2 sm:py-1"
                            />
                        </div>
                    )}
                </div>
            )}
            <div className="hidden md:block">
                <TopBarCampaignBanner />
            </div>

            <nav className="hidden bg-secondary-50 lg:block">
                <div className="mx-auto grid h-8 grid-cols-3 items-center justify-between xl:container lg:px-7 xl:flex xl:px-2">
                    <TopBarInfo />
                    <div className="flex items-center justify-center pl-16">
                        <div className="max-w-20 relative hidden h-8 flex-nowrap bg-secondary-50 text-sm lg:flex">
                            <div className="relative flex max-w-md items-center justify-between">
                                <div className="relative w-80 overflow-hidden ">
                                    <UiSlider
                                        modules={[Autoplay]}
                                        autoplay={{delay: 3000}}
                                        spaceBetween={12}
                                        loop
                                        onSwiper={swiper => {
                                            swiperRef.current = swiper;
                                        }}
                                    >
                                        {topBarItems?.map((link, index) => (
                                            <UiSlider.Slide key={index}>
                                                <div className="h-full w-80 cursor-pointer items-center justify-center font-extrabold">
                                                    <p className="cursor-pointer font-dm-serif text-[13.5px] font-bold">
                                                        {t(link.title)}
                                                    </p>
                                                </div>
                                            </UiSlider.Slide>
                                        ))}
                                    </UiSlider>
                                </div>
                            </div>
                        </div>
                    </div>

                    <TopBarRightSideInfos />
                </div>
            </nav>
        </>
    );
});
if (isDev) {
    TopBarPartial.displayName = 'TopBarPartial';
}
export default TopBarPartial;
