import {FC, memo, useRef} from 'react';
import {isDev} from '@core/helpers';
import {SliderInterface} from '@core/components/ui/Slider';
import {UiDivider, UiSlider} from '@core/components/ui';
import {Autoplay} from 'swiper/modules';
import {
    ChevronLeft,
    ChevronRight,
    CircleChecked,
    Delivery,
    PackageReturn
} from '@components/icons';
import {useTrans} from '@core/hooks';

const shops = [
    {
        id: 1,
        icon: <Delivery />,
        header: 'Fast shipping',
        description:
            'Your products will be delivered insured by the cargo company. Nehir is under the responsibility and assurance of your order until it is delivered to you by the cargo company.'
    },
    {
        id: 2,
        icon: <CircleChecked />,
        header: 'Best Price Advantage',
        description:
            'With 100% domestic production, using 18/10 Cr-Ni stainless steel, the most affordable and highest quality products are obtained from the best raw materials.'
    },
    {
        id: 3,
        icon: <PackageReturn />,
        header: 'Free Return and Exchange Guarantee',
        description:
            'All purchases you make through www.nehir.com.tr are guaranteed free of charge, instant returns and exchanges within 14 days.'
    },
    {
        id: 4,
        icon: <Delivery />,
        header: 'Fast shipping',
        description:
            'Your products will be delivered insured by the cargo company. Nehir is under the responsibility and assurance of your order until it is delivered to you by the cargo company.'
    },
    {
        id: 5,
        icon: <CircleChecked />,
        header: 'Best Price Advantage',
        description:
            'With 100% domestic production, using 18/10 Cr-Ni stainless steel, the most affordable and highest quality products are obtained from the best raw materials.'
    },
    {
        id: 6,
        icon: <PackageReturn />,
        header: 'Free Return and Exchange Guarantee',
        description:
            'All purchases you make through www.nehir.com.tr are guaranteed free of charge, instant returns and exchanges within 14 days.'
    }
];

const AdvantageSlider: FC = memo(() => {
    const sliderRef = useRef<SliderInterface>();
    const t = useTrans();

    return (
        <div className="container relative">
            <button
                onClick={() => sliderRef.current?.slidePrev()}
                className="absolute bottom-0 left-0 top-0 z-10 my-auto ml-4 inline-flex h-full w-4 items-center justify-center bg-white md:hidden"
            >
                <ChevronLeft className="h-3 w-3 stroke-current stroke-[4rem] text-brand-clr" />
            </button>
            <button
                onClick={() => sliderRef.current?.slideNext()}
                className="absolute bottom-0 right-0 top-0 z-10 my-auto mr-4 inline-flex h-full w-4 items-center justify-center bg-white md:hidden"
            >
                <ChevronRight className="h-3 w-3 stroke-current stroke-[4rem] text-brand-clr" />
            </button>

            <UiSlider
                loop
                onSwiper={swiper => {
                    sliderRef.current = swiper;
                }}
                modules={[Autoplay]}
                slidesPerView={1}
                spaceBetween={10}
                breakpoints={{
                    1024: {
                        slidesPerView: 3
                    }
                }}
            >
                {shops.map((link, index) => (
                    <UiSlider.Slide key={index}>
                        <div className="flex flex-col items-center justify-start py-24">
                            <div className="h-12 w-12">{link.icon}</div>
                            <div className="mt-2 flex-col px-4 text-center text-brand-clr">
                                <p className="font-dm-serif font-medium">
                                    {t(link.header)}
                                </p>
                                <p className="mt-2 max-w-xs text-xs">
                                    {t(link.description)}
                                </p>
                            </div>
                        </div>
                    </UiSlider.Slide>
                ))}
            </UiSlider>
        </div>
    );
});

if (isDev) {
    AdvantageSlider.displayName = 'AdvantageSlider';
}

export default AdvantageSlider;
