import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {UiButton, UiImage} from '@core/components/ui';
import {useTrans} from '@core/hooks';
import {FacebookIcon, InstagramIcon, TwitterIcon} from '@core/icons/brand';
import banner from '@assets/images/common/social-media/social-media-banner.webp';
import {Instagram} from '@components/icons';

const SocialMedia: FC = memo(() => {
    const t = useTrans();

    return (
        <>
            <div className="bg-search relative h-[320px] w-full overflow-hidden bg-brand-pink">
                <UiImage
                    src={banner}
                    alt={''}
                    width={1400}
                    height={331}
                    className="h-[320px]  w-full object-cover object-right opacity-20 sm:opacity-100 xl:object-contain"
                />

                <div className="z-8 absolute inset-0 flex flex-col items-center justify-center space-y-4 pt-2">
                    <div className="flex flex-col items-center">
                        <Instagram className="h-13 w-13" />
                        <p className="text-sm text-brand-black">nehirmutfak</p>
                    </div>

                    <div className="flex !max-w-md items-center !px-8 text-center leading-5 opacity-80">
                        {t(
                            'You can find out about new arrivals, campaigns and special deals on our social media accounts.'
                        )}
                    </div>

                    <div className="flex items-center justify-center pb-6 ">
                        <a
                            href="https://www.instagram.com/nehirmutfak/"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <UiButton className="flex h-[42px] w-[205px] flex-row justify-center space-x-2 !rounded-sm !border-none bg-secondary-100 text-white transition-all duration-300 ease-in-out hover:bg-black hover:text-white">
                                <p className="pb-0.5">
                                    {t('Follow us on Instagram')}
                                </p>
                                <InstagramIcon className="h-3.5 w-3.5 !text-white" />
                            </UiButton>
                        </a>
                    </div>

                    <div className="flex items-center justify-center space-x-6 pb-3">
                        <div className="hover:text-secondary-100">
                            <a
                                className="flex items-center justify-center gap-2"
                                href="https://www.facebook.com/NehirMutfak"
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <p className="text-[13px]">
                                    {t('Follow us on Facebook')}
                                </p>
                                <FacebookIcon className="h-3 w-3" />
                            </a>
                        </div>
                        <div className="hover:text-secondary-100">
                            <a
                                className="flex items-center justify-center gap-2"
                                href="https://twitter.com/nehirmutfakta"
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <p className="text-[13px]">
                                    {t('Follow us on Twitter')}
                                </p>
                                <TwitterIcon className="h-3 w-3" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
});

if (isDev) {
    SocialMedia.displayName = 'SocialMedia';
}

export default SocialMedia;
