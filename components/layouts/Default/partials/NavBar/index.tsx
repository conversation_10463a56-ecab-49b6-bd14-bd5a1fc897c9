import {FC, Fragment, memo, useEffect, useMemo, useState} from 'react';
import {useRouter} from 'next/router';
import {cls, isDev, orderBy} from '@core/helpers';
import {useElementSize, useStore} from '@core/hooks';
import {UiTransition} from '@core/components/ui';
import useLayout from '@core/layouts/Default/useLayout';
import {LinkType} from './types';
import NavBarSubLinksPartial from './SubLinks';
import NavBarMainLinksPartial from './MainLinks';

const NavBarPartial: FC = memo(() => {
    const router = useRouter();
    const {navigation} = useStore();
    const {ref: containerRef} = useElementSize();
    const {isMegaMenuOpened, isMegaMenuCloseInProgress, setIsMegaMenuOpened} =
        useLayout();
    const [activeLink, setActiveLink] = useState<LinkType | undefined>(
        undefined
    );

    const links = useMemo(() => {
        const orderedItems = orderBy(
            (navigation ?? []).filter(item => item.showInMainMenu),
            ['order'],
            ['asc']
        );

        return (function recurse(items: any) {
            const links: LinkType[] = [];

            for (const item of items) {
                const link: LinkType = {
                    id: item.id,
                    href: item.href,
                    title: item.name,
                    svgIcon: item.svgIcon,
                    images: item.images,
                    type: item.type
                };

                const subItems = (orderedItems as any).filter(
                    (subItem: any) =>
                        subItem.path.startsWith(`${item.path}/`) &&
                        subItem.depth === item.depth + 1 &&
                        subItem.id !== item.id
                );
                if (subItems.length > 0) {
                    link.children = recurse(subItems);
                }

                links.push(link);
            }

            return links;
        })(
            (orderedItems as any)
                .map((item: any) => {
                    // @ts-ignore
                    item.depth = item.path.split('/').length - 1;

                    return item;
                })
                .filter((item: any) => !item.path.includes('/'))
        );
    }, [navigation]);

    useEffect(() => {
        const handleRouteChange = () => {
            if (isMegaMenuOpened) {
                setIsMegaMenuOpened(false);
            }
            setActiveLink(undefined);
        };

        router.events.on('routeChangeStart', handleRouteChange);

        return () => {
            router.events.off('routeChangeStart', handleRouteChange);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const activeLinkHandler = (link: LinkType, event: React.MouseEvent) => {
        setActiveLink(link);
        setLinkElementRect(event.currentTarget.getBoundingClientRect());
        if (Array.isArray(link.children) && link.children.length > 0) {
            setIsMegaMenuOpened(true);
        } else {
            setIsMegaMenuOpened(false);
        }
    };

    const [menuElement, setMenuElement] = useState<Element | null>(null);
    const [linkElementRect, setLinkElementRect] = useState<DOMRect>();
    const [dropdownPosition, setDropdownPosition] = useState({left: 0});

    useEffect(() => {
        if (!linkElementRect || !menuElement || !activeLink?.children) return;

        const containerRect = containerRef.current.getBoundingClientRect();
        const menuRect = menuElement.getBoundingClientRect();

        const distanceToRightEdge = window.innerWidth - linkElementRect.right;
        const distanceToLeftEdge = linkElementRect.left;

        let left = linkElementRect.left - containerRect.left;
        if (distanceToRightEdge < distanceToLeftEdge) {
            left = linkElementRect.right - containerRect.left - menuRect.width;
        }

        setDropdownPosition({left});
    }, [menuElement, linkElementRect, containerRef, activeLink]);

    return (
        <>
            {links.length > 0 ? (
                <nav
                    className={cls(
                        'hidden h-nav-bar  bg-secondary-800 md:block lg:block xl:block ',
                        {
                            'z-[999]':
                                isMegaMenuOpened || isMegaMenuCloseInProgress
                        }
                    )}
                    onMouseLeave={() => {
                        setIsMegaMenuOpened(false);
                        setActiveLink(undefined);
                    }}
                >
                    <div ref={containerRef} className="relative xl:container">
                        <NavBarMainLinksPartial
                            activeLink={activeLink}
                            links={links}
                            onMouseEnter={activeLinkHandler}
                        />

                        <UiTransition
                            as={Fragment}
                            enter="transition duration-150"
                            enterFrom="opacity-0"
                            enterTo="opacity-100"
                            leave="transition duration-150"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                            show={isMegaMenuOpened}
                        >
                            <div>
                                {activeLink &&
                                    Array.isArray(activeLink.children) && (
                                        <div
                                            className="absolute !z-[999] border border-black/20   bg-white p-8 shadow-lg "
                                            ref={setMenuElement}
                                            style={{
                                                left: dropdownPosition.left
                                            }}
                                        >
                                            <NavBarSubLinksPartial
                                                activeLink={activeLink}
                                            />
                                        </div>
                                    )}
                            </div>
                        </UiTransition>
                    </div>
                </nav>
            ) : null}
        </>
    );
});

if (isDev) {
    NavBarPartial.displayName = 'NavBarPartial';
}

export default NavBarPartial;
