import {FC, memo, useMemo, useState} from 'react';
import {cls, isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';
import {LinkType} from './types';
import {ChevronRight} from '@components/icons';

const imageContainerWidths: Record<number, number> = {
    1: 300,
    2: 300,
    3: 630,
    4: 630,
    5: 960,
    6: 960
};

type NavBarSubLinksPartialProps = {
    activeLink: LinkType;
};

const NavBarSubLinksPartial: FC<NavBarSubLinksPartialProps> = memo(props => {
    const {activeLink} = props;

    const subImageLinks = useMemo(
        () =>
            (activeLink.children as LinkType[])
                .filter(
                    subLink =>
                        Array.isArray(subLink.images) &&
                        subLink.images.length > 0 &&
                        subLink.type !== 'story' &&
                        subLink.type !== 'collection' &&
                        subLink.type !== 'slide'
                )
                .slice(0, 6),
        [activeLink]
    );
    const subLinks = useMemo(
        () =>
            (activeLink.children as LinkType[]).filter(
                subLink =>
                    subLink.type !== 'story' &&
                    subLink.type !== 'collection' &&
                    subLink.type !== 'slide' &&
                    subImageLinks.findIndex(sil => sil.id === subLink.id) === -1
            ),
        [activeLink, subImageLinks]
    );
    const imagesContainerWidth = useMemo(() => {
        const imagesLength = activeLink.images?.length || 0;
        return imageContainerWidths[imagesLength] || 0;
    }, [activeLink]);

    const imagesColumnCount = useMemo(() => {
        return Math.ceil((activeLink.images?.length || 0) / 2);
    }, [activeLink.images]);

    const [isCollection, setIsCollection] = useState(false);

    useMemo(() => {
        if (subLinks.length > 0) {
            setIsCollection(
                subLinks[0].href.includes('koleksiyona-gore') &&
                    subLinks[1]?.href.includes('parca-sayisina-gore')
            );
        }
    }, [subLinks]);

    return (
        <div className="flex flex-nowrap items-stretch justify-between px-4 pb-4">
            <div className={`${isCollection ? 'grid grid-cols-2' : 'flex-1'}`}>
                {subLinks.map(subLink => (
                    <div
                        key={subLink.id}
                        className="column-breaks block w-full pb-4"
                    >
                        <div className="ml-4 flex max-w-[260px] flex-col flex-nowrap align-top text-sm">
                            <div className="flex items-center align-middle">
                                {!isCollection && (
                                    <ChevronRight
                                        className="h-3 w-3 stroke-current text-secondary-600"
                                        style={{
                                            strokeWidth: 40
                                        }}
                                    />
                                )}

                                {!isCollection ? (
                                    <UiLink
                                        className="pl-2 text-[13px] font-bold leading-9	transition duration-100 hover:text-secondary-600"
                                        href={subLink.href}
                                    >
                                        {subLink.title}
                                    </UiLink>
                                ) : (
                                    <div className="pl-2 text-xs !font-bold leading-9	 tracking-wide">
                                        {subLink.title}
                                    </div>
                                )}
                            </div>

                            {Array.isArray(subLink.children) &&
                                subLink.children.length > 0 && (
                                    <div
                                        className={`mt-1.5 flex ${
                                            subLink.children.length > 12
                                                ? 'grid grid-cols-2'
                                                : 'flex-col'
                                        } flex-nowrap space-y-1`}
                                    >
                                        {subLink.children.map(subSubLink => {
                                            if (
                                                subSubLink.type === 'story' ||
                                                subSubLink.type ===
                                                    'collection' ||
                                                subSubLink.type === 'slide'
                                            ) {
                                                return null;
                                            }
                                            return (
                                                <div
                                                    key={subSubLink.id}
                                                    className="flex items-center align-middle"
                                                >
                                                    <ChevronRight
                                                        className="h-3 w-3 stroke-current text-secondary-600"
                                                        style={{
                                                            strokeWidth: 40
                                                        }}
                                                    />
                                                    <UiLink
                                                        className="p-2 text-[13px] font-bold transition duration-100 hover:text-secondary-600"
                                                        href={subSubLink.href}
                                                    >
                                                        {subSubLink.title}
                                                    </UiLink>
                                                </div>
                                            );
                                        })}
                                    </div>
                                )}
                        </div>
                    </div>
                ))}
            </div>

            {activeLink?.images && (
                <div
                    className={cls(
                        'mt-6 flex items-start gap-5 space-y-5 pl-10',
                        {
                            'columns-1': imagesColumnCount === 1,
                            'columns-2': imagesColumnCount === 2,
                            'columns-3': imagesColumnCount === 3
                        }
                    )}
                    style={{width: `${imagesContainerWidth}px`}}
                >
                    <div
                        className={cls('block break-inside-avoid', {
                            'row-span-2': activeLink.images
                        })}
                    >
                        <UiImage
                            height={500}
                            width={400}
                            src={`${
                                (activeLink.images as string[])?.[0]
                            }?w=400&q=75`}
                            position="center"
                            fit="cover"
                            alt={activeLink.title}
                        />
                    </div>
                </div>
            )}
        </div>
    );
});

if (isDev) {
    NavBarSubLinksPartial.displayName = 'NavBarSubLinksPartial';
}

export default NavBarSubLinksPartial;
