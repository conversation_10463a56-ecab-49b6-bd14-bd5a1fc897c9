import {FC, memo} from 'react';
import storeConfig from '~/store.config';
import {cls, isDev} from '@core/helpers';
import {UiLink} from '@core/components/ui';
import {LinkType} from './types';
import useLayout from '@core/layouts/Default/useLayout';

type NavBarMainLinksPartialProps = {
    activeLink?: LinkType;
    links: LinkType[];
    onMouseEnter: (
        link: LinkType,
        event: React.MouseEvent<HTMLAnchorElement>
    ) => void;
};

const NavBarMainLinksPartial: FC<NavBarMainLinksPartialProps> = memo(
    ({links, activeLink, onMouseEnter}) => {
        const {isMegaMenuOpened} = useLayout();

        return (
            <div className="flex h-full hover:text-secondary-100">
                {links.map(link => (
                    <UiLink
                        href={link.href}
                        className={cls(
                            'group flex flex-grow items-center justify-center py-[23px] font-bold uppercase text-white hover:text-secondary-600 md:!px-2 lg:px-5',
                            {
                                'border-b-[3.5px] border-transparent': !(
                                    activeLink?.id === link.id
                                ),
                                'border-b-[3.5px] border-secondary-700':
                                    activeLink?.id === link.id
                            }
                        )}
                        style={{
                            height: `calc(${storeConfig.theme.navBarHeight} - 1rem)`
                        }}
                        onMouseEnter={event => onMouseEnter(link, event)}
                        key={link.id}
                    >
                        <div
                            className={cls(
                                'flex flex-grow items-center justify-center whitespace-nowrap !font-bold  tracking-tight md:text-2xs xl:text-xs',
                                {
                                    'hover:z-[1001]':
                                        isMegaMenuOpened &&
                                        activeLink?.id === link.id
                                }
                            )}
                        >
                            <strong>{link.title}</strong>
                        </div>
                    </UiLink>
                ))}
            </div>
        );
    }
);

if (isDev) {
    NavBarMainLinksPartial.displayName = 'NavBarMainLinksPartial';
}

export default NavBarMainLinksPartial;
