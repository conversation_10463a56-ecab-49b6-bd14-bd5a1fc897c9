import {FC, memo, useState} from 'react';
import {useRouter} from 'next/router';
import {isDev} from '@core/helpers';
import {useIOSDevice, useMobile, useTrans} from '@core/hooks';
import {UiImage, UiLink} from '@core/components/ui';
import nehirBank from '@assets/images/common/footer/bank.webp';
import nehirBankMobile from '@assets/images/common/footer/bank-mobile.webp';
import nehirSecurity from '@assets/images/common/footer/security.webp';
import nehirSecurityMobile from '@assets/images/common/footer/security.webp';
import nehirLogo from '@assets/images/common/site-logo.svg';
import entererpLogo from '@assets/images/common/footer/entererp-white.svg';
import {ChevronRightIcon, XIcon} from '@core/icons/outline';
import {FacebookIcon, InstagramIcon, TwitterIcon} from '@core/icons/brand';
import {CreatifSvg, Menu} from '@components/icons';
import AdvantageSlider from './AdvantageSlider';
import BanksSlider from './BanksSlider';
import SocialMedia from './SocialMedia';
import Notify from './Notify';

const footerNavigation = {
    products: [
        {name: 'Woman', href: ''},
        {name: 'Man', href: ''},
        {name: 'Home & Life', href: ''},
        {name: 'Shoes & Bag', href: ''},
        {name: 'Watch & Accessories', href: ''},
        {name: 'Sport & Outdoor', href: ''}
    ],
    customerService: [
        {name: 'İnvidia', href: ''},
        {name: 'The North Face', href: ''},
        {name: 'Cottonbox', href: ''},
        {name: 'Salomon', href: ''},
        {name: 'Benetton', href: ''},
        {name: 'Brity', href: ''},
        {name: 'Lafuma', href: ''}
    ],
    company: [
        {name: 'About Us', href: '/kurumsal/hakkimizda'},
        {name: 'Return, Exchange and Warranty', href: '/refund'},
        {name: 'Contact', href: '/contact'},
        {
            name: 'Frequently Asked Questions',
            href: '/frequently-asked-questions'
        }
    ],
    account: [
        {name: 'Personal Information', href: '/account/my-account'},
        {name: 'My Orders', href: '/account/my-orders'},
        {name: 'My Shopping Cart', href: '/mobile/my-cart'}
    ],
    legal: [
        {
            name: 'Cookie Policy',
            href: 'cozum-merkezi/cerez-politikasi'
        },
        {
            name: 'Contact Person Application Form',
            href: 'cozum-merkezi/ilgili-kisi-basvuru-formu'
        },
        {
            name: 'Online Katalog',
            href: ''
        },
        {
            name: 'Cancellation/Refund Policy',
            href: 'cozum-merkezi/iptaliade-kosullari'
        },
        {name: 'Process Guide', href: 'cozum-merkezi/islem-rehberi'},
        {
            name: 'Personal Data Protection and Processing Policy',
            href: 'cozum-merkezi/kisisel-verilerin-korunmasi-ve-islenmesi-politikasi'
        },
        {name: 'User Guide', href: 'cozum-merkezi/kullanim-kilavuzu'},
        {name: 'Payment Types', href: 'cozum-merkezi/odeme-turleri'},
        {name: 'Membership Agreement', href: 'cozum-merkezi/uyelik-sozlesmesi'}
    ]
};

const FooterPartial: FC = memo(() => {
    const t = useTrans();
    const router = useRouter();
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [openSection, setOpenSection] = useState(null);

    const isAuthRoute = router.route === '/auth';
    const search = router.route === '/search';

    const handleToggle = (index: any) => {
        setOpenSection(prevOpenSection =>
            prevOpenSection === index ? null : index
        );
    };

    return (
        <>
            {!isAuthRoute &&
                !search &&
                router.pathname !== '/price-list-2024-export' && (
                    <>
                        <AdvantageSlider />
                        <BanksSlider />
                        <SocialMedia />
                    </>
                )}

            {router.pathname !== '/price-list-2024-export' && <Notify />}

            <footer className="bg-secondary-200 ">
                <div className="xl:container lg:h-[500px]">
                    <div className="hidden flex-row space-x-4 pt-7 md:flex lg:flex xl:flex">
                        <div className="pl-4">
                            <UiImage
                                src={nehirLogo}
                                alt="Nehir"
                                width={133}
                                height={45}
                                priority
                            />
                        </div>
                        <div className="pl-20">
                            <div>
                                <p className="text-xs font-semibold text-brand-clr">
                                    {t('COMMUNICATION')}
                                </p>

                                <ul
                                    role="list"
                                    className="mt-3.5 space-y-1 text-3xs "
                                >
                                    <li className="mb-5 text-secondary-600">
                                        <p>{t('Factory')}</p>
                                    </li>
                                    <li className="!max-w-sm text-2xs text-brand-clr">
                                        <p className="!max-w-[170px]">
                                            {t(
                                                'We do not have retail sales at our factory address.'
                                            )}
                                        </p>
                                    </li>
                                    <li className="pt-4 text-brand-clr">
                                        <p>
                                            15 Temmuz Mh. 1498. Sokak No.35
                                            Güneşli / İstanbul
                                        </p>

                                        <p className="flex items-center">
                                            <span className="text-secondary-600">
                                                T.
                                            </span>
                                            <a href="tel:02126566550">
                                                {' '}
                                                &nbsp; 0 212 656 65 50
                                            </a>
                                        </p>

                                        <p className="flex items-center">
                                            <span className="text-secondary-600">
                                                F.
                                            </span>
                                            <span>&nbsp; 0 212 651 75 71</span>
                                        </p>

                                        <a href="mailto:<EMAIL>">
                                            <EMAIL>
                                        </a>
                                    </li>
                                    <br />
                                    <li className="text-secondary-600">
                                        <p>{t('Store')}</p>
                                    </li>
                                    <br />
                                    <li className="text-brand-clr">
                                        <p>2417 Sok. Z Blok No.83</p>
                                        <p>İstoç - İkitelli / İstanbul</p>
                                        <p>Güneşli V.D. ************</p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div className="pl-16">
                            <div className="">
                                <p className="text-xs font-semibold uppercase text-brand-clr">
                                    {t('Institutional')}
                                </p>

                                <ul
                                    role="list"
                                    className="mt-4 space-y-4 text-brand-clr xl:mt-5"
                                >
                                    {footerNavigation.company.map(item => (
                                        <li
                                            key={item.name}
                                            className="text-3xs "
                                        >
                                            <UiLink
                                                href={item.href}
                                                className="hover:text-secondary-600"
                                            >
                                                {t(item.name)}
                                            </UiLink>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                        <div className="pl-36">
                            <div>
                                <p className="text-xs font-semibold text-brand-clr">
                                    {t('CUSTOMER SERVICE')}
                                </p>

                                <ul
                                    role="list"
                                    className="mt-4 space-y-4 text-brand-clr xl:mt-6"
                                >
                                    {footerNavigation.legal.map(item => {
                                        return item.name ===
                                            'Online Katalog' ? (
                                            <li
                                                key={item.name}
                                                className="cursor-pointer text-3xs hover:text-secondary-600"
                                                onClick={() =>
                                                    window.open(
                                                        '/files/NEHIR_2025_KATALOG.pdf',
                                                        '_blank'
                                                    )
                                                }
                                            >
                                                {t(item.name)}
                                            </li>
                                        ) : (
                                            <li
                                                key={item.name}
                                                className="text-3xs "
                                            >
                                                <UiLink
                                                    href={item.href}
                                                    className="hover:text-secondary-600"
                                                >
                                                    {t(item.name)}
                                                </UiLink>
                                            </li>
                                        );
                                    })}
                                </ul>

                                <div className="pt-10">
                                    <p className="text-3xs ">
                                        {t('Follow us!')}
                                    </p>

                                    <div className="flex items-center space-x-3 pt-3">
                                        <a
                                            href="https://www.facebook.com/NehirMutfak"
                                            rel="noopener noreferrer"
                                            target="_blank"
                                            title="Facebook"
                                            className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 transition duration-150 ease-in-out hover:border-secondary-600   hover:text-secondary-600"
                                        >
                                            <FacebookIcon className="h-4 w-4" />
                                        </a>
                                        <a
                                            href="https://twitter.com/nehirmutfakta"
                                            rel="noopener noreferrer"
                                            target="_blank"
                                            title="LinkedIn"
                                            className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 transition duration-150 ease-in-out hover:border-secondary-600   hover:text-secondary-600"
                                        >
                                            <TwitterIcon className="h-4 w-4" />
                                        </a>
                                        <a
                                            href="https://www.instagram.com/nehirmutfak/"
                                            rel="noopener noreferrer"
                                            target="_blank"
                                            title="Instagram"
                                            className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 transition duration-150 ease-in-out hover:border-secondary-600   hover:text-secondary-600"
                                        >
                                            <InstagramIcon className="h-4 w-4" />
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className=""></div>
                    </div>

                    <div className="flex h-16 items-center justify-between bg-white px-3 md:hidden lg:hidden xl:hidden">
                        <div>
                            <UiImage
                                src={nehirLogo}
                                alt="Nehir"
                                width={80}
                                priority
                            />
                        </div>

                        <div className="flex flex-col items-center justify-center">
                            <div className="flex cursor-pointer flex-col items-center justify-center">
                                <Menu
                                    className={`h-6 w-6 pb-1 text-secondary-100 ${
                                        isMenuOpen ? 'hidden' : ''
                                    }`}
                                    onClick={() => setIsMenuOpen(true)}
                                />

                                <XIcon
                                    className={`h-8 w-8 text-secondary-100 ${
                                        isMenuOpen ? '' : 'hidden'
                                    }`}
                                    onClick={() => setIsMenuOpen(false)}
                                />
                                <p className="text-xs text-secondary-100">
                                    {t('SUBMENU')}
                                </p>
                            </div>
                        </div>
                    </div>

                    {isMenuOpen && (
                        <div className="border-t border-secondary-400 border-opacity-15 bg-secondary-200 pb-5  md:hidden lg:hidden xl:hidden">
                            <div
                                className="flex items-center justify-between px-5 py-6 "
                                onClick={() => handleToggle(0)}
                            >
                                <div>
                                    <p className="font-hurme text-sm font-semibold text-[#3b3b3b]">
                                        {t('COMMUNICATION')}
                                    </p>
                                </div>
                                <div className="pr-6">
                                    <ChevronRightIcon
                                        style={{strokeWidth: 60}}
                                        className={`ml-0.5 h-2 w-2 stroke-current ${
                                            openSection === 0 ? 'rotate-90' : ''
                                        }`}
                                    />
                                </div>
                            </div>
                            {openSection === 0 && (
                                <div className="grid grid-cols-2 pl-8">
                                    <div className="grid-rows-auto grid gap-4">
                                        <div className="row">
                                            <p className="text-xs text-secondary-600">
                                                {t('Factory')}
                                            </p>
                                        </div>
                                        <div className="row">
                                            <p className="max-w-sm text-2xs ">
                                                {t(
                                                    'We do not have retail sales at our factory address.'
                                                )}
                                            </p>
                                        </div>
                                        <div className="row">
                                            <p className="max-w-sm text-2xs ">
                                                15 Temmuz Mh. 1498. Sokak No.35
                                                Güneşli / İstanbul
                                            </p>
                                            <a
                                                href="tel:0 212 656 65 50"
                                                className="flex items-center text-2xs "
                                            >
                                                <span className="text-secondary-600 ">
                                                    T.
                                                </span>
                                                <span>
                                                    {' '}
                                                    &nbsp; 0 212 656 65 50
                                                </span>
                                            </a>

                                            <p className="flex items-center text-2xs ">
                                                <span className="text-secondary-600">
                                                    F.
                                                </span>
                                                <span>
                                                    &nbsp; 0 212 651 75 71
                                                </span>
                                            </p>

                                            <a
                                                href="mailto:<EMAIL>"
                                                className="text-2xs "
                                            >
                                                <EMAIL>
                                            </a>
                                        </div>
                                    </div>

                                    <div className="grid-rows-auto grid">
                                        <div className="row mt-8">
                                            <p className="text-xs text-secondary-600">
                                                {t('Store')}
                                            </p>
                                        </div>
                                        <div className="row">
                                            <p className="max-w-[250px] pr-14 text-2xs ">
                                                2417 Sok. Z Blok No.83 İstoç -
                                                İkitelli / İstanbul Güneşli V.D.
                                                ************
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            <div
                                className="flex items-center justify-between px-5 py-6 "
                                onClick={() => handleToggle(1)}
                            >
                                <div>
                                    <p className="font-hurme text-sm font-semibold text-[#3b3b3b]">
                                        {t('MY ACCOUNT')}
                                    </p>
                                </div>
                                <div className="pr-6">
                                    <ChevronRightIcon
                                        style={{strokeWidth: 60}}
                                        className={`ml-0.5 h-2 w-2 stroke-current ${
                                            openSection === 1 ? 'rotate-90' : ''
                                        }`}
                                    />
                                </div>
                            </div>
                            {openSection === 1 && (
                                <div className="px-5 py-2">
                                    <ul role="list" className="space-y-6 pl-10">
                                        {footerNavigation.account.map(item => (
                                            <li
                                                key={item.name}
                                                className="text-3xs "
                                            >
                                                <UiLink
                                                    href={item.href}
                                                    className="hover:text-secondary-600"
                                                >
                                                    {t(item.name)}
                                                </UiLink>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}

                            <div
                                className="flex items-center justify-between px-5 py-6 "
                                onClick={() => handleToggle(2)}
                            >
                                <div>
                                    <p className="font-hurme text-sm font-semibold text-[#3b3b3b]">
                                        {t('INSTITUTIONAL')}
                                    </p>
                                </div>
                                <div className="pr-6">
                                    <ChevronRightIcon
                                        style={{strokeWidth: 60}}
                                        className={`ml-0.5 h-2 w-2 stroke-current ${
                                            openSection === 2 ? 'rotate-90' : ''
                                        }`}
                                    />
                                </div>
                            </div>
                            {openSection === 2 && (
                                <div className="px-5 py-2">
                                    <ul role="list" className="space-y-6 pl-10">
                                        {footerNavigation.company.map(item => (
                                            <li
                                                key={item.name}
                                                className="text-3xs "
                                            >
                                                <UiLink
                                                    href={item.href}
                                                    className="hover:text-secondary-600"
                                                >
                                                    {t(item.name)}
                                                </UiLink>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}

                            <div
                                className="flex items-center justify-between px-5 py-6 "
                                onClick={() => handleToggle(3)}
                            >
                                <div>
                                    <p className="font-hurme text-sm font-semibold text-[#3b3b3b]">
                                        {t('CONTACT')}
                                    </p>
                                </div>
                                <div className="pr-6">
                                    <ChevronRightIcon
                                        style={{strokeWidth: 60}}
                                        className={`ml-0.5 h-2 w-2 stroke-current ${
                                            openSection === 3 ? 'rotate-90' : ''
                                        }`}
                                    />
                                </div>
                            </div>
                            {openSection === 3 && (
                                <div className="px-5 py-2">
                                    <ul role="list" className="space-y-6 pl-10">
                                        {footerNavigation.legal.map(item => {
                                            return item.name ===
                                                'Online Katalog' ? (
                                                <li
                                                    key={item.name}
                                                    className="text-3xs "
                                                >
                                                    <a
                                                        download
                                                        href={
                                                            '/files/NEHIR_2025_KATALOG.pdf'
                                                        }
                                                        className="hover:text-secondary-600"
                                                    >
                                                        {t(item.name)}
                                                    </a>
                                                </li>
                                            ) : (
                                                <li
                                                    key={item.name}
                                                    className="text-3xs "
                                                >
                                                    <UiLink
                                                        href={item.href}
                                                        className="hover:text-secondary-600"
                                                    >
                                                        {t(item.name)}
                                                    </UiLink>
                                                </li>
                                            );
                                        })}
                                    </ul>
                                </div>
                            )}

                            <div className="flex flex-col items-center justify-center">
                                <div className="pt-3">
                                    <p className=" flex justify-center text-3xs ">
                                        {t('Follow us!')}
                                    </p>

                                    <div className="flex items-center space-x-3 pt-3">
                                        <a
                                            href="https://www.facebook.com/NehirMutfak"
                                            rel="noopener noreferrer"
                                            target="_blank"
                                            title="Facebook"
                                            className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 transition duration-150 ease-in-out hover:border-secondary-600   hover:text-secondary-600"
                                        >
                                            <FacebookIcon className="h-4 w-4" />
                                        </a>
                                        <a
                                            href="https://twitter.com/nehirmutfakta"
                                            rel="noopener noreferrer"
                                            target="_blank"
                                            title="LinkedIn"
                                            className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 transition duration-150 ease-in-out hover:border-secondary-600   hover:text-secondary-600"
                                        >
                                            <TwitterIcon className="h-4 w-4" />
                                        </a>
                                        <a
                                            href="https://www.instagram.com/nehirmutfak/"
                                            rel="noopener noreferrer"
                                            target="_blank"
                                            title="Instagram"
                                            className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-500 transition duration-150 ease-in-out hover:border-secondary-600   hover:text-secondary-600"
                                        >
                                            <InstagramIcon className="h-4 w-4" />
                                        </a>
                                    </div>
                                </div>

                                <div className="pt-5">
                                    <div className="flex flex-col items-center justify-center space-y-2">
                                        <UiImage
                                            className="w-6/12"
                                            src={nehirSecurityMobile}
                                            alt={'nehir banka'}
                                        />
                                        <UiImage
                                            className="h-20	w-full"
                                            src={nehirBankMobile}
                                            alt={'nehir banka'}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                <div className="container mb-1 hidden pl-2.5 md:flex">
                    <div className="grid h-12 grid-cols-3 text-secondary-500">
                        <div className="place-items-start">
                            <UiImage src={nehirSecurity} alt="" width={169} />
                        </div>
                        <div className="col-span-2 place-items-start pl-5">
                            <UiImage src={nehirBank} alt="" width={650} />
                        </div>
                    </div>
                </div>

                <div className="h-14 bg-secondary-800 pb-2">
                    <div className="container flex h-full items-center justify-between text-secondary-500">
                        <p className="text-2xs">
                            &copy; {new Date().getFullYear()}{' '}
                            {t('NEHIR All Rights Reserved.')}
                        </p>
                        <div className="flex flex-col gap-0 pb-1 lg:flex-row lg:gap-4 lg:pb-0">
                            <a
                                target="_blank"
                                href="https://www.kreatif.net/"
                                className="flex items-center gap-1.5 md:gap-3"
                            >
                                <p className="whitespace-nowrap text-2xs font-semibold md:text-sm">
                                    Designed by
                                </p>
                                <CreatifSvg className="w-6 lg:w-7" />
                            </a>
                            <a
                                target="_blank"
                                href="https://entererp.com"
                                className="flex items-center gap-1.5 md:gap-3"
                            >
                                <p className="whitespace-nowrap text-2xs font-semibold md:text-sm">
                                    Developed by
                                </p>
                                <UiImage
                                    className="w-14 md:w-20"
                                    src={entererpLogo}
                                    alt=""
                                />
                            </a>
                        </div>
                    </div>
                </div>
            </footer>
        </>
    );
});

if (isDev) {
    FooterPartial.displayName = 'FooterPartial';
}

export default FooterPartial;
