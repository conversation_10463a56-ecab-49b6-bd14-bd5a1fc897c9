import {FC, memo, useCallback, useRef} from 'react';
import {useForm} from 'react-hook-form';
import {isDev, jsonRequest, regexp} from '@core/helpers';
import {
    UiButton,
    UiCheckbox,
    UiForm,
    UiImage,
    UiInput,
    notification
} from '@core/components/ui';
import {useTrans, useUI} from '@core/hooks';
import {IoStar} from '@components/icons';

import banner from '@assets/images/common/notify/ribbon.jpg';

interface FormData {
    email: string;
    agreeToTerms: boolean;
}

const NotifyMiniCart: FC = memo(() => {
    const t = useTrans();
    const {openSideBar} = useUI();
    const {
        register,
        reset,
        formState: {isSubmitting},
        handleSubmit
    } = useForm();
    const inProgressRef = useRef(false);
    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;

            try {
                await jsonRequest({
                    url: '/api/customers/subscribers',
                    method: 'POST',
                    data: {
                        email: data.email,
                        name: data.name,
                        source: ['e-commerce']
                    }
                });

                reset();

                notification({
                    title: t('Welcome to our newsletter!'),
                    description: t(
                        "Congratulations! You've successfully subscribed to our newsletter. Get ready to receive the latest updates, promotions, and news straight to your inbox."
                    ),
                    status: 'success'
                });
            } catch (error: any) {
                notification({
                    title: t('Oops, something went wrong.'),
                    description: t(error.message),
                    status: 'error'
                });
            }

            inProgressRef.current = false;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    const onOpenMiniCart = useCallback(() => {
        openSideBar(
            t('Commercial Electronic Message Explicit Consent Text'),
            <div className="container text-sm text-brand-black">
                {t(
                    'This text, under Law 6563 on the Regulation of Electronic Commerce and the Regulation on Commercial Communication and Commercial Electronic Messages, contains my consent to the announcing of general/special opportunities about services offered by Nehir Mutfak Eşyaları San. ve Tic. A.Ş., conducting of satisfaction surveys regarding the services offered, being informed about current developments, sending of congratulatory messages, sharing of presentations, bulletins etc., sending of commercial electronic messages and other messages in accordance with the relevant legislation for promotion and advertisement, receipt of my contact information, identity information, marketing information for these purposes in order to carry out the goods / service sales and advertisement / campaign / promotion processes and sending of messages via channels of my choice, changing of my communication preferences, halting of the incoming communications by rejecting the transactions in the messages sent to me without giving any reason, and sending of commercial electronic messages and other messages to me via SMS, automatic search, telephone call, social media and online advertising networks, e-mail/mail and other electronic communication tools / channels as per the laws.'
                )}
            </div>
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <div
            className="bg-top-right relative w-full bg-cover bg-no-repeat"
            style={{
                backgroundImage: `url('${banner.src}')`
            }}
        >
            <div className="container grid place-items-center py-4 md:py-8">
                <div className="z-[5] mb-2 flex max-w-3xl items-start gap-2 md:mb-6 md:items-center">
                    <IoStar />
                    <p className="text-sm text-white md:text-xl">
                        {t(
                            'To get the latest on our new products and campaigns;'
                        )}
                    </p>
                </div>
                <div className="flex items-start justify-center">
                    <UiForm
                        onSubmit={handleSubmit(onSubmit)}
                        className="w-full max-w-3xl"
                    >
                        <UiInput.Group className="flex items-center border-b border-secondary-900 pb-1">
                            <UiInput
                                {...register('email', {
                                    pattern: regexp.email,
                                    required: true
                                })}
                                className="xs:placeholder:text-sm border-0 bg-transparent !pl-0 !text-white placeholder:text-xs placeholder:!text-white hover:!border-secondary-900 focus:!border-secondary-100 focus:!ring-0 sm:placeholder:text-sm"
                                placeholder={t('E-posta adresini yazınız')}
                            />

                            <UiInput.RightElement className="pr-14">
                                <UiButton
                                    type="submit"
                                    className="w-28 !rounded-sm !border-none bg-white text-[10px] font-bold text-secondary-100 transition duration-300  hover:border-secondary-100 hover:bg-secondary-100 hover:text-white focus:border-secondary-100 md:w-36 md:text-sm"
                                    size="xs"
                                >
                                    {t('SEND')}
                                </UiButton>
                            </UiInput.RightElement>
                        </UiInput.Group>
                        <div className="mt-2 flex items-center gap-2">
                            <UiCheckbox
                                {...register('agreeToTerms')}
                                className="h-5 w-5 rounded-none border-gray-300 checked:hover:bg-secondary-100 focus:border-gray-300 focus:ring-0"
                            />
                            <p
                                onClick={onOpenMiniCart}
                                className="text-[9px] text-white md:text-xs"
                            >
                                {t(
                                    'I would like to know about special campaigns and new arrivals. In this context, I consent to the sharing of my personal data with domestic and foreign business partners for this purpose.'
                                )}
                            </p>
                        </div>
                    </UiForm>
                </div>
            </div>
        </div>
    );
});

if (isDev) {
    NotifyMiniCart.displayName = 'NotifyMiniCart';
}

export default NotifyMiniCart;
