import {FC, memo, PropsWithChildren, useEffect, useState} from 'react';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {useCustomer, useMobile} from '@core/hooks';
import FooterPartial from './partials/Footer';
import HeaderPartial from './partials/Header';
import SideBarPartial from './partials/SideBar';

type AccountLayoutProps = {
    pageProps: Record<string, any>;
};

const AccountLayout: FC<PropsWithChildren<AccountLayoutProps>> = memo(
    ({pageProps, children}) => {
        const customer = useCustomer();
        const {isMobile} = useMobile();
        const [minHeight, setMinHeight] = useState(
            () => `calc(100vh - ${storeConfig.theme.accountHeaderHeight} - 1px)`
        );

        useEffect(() => {
            if (isMobile) {
                setMinHeight('auto');
            } else {
                setMinHeight(
                    `calc(100vh - ${storeConfig.theme.accountHeaderHeight} - 1px)`
                );
            }
        }, [isMobile]);

        return (
            <div className="relative w-full bg-white xl:h-screen">
                <HeaderPartial />

                <main
                    className="relative xl:flex xl:flex-col"
                    style={{minHeight}}
                >
                    <div className="xl:flex-1">
                        {customer ? (
                            <div className="container">
                                <div className="xl:grid xl:grid-cols-4 xl:gap-x-8 xl:py-12">
                                    <SideBarPartial pageProps={pageProps} />

                                    <div className="xl:col-span-3 xl:rounded xl:border xl:border-gray-200 xl:p-8 xl:shadow-sm">
                                        {children}
                                    </div>
                                </div>
                            </div>
                        ) : (
                            children
                        )}
                    </div>

                    <FooterPartial />
                </main>
            </div>
        );
    }
);

if (isDev) {
    AccountLayout.displayName = 'AccountLayout';
}

export default AccountLayout;
