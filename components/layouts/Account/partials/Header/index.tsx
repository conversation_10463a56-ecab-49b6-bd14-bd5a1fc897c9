import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import HeaderLogoPartial from './Logo';
import HeaderUserNavPartial from './UserNav';

const HeaderPartial: FC = memo(() => {
    return (
        <header className="hidden h-account-header w-full border-b border-gray-200 xl:block">
            <div className="container flex h-full items-stretch">
                <HeaderLogoPartial />
                <HeaderUserNavPartial />
            </div>
        </header>
    );
});

if (isDev) {
    HeaderPartial.displayName = 'HeaderPartial';
}

export default HeaderPartial;
