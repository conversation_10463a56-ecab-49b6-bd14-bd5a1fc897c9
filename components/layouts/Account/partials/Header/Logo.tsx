import {FC, memo} from 'react';
import siteLogo from '@assets/images/common/site-logo.png';
import storeConfig from '~/store.config';
import {isDev} from '@core/helpers';
import {UiImage, UiLink} from '@core/components/ui';

const HeaderLogoPartial: FC = memo(() => {
    return (
        <div className="flex flex-1 items-center">
            <UiLink
                className="h-account-logo cursor-pointer"
                href="/"
                aria-label="Logo"
            >
                <UiImage
                    src={siteLogo}
                    alt={storeConfig.title}
                    width={120}
                    priority={true}
                />
            </UiLink>
        </div>
    );
});

if (isDev) {
    HeaderLogoPartial.displayName = 'HeaderLogoPartial';
}

export default HeaderLogoPartial;
