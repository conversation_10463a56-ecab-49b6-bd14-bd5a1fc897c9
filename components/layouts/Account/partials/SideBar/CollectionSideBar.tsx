import {FC, memo, useMemo} from 'react';
import storeConfig from '~/store.config';
import {Collection} from '@core/types';
import {cls, isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiDivider, UiLink, UiStickyBox} from '@core/components/ui';
import {
    ArrowLeftIcon,
    BellIcon,
    BookmarkIcon,
    ClockIcon,
    EnvelopeIcon
} from '@core/icons/outline';

type CollectionSideBarProps = {
    collection: Collection;
    collections: Collection[];
};

const CollectionSideBar: FC<CollectionSideBarProps> = memo(props => {
    const {collection, collections} = props;
    const t = useTrans();

    const buyLaterCollection = useMemo(
        () => collections.find(collection => !!collection.isBuyLater),
        [collections]
    );
    const alarmCollection = useMemo(
        () => collections.find(collection => !!collection.isAlarm),
        [collections]
    );
    const notifyCustomerCollection = useMemo(
        () => collections.find(collection => !!collection.isNotifyCustomer),
        [collections]
    );
    const myCollections = useMemo(
        () =>
            collections.filter(
                collection =>
                    !collection.isBuyLater &&
                    !collection.isAlarm &&
                    !collection.isNotifyCustomer
            ),
        [collections]
    );

    return (
        <div className="hidden select-none xl:block">
            <UiStickyBox>
                <div className="rounded border border-gray-200 p-8 pb-6 shadow-sm">
                    <div className="flex items-center">
                        <UiLink
                            className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-400 outline-none transition hover:bg-gray-200 hover:text-gray-500"
                            href="/account/my-collections"
                        >
                            <ArrowLeftIcon
                                className="h-5 w-5"
                                aria-hidden="true"
                            />
                        </UiLink>

                        <div className="ml-5 flex flex-col justify-center">
                            <div className="mb-0.5 text-lg font-medium">
                                {t('My Collections')}
                            </div>
                            <div className="text-sm text-muted">
                                {t('{count} collection(s)', {
                                    count: collections.length
                                })}
                            </div>
                        </div>
                    </div>

                    <UiDivider
                        orientation="horizontal"
                        className="my-6 border-gray-200"
                    />

                    <div className="mb-2 text-muted">{storeConfig.title}</div>
                    <div className="-mx-3 space-y-1">
                        <UiLink
                            href={`/account/my-collections/${
                                buyLaterCollection!.id
                            }`}
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        collection.id === buyLaterCollection!.id
                                }
                            )}
                        >
                            <ClockIcon className="mr-2 h-4 w-4" />
                            {t(buyLaterCollection!.name)}
                        </UiLink>
                        <UiLink
                            href={`/account/my-collections/${
                                alarmCollection!.id
                            }`}
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        collection.id === alarmCollection!.id
                                }
                            )}
                        >
                            <BellIcon className="mr-2 h-4 w-4" />
                            {t(alarmCollection!.name)}
                        </UiLink>
                        <UiLink
                            href={`/account/my-collections/${
                                notifyCustomerCollection!.id
                            }`}
                            className={cls(
                                'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                {
                                    'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                        collection.id ===
                                        notifyCustomerCollection!.id
                                }
                            )}
                        >
                            <EnvelopeIcon className="mr-2 h-4 w-4" />
                            {t(notifyCustomerCollection!.name)}
                        </UiLink>
                    </div>

                    {myCollections.length > 0 && (
                        <>
                            <div className="mb-2 mt-6 text-muted">
                                {t('My Collections')}
                            </div>
                            <div className="-mx-3 space-y-1">
                                {myCollections.map(c => (
                                    <UiLink
                                        key={c.id}
                                        href={`/account/my-collections/${c.id}`}
                                        className={cls(
                                            'flex cursor-pointer items-center rounded px-3 py-2 font-medium transition hover:bg-gray-100 focus:outline-none',
                                            {
                                                'bg-primary-50 text-primary-600 hover:bg-primary-100':
                                                    collection.id === c.id
                                            }
                                        )}
                                    >
                                        <BookmarkIcon className="mr-2 h-4 w-4" />
                                        {t(c.name)}
                                    </UiLink>
                                ))}
                            </div>
                        </>
                    )}
                </div>
            </UiStickyBox>
        </div>
    );
});

if (isDev) {
    CollectionSideBar.displayName = 'CollectionSideBar';
}

export default CollectionSideBar;
