import {FC, memo} from 'react';
import {isDev} from '@core/helpers';
import {useTrans} from '@core/hooks';

const FooterPartial: FC = memo(() => {
    const t = useTrans();

    return (
        <footer className="border-gary-200 hidden h-account-footer w-full border-t bg-white xl:block">
            <div className="container">
                <div className="flex items-center justify-between py-4">
                    <div className="text-center text-sm text-gray-700">
                        &copy; {new Date().getFullYear()}{' '}
                        {t('All Rights Reserved')}
                    </div>

                    <div className="flex items-center justify-end text-sm text-gray-700">
                        <span className="mr-1 inline-block">Powered by</span>
                        <a
                            className="text-primary-600 hover:underline"
                            href="https://entererp.com"
                            target="_blank"
                            rel="noreferrer"
                        >
                            EnterERP
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    );
});

if (isDev) {
    FooterPartial.displayName = 'FooterPartial';
}

export default FooterPartial;
