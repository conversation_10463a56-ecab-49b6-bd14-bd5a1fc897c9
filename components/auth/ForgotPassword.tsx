import {FC, memo, useCallback, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {useForm} from 'react-hook-form';
import {isDev, jsonRequest, regexp} from '@core/helpers';
import {useStore, useTrans} from '@core/hooks';
import {UiAlert, UiButton, UiForm} from '@core/components/ui';
import {notification} from '@core/components/ui';

type ForgotPasswordProps = {
    redirect?: string;
};

const ForgotPassword: FC<ForgotPasswordProps> = memo(({redirect}) => {
    const {
        register,
        formState: {errors},
        handleSubmit
    } = useForm();
    const router = useRouter();
    const t = useTrans();
    const {locale} = useStore();
    const [isLoading, setIsLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const inProgressRef = useRef(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;
            setIsLoading(true);
            setErrorMessage('');

            try {
                await jsonRequest({
                    url: '/api/auth/send-reset-password-link',
                    method: 'POST',
                    data: {
                        email: data.email,
                        locale
                    }
                });

                notification({
                    title: t('Reset Your Password'),
                    description: t(
                        'A password reset link has been sent to your email address.'
                    ),
                    status: 'success'
                });

                if (typeof redirect === 'string') {
                    await router.push(
                        !!redirect && redirect !== '' ? redirect : '/'
                    );
                    return;
                } else {
                    await router.push('/');
                }
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            inProgressRef.current = false;
            setIsLoading(false);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [router, redirect]
    );

    return (
        <div className="flex w-full flex-col">
            <UiForm
                onSubmit={handleSubmit(onSubmit)}
                className="w-full items-start justify-start xl:flex xl:gap-x-5 xl:pr-10 "
            >
                <div className="custom-form-input flex pb-5 xl:w-[45%] xl:pb-0">
                    <UiForm.Field
                        label={t('Email address')}
                        autoCorrect="off"
                        autoCapitalize="none"
                        rightElement={
                            <p className="mb-4 ml-5 text-secondary-100">*</p>
                        }
                        error={
                            errors.email && errors.email.type === 'required'
                                ? t('Email address is required')
                                : errors.email &&
                                  errors.email.type === 'pattern'
                                ? t('Email address is invalid')
                                : undefined
                        }
                        {...register('email', {
                            required: true,
                            pattern: regexp.email
                        })}
                    />
                </div>

                <UiButton
                    className="h-12 w-full bg-secondary-100 text-base text-white hover:bg-secondary-100 hover:text-white focus:border-secondary-100 focus:ring-secondary-100 xl:w-2/6	"
                    type="submit"
                    loading={isLoading}
                >
                    {t('Continue')}
                </UiButton>
            </UiForm>
            {!!errorMessage && (
                <UiAlert className="my-4 mr-10 xl:w-[43%]" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}
        </div>
    );
});

if (isDev) {
    ForgotPassword.displayName = 'ForgotPassword';
}

export default ForgotPassword;
