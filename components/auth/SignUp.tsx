import {
    FC,
    memo,
    Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>,
    useCallback,
    useEffect,
    useRef,
    useState
} from 'react';
import {useRouter} from 'next/router';
import {signIn} from 'next-auth/react';
import {useForm} from 'react-hook-form';
import {isDev, jsonRequest, regexp} from '@core/helpers';
import {useTrans, useUI} from '@core/hooks';
import {UiAlert, UiButton, UiCheckbox, UiForm} from '@core/components/ui';

// eslint-disable-next-line react/display-name
const StaticContent: FC<{content?: string}> = ({content = ''}) => (
    <div className="w-full px-6 pb-6 pt-6 xl:pt-0">
        <div
            className="prose"
            dangerouslySetInnerHTML={{
                __html: content
            }}
        />
    </div>
);

type SignUpProps = {
    termsOfMembershipText?: string;
    clarificationText?: string;
    redirect?: string;
};

const SignUp: FC<SignUpProps> = memo(props => {
    const {termsOfMembershipText, clarificationText, redirect} = props;
    const {
        register,
        formState: {errors},
        handleSubmit,
        watch
    } = useForm();
    const router = useRouter();
    const t = useTrans();
    const {openSideBar} = useUI();
    const password: string = watch('password', '');
    const [isLoading, setIsLoading] = useState(false);
    const [isPasswordShown, setIsPasswordShown] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const inProgressRef = useRef(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;
            setIsLoading(true);
            setErrorMessage('');

            try {
                await jsonRequest({
                    url: '/api/auth/signup',
                    method: 'POST',
                    data: {
                        ...data
                    }
                });

                const result: any = await signIn('credentials', {
                    redirect: false,
                    email: data.email,
                    password: data.password
                });

                if (!!result?.error) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new Error(result.error);
                }

                if (typeof redirect === 'string') {
                    await router.push(
                        !!redirect && redirect !== '' ? redirect : '/'
                    );
                    return;
                }
            } catch (error: any) {
                if (error.code === 'already_exists') {
                    setErrorMessage(
                        t(
                            'The e-mail address you entered is in use by another customer. Please try again with a different e-mail address.'
                        )
                    );
                } else if (error.code === 'clarification_text_not_accepted') {
                    setErrorMessage(
                        t(
                            'The clarification text for the processing of personal data must be accepted.'
                        )
                    );
                } else {
                    setErrorMessage(error.message);
                }
            }

            inProgressRef.current = false;
            setIsLoading(false);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [router, redirect]
    );

    const onClarificationTextClick: MouseEventHandler<HTMLDivElement> =
        useCallback(
            e => {
                // @ts-ignore
                const id = e.target.id;

                if (id === 'clarificationText') {
                    e.preventDefault();

                    openSideBar(
                        t('Clarification Text'),
                        <StaticContent content={clarificationText} />,
                        'large'
                    );
                }
            },
            [clarificationText, t, openSideBar]
        );
    const onMembershipAgreementTextClick: MouseEventHandler<HTMLDivElement> =
        useCallback(
            e => {
                // @ts-ignore
                const id = e.target.id;

                if (id === 'termsOfMembershipText') {
                    e.preventDefault();

                    openSideBar(
                        t('Membership Agreement'),
                        <StaticContent content={termsOfMembershipText} />,
                        'large'
                    );
                }
            },
            [termsOfMembershipText, t, openSideBar]
        );

    useEffect(() => {
        if (errors.acceptedTermsOfMembership) {
            setErrorMessage(t('Membership Conditions must be accepted.'));
        }
    }, [errors, t, handleSubmit, setErrorMessage]);

    return (
        <div>
            <p className="mb-3 text-3xs">
                <span className="mr-1 text-secondary-100">*</span>
                <span className="font-bold text-brand-clr">
                    {t('Fields marked with are required to be filled.')}
                </span>
            </p>
            <UiForm onSubmit={handleSubmit(onSubmit)}>
                {!!errorMessage && (
                    <UiAlert className="mb-2" color="danger">
                        {t(errorMessage)}
                    </UiAlert>
                )}

                <div className="space-y-5">
                    <div className="flex gap-5">
                        <div className="custom-form-input-2 h-14 w-1/2">
                            <UiForm.Field
                                rightElement={
                                    <p className="mb-4 ml-5 text-secondary-100">
                                        *
                                    </p>
                                }
                                label={t('First name')}
                                size="sm"
                                error={
                                    errors.firstName &&
                                    errors.firstName.type === 'required'
                                        ? t('First name is required')
                                        : undefined
                                }
                                {...register('firstName', {required: true})}
                            />
                        </div>
                        <div className="custom-form-input-2 h-14 w-1/2">
                            <UiForm.Field
                                label={t('Last name')}
                                rightElement={
                                    <p className="mb-4 ml-5 text-secondary-100">
                                        *
                                    </p>
                                }
                                error={
                                    errors.lastName &&
                                    errors.lastName.type === 'required'
                                        ? t('Last name is required')
                                        : undefined
                                }
                                {...register('lastName', {required: true})}
                            />
                        </div>
                    </div>
                    <div className="custom-form-input-2 h-14">
                        <UiForm.Field
                            className="mt-3"
                            label={t('Email address')}
                            rightElement={
                                <p className="mb-4 ml-5 text-secondary-100">
                                    *
                                </p>
                            }
                            error={
                                errors.email && errors.email.type === 'required'
                                    ? t('Email address is required')
                                    : errors.email &&
                                      errors.email.type === 'pattern'
                                    ? t('Email address is invalid')
                                    : undefined
                            }
                            {...register('email', {
                                required: true,
                                pattern: regexp.email
                            })}
                        />
                    </div>

                    <div className="custom-form-input-2 h-14">
                        <UiForm.Field
                            className="mt-3"
                            label={t('Password')}
                            rightElement={
                                <p className="mb-4 ml-5 text-secondary-100">
                                    *
                                </p>
                            }
                            type={isPasswordShown ? 'text' : 'password'}
                            error={
                                errors.password &&
                                errors.password.type === 'required'
                                    ? t('Password is required')
                                    : undefined
                            }
                            {...register('password', {required: true})}
                        />
                    </div>
                </div>

                <UiForm.Control className="mt-5">
                    <UiCheckbox
                        {...register('isSubscribedToNewsletter', {
                            required: false
                        })}
                        className={`mr-0.5 h-4 w-4 rounded-none !border-gray-300 checked:hover:bg-secondary-100 focus:!border-gray-300 focus:ring-0`}
                    >
                        <div className="text-xs">
                            {t(
                                'I accept the processing of my personal data and the sending of electronic messages so that I can be informed about the campaigns.'
                            )}
                        </div>
                    </UiCheckbox>
                </UiForm.Control>

                <UiForm.Control className="mt-3">
                    <UiCheckbox
                        {...register('acceptedClarificationText', {
                            required: false
                        })}
                        className={`mr-0.5 h-4 w-4 rounded-none checked:hover:bg-secondary-100 focus:ring-0`}
                    >
                        <div
                            className="mt-1 text-xs"
                            onClick={onClarificationTextClick}
                            dangerouslySetInnerHTML={{
                                __html: t(
                                    'I have read and understood the <span id="clarificationText" class="text-primary-600">clarification text</span> for the processing of my personal data.'
                                )
                            }}
                        ></div>
                    </UiCheckbox>
                </UiForm.Control>

                <UiForm.Control className="mt-3">
                    <UiCheckbox
                        className={`mr-0.5 h-4 w-4 rounded-none checked:hover:bg-secondary-100 focus:ring-0`}
                        {...register('acceptedTermsOfMembership', {
                            required: true
                        })}
                    >
                        <div
                            className="w-full text-left text-xs"
                            onClick={onMembershipAgreementTextClick}
                            dangerouslySetInnerHTML={{
                                __html: t(
                                    'I accept the <span id="termsOfMembershipText" class="cursor-pointer text-primary-600">Terms of Membership</span> by clicking Sign Up.'
                                )
                            }}
                        />
                    </UiCheckbox>
                </UiForm.Control>

                <UiButton
                    className="mt-4 h-14 w-full rounded-sm bg-secondary-800 text-white hover:bg-secondary-800 hover:text-white focus:bg-secondary-800 focus:text-white"
                    type="submit"
                    size="xl"
                    loading={isLoading}
                >
                    {t('Sign Up')}
                </UiButton>
            </UiForm>
        </div>
    );
});

if (isDev) {
    SignUp.displayName = 'SignUp';
}

export default SignUp;
