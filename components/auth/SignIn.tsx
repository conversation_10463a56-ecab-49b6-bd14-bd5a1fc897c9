import {FC, memo, useCallback, useRef, useState} from 'react';
import {useRouter} from 'next/router';
import {signIn} from 'next-auth/react';
import {useForm} from 'react-hook-form';
import {isDev, regexp} from '@core/helpers';
import {useTrans} from '@core/hooks';
import {UiAlert, UiButton, UiForm} from '@core/components/ui';
import {EyeIcon, EyeSlashIcon} from '@core/icons/outline';

type SignInProps = {
    redirect?: string;
    onForgotPasswordClick: () => void;
};

const SignIn: FC<SignInProps> = memo(({redirect, onForgotPasswordClick}) => {
    const {
        register,
        formState: {errors},
        handleSubmit,
        watch
    } = useForm();
    const router = useRouter();
    const t = useTrans();
    const password: string = watch('password', '');
    const [isLoading, setIsLoading] = useState(false);
    const [isPasswordShown, setIsPasswordShown] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const inProgressRef = useRef(false);

    const onSubmit = useCallback(
        async (data: Record<string, any>) => {
            if (inProgressRef.current) {
                return;
            }

            inProgressRef.current = true;
            setIsLoading(true);
            setErrorMessage('');

            try {
                const result: any = await signIn('credentials', {
                    redirect: false,
                    email: data.email,
                    password: data.password
                });

                if (!!result?.error) {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new Error(result.error);
                }

                if (typeof redirect === 'string') {
                    await router.push(
                        !!redirect && redirect !== ''
                            ? redirect
                            : '/account/my-orders'
                    );
                    return;
                }
            } catch (error: any) {
                setErrorMessage(error.message);
            }

            inProgressRef.current = false;
            setIsLoading(false);
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [router, redirect]
    );

    return (
        <UiForm onSubmit={handleSubmit(onSubmit)}>
            {!!errorMessage && (
                <UiAlert className="mb-4" color="danger">
                    {t(errorMessage)}
                </UiAlert>
            )}

            <div className="custom-form-input">
                <UiForm.Field
                    label={t('Email address')}
                    error={
                        errors.email && errors.email.type === 'required'
                            ? t('Email address is required')
                            : errors.email && errors.email.type === 'pattern'
                            ? t('Email address is invalid')
                            : undefined
                    }
                    {...register('email', {
                        required: true,
                        pattern: regexp.email
                    })}
                />
            </div>

            <div className="custom-form-input">
                <UiForm.Field
                    className="mt-4"
                    label={t('Password')}
                    rightElement={
                        !!password &&
                        password.length > 0 && (
                            <div
                                className="cursor-pointer"
                                onClick={() =>
                                    setIsPasswordShown(!isPasswordShown)
                                }
                            >
                                {isPasswordShown && (
                                    <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                                )}
                                {!isPasswordShown && (
                                    <EyeIcon className="h-5 w-5 text-gray-400" />
                                )}
                            </div>
                        )
                    }
                    type={isPasswordShown ? 'text' : 'password'}
                    error={
                        errors.password && errors.password.type === 'required'
                            ? t('Password is required')
                            : undefined
                    }
                    {...register('password', {required: true})}
                />
            </div>

            <span
                className="flex cursor-pointer items-center justify-end pb-5 pt-3 text-sm text-secondary-800"
                onClick={onForgotPasswordClick}
            >
                {t('Forgot Password')}
            </span>

            <UiButton
                className="rounded-xs mt-4 h-14	w-full bg-secondary-800 text-sm font-bold text-white hover:bg-secondary-800 focus:bg-secondary-800"
                type="submit"
                size="xl"
                loading={isLoading}
            >
                {t('SIGN IN')}
            </UiButton>
        </UiForm>
    );
});

if (isDev) {
    SignIn.displayName = 'SignIn';
}

export default SignIn;
