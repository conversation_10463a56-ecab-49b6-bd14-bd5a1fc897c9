type requestData = {
    nameAndSurname: string;
    email: string;
    phone: string;
    message?: string;
};

type Extra = {
    label: string;
    value: any;
}[];

type ResponseData = {
    success: boolean;
    message?: string;
};

export const onSubmitHandler = async (
    data: requestData,
    extra?: Extra
): Promise<ResponseData> => {
    if (data.nameAndSurname.trim() && data.email.trim()) {
        try {
            const requestData = {
                name: data.nameAndSurname,
                email: data.email.replace(/ /g, '').toLowerCase(),
                phone: data.phone,
                message: data.message || 'Mesaj Yok!',
                extra: extra
            };

            const response = await fetch('/api/contact/post-contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error('Failed to submit data');
            }

            const responseData: ResponseData = await response.json();
            return responseData;
        } catch (err) {
            console.error('Error submitting data:', err);
            throw err;
        }
    } else {
        throw new Error('Name and email are required');
    }
};
