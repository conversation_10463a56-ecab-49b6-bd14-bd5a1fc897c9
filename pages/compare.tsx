import {GetServerSideProps} from 'next';
import {DynamicPage, ProductListItem} from '@core/types';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import {trim} from '@core/helpers';
import storeConfig from '~/store.config';
import Compare from '@components/pages/common/Compare';

export const getServerSideProps: GetServerSideProps = async ctx => {
    let {props, notFound} = await initDynamicPageParams(ctx, {
        isSecure: false
    });

    const productIds = decodeURIComponent(trim((ctx.query.q ?? '').toString()))
        .split(',')
        .slice(0, 5);

    props.products = [];
    if (productIds.length > 0) {
        try {
            const result = await erpClient.post('catalog/products', {
                productIds,
                fields: [
                    ...storeConfig.catalog.productListItemFields,
                    'features'
                ],
                paginated: true
            });

            if (Array.isArray(result.products) && result.products.length > 0) {
                props.products = result.products.filter(
                    (product: ProductListItem) => {
                        return productIds.includes(product.productId);
                    }
                );
            }
        } catch (error) {}
    }

    return {
        props,
        notFound
    };
};

const ComparePage: DynamicPage<typeof getServerSideProps> = props => {
    const {products, ...rest} = props;

    return <Compare products={products} {...rest} />;
};

ComparePage.layout = 'default';

export default ComparePage;
