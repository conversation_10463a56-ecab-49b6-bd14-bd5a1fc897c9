import '@core/assets/css/main.css';
import {FC, Fragment, memo, PropsWithChildren, useEffect, useMemo} from 'react';
import dynamic from 'next/dynamic';
import {SessionProvider} from 'next-auth/react';
import {appWithTranslation} from 'next-i18next';
import {Cookies} from 'react-cookie-consent';
import type {AppProps} from '@core/types';
import {isDev} from '@core/helpers';
import {useViewportSize} from '@core/hooks';
import {
    CartProvider,
    MobileProvider,
    StoreProvider,
    UiProvider
} from '@core/context';
import UiDialogs from '@core/context/UiContext/Dialogs';
import Head from '@components/common/Head';
import BlogLayout from '@components/layouts/Blog';
import DefaultLayout from '@components/layouts/Default';
import EmptyLayout from '@components/layouts/Empty';
import Socket from '@components/common/Socket';
import Font from '@components/common/Font';
import {Toaster} from 'sonner';
import 'swiper/css';
import 'swiper/css/pagination';
import WhatsappIcon from '@components/common/WhatsappIcon';
import Popup from '@components/common/Popup';
import Script from 'next/script';

const AccountLayout = dynamic(() => import('@components/layouts/Account'));
const AuthLayout = dynamic(() => import('@components/layouts/Auth'));

const layouts: Record<string, any> = {
    account: AccountLayout,
    auth: AuthLayout,
    default: DefaultLayout,
    blog: BlogLayout,
    empty: EmptyLayout
};

const Analytics = dynamic(() => import('@components/common/Analytics'), {
    ssr: false
});

const ScrollToTop = dynamic(() => import('@components/common/ScrollToTop'), {
    ssr: false
});

const App: FC<AppProps> = memo(({Component, pageProps}) => {
    const storeInfo = useMemo(() => pageProps.storeInfo, [pageProps.storeInfo]);
    const session = useMemo(() => pageProps.session, [pageProps.session]);
    const slug = useMemo(() => pageProps.slug, [pageProps.slug]);
    const cart = useMemo(() => pageProps.cart, [pageProps.cart]);

    const navigationItem = useMemo(
        () => pageProps.navigationItem,
        [pageProps.navigationItem]
    );

    const pageType = useMemo(
        () => pageProps.pageType ?? 'other',
        [pageProps.pageType]
    );

    let Layout: FC<PropsWithChildren<{pageProps: any}>> = DefaultLayout;
    if (typeof Component.layout === 'string') {
        Layout = layouts[Component.layout] ?? DefaultLayout;
    }

    let CookieConsent: any = Fragment;
    if (Cookies.get('nehir-cookie-consent') === undefined) {
        CookieConsent = dynamic(
            () => import('@components/common/CookieConsent'),
            {ssr: false}
        );
    }

    const {width} = useViewportSize();

    useEffect(() => {
        if (pageType !== 'product' && width < 768) {
            document.getElementsByTagName('html')[0].style.minHeight = '100%';
            document.getElementsByTagName('html')[0].style.height = 'auto';
            document.getElementsByTagName('html')[0].style.overflow = 'auto';
            document.getElementsByTagName('body')[0].style.minHeight = '100%';
            document.getElementsByTagName('body')[0].style.height = 'auto';
            document.getElementsByTagName('body')[0].style.overflow = 'auto';
            document.getElementById('__next')!.style.minHeight = '100%';
            document.getElementById('__next')!.style.height = 'auto';
            document.getElementById('__next')!.style.overflow = 'auto';
        }

        try {
            Cookies.set('lastViewed', '');
            Cookies.set('previouslyItems', '');
            Cookies.set('comparison-list', '');
        } catch (error) {}

        return () => {
            document.getElementsByTagName('html')[0].style.minHeight = '';
            document.getElementsByTagName('html')[0].style.height = '';
            document.getElementsByTagName('html')[0].style.overflow = '';
            document.getElementsByTagName('body')[0].style.minHeight = '';
            document.getElementsByTagName('body')[0].style.height = '';
            document.getElementsByTagName('body')[0].style.overflow = '';
            document.getElementById('__next')!.style.minHeight = '';
            document.getElementById('__next')!.style.height = '';
            document.getElementById('__next')!.style.overflow = '';
        };
    }, [pageType, width]);

    return (
        <>
            <Head />
            <Font />

            <SessionProvider session={session}>
                <UiProvider>
                    <StoreProvider
                        info={storeInfo}
                        navigationItem={navigationItem}
                        pageType={pageType}
                        slug={slug}
                    >
                        <CartProvider cart={cart}>
                            <MobileProvider>
                                <Layout pageProps={pageProps}>
                                    <Component {...pageProps} />
                                    <CookieConsent />
                                    <WhatsappIcon />
                                    <Popup />
                                    <ScrollToTop />
                                </Layout>
                                <Socket />
                                <UiDialogs />
                            </MobileProvider>
                        </CartProvider>
                    </StoreProvider>
                </UiProvider>
            </SessionProvider>
            <Toaster
                position="top-right"
                toastOptions={{duration: 3000, closeButton: true}}
                className="[&>li]:!shadow-sm"
                offset={20}
            />
            <Analytics />
            <Script
                id="ytag"
                async
                // @ts-ignore
                pid="ee55da86-dad8-43d8-b9f2-9c95c4fc3e98"
                src="https://cdn.yapaytech.com/ytag/script.js"
                strategy='lazyOnload'
            />
        </>
    );
});

if (isDev) {
    App.displayName = 'App';
}

export default appWithTranslation(App);
