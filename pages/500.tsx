import {memo} from 'react';
import {GetStaticPropsContext} from 'next';
import {StaticPage as StaticPageType} from '@core/types';
import {isDev} from '@core/helpers';
import {initStaticPageParams} from '@core/helpers/server';
import {useTrans} from '@core/hooks';

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    let {props, revalidate, notFound} = await initStaticPageParams(ctx);

    return {
        props,
        revalidate,
        notFound
    };
};

const ErrorPage: StaticPageType<typeof getStaticProps> = memo(() => {
    const t = useTrans();

    return (
        <div className="flex flex-1 flex-col items-center justify-center px-12 py-24">
            <div className="flex h-40 w-40 items-center justify-center rounded-lg border border-dashed border-red-500 text-red-500">
                <p className="text-6xl">500</p>
            </div>

            <h1 className="pt-8 text-center text-2xl font-semibold">
                {t('Oops, something went wrong.')}
            </h1>

            <p className="px-10 pt-2 text-center text-muted xl:w-8/12">
                {t(
                    'Try to refresh this page or feel free to contact us if the problem persists.'
                )}
            </p>
        </div>
    );
});

if (isDev) {
    ErrorPage.displayName = 'ErrorPage';
}

ErrorPage.layout = 'default';

export default ErrorPage;
