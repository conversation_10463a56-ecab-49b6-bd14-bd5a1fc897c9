import {NextApiRequest, NextApiResponse} from 'next';

export default async function postContact(
    req: NextApiRequest,
    res: NextApiResponse
): Promise<void> {
    const CONTACT_URL = process.env.NEXT_PUBLIC_API_URL?.replace('/store', '');

    const response = await fetch(`${CONTACT_URL}/help-desk/create-ticket`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(req.body)
    });

    res.status(response.status).json(await response.json());
}
