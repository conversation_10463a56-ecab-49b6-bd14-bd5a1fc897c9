import {NextApiRequest, NextApiResponse} from 'next';
import {sendMail, createEmailTemplate} from '@components/common/mail';

interface SendEmailRequest {
    to: string;
    subject: string;
    text: string;
}

export default async function handler(
    req: NextApiRequest,
    res: NextApiResponse
) {
    if (req.method !== 'POST') {
        return res.status(405).json({error: 'Method Not Allowed'});
    }

    const {to, subject, text}: SendEmailRequest = req.body;

    if (!to || !subject || !text) {
        return res.status(400).json({error: 'Missing required fields'});
    }

    const html = createEmailTemplate({to, subject, text});

    try {
        await sendMail({to, subject, text, html});
        return res.status(200).json({message: 'Email sent successfully'});
    } catch (error) {
        console.error('Error sending email: ', error);
        return res.status(500).json({error: 'Failed to send email'});
    }
}
