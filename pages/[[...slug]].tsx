import {memo} from 'react';
import {GetStaticPaths, GetStaticPropsContext} from 'next';
import {StaticPage as StaticPageType} from '@core/types';
import {isDev} from '@core/helpers';
import {initStaticPageParams} from '@core/helpers/server';
import ContentPage from '@components/pages/common/ContentPage';
import HomePage from '@components/pages/common/Home';
import CatalogPage from '@components/pages/store/Catalog';
import ProductPage from '@components/pages/store/Product';

export const getStaticPaths: GetStaticPaths = async context => {
    return {
        paths: [{params: {slug: []}}],
        fallback: 'blocking'
    };
};

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    let {props, revalidate, notFound} = await initStaticPageParams(ctx);

    if (!notFound) {
        if (
            props.pageType == 'home' &&
            typeof HomePage.initPageProps === 'function'
        ) {
            props = (await HomePage.initPageProps(props as any)) as any;
        } else if (
            props.pageType == 'catalog' &&
            typeof CatalogPage.initPageProps === 'function'
        ) {
            props = (await CatalogPage.initPageProps(props as any)) as any;
        } else if (
            props.pageType == 'product' &&
            typeof ProductPage.initPageProps === 'function'
        ) {
            props = (await ProductPage.initPageProps(props as any)) as any;
        } else if (
            props.pageType == 'page' &&
            typeof ContentPage.initPageProps === 'function'
        ) {
            props = (await ContentPage.initPageProps(props as any)) as any;
        }
    }

    return {
        props,
        revalidate,
        notFound
    };
};

const StaticPage: StaticPageType<typeof getStaticProps> = memo(props => {
    const {pageType, ...rest} = props;

    if (pageType === 'home') {
        // @ts-ignore
        return <HomePage key="home" {...rest} />;
    } else if (pageType === 'catalog') {
        // @ts-ignore
        return <CatalogPage key={rest.slug} {...rest} />;
    } else if (pageType === 'product') {
        // @ts-ignore
        return <ProductPage key={rest.slug} {...rest} />;
    } else if (pageType === 'page') {
        // @ts-ignore
        return <ContentPage key={rest.slug} {...rest} />;
    }

    return null;
});

if (isDev) {
    StaticPage.displayName = 'StaticPage';
}

StaticPage.layout = 'default';

export default StaticPage;
