import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import ResetPassword from '@components/pages/auth/ResetPassword';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound} = await initDynamicPageParams(ctx, {
        isSecure: false
    });
    const token = ctx.params?.token ?? null;

    if (!token) {
        return {
            props: {},
            notFound: true
        };
    }

    return {
        props: {
            ...props,
            token
        },
        notFound
    };
};

const ResetPasswordPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <ResetPassword {...props} />;
};

ResetPasswordPage.layout = 'auth';

export default ResetPasswordPage;
