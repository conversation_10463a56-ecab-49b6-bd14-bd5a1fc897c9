import {memo} from 'react';
import {GetStaticPropsContext} from 'next';
import {StaticPage as StaticPageType} from '@core/types';
import {isDev} from '@core/helpers';
import {initStaticPageParams} from '@core/helpers/server';

import RefundPage from '@components/pages/common/Refund';

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    let {props, revalidate, notFound} = await initStaticPageParams(ctx);

    return {
        props,
        revalidate,
        notFound
    };
};

const Refund: StaticPageType<typeof getStaticProps> = memo(() => {
    return <RefundPage />;
});

if (isDev) {
    Refund.displayName = 'Refund';
}

Refund.layout = 'default';

export default Refund;
