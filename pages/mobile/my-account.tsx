import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import MobileMyAccount from '@components/pages/mobile/MyAccount';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx, {
        isSecure: true
    });

    return {
        props,
        notFound,
        redirect
    };
};

const MobileMyAccountPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MobileMyAccount {...props} />;
};

MobileMyAccountPage.layout = 'empty';

export default MobileMyAccountPage;
