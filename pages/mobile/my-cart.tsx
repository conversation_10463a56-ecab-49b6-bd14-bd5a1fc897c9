import {GetStaticPropsContext} from 'next';
import {StaticPage as StaticPageType} from '@core/types';
import {initStaticPageParams} from '@core/helpers/server';
import MobileMyCart from '@components/pages/mobile/MyCart';

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    let {props, revalidate, notFound} = await initStaticPageParams(ctx);

    return {
        props,
        revalidate,
        notFound
    };
};

const MobileMyCartPage: StaticPageType<typeof getStaticProps> = props => {
    // @ts-ignore
    return <MobileMyCart {...props} />;
};

MobileMyCartPage.layout = 'empty';

export default MobileMyCartPage;
