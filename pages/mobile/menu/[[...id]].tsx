import {GetStaticPaths, GetStaticPropsContext} from 'next';
import {StaticPage as StaticPageType} from '@core/types';
import {initStaticPageParams} from '@core/helpers/server';
import MobileMenu from '@components/pages/mobile/Menu';

type Path = {params: {id: string[]}; locale?: string};

export const getStaticPaths: GetStaticPaths = async context => {
    const paths: Path[] = [{params: {id: []}}];

    return {
        paths: paths,
        fallback: 'blocking'
    };
};

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    let {props, revalidate, notFound} = await initStaticPageParams(ctx);

    const params = ctx.params;
    if (
        typeof params !== 'undefined' &&
        Array.isArray(params.id) &&
        params.id.length > 0
    ) {
        props.currentId = params.id[0];
    }

    return {
        props,
        revalidate,
        notFound
    };
};

const MobileMenuPage: StaticPageType<typeof getStaticProps> = props => {
    // @ts-ignore
    return <MobileMenu {...props} />;
};

MobileMenuPage.layout = 'empty';

export default MobileMenuPage;
