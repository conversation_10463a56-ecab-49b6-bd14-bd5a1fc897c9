import {memo} from 'react';
import {GetStaticPropsContext} from 'next';
import {StaticPage as StaticPageType} from '@core/types';
import {isDev} from '@core/helpers';
import {initStaticPageParams} from '@core/helpers/server';
import ContactPage from '@components/pages/common/Contact';

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    let {props, revalidate, notFound} = await initStaticPageParams(ctx);

    return {
        props,
        revalidate,
        notFound
    };
};

const Contact: StaticPageType<typeof getStaticProps> = memo(() => {
    return <ContactPage />;
});

if (isDev) {
    Contact.displayName = 'Contact';
}

Contact.layout = 'default';

export default Contact;
