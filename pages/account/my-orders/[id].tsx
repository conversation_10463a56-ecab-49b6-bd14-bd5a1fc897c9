import {GetServerSideProps} from 'next';
import storeConfig from '~/store.config';
import {DynamicPage} from '@core/types';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import Order from '@components/pages/account/MyOrders/Order';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx, {
        isSecure: true
    });

    if (!!props.session && props.session) {
        // Get locale.
        const locale = ctx.locale ?? storeConfig.defaultLocale;

        // Get orders.
        props.order = await erpClient.post('customers/order', {
            cartId: ctx.params?.id
        });

        // Delivery options.
        props.deliveryOptions = await erpClient.post(
            'checkout/delivery-options',
            {
                cartId: props.order.id
            }
        );

        // Payment methods.
        props.paymentMethods = await erpClient.post(
            'checkout/payment-methods',
            {
                cartId: props.order.id
            }
        );
    }

    return {
        props,
        notFound,
        redirect
    };
};

const OrderPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <Order {...props} />;
};

OrderPage.layout = 'account';

export default OrderPage;
