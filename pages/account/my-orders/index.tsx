import {GetServerSideProps} from 'next';
import storeConfig from '~/store.config';
import {DynamicPage} from '@core/types';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import MyOrders from '@components/pages/account/MyOrders';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx, {
        isSecure: true
    });

    if (!!props.session && props.session) {
        // Get locale.
        const locale = ctx.locale ?? storeConfig.defaultLocale;

        // Get orders.
        props.orders = await erpClient.post('customers/orders', {
            customerId: props.session.user?.id
        });

        // Payment methods.
        props.paymentMethods = await erpClient.post('checkout/payment-methods');
    }

    return {
        props,
        notFound,
        redirect
    };
};

const MyOrdersPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyOrders {...props} />;
};

MyOrdersPage.layout = 'account';

export default MyOrdersPage;
