import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import MyFavorites from '@components/pages/account/MyFavorites';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx, {
        isSecure: true
    });

    if (!!props.session && props.session) {
        const result = await erpClient.post('customers/favorite-products', {
            customerId: props.session.user?.id,
            limit: 32
        });

        props.products = result.data;
        props.total = result.total;
        props.hasNextPage = result.hasNextPage;
    }

    return {
        props,
        notFound,
        redirect
    };
};

const MyFavoritesPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyFavorites {...props} />;
};

MyFavoritesPage.layout = 'account';

export default MyFavoritesPage;
