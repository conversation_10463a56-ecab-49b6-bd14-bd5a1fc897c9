import {GetServerSideProps} from 'next';
import storeConfig from '~/store.config';
import {DynamicPage} from '@core/types';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import MyReviews from '@components/pages/account/MyReviews';

export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx, {
        isSecure: true
    });

    // Get locale.
    const locale = ctx.locale ?? storeConfig.defaultLocale;

    if (!!props.session && props.session) {
        const result = await erpClient.post('customers/reviews', {
            customerId: props.session.user?.id
        });
        const toReview: Record<string, any>[] = [];
        const reviewed: Record<string, any>[] = [];

        for (const item of result) {
            if (item.isReviewed) {
                reviewed.push(item);
            } else {
                toReview.push(item);
            }
        }

        props.toReviewCount = toReview.length;
        props.reviewedCount = reviewed.length;
        props.toReview = toReview;
        props.reviewed = reviewed.map(item => {
            const formatter = new Intl.DateTimeFormat(locale, {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
            const date = new Date(Date.parse(item.date));

            item.dateFornatted = formatter.format(date);

            return item;
        });
    }

    return {
        props,
        notFound,
        redirect
    };
};

const MyReviewsPage: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <MyReviews {...props} />;
};

MyReviewsPage.layout = 'account';

export default MyReviewsPage;
