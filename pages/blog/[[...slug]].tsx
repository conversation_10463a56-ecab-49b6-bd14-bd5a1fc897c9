import {memo} from 'react';
import {GetStaticPaths, GetStaticPropsContext} from 'next';
import {StaticPage as StaticPageType} from '@core/types';
import {isDev} from '@core/helpers';
import {determineBlogLayout} from '@core/helpers/blog';
import {initStaticPageParams} from '@core/helpers/server';
import BlogContent from '@components/pages/common/Blog/content';
import Blog from '@components/pages/common/Blog/home';

export const getStaticPaths: GetStaticPaths = async () => {
    return {
        paths: [{params: {slug: []}}],
        fallback: 'blocking'
    };
};

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    const {props, revalidate} = await initStaticPageParams(ctx);

    const {props: blogPageProps, notFound} = await determineBlogLayout({
        /* @ts-ignore */
        props,
        slug: ctx.params?.slug
    });

    return {
        props: blogPageProps,
        revalidate,
        notFound
    };
};

const BlogPage: StaticPageType<typeof getStaticProps> = memo(
    ({pageType, ...rest}) => {
        if (pageType === 'blog') {
            return <Blog {...rest} />;
        } else if (pageType === 'content') {
            return <BlogContent {...rest} />;
        } else {
            return null;
        }
    }
);

if (isDev) {
    BlogPage.displayName = 'BlogPage';
}

BlogPage.layout = 'blog';

export default BlogPage;
