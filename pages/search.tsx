import {GetServerSideProps} from 'next';
import storeConfig from '~/store.config';
import {DynamicPage} from '@core/types';
import {trim} from '@core/helpers';
import {erpClient, initDynamicPageParams} from '@core/helpers/server';
import CatalogPage from '@components/pages/store/Catalog';

export const getServerSideProps: GetServerSideProps = async ctx => {
    let {props, notFound} = await initDynamicPageParams(ctx, {
        isSecure: false
    });
    props.search = trim((ctx.query.query ?? '').toString());

    if (typeof CatalogPage.initPageProps === 'function') {
        props = (await CatalogPage.initPageProps(props as any)) as any;
    }

    try {
        const result = await erpClient.post('catalog/products', {
            search: props.search,
            fields: storeConfig.catalog.productListItemFields,
            skip: 0,
            limit: storeConfig.catalog.productsPerPage || 48,
            sort: {updatedAt: -1},
            paginated: true
        });

        // Set page type.
        props.pageType = 'catalog';

        // Set products.
        props.products = result.products;
        props.hasNextPage = result.hasNextPage;
        props.totalProductCountText = result.totalProductCountText;

        // Get filters.
        let filters: any = null;
        try {
            filters = await erpClient.post('catalog/filters', {
                search: props.search
            });
        } catch (error) {}

        // Set filters.
        if (!!filters) {
            props.filters = filters;
        }

        // Breadcrumbs.
        props.breadcrumbs = [];
    } catch (error) {}

    return {
        props,
        notFound
    };
};

const SearchPage: DynamicPage<typeof getServerSideProps> = props => {
    const {search, ...rest} = props;

    // @ts-ignore
    return <CatalogPage key={search} search={search} {...rest} />;
};

SearchPage.layout = 'default';

export default SearchPage;
