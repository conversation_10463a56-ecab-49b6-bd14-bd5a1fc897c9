import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';

import {erpClient, initDynamicPageParams} from '@core/helpers/server';

import PriceListPage from '@components/pages/common/PriceList';
import storeConfig from '~/store.config';
export const getServerSideProps: GetServerSideProps = async ctx => {
    const {props, notFound, redirect} = await initDynamicPageParams(ctx, {
        isSecure: false
    });

    // Get locale.
    const locale = ctx.locale ?? storeConfig.defaultLocale;

    // Get countries.
    props.countries = await erpClient.post('common/countries', {
        'en-US': locale
    });
    const defaultCountry = props.countries.find(
        (country: any) => !!country.isDefault
    );

    return {
        props,
        notFound,
        redirect
    };
};

const PriceList: DynamicPage<typeof getServerSideProps> = props => {
    // @ts-ignore
    return <PriceListPage {...props} />;
};

PriceList.layout = 'default';

export default PriceList;
