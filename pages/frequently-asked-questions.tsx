import {memo} from 'react';
import {GetStaticPropsContext} from 'next';
import {StaticPage as StaticPageType} from '@core/types';
import {isDev} from '@core/helpers';
import {initStaticPageParams} from '@core/helpers/server';

import FrequentlyAskedQuestionsPage from '@components/pages/common/FrequentlyAskedQuestions';

export const getStaticProps = async (ctx: GetStaticPropsContext) => {
    let {props, revalidate, notFound} = await initStaticPageParams(ctx);

    return {
        props,
        revalidate,
        notFound
    };
};

const FrequentlyAskedQuestions: StaticPageType<typeof getStaticProps> = memo(
    () => {
        return <FrequentlyAskedQuestionsPage />;
    }
);

if (isDev) {
    FrequentlyAskedQuestions.displayName = 'FrequentlyAskedQuestions';
}

FrequentlyAskedQuestions.layout = 'default';

export default FrequentlyAskedQuestions;
