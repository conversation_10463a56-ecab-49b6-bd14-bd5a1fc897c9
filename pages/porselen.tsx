import {GetServerSideProps} from 'next';
import {DynamicPage} from '@core/types';
import {initDynamicPageParams} from '@core/helpers/server';
import ThankYou from '@components/pages/store/ThankYou';
import Porcelain from '@components/pages/store/Porcelain';

export const getServerSideProps: GetServerSideProps = async ctx => {
    let {props, notFound} = await initDynamicPageParams(ctx, {
        isSecure: false
    });

    return {
        props,
        notFound
    };
};

const PorcelainPage: DynamicPage<typeof getServerSideProps> = () => {
    return <Porcelain />;
};

PorcelainPage.layout = 'default';

export default PorcelainPage;
